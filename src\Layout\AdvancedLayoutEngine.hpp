#pragma once

#include <QObject>
#include <QWidget>
#include <QLayout>
#include <QRect>
#include <QSize>
#include <QMargins>
#include <QResizeEvent>
#include <QTimer>
#include <QString>
#include <QVariant>
#include <QDebug>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>

namespace DeclarativeUI::Layout {

// **Forward declarations**
class GridLayout;
class FlexLayout;
class ResponsiveLayout;
class ConstraintLayout;
class LayoutItem;

/**
 * @brief Advanced Layout Engine providing CSS Grid, Flexbox, and Constraint-based layouts
 * 
 * This engine extends Qt's layout system with modern layout capabilities:
 * - CSS Grid-like layouts with named grid lines, areas, and advanced sizing
 * - Flexbox-style layouts with flexible item arrangement and alignment
 * - Responsive design with breakpoints and adaptive layouts
 * - Constraint-based positioning with relationships between elements
 */
class AdvancedLayoutEngine : public QObject {
    Q_OBJECT

public:
    explicit AdvancedLayoutEngine(QObject* parent = nullptr);
    ~AdvancedLayoutEngine() override;

    // **Layout Creation**
    std::shared_ptr<GridLayout> createGridLayout(QWidget* container);
    std::shared_ptr<FlexLayout> createFlexLayout(QWidget* container);
    std::shared_ptr<ResponsiveLayout> createResponsiveLayout(QWidget* container);
    std::shared_ptr<ConstraintLayout> createConstraintLayout(QWidget* container);

    // **Global Configuration**
    void setDefaultBreakpoints(const std::vector<int>& breakpoints);
    void setDefaultGridGap(int gap);
    void setDefaultFlexGap(int gap);
    void enableDebugMode(bool enabled);
    bool isDebugModeEnabled() const { return debug_mode_; }

    // **Responsive Management**
    void registerResponsiveContainer(QWidget* container, std::shared_ptr<ResponsiveLayout> layout);
    void unregisterResponsiveContainer(QWidget* container);
    void updateAllResponsiveLayouts();

signals:
    void breakpointChanged(int newBreakpoint);
    void layoutUpdated(QWidget* container);

private slots:
    void onScreenSizeChanged();
    void onContainerResized();

private:
    void setupGlobalEventHandling();
    void detectCurrentBreakpoint();
    int getCurrentScreenWidth() const;

    // **Configuration**
    std::vector<int> default_breakpoints_;
    int default_grid_gap_;
    int default_flex_gap_;
    bool debug_mode_;
    int current_breakpoint_;

    // **Responsive Management**
    std::unordered_map<QWidget*, std::shared_ptr<ResponsiveLayout>> responsive_containers_;
    QTimer* resize_timer_;
};

/**
 * @brief CSS Grid-like layout with advanced grid features
 */
class GridLayout : public QObject {
    Q_OBJECT

public:
    enum class GridSizing {
        Auto,           // auto
        MinContent,     // min-content
        MaxContent,     // max-content
        FitContent,     // fit-content()
        Fraction,       // fr units
        Fixed,          // px units
        Percentage      // % units
    };

    enum class GridAlignment {
        Start,
        End,
        Center,
        Stretch,
        SpaceBetween,
        SpaceAround,
        SpaceEvenly
    };

    struct GridTrack {
        GridSizing sizing = GridSizing::Auto;
        double value = 1.0;
        int min_size = 0;
        int max_size = INT_MAX;
        QString name;
    };

    struct GridArea {
        QString name;
        int row_start = 1;
        int row_end = 2;
        int col_start = 1;
        int col_end = 2;
    };

    struct GridItem {
        QWidget* widget = nullptr;
        int row_start = 1;
        int row_end = 2;
        int col_start = 1;
        int col_end = 2;
        QString area_name;
        GridAlignment justify_self = GridAlignment::Stretch;
        GridAlignment align_self = GridAlignment::Stretch;
        int order = 0;
    };

    explicit GridLayout(QWidget* container, QObject* parent = nullptr);

    // **Grid Definition**
    GridLayout& templateRows(const std::vector<GridTrack>& tracks);
    GridLayout& templateColumns(const std::vector<GridTrack>& tracks);
    GridLayout& templateAreas(const std::vector<QString>& areas);
    GridLayout& gap(int row_gap, int col_gap);
    GridLayout& gap(int gap);

    // **Grid Alignment**
    GridLayout& justifyContent(GridAlignment alignment);
    GridLayout& alignContent(GridAlignment alignment);
    GridLayout& justifyItems(GridAlignment alignment);
    GridLayout& alignItems(GridAlignment alignment);

    // **Item Management**
    GridLayout& addItem(QWidget* widget, int row, int col, int row_span = 1, int col_span = 1);
    GridLayout& addItem(QWidget* widget, const QString& area_name);
    GridLayout& setItemAlignment(QWidget* widget, GridAlignment justify, GridAlignment align);
    GridLayout& setItemOrder(QWidget* widget, int order);

    // **Named Lines and Areas**
    GridLayout& addNamedLine(const QString& name, int line, bool is_row);
    GridLayout& defineArea(const QString& name, int row_start, int row_end, int col_start, int col_end);

    // **Layout Operations**
    void updateLayout();
    void invalidateLayout();
    QSize calculateMinimumSize() const;
    QSize calculatePreferredSize() const;

private:
    void calculateGridSizes();
    void positionGridItems();
    int resolveTrackSize(const GridTrack& track, int available_space, int content_size) const;
    QRect calculateItemRect(const GridItem& item) const;
    void parseTemplateAreas(const std::vector<QString>& areas);

    QWidget* container_;
    std::vector<GridTrack> row_tracks_;
    std::vector<GridTrack> col_tracks_;
    std::vector<GridItem> items_;
    std::unordered_map<QString, GridArea> named_areas_;
    std::unordered_map<QString, std::vector<int>> named_lines_;

    // **Layout Properties**
    int row_gap_ = 0;
    int col_gap_ = 0;
    GridAlignment justify_content_ = GridAlignment::Start;
    GridAlignment align_content_ = GridAlignment::Start;
    GridAlignment justify_items_ = GridAlignment::Stretch;
    GridAlignment align_items_ = GridAlignment::Stretch;

    // **Calculated Values**
    std::vector<int> row_sizes_;
    std::vector<int> col_sizes_;
    std::vector<int> row_positions_;
    std::vector<int> col_positions_;
    bool layout_dirty_ = true;
};

/**
 * @brief Flexbox-style layout with flexible item arrangement
 */
class FlexLayout : public QObject {
    Q_OBJECT

public:
    enum class FlexDirection {
        Row,            // left to right
        RowReverse,     // right to left
        Column,         // top to bottom
        ColumnReverse   // bottom to top
    };

    enum class FlexWrap {
        NoWrap,         // single line
        Wrap,           // multi-line
        WrapReverse     // multi-line, reversed
    };

    enum class FlexAlignment {
        Start,
        End,
        Center,
        Stretch,
        Baseline,
        SpaceBetween,
        SpaceAround,
        SpaceEvenly
    };

    struct FlexItem {
        QWidget* widget = nullptr;
        double flex_grow = 0.0;
        double flex_shrink = 1.0;
        int flex_basis = -1;  // -1 means auto
        FlexAlignment align_self = FlexAlignment::Stretch;
        int order = 0;
        QMargins margins;
    };

    explicit FlexLayout(QWidget* container, QObject* parent = nullptr);

    // **Flex Container Properties**
    FlexLayout& direction(FlexDirection dir);
    FlexLayout& wrap(FlexWrap wrap_mode);
    FlexLayout& justifyContent(FlexAlignment alignment);
    FlexLayout& alignItems(FlexAlignment alignment);
    FlexLayout& alignContent(FlexAlignment alignment);
    FlexLayout& gap(int gap);

    // **Item Management**
    FlexLayout& addItem(QWidget* widget, double grow = 0.0, double shrink = 1.0, int basis = -1);
    FlexLayout& setItemFlex(QWidget* widget, double grow, double shrink, int basis);
    FlexLayout& setItemAlignment(QWidget* widget, FlexAlignment align);
    FlexLayout& setItemOrder(QWidget* widget, int order);
    FlexLayout& setItemMargins(QWidget* widget, const QMargins& margins);

    // **Layout Operations**
    void updateLayout();
    void invalidateLayout();
    QSize calculateMinimumSize() const;
    QSize calculatePreferredSize() const;

private:
    void calculateFlexLayout();
    void distributeSpace();
    void positionFlexItems();
    int getMainAxisSize(const QSize& size) const;
    int getCrossAxisSize(const QSize& size) const;
    QSize createSize(int main, int cross) const;

    QWidget* container_;
    std::vector<FlexItem> items_;

    // **Flex Properties**
    FlexDirection direction_ = FlexDirection::Row;
    FlexWrap wrap_ = FlexWrap::NoWrap;
    FlexAlignment justify_content_ = FlexAlignment::Start;
    FlexAlignment align_items_ = FlexAlignment::Stretch;
    FlexAlignment align_content_ = FlexAlignment::Start;
    int gap_ = 0;

    // **Calculated Values**
    std::vector<std::vector<FlexItem*>> flex_lines_;
    bool layout_dirty_ = true;
};

/**
 * @brief Responsive layout with breakpoints and adaptive behavior
 */
class ResponsiveLayout : public QObject {
    Q_OBJECT

public:
    struct Breakpoint {
        QString name;
        int min_width = 0;
        int max_width = INT_MAX;
        std::function<void()> layout_configurator;
    };

    struct ResponsiveProperty {
        QString property_name;
        std::unordered_map<QString, QVariant> breakpoint_values;
    };

    explicit ResponsiveLayout(QWidget* container, QObject* parent = nullptr);

    // **Breakpoint Management**
    ResponsiveLayout& addBreakpoint(const QString& name, int min_width, int max_width = INT_MAX);
    ResponsiveLayout& setLayoutForBreakpoint(const QString& breakpoint, std::function<void()> configurator);
    ResponsiveLayout& setPropertyForBreakpoint(const QString& property, const QString& breakpoint, const QVariant& value);

    // **Responsive Properties**
    ResponsiveLayout& responsiveProperty(const QString& property, const std::unordered_map<QString, QVariant>& values);
    ResponsiveLayout& responsiveSpacing(const std::unordered_map<QString, int>& values);
    ResponsiveLayout& responsiveMargins(const std::unordered_map<QString, QMargins>& values);
    ResponsiveLayout& responsiveVisibility(const std::unordered_map<QString, bool>& values);

    // **Layout Operations**
    void updateForCurrentBreakpoint(int container_width);
    void applyBreakpoint(const QString& breakpoint_name);
    QString getCurrentBreakpoint() const;
    std::vector<QString> getAvailableBreakpoints() const;

signals:
    void breakpointChanged(const QString& old_breakpoint, const QString& new_breakpoint);

private:
    QString findMatchingBreakpoint(int width) const;
    void applyResponsiveProperties(const QString& breakpoint);

    QWidget* container_;
    std::vector<Breakpoint> breakpoints_;
    std::vector<ResponsiveProperty> responsive_properties_;
    QString current_breakpoint_;
};

/**
 * @brief Constraint-based layout with relationships between elements
 */
class ConstraintLayout : public QObject {
    Q_OBJECT

public:
    enum class ConstraintType {
        Equal,              // item1.attr == item2.attr + constant
        LessThanOrEqual,    // item1.attr <= item2.attr + constant
        GreaterThanOrEqual  // item1.attr >= item2.attr + constant
    };

    enum class ConstraintAttribute {
        Left,
        Right,
        Top,
        Bottom,
        Width,
        Height,
        CenterX,
        CenterY,
        Baseline
    };

    enum class ConstraintPriority {
        Required = 1000,
        High = 750,
        Medium = 500,
        Low = 250
    };

    struct Constraint {
        QWidget* first_item = nullptr;
        ConstraintAttribute first_attribute = ConstraintAttribute::Left;
        ConstraintType relation = ConstraintType::Equal;
        QWidget* second_item = nullptr;
        ConstraintAttribute second_attribute = ConstraintAttribute::Left;
        double multiplier = 1.0;
        double constant = 0.0;
        ConstraintPriority priority = ConstraintPriority::Required;
        bool active = true;
        QString identifier;
    };

    explicit ConstraintLayout(QWidget* container, QObject* parent = nullptr);

    // **Constraint Creation**
    ConstraintLayout& addConstraint(QWidget* first_item, ConstraintAttribute first_attr,
                                   ConstraintType relation,
                                   QWidget* second_item, ConstraintAttribute second_attr,
                                   double multiplier = 1.0, double constant = 0.0,
                                   ConstraintPriority priority = ConstraintPriority::Required);

    ConstraintLayout& addConstraint(const QString& identifier, const Constraint& constraint);

    // **Convenience Methods**
    ConstraintLayout& centerHorizontally(QWidget* item, QWidget* reference = nullptr);
    ConstraintLayout& centerVertically(QWidget* item, QWidget* reference = nullptr);
    ConstraintLayout& alignLeft(QWidget* item, QWidget* reference, double offset = 0.0);
    ConstraintLayout& alignRight(QWidget* item, QWidget* reference, double offset = 0.0);
    ConstraintLayout& alignTop(QWidget* item, QWidget* reference, double offset = 0.0);
    ConstraintLayout& alignBottom(QWidget* item, QWidget* reference, double offset = 0.0);
    ConstraintLayout& setWidth(QWidget* item, int width);
    ConstraintLayout& setHeight(QWidget* item, int height);
    ConstraintLayout& setAspectRatio(QWidget* item, double ratio);

    // **Constraint Management**
    ConstraintLayout& removeConstraint(const QString& identifier);
    ConstraintLayout& activateConstraint(const QString& identifier, bool active = true);
    ConstraintLayout& setConstraintPriority(const QString& identifier, ConstraintPriority priority);

    // **Layout Operations**
    void updateLayout();
    void invalidateLayout();
    bool solveConstraints();
    std::vector<QString> getConflictingConstraints() const;

private:
    struct ConstraintVariable {
        QWidget* widget;
        ConstraintAttribute attribute;
        double value = 0.0;
        bool is_constant = false;
    };

    void buildConstraintSystem();
    bool solveLinearSystem();
    double getAttributeValue(QWidget* widget, ConstraintAttribute attribute) const;
    void setAttributeValue(QWidget* widget, ConstraintAttribute attribute, double value);
    QRect calculateWidgetGeometry(QWidget* widget) const;

    QWidget* container_;
    std::vector<Constraint> constraints_;
    std::unordered_map<QString, size_t> constraint_map_;
    std::vector<ConstraintVariable> variables_;
    bool layout_dirty_ = true;
    std::vector<QString> conflicting_constraints_;
};

/**
 * @brief Layout utilities and helper functions
 */
class LayoutUtils {
public:
    // **Size Calculations**
    static QSize calculateIntrinsicSize(QWidget* widget);
    static QSize calculateMinimumSize(QWidget* widget);
    static QSize calculateMaximumSize(QWidget* widget);
    static QSize calculatePreferredSize(QWidget* widget);

    // **Responsive Utilities**
    static int getScreenWidth();
    static int getScreenHeight();
    static double getDevicePixelRatio();
    static bool isTouchDevice();
    static bool isHighDPI();

    // **Layout Debugging**
    static void debugLayout(QWidget* container, const QString& layout_type);
    static void highlightLayoutBounds(QWidget* widget, const QColor& color = Qt::red);
    static void showLayoutGrid(QWidget* container, int grid_size = 10);

    // **Animation Helpers**
    static void animateLayoutChange(QWidget* container, int duration_ms = 300);
    static void animateWidgetMove(QWidget* widget, const QRect& target_geometry, int duration_ms = 300);
};

} // namespace DeclarativeUI::Layout
