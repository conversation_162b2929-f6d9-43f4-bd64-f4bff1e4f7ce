#pragma once

#include "TranslationManager.hpp"
#include "LocaleManager.hpp"
#include "RTLSupport.hpp"
#include <QObject>
#include <QString>
#include <QLocale>
#include <QWidget>
#include <QHash>
#include <QMutex>

namespace DeclarativeUI {
namespace I18n {

/**
 * @brief Central coordinator for all internationalization features
 * 
 * This class provides a unified interface to all i18n components:
 * - Translation management
 * - Locale formatting
 * - RTL support
 * - Language switching
 * - Global i18n configuration
 */
class I18nManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief I18n initialization configuration
     */
    struct I18nConfiguration {
        QString translations_directory = "translations";
        QString default_language = "en";
        QStringList fallback_languages = {"en"};
        bool enable_hot_reload = false;
        bool enable_rtl_support = true;
        bool enable_global_rtl = false;
        bool track_missing_translations = true;
        bool cache_translations = true;
        
        I18nConfiguration& translationsDir(const QString& dir) {
            translations_directory = dir;
            return *this;
        }
        
        I18nConfiguration& defaultLanguage(const QString& lang) {
            default_language = lang;
            return *this;
        }
        
        I18nConfiguration& fallbackLanguages(const QStringList& langs) {
            fallback_languages = langs;
            return *this;
        }
        
        I18nConfiguration& hotReload(bool enable) {
            enable_hot_reload = enable;
            return *this;
        }
        
        I18nConfiguration& rtlSupport(bool enable) {
            enable_rtl_support = enable;
            return *this;
        }
        
        I18nConfiguration& globalRTL(bool enable) {
            enable_global_rtl = enable;
            return *this;
        }
    };

    /**
     * @brief Language information
     */
    struct LanguageInfo {
        QString code;
        QString name;
        QString native_name;
        QLocale locale;
        bool is_rtl;
        bool is_available;
        
        LanguageInfo() = default;
        LanguageInfo(const QString& lang_code, const QLocale& loc)
            : code(lang_code), locale(loc) {
            name = loc.languageToString(loc.language());
            native_name = loc.nativeLanguageName();
            is_rtl = (loc.textDirection() == Qt::RightToLeft);
            is_available = false;
        }
    };

    /**
     * @brief Get singleton instance
     */
    static I18nManager& instance();

    /**
     * @brief Initialize the i18n system
     */
    bool initialize(const I18nConfiguration& config = I18nConfiguration());

    /**
     * @brief Check if system is initialized
     */
    bool isInitialized() const { return initialized_; }

    /**
     * @brief Language management
     */
    bool setLanguage(const QString& language_code);
    bool setLanguage(const QLocale& locale);
    QString getCurrentLanguage() const;
    QLocale getCurrentLocale() const;
    QStringList getAvailableLanguages() const;
    QList<LanguageInfo> getLanguageInfoList() const;

    /**
     * @brief Translation methods (delegated to TranslationManager)
     */
    QString translate(const QString& key, const TranslationManager::TranslationParams& params = TranslationManager::TranslationParams()) const;
    QString translate(const QString& key, const QString& default_text, const TranslationManager::TranslationParams& params = TranslationManager::TranslationParams()) const;
    QString translatePlural(const QString& key, int count, const TranslationManager::TranslationParams& params = TranslationManager::TranslationParams()) const;

    /**
     * @brief Locale formatting methods (delegated to LocaleManager)
     */
    QString formatNumber(double number, const LocaleManager::NumberFormatOptions& options = LocaleManager::NumberFormatOptions()) const;
    QString formatCurrency(double amount, const LocaleManager::CurrencyFormatOptions& options = LocaleManager::CurrencyFormatOptions()) const;
    QString formatDate(const QDate& date, const LocaleManager::DateTimeFormatOptions& options = LocaleManager::DateTimeFormatOptions()) const;
    QString formatTime(const QTime& time, const LocaleManager::DateTimeFormatOptions& options = LocaleManager::DateTimeFormatOptions()) const;
    QString formatDateTime(const QDateTime& datetime, const LocaleManager::DateTimeFormatOptions& options = LocaleManager::DateTimeFormatOptions()) const;

    /**
     * @brief RTL support methods (delegated to RTLSupport)
     */
    void applyRTLSupport(QWidget* widget, const RTLSupport::RTLConfiguration& config = RTLSupport::RTLConfiguration()) const;
    void applyRTLSupportRecursive(QWidget* widget, const RTLSupport::RTLConfiguration& config = RTLSupport::RTLConfiguration()) const;
    bool isRightToLeft() const;
    Qt::Alignment convertAlignment(Qt::Alignment alignment) const;

    /**
     * @brief Unified widget internationalization
     */
    void internationalizeWidget(QWidget* widget);
    void internationalizeWidgetRecursive(QWidget* widget);

    /**
     * @brief Configuration management
     */
    I18nConfiguration getConfiguration() const { return config_; }
    void updateConfiguration(const I18nConfiguration& config);

    /**
     * @brief Debugging and validation
     */
    QStringList getMissingTranslations() const;
    void clearMissingTranslations();
    bool validateTranslations() const;
    void generateTranslationReport() const;

    /**
     * @brief Hot reload support
     */
    void enableHotReload(bool enable = true);
    void reloadTranslations();

    /**
     * @brief Utility methods
     */
    bool isLanguageAvailable(const QString& language_code) const;
    LanguageInfo getLanguageInfo(const QString& language_code) const;
    QString detectSystemLanguage() const;
    void setSystemLanguage();

signals:
    /**
     * @brief Emitted when language changes
     */
    void languageChanged(const QString& new_language, const QString& old_language);

    /**
     * @brief Emitted when locale changes
     */
    void localeChanged(const QLocale& new_locale, const QLocale& old_locale);

    /**
     * @brief Emitted when layout direction changes
     */
    void layoutDirectionChanged(bool is_rtl);

    /**
     * @brief Emitted when translations are reloaded
     */
    void translationsReloaded();

    /**
     * @brief Emitted when a translation is missing
     */
    void translationMissing(const QString& key, const QString& language);

private:
    explicit I18nManager(QObject* parent = nullptr);
    ~I18nManager() override = default;

    // Helper methods
    void connectSignals();
    void updateLanguageInfo();
    void syncComponents();

    // State
    bool initialized_;
    I18nConfiguration config_;
    QHash<QString, LanguageInfo> language_info_;

    // Component references
    TranslationManager& translation_manager_;
    LocaleManager& locale_manager_;
    RTLSupport& rtl_support_;

    static I18nManager* instance_;
    static QMutex instance_mutex_;
};

/**
 * @brief Convenience macros for unified i18n access
 */
#define DUI_I18N DeclarativeUI::I18n::I18nManager::instance()
#define DUI_TR(key) DUI_I18N.translate(key)
#define DUI_TR_DEFAULT(key, default_text) DUI_I18N.translate(key, default_text)
#define DUI_TR_PLURAL(key, count) DUI_I18N.translatePlural(key, count)
#define DUI_FORMAT_NUM(num) DUI_I18N.formatNumber(num)
#define DUI_FORMAT_CURR(amount, code) DUI_I18N.formatCurrency(amount, LocaleManager::CurrencyFormatOptions().currency(code))
#define DUI_INTERNATIONALIZE(widget) DUI_I18N.internationalizeWidget(widget)
#define DUI_INTERNATIONALIZE_RECURSIVE(widget) DUI_I18N.internationalizeWidgetRecursive(widget)

} // namespace I18n
} // namespace DeclarativeUI
