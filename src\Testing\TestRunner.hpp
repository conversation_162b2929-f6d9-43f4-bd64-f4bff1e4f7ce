#pragma once

#include "TestingFramework.hpp"
#include <QObject>
#include <QTimer>
#include <QElapsedTimer>
#include <QThread>
#include <QThreadPool>
#include <QRunnable>
#include <QMutex>
#include <QWaitCondition>
#include <QFuture>
#include <QtConcurrent>
#include <memory>
#include <vector>
#include <queue>
#include <functional>

namespace DeclarativeUI::Testing {

/**
 * @brief Advanced test runner with parallel execution, filtering, and reporting
 */
class TestRunner : public QObject {
    Q_OBJECT

public:
    explicit TestRunner(QObject* parent = nullptr);
    ~TestRunner();

    // **Test Management**
    void addTest(std::unique_ptr<TestCase> test);
    void addTestSuite(const QString& suite_name, std::vector<std::unique_ptr<TestCase>> tests);
    void removeTest(const QString& test_name);
    void clearTests();

    // **Test Execution**
    void runAllTests();
    void runTestsByTag(const QStringList& tags);
    void runTestsByName(const QStringList& test_names);
    void runTestSuite(const QString& suite_name);
    void stopExecution();

    // **Configuration**
    void setConfiguration(const TestSuiteConfig& config);
    TestSuiteConfig getConfiguration() const { return config_; }
    
    void setParallelExecution(bool enabled);
    void setMaxConcurrentTests(int max_concurrent);
    void setTimeout(int seconds);
    void setRetryCount(int count);
    void setFailFast(bool enabled);  // Stop on first failure

    // **Filtering**
    void setTagFilter(const QStringList& required_tags, const QStringList& excluded_tags = {});
    void setNameFilter(const QRegularExpression& pattern);
    void clearFilters();

    // **Results**
    std::vector<TestResult> getResults() const { return results_; }
    TestResult getResult(const QString& test_name) const;
    int getPassedCount() const;
    int getFailedCount() const;
    int getSkippedCount() const;
    int getErrorCount() const;
    double getSuccessRate() const;
    qint64 getTotalExecutionTime() const { return total_execution_time_; }

    // **Reporting**
    void generateReport(const QString& output_file, const QString& format = "html");
    void generateJUnitXML(const QString& output_file);
    void generateJSON(const QString& output_file);
    void printSummary();

signals:
    void testStarted(const QString& test_name);
    void testFinished(const TestResult& result);
    void suiteStarted(const QString& suite_name);
    void suiteFinished(const QString& suite_name, int passed, int failed);
    void executionStarted(int total_tests);
    void executionFinished(int passed, int failed, qint64 total_time);
    void progressUpdated(int completed, int total);

private slots:
    void onTestStarted(const QString& test_name);
    void onTestFinished(const TestResult& result);

private:
    // **Test Execution**
    class TestExecutor : public QRunnable {
    public:
        TestExecutor(TestCase* test, TestRunner* runner);
        void run() override;

    private:
        TestCase* test_;
        TestRunner* runner_;
    };

    void executeTest(TestCase* test);
    void executeTestSequential(TestCase* test);
    void executeTestParallel(TestCase* test);
    bool shouldRunTest(TestCase* test) const;
    void processTestResult(const TestResult& result);

    // **Data Members**
    TestSuiteConfig config_;
    std::vector<std::unique_ptr<TestCase>> tests_;
    std::unordered_map<QString, std::vector<std::unique_ptr<TestCase>>> test_suites_;
    std::vector<TestResult> results_;
    
    // **Execution Control**
    bool parallel_execution_ = false;
    int max_concurrent_tests_ = QThread::idealThreadCount();
    int timeout_seconds_ = 30;
    int retry_count_ = 0;
    bool fail_fast_ = false;
    bool execution_stopped_ = false;
    
    // **Filtering**
    QStringList required_tags_;
    QStringList excluded_tags_;
    QRegularExpression name_filter_;
    
    // **Statistics**
    QElapsedTimer execution_timer_;
    qint64 total_execution_time_ = 0;
    QAtomicInt completed_tests_;
    QAtomicInt running_tests_;
    
    // **Thread Safety**
    mutable QMutex results_mutex_;
    mutable QMutex execution_mutex_;
    QWaitCondition execution_finished_;
    
    // **Utilities**
    std::unique_ptr<UIAutomation> ui_automation_;
    std::unique_ptr<VisualTesting> visual_testing_;
    std::unique_ptr<ComponentTester> component_tester_;
    std::unique_ptr<PerformanceBenchmark> performance_benchmark_;
};

/**
 * @brief Advanced test reporter with multiple output formats
 */
class TestReporter : public QObject {
    Q_OBJECT

public:
    explicit TestReporter(QObject* parent = nullptr);

    // **Report Generation**
    void generateHTMLReport(const std::vector<TestResult>& results, const QString& output_file);
    void generateJUnitXMLReport(const std::vector<TestResult>& results, const QString& output_file);
    void generateJSONReport(const std::vector<TestResult>& results, const QString& output_file);
    void generateMarkdownReport(const std::vector<TestResult>& results, const QString& output_file);
    void generateCSVReport(const std::vector<TestResult>& results, const QString& output_file);

    // **Console Output**
    void printSummary(const std::vector<TestResult>& results);
    void printDetailedResults(const std::vector<TestResult>& results);
    void printProgressBar(int completed, int total);

    // **Configuration**
    void setIncludePassedTests(bool include) { include_passed_tests_ = include; }
    void setIncludeMetadata(bool include) { include_metadata_ = include; }
    void setIncludeStackTraces(bool include) { include_stack_traces_ = include; }
    void setColorOutput(bool enabled) { color_output_ = enabled; }

    // **Custom Templates**
    void setHTMLTemplate(const QString& template_file);
    void setCustomCSS(const QString& css_content);

signals:
    void reportGenerated(const QString& output_file, const QString& format);

private:
    // **HTML Generation**
    QString generateHTMLHeader(const std::vector<TestResult>& results);
    QString generateHTMLSummary(const std::vector<TestResult>& results);
    QString generateHTMLTestList(const std::vector<TestResult>& results);
    QString generateHTMLFooter();

    // **XML Generation**
    QString generateJUnitTestSuite(const std::vector<TestResult>& results);
    QString generateJUnitTestCase(const TestResult& result);

    // **Utilities**
    QString formatDuration(qint64 milliseconds);
    QString formatTimestamp(const QDateTime& timestamp);
    QString escapeHTML(const QString& text);
    QString escapeXML(const QString& text);
    QString getStatusColor(TestResult::Status status);
    QString getStatusIcon(TestResult::Status status);

    // **Configuration**
    bool include_passed_tests_ = true;
    bool include_metadata_ = true;
    bool include_stack_traces_ = true;
    bool color_output_ = true;
    QString html_template_;
    QString custom_css_;
};

/**
 * @brief Accessibility testing utilities
 */
class AccessibilityTester : public QObject {
    Q_OBJECT

public:
    explicit AccessibilityTester(QObject* parent = nullptr);

    // **Accessibility Checks**
    struct AccessibilityResult {
        bool passed = false;
        QString rule_name;
        QString description;
        QString severity;  // "error", "warning", "info"
        QString element_info;
        QStringList suggestions;
        QJsonObject metadata;
    };

    std::vector<AccessibilityResult> checkWidget(QWidget* widget);
    std::vector<AccessibilityResult> checkColorContrast(QWidget* widget);
    std::vector<AccessibilityResult> checkKeyboardNavigation(QWidget* widget);
    std::vector<AccessibilityResult> checkScreenReaderSupport(QWidget* widget);
    std::vector<AccessibilityResult> checkFocusManagement(QWidget* widget);

    // **WCAG Compliance**
    bool checkWCAGCompliance(QWidget* widget, const QString& level = "AA");
    QJsonObject generateWCAGReport(QWidget* widget);

    // **Configuration**
    void setColorContrastRatio(double ratio) { min_contrast_ratio_ = ratio; }
    void setFontSizeMinimum(int size) { min_font_size_ = size; }
    void setCheckLevel(const QString& level) { check_level_ = level; }

signals:
    void accessibilityCheckCompleted(const std::vector<AccessibilityResult>& results);

private:
    double calculateContrastRatio(const QColor& foreground, const QColor& background);
    bool isKeyboardAccessible(QWidget* widget);
    bool hasProperLabels(QWidget* widget);
    bool hasProperRoles(QWidget* widget);

    double min_contrast_ratio_ = 4.5;  // WCAG AA standard
    int min_font_size_ = 12;
    QString check_level_ = "AA";
};

/**
 * @brief Memory profiler for detecting leaks and monitoring usage
 */
class MemoryProfiler : public QObject {
    Q_OBJECT

public:
    explicit MemoryProfiler(QObject* parent = nullptr);

    // **Memory Monitoring**
    struct MemorySnapshot {
        qint64 timestamp_ms;
        qint64 total_memory_kb;
        qint64 heap_memory_kb;
        qint64 stack_memory_kb;
        int object_count;
        QHash<QString, int> object_types;
        QJsonObject metadata;
    };

    void startProfiling();
    void stopProfiling();
    MemorySnapshot takeSnapshot();
    std::vector<MemorySnapshot> getSnapshots() const { return snapshots_; }

    // **Leak Detection**
    struct LeakReport {
        bool leaks_detected = false;
        qint64 leaked_memory_kb = 0;
        int leaked_objects = 0;
        QStringList leaked_types;
        std::vector<MemorySnapshot> leak_points;
        QJsonObject metadata;
    };

    LeakReport detectLeaks();
    void clearSnapshots();

    // **Configuration**
    void setSnapshotInterval(int milliseconds) { snapshot_interval_ms_ = milliseconds; }
    void setLeakThreshold(qint64 threshold_kb) { leak_threshold_kb_ = threshold_kb; }

signals:
    void snapshotTaken(const MemorySnapshot& snapshot);
    void leaksDetected(const LeakReport& report);

private slots:
    void takePeriodicSnapshot();

private:
    qint64 getCurrentMemoryUsage();
    int getCurrentObjectCount();
    QHash<QString, int> getCurrentObjectTypes();

    bool profiling_active_ = false;
    int snapshot_interval_ms_ = 1000;
    qint64 leak_threshold_kb_ = 1024;  // 1MB
    std::vector<MemorySnapshot> snapshots_;
    QTimer* snapshot_timer_;
};

} // namespace DeclarativeUI::Testing
