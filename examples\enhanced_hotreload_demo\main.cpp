#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QTextEdit>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QDebug>

// Enhanced Hot-Reload System Headers
#include "HotReload/HotReloadConfig.hpp"
#include "HotReload/AdvancedFileFilter.hpp"
#include "HotReload/HotReloadDashboard.hpp"
#include "HotReload/SelectiveReloadManager.hpp"
#include "HotReload/ErrorRecoveryManager.hpp"

using namespace DeclarativeUI::HotReload;

class EnhancedHotReloadDemo : public QMainWindow {
    Q_OBJECT

public:
    EnhancedHotReloadDemo(QWidget* parent = nullptr) : QMainWindow(parent) {
        setupUI();
        initializeHotReloadSystem();
        connectSignals();
        
        // Start with a demo configuration
        loadDemoConfiguration();
        
        setWindowTitle("Enhanced Hot-Reload System Demo");
        resize(1200, 800);
    }

private slots:
    void onLoadConfiguration() {
        QString filename = QFileDialog::getOpenFileName(
            this, "Load Configuration", "", "JSON Files (*.json)");
        if (!filename.isEmpty()) {
            if (config_.loadFromFile(filename)) {
                status_label_->setText("Configuration loaded successfully");
                updateConfigurationDisplay();
            } else {
                QMessageBox::warning(this, "Error", "Failed to load configuration");
            }
        }
    }
    
    void onSaveConfiguration() {
        QString filename = QFileDialog::getSaveFileName(
            this, "Save Configuration", "", "JSON Files (*.json)");
        if (!filename.isEmpty()) {
            if (config_.saveToFile(filename)) {
                status_label_->setText("Configuration saved successfully");
            } else {
                QMessageBox::warning(this, "Error", "Failed to save configuration");
            }
        }
    }
    
    void onStartMonitoring() {
        dashboard_->startMonitoring();
        reload_manager_.startMonitoring();
        start_button_->setEnabled(false);
        stop_button_->setEnabled(true);
        status_label_->setText("Hot-reload monitoring started");
    }
    
    void onStopMonitoring() {
        dashboard_->stopMonitoring();
        reload_manager_.stopMonitoring();
        start_button_->setEnabled(true);
        stop_button_->setEnabled(false);
        status_label_->setText("Hot-reload monitoring stopped");
    }
    
    void onTestSelectiveReload() {
        // Demonstrate selective reload functionality
        QStringList components = {"demo_widget", "toolbar", "status_bar"};
        
        for (const QString& component : components) {
            ReloadResult result = reload_manager_.reloadComponent(component);
            QString message = QString("Reload %1: %2")
                .arg(component)
                .arg(result.success ? "SUCCESS" : "FAILED");
            log_text_->append(message);
        }
    }
    
    void onTestErrorRecovery() {
        // Simulate various error scenarios for testing
        ErrorContext error;
        error.error_type = ErrorType::FileNotFound;
        error.file_path = "test_file.cpp";
        error.error_message = "File not found during reload";
        
        RecoveryResult result = recovery_manager_.handleError(error);
        QString message = QString("Error recovery test: %1")
            .arg(result.success ? "SUCCESS" : "FAILED");
        log_text_->append(message);
    }
    
    void onFilterTest() {
        // Demonstrate advanced file filtering
        QStringList test_files = {
            "src/main.cpp",
            "src/widget.hpp", 
            "build/main.o",
            "temp/cache.tmp",
            "docs/readme.md",
            "large_file.bin"
        };
        
        QStringList filtered = filter_.filterFiles(test_files);
        
        log_text_->append("=== File Filter Test ===");
        log_text_->append("Input files:");
        for (const QString& file : test_files) {
            log_text_->append("  " + file);
        }
        log_text_->append("Filtered files:");
        for (const QString& file : filtered) {
            log_text_->append("  " + file);
        }
        
        // Show filter statistics
        FilterStats stats = filter_.getStatistics();
        log_text_->append(QString("Filter stats - Processed: %1, Accepted: %2, Cache hits: %3")
            .arg(stats.files_processed)
            .arg(stats.files_accepted)
            .arg(stats.cache_hits));
    }

private:
    void setupUI() {
        auto* central_widget = new QWidget;
        setCentralWidget(central_widget);
        
        auto* main_layout = new QVBoxLayout(central_widget);
        
        // Control panel
        auto* control_group = new QGroupBox("Hot-Reload Controls");
        auto* control_layout = new QHBoxLayout(control_group);
        
        start_button_ = new QPushButton("Start Monitoring");
        stop_button_ = new QPushButton("Stop Monitoring");
        stop_button_->setEnabled(false);
        
        auto* load_config_button = new QPushButton("Load Config");
        auto* save_config_button = new QPushButton("Save Config");
        auto* test_reload_button = new QPushButton("Test Selective Reload");
        auto* test_recovery_button = new QPushButton("Test Error Recovery");
        auto* test_filter_button = new QPushButton("Test File Filter");
        
        control_layout->addWidget(start_button_);
        control_layout->addWidget(stop_button_);
        control_layout->addWidget(load_config_button);
        control_layout->addWidget(save_config_button);
        control_layout->addWidget(test_reload_button);
        control_layout->addWidget(test_recovery_button);
        control_layout->addWidget(test_filter_button);
        control_layout->addStretch();
        
        main_layout->addWidget(control_group);
        
        // Main content area with splitter
        auto* splitter = new QSplitter(Qt::Horizontal);
        
        // Left side: Dashboard
        dashboard_ = new HotReloadDashboard();
        splitter->addWidget(dashboard_);
        
        // Right side: Configuration and log
        auto* right_widget = new QWidget;
        auto* right_layout = new QVBoxLayout(right_widget);
        
        // Configuration display
        auto* config_group = new QGroupBox("Current Configuration");
        auto* config_layout = new QVBoxLayout(config_group);
        config_display_ = new QTextEdit;
        config_display_->setMaximumHeight(200);
        config_layout->addWidget(config_display_);
        right_layout->addWidget(config_group);
        
        // Log display
        auto* log_group = new QGroupBox("Activity Log");
        auto* log_layout = new QVBoxLayout(log_group);
        log_text_ = new QTextEdit;
        log_layout->addWidget(log_text_);
        right_layout->addWidget(log_group);
        
        splitter->addWidget(right_widget);
        splitter->setSizes({600, 400});
        
        main_layout->addWidget(splitter);
        
        // Status bar
        status_label_ = new QLabel("Ready");
        statusBar()->addWidget(status_label_);
        
        // Connect control buttons
        connect(start_button_, &QPushButton::clicked, this, &EnhancedHotReloadDemo::onStartMonitoring);
        connect(stop_button_, &QPushButton::clicked, this, &EnhancedHotReloadDemo::onStopMonitoring);
        connect(load_config_button, &QPushButton::clicked, this, &EnhancedHotReloadDemo::onLoadConfiguration);
        connect(save_config_button, &QPushButton::clicked, this, &EnhancedHotReloadDemo::onSaveConfiguration);
        connect(test_reload_button, &QPushButton::clicked, this, &EnhancedHotReloadDemo::onTestSelectiveReload);
        connect(test_recovery_button, &QPushButton::clicked, this, &EnhancedHotReloadDemo::onTestErrorRecovery);
        connect(test_filter_button, &QPushButton::clicked, this, &EnhancedHotReloadDemo::onFilterTest);
    }
    
    void initializeHotReloadSystem() {
        // Initialize configuration system
        config_.setActiveProfile("development");
        
        // Setup advanced file filter with demo rules
        setupDemoFileFilter();
        
        // Configure dashboard
        dashboard_->setConfiguration(&config_);
        dashboard_->setAdvancedFilter(&filter_);
        dashboard_->setUpdateInterval(1000);
        dashboard_->enableRealTimeUpdates(true);
        
        // Setup selective reload manager
        reload_manager_.setConfiguration(&config_);
        reload_manager_.setFileFilter(&filter_);
        
        // Register demo components for selective reload
        reload_manager_.registerComponent("demo_widget", this);
        reload_manager_.registerComponent("toolbar", this);
        reload_manager_.registerComponent("status_bar", statusBar());
        
        // Configure error recovery
        recovery_manager_.enableAutoRecovery(true);
        recovery_manager_.setMaxRecoveryAttempts(3);
        
        // Connect systems together
        dashboard_->setSelectiveReloadManager(&reload_manager_);
        dashboard_->setErrorRecoveryManager(&recovery_manager_);
        reload_manager_.setErrorRecoveryManager(&recovery_manager_);
    }
    
    void setupDemoFileFilter() {
        // Add common exclusion rules
        FilterRule build_rule;
        build_rule.pattern = "build/**/*";
        build_rule.match_type = FilterRule::MatchType::Glob;
        build_rule.type = FilterRule::Type::Exclude;
        build_rule.priority = 100;
        filter_.addRule(build_rule);
        
        FilterRule temp_rule;
        temp_rule.pattern = "*.{tmp,bak,cache}";
        temp_rule.match_type = FilterRule::MatchType::Glob;
        temp_rule.type = FilterRule::Type::Exclude;
        temp_rule.priority = 90;
        filter_.addRule(temp_rule);
        
        // Add size-based exclusion for large files
        FilterRule size_rule;
        size_rule.match_type = FilterRule::MatchType::Size;
        size_rule.type = FilterRule::Type::Exclude;
        size_rule.min_size_bytes = 10 * 1024 * 1024;  // 10MB
        size_rule.max_size_bytes = LLONG_MAX;
        size_rule.priority = 80;
        filter_.addRule(size_rule);
        
        // Include source files
        FilterRule source_rule;
        source_rule.pattern = "*.{cpp,hpp,h,c,qml,js,css}";
        source_rule.match_type = FilterRule::MatchType::Glob;
        source_rule.type = FilterRule::Type::Include;
        source_rule.priority = 50;
        filter_.addRule(source_rule);
    }
    
    void loadDemoConfiguration() {
        // Create a demo development profile
        ConfigProfile dev_profile;
        dev_profile.name = "development";
        dev_profile.watch_interval = 100;
        dev_profile.enable_debug_logging = true;
        dev_profile.auto_reload = true;
        dev_profile.debounce_delay = 500;
        dev_profile.max_reload_attempts = 3;
        dev_profile.backup_enabled = true;
        
        config_.addProfile(dev_profile);
        config_.setActiveProfile("development");
        
        updateConfigurationDisplay();
        status_label_->setText("Demo configuration loaded");
    }
    
    void updateConfigurationDisplay() {
        ConfigProfile* active = config_.getActiveProfile();
        if (active) {
            QString config_text = QString(
                "Active Profile: %1\n"
                "Watch Interval: %2ms\n"
                "Debug Logging: %3\n"
                "Auto Reload: %4\n"
                "Debounce Delay: %5ms\n"
                "Max Attempts: %6\n"
                "Backup Enabled: %7"
            ).arg(active->name)
             .arg(active->watch_interval)
             .arg(active->enable_debug_logging ? "Yes" : "No")
             .arg(active->auto_reload ? "Yes" : "No")
             .arg(active->debounce_delay)
             .arg(active->max_reload_attempts)
             .arg(active->backup_enabled ? "Yes" : "No");
            
            config_display_->setPlainText(config_text);
        }
    }
    
    void connectSignals() {
        // Connect dashboard signals for logging
        connect(dashboard_, &HotReloadDashboard::monitoringStarted, 
                this, [this]() { log_text_->append("Dashboard monitoring started"); });
        connect(dashboard_, &HotReloadDashboard::monitoringStopped,
                this, [this]() { log_text_->append("Dashboard monitoring stopped"); });
    }

private:
    // UI Components
    QPushButton* start_button_;
    QPushButton* stop_button_;
    QLabel* status_label_;
    QTextEdit* config_display_;
    QTextEdit* log_text_;
    
    // Hot-Reload System Components
    HotReloadConfig config_;
    AdvancedFileFilter filter_;
    HotReloadDashboard* dashboard_;
    SelectiveReloadManager reload_manager_;
    ErrorRecoveryManager recovery_manager_;
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    EnhancedHotReloadDemo demo;
    demo.show();
    
    return app.exec();
}

#include "main.moc"
