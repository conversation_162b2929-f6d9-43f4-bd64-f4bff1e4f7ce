#include "AdvancedLayoutEngine.hpp"
#include <QApplication>
#include <QScreen>
#include <QResizeEvent>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QGraphicsEffect>
#include <QPainter>
#include <QStyleOption>
#include <algorithm>
#include <cmath>

namespace DeclarativeUI::Layout {

// **AdvancedLayoutEngine Implementation**

AdvancedLayoutEngine::AdvancedLayoutEngine(QObject* parent)
    : QObject(parent)
    , default_breakpoints_{576, 768, 992, 1200}  // Bootstrap-like breakpoints
    , default_grid_gap_(16)
    , default_flex_gap_(8)
    , debug_mode_(false)
    , current_breakpoint_(0)
{
    setupGlobalEventHandling();
    detectCurrentBreakpoint();
    
    qDebug() << "🏗️ Advanced Layout Engine initialized with breakpoints:" << default_breakpoints_;
}

AdvancedLayoutEngine::~AdvancedLayoutEngine() {
    qDebug() << "🏗️ Advanced Layout Engine destroyed";
}

std::shared_ptr<GridLayout> AdvancedLayoutEngine::createGridLayout(QWidget* container) {
    auto grid_layout = std::make_shared<GridLayout>(container, this);
    grid_layout->gap(default_grid_gap_);
    
    qDebug() << "🔲 Created Grid Layout for container:" << container->objectName();
    return grid_layout;
}

std::shared_ptr<FlexLayout> AdvancedLayoutEngine::createFlexLayout(QWidget* container) {
    auto flex_layout = std::make_shared<FlexLayout>(container, this);
    flex_layout->gap(default_flex_gap_);
    
    qDebug() << "📦 Created Flex Layout for container:" << container->objectName();
    return flex_layout;
}

std::shared_ptr<ResponsiveLayout> AdvancedLayoutEngine::createResponsiveLayout(QWidget* container) {
    auto responsive_layout = std::make_shared<ResponsiveLayout>(container, this);
    
    // Add default breakpoints
    responsive_layout->addBreakpoint("xs", 0, 575)
                     .addBreakpoint("sm", 576, 767)
                     .addBreakpoint("md", 768, 991)
                     .addBreakpoint("lg", 992, 1199)
                     .addBreakpoint("xl", 1200);
    
    registerResponsiveContainer(container, responsive_layout);
    
    qDebug() << "📱 Created Responsive Layout for container:" << container->objectName();
    return responsive_layout;
}

std::shared_ptr<ConstraintLayout> AdvancedLayoutEngine::createConstraintLayout(QWidget* container) {
    auto constraint_layout = std::make_shared<ConstraintLayout>(container, this);
    
    qDebug() << "🔗 Created Constraint Layout for container:" << container->objectName();
    return constraint_layout;
}

void AdvancedLayoutEngine::setDefaultBreakpoints(const std::vector<int>& breakpoints) {
    default_breakpoints_ = breakpoints;
    std::sort(default_breakpoints_.begin(), default_breakpoints_.end());
    detectCurrentBreakpoint();
    updateAllResponsiveLayouts();
}

void AdvancedLayoutEngine::setDefaultGridGap(int gap) {
    default_grid_gap_ = gap;
}

void AdvancedLayoutEngine::setDefaultFlexGap(int gap) {
    default_flex_gap_ = gap;
}

void AdvancedLayoutEngine::enableDebugMode(bool enabled) {
    debug_mode_ = enabled;
    qDebug() << "🐛 Layout debug mode:" << (enabled ? "enabled" : "disabled");
}

void AdvancedLayoutEngine::registerResponsiveContainer(QWidget* container, std::shared_ptr<ResponsiveLayout> layout) {
    responsive_containers_[container] = layout;
    
    // Connect to container's resize events
    container->installEventFilter(this);
}

void AdvancedLayoutEngine::unregisterResponsiveContainer(QWidget* container) {
    responsive_containers_.erase(container);
    container->removeEventFilter(this);
}

void AdvancedLayoutEngine::updateAllResponsiveLayouts() {
    int screen_width = getCurrentScreenWidth();
    
    for (auto& [container, layout] : responsive_containers_) {
        if (container && layout) {
            layout->updateForCurrentBreakpoint(container->width());
        }
    }
}

void AdvancedLayoutEngine::setupGlobalEventHandling() {
    // Setup resize timer for debouncing
    resize_timer_ = new QTimer(this);
    resize_timer_->setSingleShot(true);
    resize_timer_->setInterval(100);  // 100ms debounce
    connect(resize_timer_, &QTimer::timeout, this, &AdvancedLayoutEngine::onContainerResized);
    
    // Connect to screen changes
    if (QApplication::primaryScreen()) {
        connect(QApplication::primaryScreen(), &QScreen::geometryChanged,
                this, &AdvancedLayoutEngine::onScreenSizeChanged);
    }
}

void AdvancedLayoutEngine::detectCurrentBreakpoint() {
    int screen_width = getCurrentScreenWidth();
    
    for (size_t i = 0; i < default_breakpoints_.size(); ++i) {
        if (screen_width < default_breakpoints_[i]) {
            if (current_breakpoint_ != static_cast<int>(i)) {
                current_breakpoint_ = static_cast<int>(i);
                emit breakpointChanged(current_breakpoint_);
            }
            return;
        }
    }
    
    // Larger than all breakpoints
    int new_breakpoint = static_cast<int>(default_breakpoints_.size());
    if (current_breakpoint_ != new_breakpoint) {
        current_breakpoint_ = new_breakpoint;
        emit breakpointChanged(current_breakpoint_);
    }
}

int AdvancedLayoutEngine::getCurrentScreenWidth() const {
    if (QApplication::primaryScreen()) {
        return QApplication::primaryScreen()->availableGeometry().width();
    }
    return 1920;  // Default fallback
}

void AdvancedLayoutEngine::onScreenSizeChanged() {
    detectCurrentBreakpoint();
    updateAllResponsiveLayouts();
}

void AdvancedLayoutEngine::onContainerResized() {
    updateAllResponsiveLayouts();
}

// **GridLayout Implementation**

GridLayout::GridLayout(QWidget* container, QObject* parent)
    : QObject(parent), container_(container)
{
    if (container_) {
        container_->installEventFilter(this);
    }
}

GridLayout& GridLayout::templateRows(const std::vector<GridTrack>& tracks) {
    row_tracks_ = tracks;
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::templateColumns(const std::vector<GridTrack>& tracks) {
    col_tracks_ = tracks;
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::templateAreas(const std::vector<QString>& areas) {
    parseTemplateAreas(areas);
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::gap(int row_gap, int col_gap) {
    row_gap_ = row_gap;
    col_gap_ = col_gap;
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::gap(int gap) {
    return this->gap(gap, gap);
}

GridLayout& GridLayout::justifyContent(GridAlignment alignment) {
    justify_content_ = alignment;
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::alignContent(GridAlignment alignment) {
    align_content_ = alignment;
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::justifyItems(GridAlignment alignment) {
    justify_items_ = alignment;
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::alignItems(GridAlignment alignment) {
    align_items_ = alignment;
    layout_dirty_ = true;
    return *this;
}

GridLayout& GridLayout::addItem(QWidget* widget, int row, int col, int row_span, int col_span) {
    if (!widget) return *this;
    
    GridItem item;
    item.widget = widget;
    item.row_start = row;
    item.row_end = row + row_span;
    item.col_start = col;
    item.col_end = col + col_span;
    
    items_.push_back(item);
    widget->setParent(container_);
    layout_dirty_ = true;
    
    return *this;
}

GridLayout& GridLayout::addItem(QWidget* widget, const QString& area_name) {
    if (!widget || area_name.isEmpty()) return *this;
    
    auto it = named_areas_.find(area_name);
    if (it != named_areas_.end()) {
        const GridArea& area = it->second;
        return addItem(widget, area.row_start, area.col_start, 
                      area.row_end - area.row_start, area.col_end - area.col_start);
    }
    
    qWarning() << "🔲 Grid area not found:" << area_name;
    return *this;
}

GridLayout& GridLayout::setItemAlignment(QWidget* widget, GridAlignment justify, GridAlignment align) {
    for (auto& item : items_) {
        if (item.widget == widget) {
            item.justify_self = justify;
            item.align_self = align;
            layout_dirty_ = true;
            break;
        }
    }
    return *this;
}

GridLayout& GridLayout::setItemOrder(QWidget* widget, int order) {
    for (auto& item : items_) {
        if (item.widget == widget) {
            item.order = order;
            layout_dirty_ = true;
            break;
        }
    }
    return *this;
}

GridLayout& GridLayout::addNamedLine(const QString& name, int line, bool is_row) {
    if (is_row) {
        named_lines_["row-" + name].push_back(line);
    } else {
        named_lines_["col-" + name].push_back(line);
    }
    return *this;
}

GridLayout& GridLayout::defineArea(const QString& name, int row_start, int row_end, int col_start, int col_end) {
    GridArea area;
    area.name = name;
    area.row_start = row_start;
    area.row_end = row_end;
    area.col_start = col_start;
    area.col_end = col_end;
    
    named_areas_[name] = area;
    return *this;
}

void GridLayout::updateLayout() {
    if (!container_ || !layout_dirty_) return;
    
    calculateGridSizes();
    positionGridItems();
    layout_dirty_ = false;
    
    auto* engine = qobject_cast<AdvancedLayoutEngine*>(parent());
    if (engine && engine->isDebugModeEnabled()) {
        qDebug() << "🔲 Grid layout updated for container:" << container_->objectName();
    }
}

void GridLayout::invalidateLayout() {
    layout_dirty_ = true;
}

QSize GridLayout::calculateMinimumSize() const {
    // Calculate minimum size based on grid tracks and items
    int total_width = 0;
    int total_height = 0;
    
    for (const auto& track : col_tracks_) {
        total_width += track.min_size;
    }
    total_width += (col_tracks_.size() - 1) * col_gap_;
    
    for (const auto& track : row_tracks_) {
        total_height += track.min_size;
    }
    total_height += (row_tracks_.size() - 1) * row_gap_;
    
    return QSize(total_width, total_height);
}

QSize GridLayout::calculatePreferredSize() const {
    // Calculate preferred size based on content and track definitions
    return calculateMinimumSize();  // Simplified for now
}

void GridLayout::calculateGridSizes() {
    if (!container_) return;

    QRect container_rect = container_->contentsRect();
    int available_width = container_rect.width();
    int available_height = container_rect.height();

    // Calculate column sizes
    col_sizes_.clear();
    col_positions_.clear();

    if (!col_tracks_.empty()) {
        // Reserve space for gaps
        int total_gap_width = (col_tracks_.size() - 1) * col_gap_;
        int remaining_width = available_width - total_gap_width;

        // First pass: calculate fixed and auto sizes
        std::vector<double> flex_factors;
        double total_flex = 0.0;

        for (const auto& track : col_tracks_) {
            int track_size = 0;

            switch (track.sizing) {
                case GridSizing::Fixed:
                    track_size = static_cast<int>(track.value);
                    break;
                case GridSizing::Percentage:
                    track_size = static_cast<int>(available_width * track.value / 100.0);
                    break;
                case GridSizing::Auto:
                case GridSizing::MinContent:
                case GridSizing::MaxContent:
                case GridSizing::FitContent:
                    track_size = 100;  // Default size, will be refined
                    break;
                case GridSizing::Fraction:
                    flex_factors.push_back(track.value);
                    total_flex += track.value;
                    track_size = 0;  // Will be calculated in second pass
                    break;
            }

            track_size = std::max(track_size, track.min_size);
            track_size = std::min(track_size, track.max_size);

            col_sizes_.push_back(track_size);
            if (track.sizing != GridSizing::Fraction) {
                remaining_width -= track_size;
            }
        }

        // Second pass: distribute remaining space to flex tracks
        if (total_flex > 0.0 && remaining_width > 0) {
            size_t flex_index = 0;
            for (size_t i = 0; i < col_tracks_.size(); ++i) {
                if (col_tracks_[i].sizing == GridSizing::Fraction) {
                    int flex_size = static_cast<int>(remaining_width * flex_factors[flex_index] / total_flex);
                    col_sizes_[i] = std::max(flex_size, col_tracks_[i].min_size);
                    col_sizes_[i] = std::min(col_sizes_[i], col_tracks_[i].max_size);
                    flex_index++;
                }
            }
        }

        // Calculate column positions
        int current_x = container_rect.x();
        for (size_t i = 0; i < col_sizes_.size(); ++i) {
            col_positions_.push_back(current_x);
            current_x += col_sizes_[i] + col_gap_;
        }
    }

    // Calculate row sizes (similar logic)
    row_sizes_.clear();
    row_positions_.clear();

    if (!row_tracks_.empty()) {
        int total_gap_height = (row_tracks_.size() - 1) * row_gap_;
        int remaining_height = available_height - total_gap_height;

        std::vector<double> flex_factors;
        double total_flex = 0.0;

        for (const auto& track : row_tracks_) {
            int track_size = resolveTrackSize(track, remaining_height, 100);

            if (track.sizing == GridSizing::Fraction) {
                flex_factors.push_back(track.value);
                total_flex += track.value;
                track_size = 0;
            } else {
                remaining_height -= track_size;
            }

            row_sizes_.push_back(track_size);
        }

        // Distribute flex space
        if (total_flex > 0.0 && remaining_height > 0) {
            size_t flex_index = 0;
            for (size_t i = 0; i < row_tracks_.size(); ++i) {
                if (row_tracks_[i].sizing == GridSizing::Fraction) {
                    int flex_size = static_cast<int>(remaining_height * flex_factors[flex_index] / total_flex);
                    row_sizes_[i] = std::max(flex_size, row_tracks_[i].min_size);
                    row_sizes_[i] = std::min(row_sizes_[i], row_tracks_[i].max_size);
                    flex_index++;
                }
            }
        }

        // Calculate row positions
        int current_y = container_rect.y();
        for (size_t i = 0; i < row_sizes_.size(); ++i) {
            row_positions_.push_back(current_y);
            current_y += row_sizes_[i] + row_gap_;
        }
    }
}

void GridLayout::positionGridItems() {
    // Sort items by order
    std::vector<GridItem> sorted_items = items_;
    std::sort(sorted_items.begin(), sorted_items.end(),
              [](const GridItem& a, const GridItem& b) {
                  return a.order < b.order;
              });

    for (const auto& item : sorted_items) {
        if (!item.widget) continue;

        QRect item_rect = calculateItemRect(item);
        item.widget->setGeometry(item_rect);
        item.widget->show();
    }
}

int GridLayout::resolveTrackSize(const GridTrack& track, int available_space, int content_size) const {
    int size = 0;

    switch (track.sizing) {
        case GridSizing::Fixed:
            size = static_cast<int>(track.value);
            break;
        case GridSizing::Percentage:
            size = static_cast<int>(available_space * track.value / 100.0);
            break;
        case GridSizing::Auto:
            size = content_size;
            break;
        case GridSizing::MinContent:
            size = content_size / 2;  // Simplified
            break;
        case GridSizing::MaxContent:
            size = content_size * 2;  // Simplified
            break;
        case GridSizing::FitContent:
            size = std::min(content_size, static_cast<int>(track.value));
            break;
        case GridSizing::Fraction:
            size = 0;  // Handled separately
            break;
    }

    size = std::max(size, track.min_size);
    size = std::min(size, track.max_size);

    return size;
}

QRect GridLayout::calculateItemRect(const GridItem& item) const {
    if (col_positions_.empty() || row_positions_.empty() ||
        col_sizes_.empty() || row_sizes_.empty()) {
        return QRect();
    }

    // Ensure indices are within bounds
    int col_start = std::max(0, std::min(item.col_start - 1, static_cast<int>(col_positions_.size()) - 1));
    int col_end = std::max(col_start + 1, std::min(item.col_end - 1, static_cast<int>(col_positions_.size())));
    int row_start = std::max(0, std::min(item.row_start - 1, static_cast<int>(row_positions_.size()) - 1));
    int row_end = std::max(row_start + 1, std::min(item.row_end - 1, static_cast<int>(row_positions_.size())));

    int x = col_positions_[col_start];
    int y = row_positions_[row_start];

    int width = 0;
    for (int i = col_start; i < col_end && i < static_cast<int>(col_sizes_.size()); ++i) {
        width += col_sizes_[i];
        if (i < col_end - 1) width += col_gap_;
    }

    int height = 0;
    for (int i = row_start; i < row_end && i < static_cast<int>(row_sizes_.size()); ++i) {
        height += row_sizes_[i];
        if (i < row_end - 1) height += row_gap_;
    }

    return QRect(x, y, width, height);
}

void GridLayout::parseTemplateAreas(const std::vector<QString>& areas) {
    named_areas_.clear();

    for (int row = 0; row < static_cast<int>(areas.size()); ++row) {
        QStringList cells = areas[row].split(' ', Qt::SkipEmptyParts);

        for (int col = 0; col < cells.size(); ++col) {
            QString area_name = cells[col];
            if (area_name == "." || area_name.isEmpty()) continue;

            // Check if this area already exists
            auto it = named_areas_.find(area_name);
            if (it == named_areas_.end()) {
                // New area
                GridArea area;
                area.name = area_name;
                area.row_start = row + 1;
                area.row_end = row + 2;
                area.col_start = col + 1;
                area.col_end = col + 2;
                named_areas_[area_name] = area;
            } else {
                // Extend existing area
                GridArea& area = it->second;
                area.row_end = std::max(area.row_end, row + 2);
                area.col_end = std::max(area.col_end, col + 2);
            }
        }
    }
}

// **FlexLayout Implementation**

FlexLayout::FlexLayout(QWidget* container, QObject* parent)
    : QObject(parent), container_(container)
{
    if (container_) {
        container_->installEventFilter(this);
    }
}

FlexLayout& FlexLayout::direction(FlexDirection dir) {
    direction_ = dir;
    layout_dirty_ = true;
    return *this;
}

FlexLayout& FlexLayout::wrap(FlexWrap wrap_mode) {
    wrap_ = wrap_mode;
    layout_dirty_ = true;
    return *this;
}

FlexLayout& FlexLayout::justifyContent(FlexAlignment alignment) {
    justify_content_ = alignment;
    layout_dirty_ = true;
    return *this;
}

FlexLayout& FlexLayout::alignItems(FlexAlignment alignment) {
    align_items_ = alignment;
    layout_dirty_ = true;
    return *this;
}

FlexLayout& FlexLayout::alignContent(FlexAlignment alignment) {
    align_content_ = alignment;
    layout_dirty_ = true;
    return *this;
}

FlexLayout& FlexLayout::gap(int gap) {
    gap_ = gap;
    layout_dirty_ = true;
    return *this;
}

FlexLayout& FlexLayout::addItem(QWidget* widget, double grow, double shrink, int basis) {
    if (!widget) return *this;

    FlexItem item;
    item.widget = widget;
    item.flex_grow = grow;
    item.flex_shrink = shrink;
    item.flex_basis = basis;

    items_.push_back(item);
    widget->setParent(container_);
    layout_dirty_ = true;

    return *this;
}

FlexLayout& FlexLayout::setItemFlex(QWidget* widget, double grow, double shrink, int basis) {
    for (auto& item : items_) {
        if (item.widget == widget) {
            item.flex_grow = grow;
            item.flex_shrink = shrink;
            item.flex_basis = basis;
            layout_dirty_ = true;
            break;
        }
    }
    return *this;
}

FlexLayout& FlexLayout::setItemAlignment(QWidget* widget, FlexAlignment align) {
    for (auto& item : items_) {
        if (item.widget == widget) {
            item.align_self = align;
            layout_dirty_ = true;
            break;
        }
    }
    return *this;
}

FlexLayout& FlexLayout::setItemOrder(QWidget* widget, int order) {
    for (auto& item : items_) {
        if (item.widget == widget) {
            item.order = order;
            layout_dirty_ = true;
            break;
        }
    }
    return *this;
}

FlexLayout& FlexLayout::setItemMargins(QWidget* widget, const QMargins& margins) {
    for (auto& item : items_) {
        if (item.widget == widget) {
            item.margins = margins;
            layout_dirty_ = true;
            break;
        }
    }
    return *this;
}

void FlexLayout::updateLayout() {
    if (!container_ || !layout_dirty_) return;

    calculateFlexLayout();
    layout_dirty_ = false;

    auto* engine = qobject_cast<AdvancedLayoutEngine*>(parent());
    if (engine && engine->isDebugModeEnabled()) {
        qDebug() << "📦 Flex layout updated for container:" << container_->objectName();
    }
}

void FlexLayout::invalidateLayout() {
    layout_dirty_ = true;
}

QSize FlexLayout::calculateMinimumSize() const {
    int total_main = 0;
    int max_cross = 0;

    for (const auto& item : items_) {
        if (!item.widget) continue;

        QSize item_size = item.widget->minimumSizeHint();
        if (item_size.isEmpty()) {
            item_size = item.widget->sizeHint();
        }

        int main_size = getMainAxisSize(item_size);
        int cross_size = getCrossAxisSize(item_size);

        if (wrap_ == FlexWrap::NoWrap) {
            total_main += main_size;
            max_cross = std::max(max_cross, cross_size);
        } else {
            // For wrapping, this is more complex - simplified here
            total_main = std::max(total_main, main_size);
            max_cross += cross_size;
        }
    }

    // Add gaps
    if (items_.size() > 1) {
        total_main += (items_.size() - 1) * gap_;
    }

    return createSize(total_main, max_cross);
}

QSize FlexLayout::calculatePreferredSize() const {
    return calculateMinimumSize();  // Simplified
}

void FlexLayout::calculateFlexLayout() {
    if (!container_ || items_.empty()) return;

    // Sort items by order
    std::vector<FlexItem> sorted_items = items_;
    std::sort(sorted_items.begin(), sorted_items.end(),
              [](const FlexItem& a, const FlexItem& b) {
                  return a.order < b.order;
              });

    // Create flex lines based on wrapping
    flex_lines_.clear();

    if (wrap_ == FlexWrap::NoWrap) {
        // Single line
        std::vector<FlexItem*> line;
        for (auto& item : sorted_items) {
            line.push_back(&item);
        }
        flex_lines_.push_back(line);
    } else {
        // Multi-line wrapping (simplified implementation)
        QRect container_rect = container_->contentsRect();
        int available_main = getMainAxisSize(container_rect.size());

        std::vector<FlexItem*> current_line;
        int current_line_size = 0;

        for (auto& item : sorted_items) {
            if (!item.widget) continue;

            QSize item_size = item.widget->sizeHint();
            int item_main_size = getMainAxisSize(item_size);

            if (current_line.empty() || current_line_size + item_main_size + gap_ <= available_main) {
                current_line.push_back(&item);
                current_line_size += item_main_size;
                if (!current_line.empty()) current_line_size += gap_;
            } else {
                // Start new line
                if (!current_line.empty()) {
                    flex_lines_.push_back(current_line);
                }
                current_line.clear();
                current_line.push_back(&item);
                current_line_size = item_main_size;
            }
        }

        if (!current_line.empty()) {
            flex_lines_.push_back(current_line);
        }
    }

    distributeSpace();
    positionFlexItems();
}

void FlexLayout::distributeSpace() {
    if (!container_ || flex_lines_.empty()) return;

    QRect container_rect = container_->contentsRect();
    int available_main = getMainAxisSize(container_rect.size());

    for (auto& line : flex_lines_) {
        if (line.empty()) continue;

        // Calculate total flex grow and shrink factors
        double total_grow = 0.0;
        double total_shrink = 0.0;
        int total_basis = 0;
        int total_gaps = (line.size() - 1) * gap_;

        for (auto* item : line) {
            if (!item || !item->widget) continue;

            total_grow += item->flex_grow;
            total_shrink += item->flex_shrink;

            if (item->flex_basis >= 0) {
                total_basis += item->flex_basis;
            } else {
                QSize item_size = item->widget->sizeHint();
                total_basis += getMainAxisSize(item_size);
            }
        }

        int remaining_space = available_main - total_basis - total_gaps;

        // Distribute remaining space
        for (auto* item : line) {
            if (!item || !item->widget) continue;

            int item_main_size;
            if (item->flex_basis >= 0) {
                item_main_size = item->flex_basis;
            } else {
                QSize item_size = item->widget->sizeHint();
                item_main_size = getMainAxisSize(item_size);
            }

            if (remaining_space > 0 && total_grow > 0.0) {
                // Distribute positive space
                int grow_space = static_cast<int>(remaining_space * item->flex_grow / total_grow);
                item_main_size += grow_space;
            } else if (remaining_space < 0 && total_shrink > 0.0) {
                // Distribute negative space
                int shrink_space = static_cast<int>(-remaining_space * item->flex_shrink / total_shrink);
                item_main_size = std::max(0, item_main_size - shrink_space);
            }

            // Store calculated size (we'll use this in positioning)
            item->widget->setProperty("_flex_main_size", item_main_size);
        }
    }
}

void FlexLayout::positionFlexItems() {
    if (!container_ || flex_lines_.empty()) return;

    QRect container_rect = container_->contentsRect();
    int container_main = getMainAxisSize(container_rect.size());
    int container_cross = getCrossAxisSize(container_rect.size());

    int current_cross_pos = (direction_ == FlexDirection::Column || direction_ == FlexDirection::ColumnReverse)
                           ? container_rect.x() : container_rect.y();

    for (auto& line : flex_lines_) {
        if (line.empty()) continue;

        // Calculate line cross size
        int line_cross_size = 0;
        for (auto* item : line) {
            if (!item || !item->widget) continue;
            QSize item_size = item->widget->sizeHint();
            line_cross_size = std::max(line_cross_size, getCrossAxisSize(item_size));
        }

        // Calculate total main size of line
        int total_main_size = 0;
        for (auto* item : line) {
            if (!item || !item->widget) continue;
            int item_main_size = item->widget->property("_flex_main_size").toInt();
            total_main_size += item_main_size;
        }
        total_main_size += (line.size() - 1) * gap_;

        // Calculate main axis start position based on justify-content
        int main_start_pos;
        int item_spacing = 0;

        switch (justify_content_) {
            case FlexAlignment::Start:
                main_start_pos = (direction_ == FlexDirection::Row || direction_ == FlexDirection::Column)
                               ? container_rect.x() : container_rect.right() - total_main_size;
                break;
            case FlexAlignment::End:
                main_start_pos = (direction_ == FlexDirection::Row || direction_ == FlexDirection::Column)
                               ? container_rect.right() - total_main_size : container_rect.x();
                break;
            case FlexAlignment::Center:
                main_start_pos = (direction_ == FlexDirection::Row || direction_ == FlexDirection::Column)
                               ? container_rect.x() + (container_main - total_main_size) / 2
                               : container_rect.right() - (container_main + total_main_size) / 2;
                break;
            case FlexAlignment::SpaceBetween:
                main_start_pos = (direction_ == FlexDirection::Row || direction_ == FlexDirection::Column)
                               ? container_rect.x() : container_rect.right();
                if (line.size() > 1) {
                    item_spacing = (container_main - total_main_size + (line.size() - 1) * gap_) / (line.size() - 1);
                }
                break;
            case FlexAlignment::SpaceAround:
                if (line.size() > 0) {
                    item_spacing = (container_main - total_main_size + (line.size() - 1) * gap_) / line.size();
                    main_start_pos = (direction_ == FlexDirection::Row || direction_ == FlexDirection::Column)
                                   ? container_rect.x() + item_spacing / 2
                                   : container_rect.right() - item_spacing / 2;
                } else {
                    main_start_pos = container_rect.x();
                }
                break;
            case FlexAlignment::SpaceEvenly:
                if (line.size() > 0) {
                    item_spacing = (container_main - total_main_size + (line.size() - 1) * gap_) / (line.size() + 1);
                    main_start_pos = (direction_ == FlexDirection::Row || direction_ == FlexDirection::Column)
                                   ? container_rect.x() + item_spacing
                                   : container_rect.right() - item_spacing;
                } else {
                    main_start_pos = container_rect.x();
                }
                break;
            default:
                main_start_pos = container_rect.x();
                break;
        }

        // Position items in the line
        int current_main_pos = main_start_pos;

        for (auto* item : line) {
            if (!item || !item->widget) continue;

            int item_main_size = item->widget->property("_flex_main_size").toInt();
            QSize item_size = item->widget->sizeHint();
            int item_cross_size = getCrossAxisSize(item_size);

            // Calculate cross axis position based on align-items or align-self
            FlexAlignment item_alignment = (item->align_self != FlexAlignment::Stretch)
                                         ? item->align_self : align_items_;

            int cross_pos = current_cross_pos;
            switch (item_alignment) {
                case FlexAlignment::Start:
                    // cross_pos already set correctly
                    break;
                case FlexAlignment::End:
                    cross_pos += line_cross_size - item_cross_size;
                    break;
                case FlexAlignment::Center:
                    cross_pos += (line_cross_size - item_cross_size) / 2;
                    break;
                case FlexAlignment::Stretch:
                    item_cross_size = line_cross_size;
                    break;
                default:
                    break;
            }

            // Apply margins
            QMargins margins = item->margins;

            // Create final geometry
            QRect item_rect;
            if (direction_ == FlexDirection::Row) {
                item_rect = QRect(current_main_pos + margins.left(),
                                cross_pos + margins.top(),
                                item_main_size - margins.left() - margins.right(),
                                item_cross_size - margins.top() - margins.bottom());
            } else if (direction_ == FlexDirection::RowReverse) {
                item_rect = QRect(current_main_pos - item_main_size + margins.right(),
                                cross_pos + margins.top(),
                                item_main_size - margins.left() - margins.right(),
                                item_cross_size - margins.top() - margins.bottom());
            } else if (direction_ == FlexDirection::Column) {
                item_rect = QRect(cross_pos + margins.left(),
                                current_main_pos + margins.top(),
                                item_cross_size - margins.left() - margins.right(),
                                item_main_size - margins.top() - margins.bottom());
            } else { // ColumnReverse
                item_rect = QRect(cross_pos + margins.left(),
                                current_main_pos - item_main_size + margins.bottom(),
                                item_cross_size - margins.left() - margins.right(),
                                item_main_size - margins.top() - margins.bottom());
            }

            item->widget->setGeometry(item_rect);
            item->widget->show();

            // Move to next position
            if (direction_ == FlexDirection::Row || direction_ == FlexDirection::Column) {
                current_main_pos += item_main_size + (item_spacing > gap_ ? item_spacing : gap_);
            } else {
                current_main_pos -= item_main_size + (item_spacing > gap_ ? item_spacing : gap_);
            }
        }

        // Move to next line
        current_cross_pos += line_cross_size + gap_;
    }
}

int FlexLayout::getMainAxisSize(const QSize& size) const {
    switch (direction_) {
        case FlexDirection::Row:
        case FlexDirection::RowReverse:
            return size.width();
        case FlexDirection::Column:
        case FlexDirection::ColumnReverse:
            return size.height();
    }
    return size.width();
}

int FlexLayout::getCrossAxisSize(const QSize& size) const {
    switch (direction_) {
        case FlexDirection::Row:
        case FlexDirection::RowReverse:
            return size.height();
        case FlexDirection::Column:
        case FlexDirection::ColumnReverse:
            return size.width();
    }
    return size.height();
}

QSize FlexLayout::createSize(int main, int cross) const {
    switch (direction_) {
        case FlexDirection::Row:
        case FlexDirection::RowReverse:
            return QSize(main, cross);
        case FlexDirection::Column:
        case FlexDirection::ColumnReverse:
            return QSize(cross, main);
    }
    return QSize(main, cross);
}

// **ResponsiveLayout Implementation**

ResponsiveLayout::ResponsiveLayout(QWidget* container, QObject* parent)
    : QObject(parent), container_(container)
{
    if (container_) {
        container_->installEventFilter(this);
    }
}

ResponsiveLayout& ResponsiveLayout::addBreakpoint(const QString& name, int min_width, int max_width) {
    Breakpoint bp;
    bp.name = name;
    bp.min_width = min_width;
    bp.max_width = max_width;

    breakpoints_.push_back(bp);

    // Sort breakpoints by min_width
    std::sort(breakpoints_.begin(), breakpoints_.end(),
              [](const Breakpoint& a, const Breakpoint& b) {
                  return a.min_width < b.min_width;
              });

    return *this;
}

ResponsiveLayout& ResponsiveLayout::setLayoutForBreakpoint(const QString& breakpoint, std::function<void()> configurator) {
    for (auto& bp : breakpoints_) {
        if (bp.name == breakpoint) {
            bp.layout_configurator = configurator;
            break;
        }
    }
    return *this;
}

ResponsiveLayout& ResponsiveLayout::setPropertyForBreakpoint(const QString& property, const QString& breakpoint, const QVariant& value) {
    // Find or create responsive property
    ResponsiveProperty* resp_prop = nullptr;
    for (auto& prop : responsive_properties_) {
        if (prop.property_name == property) {
            resp_prop = &prop;
            break;
        }
    }

    if (!resp_prop) {
        ResponsiveProperty new_prop;
        new_prop.property_name = property;
        responsive_properties_.push_back(new_prop);
        resp_prop = &responsive_properties_.back();
    }

    resp_prop->breakpoint_values[breakpoint] = value;
    return *this;
}

ResponsiveLayout& ResponsiveLayout::responsiveProperty(const QString& property, const std::unordered_map<QString, QVariant>& values) {
    ResponsiveProperty resp_prop;
    resp_prop.property_name = property;
    resp_prop.breakpoint_values = values;

    // Remove existing property with same name
    responsive_properties_.erase(
        std::remove_if(responsive_properties_.begin(), responsive_properties_.end(),
                      [&property](const ResponsiveProperty& prop) {
                          return prop.property_name == property;
                      }),
        responsive_properties_.end());

    responsive_properties_.push_back(resp_prop);
    return *this;
}

ResponsiveLayout& ResponsiveLayout::responsiveSpacing(const std::unordered_map<QString, int>& values) {
    std::unordered_map<QString, QVariant> variant_values;
    for (const auto& [breakpoint, value] : values) {
        variant_values[breakpoint] = value;
    }
    return responsiveProperty("spacing", variant_values);
}

ResponsiveLayout& ResponsiveLayout::responsiveMargins(const std::unordered_map<QString, QMargins>& values) {
    std::unordered_map<QString, QVariant> variant_values;
    for (const auto& [breakpoint, value] : values) {
        variant_values[breakpoint] = QVariant::fromValue(value);
    }
    return responsiveProperty("margins", variant_values);
}

ResponsiveLayout& ResponsiveLayout::responsiveVisibility(const std::unordered_map<QString, bool>& values) {
    std::unordered_map<QString, QVariant> variant_values;
    for (const auto& [breakpoint, value] : values) {
        variant_values[breakpoint] = value;
    }
    return responsiveProperty("visible", variant_values);
}

void ResponsiveLayout::updateForCurrentBreakpoint(int container_width) {
    QString new_breakpoint = findMatchingBreakpoint(container_width);

    if (new_breakpoint != current_breakpoint_) {
        QString old_breakpoint = current_breakpoint_;
        current_breakpoint_ = new_breakpoint;

        applyBreakpoint(current_breakpoint_);
        emit breakpointChanged(old_breakpoint, current_breakpoint_);
    }
}

void ResponsiveLayout::applyBreakpoint(const QString& breakpoint_name) {
    // Apply layout configurator
    for (const auto& bp : breakpoints_) {
        if (bp.name == breakpoint_name && bp.layout_configurator) {
            bp.layout_configurator();
            break;
        }
    }

    // Apply responsive properties
    applyResponsiveProperties(breakpoint_name);
}

QString ResponsiveLayout::getCurrentBreakpoint() const {
    return current_breakpoint_;
}

std::vector<QString> ResponsiveLayout::getAvailableBreakpoints() const {
    std::vector<QString> names;
    for (const auto& bp : breakpoints_) {
        names.push_back(bp.name);
    }
    return names;
}

QString ResponsiveLayout::findMatchingBreakpoint(int width) const {
    for (const auto& bp : breakpoints_) {
        if (width >= bp.min_width && width <= bp.max_width) {
            return bp.name;
        }
    }

    // Return largest breakpoint if no match
    if (!breakpoints_.empty()) {
        return breakpoints_.back().name;
    }

    return "default";
}

void ResponsiveLayout::applyResponsiveProperties(const QString& breakpoint) {
    if (!container_) return;

    for (const auto& prop : responsive_properties_) {
        auto it = prop.breakpoint_values.find(breakpoint);
        if (it != prop.breakpoint_values.end()) {
            container_->setProperty(prop.property_name.toUtf8().constData(), it->second);
        }
    }
}

// **ConstraintLayout Implementation**

ConstraintLayout::ConstraintLayout(QWidget* container, QObject* parent)
    : QObject(parent), container_(container)
{
    if (container_) {
        container_->installEventFilter(this);
    }
}

ConstraintLayout& ConstraintLayout::addConstraint(QWidget* first_item, ConstraintAttribute first_attr,
                                                 ConstraintType relation,
                                                 QWidget* second_item, ConstraintAttribute second_attr,
                                                 double multiplier, double constant,
                                                 ConstraintPriority priority) {
    Constraint constraint;
    constraint.first_item = first_item;
    constraint.first_attribute = first_attr;
    constraint.relation = relation;
    constraint.second_item = second_item;
    constraint.second_attribute = second_attr;
    constraint.multiplier = multiplier;
    constraint.constant = constant;
    constraint.priority = priority;
    constraint.active = true;
    constraint.identifier = QString("constraint_%1").arg(constraints_.size());

    constraints_.push_back(constraint);
    constraint_map_[constraint.identifier] = constraints_.size() - 1;
    layout_dirty_ = true;

    return *this;
}

ConstraintLayout& ConstraintLayout::addConstraint(const QString& identifier, const Constraint& constraint) {
    Constraint new_constraint = constraint;
    new_constraint.identifier = identifier;

    // Remove existing constraint with same identifier
    removeConstraint(identifier);

    constraints_.push_back(new_constraint);
    constraint_map_[identifier] = constraints_.size() - 1;
    layout_dirty_ = true;

    return *this;
}

ConstraintLayout& ConstraintLayout::centerHorizontally(QWidget* item, QWidget* reference) {
    QWidget* ref = reference ? reference : container_;
    return addConstraint(item, ConstraintAttribute::CenterX, ConstraintType::Equal,
                        ref, ConstraintAttribute::CenterX);
}

ConstraintLayout& ConstraintLayout::centerVertically(QWidget* item, QWidget* reference) {
    QWidget* ref = reference ? reference : container_;
    return addConstraint(item, ConstraintAttribute::CenterY, ConstraintType::Equal,
                        ref, ConstraintAttribute::CenterY);
}

ConstraintLayout& ConstraintLayout::alignLeft(QWidget* item, QWidget* reference, double offset) {
    QWidget* ref = reference ? reference : container_;
    return addConstraint(item, ConstraintAttribute::Left, ConstraintType::Equal,
                        ref, ConstraintAttribute::Left, 1.0, offset);
}

ConstraintLayout& ConstraintLayout::alignRight(QWidget* item, QWidget* reference, double offset) {
    QWidget* ref = reference ? reference : container_;
    return addConstraint(item, ConstraintAttribute::Right, ConstraintType::Equal,
                        ref, ConstraintAttribute::Right, 1.0, offset);
}

ConstraintLayout& ConstraintLayout::alignTop(QWidget* item, QWidget* reference, double offset) {
    QWidget* ref = reference ? reference : container_;
    return addConstraint(item, ConstraintAttribute::Top, ConstraintType::Equal,
                        ref, ConstraintAttribute::Top, 1.0, offset);
}

ConstraintLayout& ConstraintLayout::alignBottom(QWidget* item, QWidget* reference, double offset) {
    QWidget* ref = reference ? reference : container_;
    return addConstraint(item, ConstraintAttribute::Bottom, ConstraintType::Equal,
                        ref, ConstraintAttribute::Bottom, 1.0, offset);
}

ConstraintLayout& ConstraintLayout::setWidth(QWidget* item, int width) {
    return addConstraint(item, ConstraintAttribute::Width, ConstraintType::Equal,
                        nullptr, ConstraintAttribute::Width, 0.0, width);
}

ConstraintLayout& ConstraintLayout::setHeight(QWidget* item, int height) {
    return addConstraint(item, ConstraintAttribute::Height, ConstraintType::Equal,
                        nullptr, ConstraintAttribute::Height, 0.0, height);
}

ConstraintLayout& ConstraintLayout::setAspectRatio(QWidget* item, double ratio) {
    return addConstraint(item, ConstraintAttribute::Width, ConstraintType::Equal,
                        item, ConstraintAttribute::Height, ratio, 0.0);
}

ConstraintLayout& ConstraintLayout::removeConstraint(const QString& identifier) {
    auto it = constraint_map_.find(identifier);
    if (it != constraint_map_.end()) {
        size_t index = it->second;
        if (index < constraints_.size()) {
            constraints_.erase(constraints_.begin() + index);

            // Update constraint map indices
            constraint_map_.clear();
            for (size_t i = 0; i < constraints_.size(); ++i) {
                constraint_map_[constraints_[i].identifier] = i;
            }

            layout_dirty_ = true;
        }
    }
    return *this;
}

ConstraintLayout& ConstraintLayout::activateConstraint(const QString& identifier, bool active) {
    auto it = constraint_map_.find(identifier);
    if (it != constraint_map_.end()) {
        size_t index = it->second;
        if (index < constraints_.size()) {
            constraints_[index].active = active;
            layout_dirty_ = true;
        }
    }
    return *this;
}

ConstraintLayout& ConstraintLayout::setConstraintPriority(const QString& identifier, ConstraintPriority priority) {
    auto it = constraint_map_.find(identifier);
    if (it != constraint_map_.end()) {
        size_t index = it->second;
        if (index < constraints_.size()) {
            constraints_[index].priority = priority;
            layout_dirty_ = true;
        }
    }
    return *this;
}

void ConstraintLayout::updateLayout() {
    if (!container_ || !layout_dirty_) return;

    buildConstraintSystem();
    bool success = solveConstraints();

    if (!success) {
        qWarning() << "🔗 Failed to solve constraint system for container:" << container_->objectName();
        qWarning() << "🔗 Conflicting constraints:" << getConflictingConstraints();
    }

    layout_dirty_ = false;
}

void ConstraintLayout::invalidateLayout() {
    layout_dirty_ = true;
}

bool ConstraintLayout::solveConstraints() {
    // Simplified constraint solver - in a real implementation, this would use
    // a proper linear programming solver like Cassowary

    conflicting_constraints_.clear();

    // For now, just apply constraints in order of priority
    std::vector<Constraint> sorted_constraints = constraints_;
    std::sort(sorted_constraints.begin(), sorted_constraints.end(),
              [](const Constraint& a, const Constraint& b) {
                  return static_cast<int>(a.priority) > static_cast<int>(b.priority);
              });

    for (const auto& constraint : sorted_constraints) {
        if (!constraint.active || !constraint.first_item) continue;

        try {
            double first_value = getAttributeValue(constraint.first_item, constraint.first_attribute);
            double second_value = 0.0;

            if (constraint.second_item) {
                second_value = getAttributeValue(constraint.second_item, constraint.second_attribute);
            }

            double target_value = second_value * constraint.multiplier + constraint.constant;

            // Apply constraint based on relation type
            switch (constraint.relation) {
                case ConstraintType::Equal:
                    setAttributeValue(constraint.first_item, constraint.first_attribute, target_value);
                    break;
                case ConstraintType::LessThanOrEqual:
                    if (first_value > target_value) {
                        setAttributeValue(constraint.first_item, constraint.first_attribute, target_value);
                    }
                    break;
                case ConstraintType::GreaterThanOrEqual:
                    if (first_value < target_value) {
                        setAttributeValue(constraint.first_item, constraint.first_attribute, target_value);
                    }
                    break;
            }
        } catch (...) {
            conflicting_constraints_.push_back(constraint.identifier);
        }
    }

    return conflicting_constraints_.empty();
}

std::vector<QString> ConstraintLayout::getConflictingConstraints() const {
    return conflicting_constraints_;
}

void ConstraintLayout::buildConstraintSystem() {
    variables_.clear();

    // Collect all widgets that have constraints
    std::set<QWidget*> constrained_widgets;
    for (const auto& constraint : constraints_) {
        if (constraint.active && constraint.first_item) {
            constrained_widgets.insert(constraint.first_item);
        }
        if (constraint.active && constraint.second_item) {
            constrained_widgets.insert(constraint.second_item);
        }
    }

    // Create variables for each widget attribute
    for (QWidget* widget : constrained_widgets) {
        if (!widget) continue;

        QRect current_geometry = widget->geometry();

        variables_.push_back({widget, ConstraintAttribute::Left, static_cast<double>(current_geometry.left()), false});
        variables_.push_back({widget, ConstraintAttribute::Right, static_cast<double>(current_geometry.right()), false});
        variables_.push_back({widget, ConstraintAttribute::Top, static_cast<double>(current_geometry.top()), false});
        variables_.push_back({widget, ConstraintAttribute::Bottom, static_cast<double>(current_geometry.bottom()), false});
        variables_.push_back({widget, ConstraintAttribute::Width, static_cast<double>(current_geometry.width()), false});
        variables_.push_back({widget, ConstraintAttribute::Height, static_cast<double>(current_geometry.height()), false});
        variables_.push_back({widget, ConstraintAttribute::CenterX, static_cast<double>(current_geometry.center().x()), false});
        variables_.push_back({widget, ConstraintAttribute::CenterY, static_cast<double>(current_geometry.center().y()), false});
    }
}

bool ConstraintLayout::solveLinearSystem() {
    // Simplified linear system solver
    // In a real implementation, this would use a proper constraint solver
    return true;
}

double ConstraintLayout::getAttributeValue(QWidget* widget, ConstraintAttribute attribute) const {
    if (!widget) return 0.0;

    QRect geometry = widget->geometry();

    switch (attribute) {
        case ConstraintAttribute::Left:
            return geometry.left();
        case ConstraintAttribute::Right:
            return geometry.right();
        case ConstraintAttribute::Top:
            return geometry.top();
        case ConstraintAttribute::Bottom:
            return geometry.bottom();
        case ConstraintAttribute::Width:
            return geometry.width();
        case ConstraintAttribute::Height:
            return geometry.height();
        case ConstraintAttribute::CenterX:
            return geometry.center().x();
        case ConstraintAttribute::CenterY:
            return geometry.center().y();
        case ConstraintAttribute::Baseline:
            return geometry.bottom() - 10;  // Simplified baseline calculation
    }

    return 0.0;
}

void ConstraintLayout::setAttributeValue(QWidget* widget, ConstraintAttribute attribute, double value) {
    if (!widget) return;

    QRect geometry = widget->geometry();

    switch (attribute) {
        case ConstraintAttribute::Left:
            geometry.moveLeft(static_cast<int>(value));
            break;
        case ConstraintAttribute::Right:
            geometry.moveRight(static_cast<int>(value));
            break;
        case ConstraintAttribute::Top:
            geometry.moveTop(static_cast<int>(value));
            break;
        case ConstraintAttribute::Bottom:
            geometry.moveBottom(static_cast<int>(value));
            break;
        case ConstraintAttribute::Width:
            geometry.setWidth(static_cast<int>(value));
            break;
        case ConstraintAttribute::Height:
            geometry.setHeight(static_cast<int>(value));
            break;
        case ConstraintAttribute::CenterX:
            geometry.moveCenter(QPoint(static_cast<int>(value), geometry.center().y()));
            break;
        case ConstraintAttribute::CenterY:
            geometry.moveCenter(QPoint(geometry.center().x(), static_cast<int>(value)));
            break;
        case ConstraintAttribute::Baseline:
            // Baseline is read-only for now
            break;
    }

    widget->setGeometry(geometry);
}

QRect ConstraintLayout::calculateWidgetGeometry(QWidget* widget) const {
    if (!widget) return QRect();

    // Calculate final geometry based on all constraints
    return widget->geometry();  // Simplified
}

// **LayoutUtils Implementation**

QSize LayoutUtils::calculateIntrinsicSize(QWidget* widget) {
    if (!widget) return QSize();

    QSize intrinsic = widget->sizeHint();
    if (intrinsic.isEmpty()) {
        intrinsic = widget->minimumSizeHint();
    }
    if (intrinsic.isEmpty()) {
        intrinsic = QSize(100, 30);  // Default fallback
    }

    return intrinsic;
}

QSize LayoutUtils::calculateMinimumSize(QWidget* widget) {
    if (!widget) return QSize();

    QSize minimum = widget->minimumSize();
    if (minimum.isEmpty()) {
        minimum = widget->minimumSizeHint();
    }
    if (minimum.isEmpty()) {
        minimum = QSize(0, 0);
    }

    return minimum;
}

QSize LayoutUtils::calculateMaximumSize(QWidget* widget) {
    if (!widget) return QSize();

    QSize maximum = widget->maximumSize();
    if (maximum.width() == QWIDGETSIZE_MAX) {
        maximum.setWidth(1000);  // Reasonable default
    }
    if (maximum.height() == QWIDGETSIZE_MAX) {
        maximum.setHeight(1000);  // Reasonable default
    }

    return maximum;
}

QSize LayoutUtils::calculatePreferredSize(QWidget* widget) {
    if (!widget) return QSize();

    return widget->sizeHint();
}

int LayoutUtils::getScreenWidth() {
    if (QApplication::primaryScreen()) {
        return QApplication::primaryScreen()->availableGeometry().width();
    }
    return 1920;
}

int LayoutUtils::getScreenHeight() {
    if (QApplication::primaryScreen()) {
        return QApplication::primaryScreen()->availableGeometry().height();
    }
    return 1080;
}

double LayoutUtils::getDevicePixelRatio() {
    if (QApplication::primaryScreen()) {
        return QApplication::primaryScreen()->devicePixelRatio();
    }
    return 1.0;
}

bool LayoutUtils::isTouchDevice() {
    // Check if any touch devices are available
    const auto devices = QInputDevice::devices();
    for (const auto* device : devices) {
        if (device->type() == QInputDevice::DeviceType::TouchScreen ||
            device->type() == QInputDevice::DeviceType::TouchPad) {
            return true;
        }
    }
    return false;
}

bool LayoutUtils::isHighDPI() {
    return getDevicePixelRatio() > 1.5;
}

void LayoutUtils::debugLayout(QWidget* container, const QString& layout_type) {
    if (!container) return;

    qDebug() << "🐛 Layout Debug for" << layout_type << "container:" << container->objectName();
    qDebug() << "🐛 Container geometry:" << container->geometry();
    qDebug() << "🐛 Container size hint:" << container->sizeHint();
    qDebug() << "🐛 Container minimum size:" << container->minimumSize();
    qDebug() << "🐛 Container maximum size:" << container->maximumSize();

    // Debug child widgets
    const auto children = container->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
    for (QWidget* child : children) {
        qDebug() << "🐛   Child:" << child->objectName() << "geometry:" << child->geometry();
    }
}

void LayoutUtils::highlightLayoutBounds(QWidget* widget, const QColor& color) {
    if (!widget) return;

    // Create a simple border effect
    widget->setStyleSheet(QString("border: 2px solid %1;").arg(color.name()));
}

void LayoutUtils::showLayoutGrid(QWidget* container, int grid_size) {
    if (!container) return;

    // This would typically create an overlay widget with grid lines
    qDebug() << "🔲 Layout grid overlay not implemented yet for container:" << container->objectName();
}

void LayoutUtils::animateLayoutChange(QWidget* container, int duration_ms) {
    if (!container) return;

    // Create animation for layout changes
    auto* animation = new QPropertyAnimation(container, "geometry");
    animation->setDuration(duration_ms);
    animation->setEasingCurve(QEasingCurve::OutCubic);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void LayoutUtils::animateWidgetMove(QWidget* widget, const QRect& target_geometry, int duration_ms) {
    if (!widget) return;

    auto* animation = new QPropertyAnimation(widget, "geometry");
    animation->setDuration(duration_ms);
    animation->setStartValue(widget->geometry());
    animation->setEndValue(target_geometry);
    animation->setEasingCurve(QEasingCurve::OutCubic);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

} // namespace DeclarativeUI::Layout
