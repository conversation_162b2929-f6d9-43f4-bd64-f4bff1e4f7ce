# Testing Configuration for DeclarativeUI Framework

# =============================================================================
# Test Framework Setup
# =============================================================================

# Enable testing
enable_testing()

# Find Qt Test module
find_package(Qt6 REQUIRED COMPONENTS Test)

# =============================================================================
# Test Configuration Options
# =============================================================================

option(BUILD_TESTS "Build test applications" ON)
option(ENABLE_TEST_COVERAGE "Enable test coverage reporting" OFF)
option(ENABLE_BENCHMARK_TESTS "Enable benchmark tests" OFF)
option(ENABLE_INTEGRATION_TESTS "Enable integration tests" ON)
option(ENABLE_PERFORMANCE_TESTS "Enable performance tests" OFF)

# =============================================================================
# Test Utilities
# =============================================================================

# Function to create a test executable
function(add_declarative_ui_test test_name)
    set(options BENCHMARK INTEGRATION PERFORMANCE)
    set(oneValueArgs SOURCE_FILE WORKING_DIRECTORY TIMEOUT)
    set(multiValueArgs SOURCES LIBRARIES DEPENDENCIES)
    
    cmake_parse_arguments(TEST "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # Determine source files
    if(TEST_SOURCE_FILE)
        set(test_sources ${TEST_SOURCE_FILE})
    elseif(TEST_SOURCES)
        set(test_sources ${TEST_SOURCES})
    else()
        set(test_sources ${test_name}.cpp)
    endif()
    
    # Create test executable
    add_executable(${test_name} ${test_sources})
    
    # Configure target
    configure_declarative_ui_target(${test_name})
    
    # Link libraries
    target_link_libraries(${test_name} PRIVATE
        DeclarativeUI
        Qt6::Test
        ${TEST_LIBRARIES}
    )
    
    # Set output directory
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    # Set test properties
    set(test_timeout 30)
    if(TEST_TIMEOUT)
        set(test_timeout ${TEST_TIMEOUT})
    endif()
    
    set(test_working_dir ${CMAKE_BINARY_DIR}/tests)
    if(TEST_WORKING_DIRECTORY)
        set(test_working_dir ${TEST_WORKING_DIRECTORY})
    endif()
    
    # Add to CTest
    add_test(NAME ${test_name} COMMAND ${test_name})
    set_tests_properties(${test_name} PROPERTIES
        TIMEOUT ${test_timeout}
        WORKING_DIRECTORY ${test_working_dir}
    )
    
    # Set test labels
    set(test_labels "unit")
    if(TEST_BENCHMARK)
        list(APPEND test_labels "benchmark")
        set_tests_properties(${test_name} PROPERTIES TIMEOUT 300)
    endif()
    
    if(TEST_INTEGRATION)
        list(APPEND test_labels "integration")
        set_tests_properties(${test_name} PROPERTIES TIMEOUT 60)
    endif()
    
    if(TEST_PERFORMANCE)
        list(APPEND test_labels "performance")
        set_tests_properties(${test_name} PROPERTIES TIMEOUT 120)
    endif()
    
    set_tests_properties(${test_name} PROPERTIES LABELS "${test_labels}")
    
    # Add dependencies
    if(TEST_DEPENDENCIES)
        add_dependencies(${test_name} ${TEST_DEPENDENCIES})
    endif()
    
    # Apply static analysis if enabled
    if(COMMAND apply_static_analysis)
        apply_static_analysis(${test_name})
    endif()
endfunction()

# Function to add a Qt-based test
function(add_qt_test test_name)
    add_declarative_ui_test(${test_name} ${ARGN})
    
    # Add Qt-specific test properties
    set_tests_properties(${test_name} PROPERTIES
        ENVIRONMENT "QT_QPA_PLATFORM=offscreen"
    )
endfunction()

# Function to add a benchmark test
function(add_benchmark_test test_name)
    if(ENABLE_BENCHMARK_TESTS)
        add_declarative_ui_test(${test_name} BENCHMARK ${ARGN})
        
        # Add benchmark-specific properties
        set_tests_properties(${test_name} PROPERTIES
            ENVIRONMENT "QT_QPA_PLATFORM=offscreen"
            TIMEOUT 300
        )
    endif()
endfunction()

# Function to add an integration test
function(add_integration_test test_name)
    if(ENABLE_INTEGRATION_TESTS)
        add_declarative_ui_test(${test_name} INTEGRATION ${ARGN})
        
        # Add integration-specific properties
        set_tests_properties(${test_name} PROPERTIES
            TIMEOUT 60
        )
    endif()
endfunction()

# Function to add a performance test
function(add_performance_test test_name)
    if(ENABLE_PERFORMANCE_TESTS)
        add_declarative_ui_test(${test_name} PERFORMANCE ${ARGN})
        
        # Add performance-specific properties
        set_tests_properties(${test_name} PROPERTIES
            TIMEOUT 120
        )
    endif()
endfunction()

# =============================================================================
# Test Data and Resources
# =============================================================================

# Function to copy test resources
function(copy_test_resources target_name)
    set(resource_dir ${CMAKE_SOURCE_DIR}/tests/resources)
    set(output_dir ${CMAKE_BINARY_DIR}/tests/resources)
    
    if(EXISTS ${resource_dir})
        add_custom_command(TARGET ${target_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${resource_dir} ${output_dir}
            COMMENT "Copying test resources"
        )
    endif()
endfunction()

# =============================================================================
# Test Coverage
# =============================================================================

if(ENABLE_TEST_COVERAGE)
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        # Add coverage flags
        add_compile_options(--coverage -O0 -g)
        add_link_options(--coverage)
        
        # Find coverage tools
        find_program(LCOV_EXE NAMES lcov)
        find_program(GENHTML_EXE NAMES genhtml)
        
        if(LCOV_EXE AND GENHTML_EXE)
            # Create coverage targets
            add_custom_target(coverage-clean
                COMMAND ${LCOV_EXE} --directory ${CMAKE_BINARY_DIR} --zerocounters
                COMMENT "Cleaning coverage data"
            )
            
            add_custom_target(coverage-run
                COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
                DEPENDS coverage-clean
                COMMENT "Running tests for coverage"
            )
            
            add_custom_target(coverage-collect
                COMMAND ${LCOV_EXE} --capture --directory ${CMAKE_BINARY_DIR} --output-file coverage.info
                COMMAND ${LCOV_EXE} --remove coverage.info '/usr/*' '*/Qt/*' '*/build/*' '*/tests/*' --output-file coverage.info
                DEPENDS coverage-run
                COMMENT "Collecting coverage data"
            )
            
            add_custom_target(coverage-html
                COMMAND ${GENHTML_EXE} coverage.info --output-directory coverage-html
                DEPENDS coverage-collect
                COMMENT "Generating HTML coverage report"
            )
            
            add_custom_target(coverage
                DEPENDS coverage-html
                COMMENT "Generate complete coverage report"
            )
            
            message(STATUS "Test coverage enabled")
        else()
            message(WARNING "lcov and genhtml required for coverage reporting")
        endif()
    else()
        message(WARNING "Test coverage only supported with GCC or Clang")
    endif()
endif()

# =============================================================================
# Test Execution Targets
# =============================================================================

# Target to run all tests
add_custom_target(run-tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --parallel ${CMAKE_BUILD_PARALLEL_LEVEL}
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running all tests"
)

# Target to run unit tests only
add_custom_target(run-unit-tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --label-regex "unit"
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running unit tests"
)

# Target to run integration tests only
add_custom_target(run-integration-tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --label-regex "integration"
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running integration tests"
)

# Target to run benchmark tests only
if(ENABLE_BENCHMARK_TESTS)
    add_custom_target(run-benchmark-tests
        COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --label-regex "benchmark"
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Running benchmark tests"
    )
endif()

# Target to run performance tests only
if(ENABLE_PERFORMANCE_TESTS)
    add_custom_target(run-performance-tests
        COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --label-regex "performance"
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Running performance tests"
    )
endif()

# =============================================================================
# Test Reporting
# =============================================================================

# Function to generate test report
function(generate_test_report)
    find_program(XSLTPROC_EXE NAMES xsltproc)
    
    if(XSLTPROC_EXE)
        add_custom_target(test-report
            COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure -T Test
            COMMAND ${XSLTPROC_EXE} 
                ${CMAKE_SOURCE_DIR}/cmake/CTestResults.xsl
                ${CMAKE_BINARY_DIR}/Testing/*/Test.xml
                > ${CMAKE_BINARY_DIR}/test-report.html
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            COMMENT "Generating test report"
        )
    endif()
endfunction()

# =============================================================================
# Memory Testing
# =============================================================================

option(ENABLE_MEMCHECK "Enable memory checking with Valgrind" OFF)

if(ENABLE_MEMCHECK)
    find_program(VALGRIND_EXE NAMES valgrind)
    
    if(VALGRIND_EXE)
        set(MEMORYCHECK_COMMAND ${VALGRIND_EXE})
        set(MEMORYCHECK_COMMAND_OPTIONS 
            "--leak-check=full"
            "--show-leak-kinds=all"
            "--track-origins=yes"
            "--error-exitcode=1"
        )
        
        # Configure CTest to use Valgrind
        configure_file(
            ${CMAKE_SOURCE_DIR}/cmake/CTestCustom.cmake.in
            ${CMAKE_BINARY_DIR}/CTestCustom.cmake
            @ONLY
        )
        
        add_custom_target(memcheck
            COMMAND ${CMAKE_CTEST_COMMAND} -T MemCheck --output-on-failure
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            COMMENT "Running memory check with Valgrind"
        )
        
        message(STATUS "Memory checking with Valgrind enabled")
    else()
        message(WARNING "Valgrind not found. Memory checking disabled.")
    endif()
endif()

# =============================================================================
# Test Configuration Summary
# =============================================================================

message(STATUS "Test Configuration:")
message(STATUS "  Build tests: ${BUILD_TESTS}")
message(STATUS "  Test coverage: ${ENABLE_TEST_COVERAGE}")
message(STATUS "  Benchmark tests: ${ENABLE_BENCHMARK_TESTS}")
message(STATUS "  Integration tests: ${ENABLE_INTEGRATION_TESTS}")
message(STATUS "  Performance tests: ${ENABLE_PERFORMANCE_TESTS}")
message(STATUS "  Memory checking: ${ENABLE_MEMCHECK}")

# =============================================================================
# Test Discovery
# =============================================================================

# Automatically discover and add tests from directories
function(discover_tests test_dir)
    if(EXISTS ${CMAKE_SOURCE_DIR}/${test_dir})
        file(GLOB_RECURSE test_files ${CMAKE_SOURCE_DIR}/${test_dir}/*.cpp)
        
        foreach(test_file ${test_files})
            get_filename_component(test_name ${test_file} NAME_WE)
            
            # Skip files that don't look like test files
            if(test_name MATCHES "^test_.*" OR test_name MATCHES ".*_test$")
                add_declarative_ui_test(${test_name} SOURCE_FILE ${test_file})
            endif()
        endforeach()
    endif()
endfunction()

# Discover tests in standard directories
if(BUILD_TESTS)
    discover_tests(tests/unit)
    discover_tests(tests/integration)
    
    if(ENABLE_BENCHMARK_TESTS)
        discover_tests(tests/benchmark)
    endif()
    
    if(ENABLE_PERFORMANCE_TESTS)
        discover_tests(tests/performance)
    endif()
endif()
