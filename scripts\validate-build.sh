#!/bin/bash
# Validate build configuration and dependencies

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

errors=0
warnings=0

echo -e "${BLUE}🔍 Validating build configuration...${NC}"

# =============================================================================
# Check Build Environment
# =============================================================================

echo "Checking build environment..."

# Check CMake version
if command -v cmake >/dev/null 2>&1; then
    CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
    echo -e "${GREEN}✅ CMake $CMAKE_VERSION found${NC}"
    
    # Check if version is 3.20+
    if [[ $(echo "$CMAKE_VERSION 3.20" | tr ' ' '\n' | sort -V | head -n1) != "3.20" ]]; then
        echo -e "${RED}❌ CMake version 3.20 or higher required${NC}"
        ((errors++))
    fi
else
    echo -e "${RED}❌ CMake not found${NC}"
    ((errors++))
fi

# Check C++ compiler
if command -v g++ >/dev/null 2>&1; then
    GCC_VERSION=$(g++ --version | head -n1 | grep -oP '\d+\.\d+' | head -n1)
    echo -e "${GREEN}✅ GCC $GCC_VERSION found${NC}"
    
    # Check if version supports C++20
    if [[ $(echo "$GCC_VERSION 10.0" | tr ' ' '\n' | sort -V | head -n1) != "10.0" ]]; then
        echo -e "${YELLOW}⚠️  GCC 10+ recommended for full C++20 support${NC}"
        ((warnings++))
    fi
elif command -v clang++ >/dev/null 2>&1; then
    CLANG_VERSION=$(clang++ --version | head -n1 | grep -oP '\d+\.\d+' | head -n1)
    echo -e "${GREEN}✅ Clang $CLANG_VERSION found${NC}"
    
    # Check if version supports C++20
    if [[ $(echo "$CLANG_VERSION 12.0" | tr ' ' '\n' | sort -V | head -n1) != "12.0" ]]; then
        echo -e "${YELLOW}⚠️  Clang 12+ recommended for full C++20 support${NC}"
        ((warnings++))
    fi
else
    echo -e "${RED}❌ No suitable C++ compiler found${NC}"
    ((errors++))
fi

# =============================================================================
# Check Qt6 Installation
# =============================================================================

echo "Checking Qt6 installation..."

# Check for Qt6 tools
if command -v qmake6 >/dev/null 2>&1; then
    QT_VERSION=$(qmake6 -query QT_VERSION)
    echo -e "${GREEN}✅ Qt6 $QT_VERSION found${NC}"
elif command -v qmake >/dev/null 2>&1; then
    QT_VERSION=$(qmake -query QT_VERSION)
    if [[ $QT_VERSION == 6.* ]]; then
        echo -e "${GREEN}✅ Qt6 $QT_VERSION found${NC}"
    else
        echo -e "${YELLOW}⚠️  Qt5 found, but Qt6 is required${NC}"
        ((warnings++))
    fi
else
    echo -e "${YELLOW}⚠️  Qt tools not found in PATH${NC}"
    ((warnings++))
fi

# Check for Qt6 development packages
QT6_FOUND=false
for qt_dir in /usr/lib/qt6 /opt/qt6 /usr/local/qt6 $HOME/Qt/6.* /usr/lib/x86_64-linux-gnu/qt6; do
    if [ -d "$qt_dir" ]; then
        echo -e "${GREEN}✅ Qt6 installation found at $qt_dir${NC}"
        QT6_FOUND=true
        break
    fi
done

if [ "$QT6_FOUND" = false ]; then
    echo -e "${YELLOW}⚠️  Qt6 installation not found in standard locations${NC}"
    echo "   Set CMAKE_PREFIX_PATH to Qt6 installation directory"
    ((warnings++))
fi

# =============================================================================
# Check Project Structure
# =============================================================================

echo "Checking project structure..."

# Check for required files
REQUIRED_FILES=(
    "CMakeLists.txt"
    "src"
    "README.md"
    "LICENSE"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -e "$file" ]; then
        echo -e "${GREEN}✅ $file found${NC}"
    else
        echo -e "${RED}❌ $file missing${NC}"
        ((errors++))
    fi
done

# Check for recommended files
RECOMMENDED_FILES=(
    "CONTRIBUTING.md"
    ".gitignore"
    ".gitattributes"
    ".editorconfig"
    "CMakePresets.json"
)

for file in "${RECOMMENDED_FILES[@]}"; do
    if [ -e "$file" ]; then
        echo -e "${GREEN}✅ $file found${NC}"
    else
        echo -e "${YELLOW}⚠️  $file recommended${NC}"
        ((warnings++))
    fi
done

# =============================================================================
# Validate CMake Configuration
# =============================================================================

echo "Validating CMake configuration..."

if [ -f "CMakeLists.txt" ]; then
    # Check for minimum CMake version
    if grep -q "cmake_minimum_required.*3\.20" CMakeLists.txt; then
        echo -e "${GREEN}✅ CMake minimum version specified${NC}"
    else
        echo -e "${YELLOW}⚠️  CMake minimum version should be 3.20+${NC}"
        ((warnings++))
    fi
    
    # Check for C++20 standard
    if grep -q "CMAKE_CXX_STANDARD.*20" CMakeLists.txt; then
        echo -e "${GREEN}✅ C++20 standard specified${NC}"
    else
        echo -e "${YELLOW}⚠️  C++20 standard should be specified${NC}"
        ((warnings++))
    fi
    
    # Check for Qt6 dependency
    if grep -q "find_package.*Qt6" CMakeLists.txt; then
        echo -e "${GREEN}✅ Qt6 dependency specified${NC}"
    else
        echo -e "${RED}❌ Qt6 dependency missing${NC}"
        ((errors++))
    fi
fi

# =============================================================================
# Check Build Directory
# =============================================================================

echo "Checking build directory..."

if [ -d "build" ]; then
    echo -e "${GREEN}✅ Build directory exists${NC}"
    
    # Check for CMake cache
    if [ -f "build/CMakeCache.txt" ]; then
        echo -e "${GREEN}✅ CMake cache found${NC}"
        
        # Check Qt6 in cache
        if grep -q "Qt6" build/CMakeCache.txt; then
            echo -e "${GREEN}✅ Qt6 configured in build${NC}"
        else
            echo -e "${YELLOW}⚠️  Qt6 not found in CMake cache${NC}"
            ((warnings++))
        fi
    else
        echo -e "${YELLOW}⚠️  CMake not configured yet${NC}"
        ((warnings++))
    fi
else
    echo -e "${YELLOW}⚠️  Build directory not found${NC}"
    echo "   Run: mkdir build && cd build && cmake .."
    ((warnings++))
fi

# =============================================================================
# Check Dependencies
# =============================================================================

echo "Checking optional dependencies..."

# Check for development tools
DEV_TOOLS=(
    "clang-format:Code formatting"
    "doxygen:Documentation generation"
    "valgrind:Memory checking"
    "gdb:Debugging"
)

for tool_info in "${DEV_TOOLS[@]}"; do
    tool=$(echo "$tool_info" | cut -d: -f1)
    desc=$(echo "$tool_info" | cut -d: -f2)
    
    if command -v "$tool" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ $tool found ($desc)${NC}"
    else
        echo -e "${YELLOW}⚠️  $tool not found ($desc)${NC}"
    fi
done

# =============================================================================
# Summary
# =============================================================================

echo ""
echo -e "${BLUE}📊 Validation Summary:${NC}"
echo "   Errors: $errors"
echo "   Warnings: $warnings"

if [ $errors -gt 0 ]; then
    echo -e "${RED}❌ Build validation failed with $errors errors${NC}"
    echo ""
    echo -e "${BLUE}🔧 To fix errors:${NC}"
    echo "   1. Install missing dependencies"
    echo "   2. Update CMakeLists.txt configuration"
    echo "   3. Set up Qt6 environment variables"
    echo "   4. Re-run this script"
    exit 1
elif [ $warnings -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Build validation completed with $warnings warnings${NC}"
    echo ""
    echo -e "${BLUE}💡 Recommendations:${NC}"
    echo "   1. Install recommended development tools"
    echo "   2. Add missing configuration files"
    echo "   3. Update compiler versions for better C++20 support"
    exit 0
else
    echo -e "${GREEN}✅ Build validation passed${NC}"
    echo ""
    echo -e "${BLUE}🚀 Ready to build:${NC}"
    echo "   mkdir build && cd build"
    echo "   cmake --preset=default .."
    echo "   cmake --build . --config Release"
    exit 0
fi
