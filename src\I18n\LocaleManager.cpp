#include "LocaleManager.hpp"
#include <QCollator>
#include <QRegularExpression>
#include <QDebug>

namespace DeclarativeUI {
namespace I18n {

LocaleManager* LocaleManager::instance_ = nullptr;
QMutex LocaleManager::instance_mutex_;

LocaleManager& LocaleManager::instance() {
    QMutexLocker locker(&instance_mutex_);
    if (!instance_) {
        instance_ = new LocaleManager();
    }
    return *instance_;
}

LocaleManager::LocaleManager(QObject* parent)
    : QObject(parent)
    , current_locale_(QLocale::system())
{
    qDebug() << "🌍 LocaleManager initialized with locale:" << current_locale_.name();
}

void LocaleManager::setLocale(const QLocale& locale) {
    QLocale old_locale = current_locale_;
    current_locale_ = locale;
    
    // Clear cache when locale changes
    QMutexLocker locker(&cache_mutex_);
    format_cache_.clear();
    
    qDebug() << "🌍 Locale changed from" << old_locale.name() << "to" << current_locale_.name();
    emit localeChanged(current_locale_, old_locale);
}

void LocaleManager::setLocale(const QString& locale_name) {
    setLocale(QLocale(locale_name));
}

LocaleManager::TextDirection LocaleManager::getTextDirection() const {
    return getTextDirection(current_locale_);
}

LocaleManager::TextDirection LocaleManager::getTextDirection(const QLocale& locale) const {
    switch (locale.textDirection()) {
        case Qt::LeftToRight:
            return TextDirection::LeftToRight;
        case Qt::RightToLeft:
            return TextDirection::RightToLeft;
        default:
            return TextDirection::LeftToRight;
    }
}

bool LocaleManager::isRightToLeft() const {
    return isRightToLeft(current_locale_);
}

bool LocaleManager::isRightToLeft(const QLocale& locale) const {
    return locale.textDirection() == Qt::RightToLeft;
}

QString LocaleManager::formatNumber(double number, const NumberFormatOptions& options) const {
    QString formatted = current_locale_.toString(number, 'f', 
        options.decimal_places >= 0 ? options.decimal_places : current_locale_.numberOptions().testFlag(QLocale::OmitGroupSeparator) ? 0 : 2);
    
    return applyNumberFormatOptions(formatted, options);
}

QString LocaleManager::formatInteger(int number, const NumberFormatOptions& options) const {
    QString formatted = current_locale_.toString(number);
    return applyNumberFormatOptions(formatted, options);
}

QString LocaleManager::formatPercent(double value, const NumberFormatOptions& options) const {
    NumberFormatOptions percent_options = options;
    if (percent_options.decimal_places < 0) {
        percent_options.decimal_places = 1;
    }
    
    QString formatted = formatNumber(value * 100, percent_options);
    return formatted + current_locale_.percent();
}

QString LocaleManager::formatCurrency(double amount, const CurrencyFormatOptions& options) const {
    QString currency_code = options.currency_code;
    if (currency_code.isEmpty()) {
        currency_code = current_locale_.currencySymbol(QLocale::CurrencyIsoCode);
    }
    
    QString currency_symbol = options.currency_symbol;
    if (currency_symbol.isEmpty()) {
        currency_symbol = current_locale_.currencySymbol(QLocale::CurrencySymbol);
    }
    
    QString formatted = current_locale_.toCurrencyString(amount, currency_symbol, options.decimal_places);
    
    if (options.show_currency_code && !currency_code.isEmpty()) {
        formatted += " " + currency_code;
    }
    
    return formatted;
}

QString LocaleManager::formatCurrency(double amount, const QString& currency_code) const {
    CurrencyFormatOptions options;
    options.currency_code = currency_code;
    return formatCurrency(amount, options);
}

QString LocaleManager::formatDate(const QDate& date, const DateTimeFormatOptions& options) const {
    if (!date.isValid()) {
        return QString();
    }
    
    if (options.use_relative_dates) {
        QString relative = formatRelativeDate(date);
        if (!relative.isEmpty()) {
            return relative;
        }
    }
    
    if (!options.custom_format.isEmpty()) {
        return current_locale_.toString(date, options.custom_format);
    }
    
    return current_locale_.toString(date, options.format_type);
}

QString LocaleManager::formatTime(const QTime& time, const DateTimeFormatOptions& options) const {
    if (!time.isValid()) {
        return QString();
    }
    
    if (!options.custom_format.isEmpty()) {
        return current_locale_.toString(time, options.custom_format);
    }
    
    return current_locale_.toString(time, options.format_type);
}

QString LocaleManager::formatDateTime(const QDateTime& datetime, const DateTimeFormatOptions& options) const {
    if (!datetime.isValid()) {
        return QString();
    }
    
    if (options.use_relative_dates) {
        QString relative = formatRelativeDateTime(datetime);
        if (!relative.isEmpty()) {
            return relative;
        }
    }
    
    if (!options.custom_format.isEmpty()) {
        return current_locale_.toString(datetime, options.custom_format);
    }
    
    return current_locale_.toString(datetime, options.format_type);
}

QString LocaleManager::formatRelativeDate(const QDate& date) const {
    if (!date.isValid()) {
        return QString();
    }
    
    QDate today = QDate::currentDate();
    int days_diff = today.daysTo(date);
    
    return getRelativeDateString(days_diff);
}

QString LocaleManager::formatRelativeDateTime(const QDateTime& datetime) const {
    if (!datetime.isValid()) {
        return QString();
    }
    
    QDateTime now = QDateTime::currentDateTime();
    qint64 seconds_diff = now.secsTo(datetime);
    
    // If more than 7 days, use regular date formatting
    if (qAbs(seconds_diff) > 7 * 24 * 60 * 60) {
        return formatDateTime(datetime);
    }
    
    return getRelativeTimeString(datetime);
}

Qt::DayOfWeek LocaleManager::getFirstDayOfWeek() const {
    return current_locale_.firstDayOfWeek();
}

QList<Qt::DayOfWeek> LocaleManager::getWeekendDays() const {
    return current_locale_.weekdays();
}

QString LocaleManager::getAMText() const {
    return current_locale_.amText();
}

QString LocaleManager::getPMText() const {
    return current_locale_.pmText();
}

double LocaleManager::parseNumber(const QString& text, bool* ok) const {
    return current_locale_.toDouble(text, ok);
}

QDate LocaleManager::parseDate(const QString& text, const QString& format, bool* ok) const {
    if (format.isEmpty()) {
        return current_locale_.toDate(text, QLocale::ShortFormat);
    }
    return current_locale_.toDate(text, format);
}

QTime LocaleManager::parseTime(const QString& text, const QString& format, bool* ok) const {
    if (format.isEmpty()) {
        return current_locale_.toTime(text, QLocale::ShortFormat);
    }
    return current_locale_.toTime(text, format);
}

QDateTime LocaleManager::parseDateTime(const QString& text, const QString& format, bool* ok) const {
    if (format.isEmpty()) {
        return current_locale_.toDateTime(text, QLocale::ShortFormat);
    }
    return current_locale_.toDateTime(text, format);
}

int LocaleManager::compareStrings(const QString& s1, const QString& s2) const {
    QCollator collator(current_locale_);
    return collator.compare(s1, s2);
}

QStringList LocaleManager::sortStrings(const QStringList& strings) const {
    QStringList sorted = strings;
    QCollator collator(current_locale_);
    std::sort(sorted.begin(), sorted.end(), collator);
    return sorted;
}

QStringList LocaleManager::getAvailableLocales() const {
    QStringList locales;
    for (const QLocale& locale : QLocale::matchingLocales(QLocale::AnyLanguage, QLocale::AnyScript, QLocale::AnyCountry)) {
        locales << locale.name();
    }
    return locales;
}

QString LocaleManager::getLocaleName(const QLocale& locale) const {
    return locale.name();
}

QString LocaleManager::getNativeLocaleName(const QLocale& locale) const {
    return locale.nativeLanguageName() + " (" + locale.nativeCountryName() + ")";
}

// Private helper methods

QString LocaleManager::applyNumberFormatOptions(const QString& formatted, const NumberFormatOptions& options) const {
    QString result = formatted;

    // Apply custom decimal point
    if (!options.custom_decimal_point.isEmpty()) {
        result.replace(current_locale_.decimalPoint(), options.custom_decimal_point);
    }

    // Apply custom thousands separator
    if (!options.custom_thousands_separator.isEmpty()) {
        result.replace(current_locale_.groupSeparator(), options.custom_thousands_separator);
    }

    // Remove grouping if disabled
    if (!options.use_grouping) {
        result.remove(current_locale_.groupSeparator());
    }

    // Add positive sign if requested
    if (options.show_positive_sign && !result.startsWith('-')) {
        result.prepend('+');
    }

    return result;
}

QString LocaleManager::getRelativeDateString(int days_diff) const {
    if (days_diff == 0) {
        return "Today";  // Should be translated
    } else if (days_diff == -1) {
        return "Yesterday";  // Should be translated
    } else if (days_diff == 1) {
        return "Tomorrow";  // Should be translated
    } else if (days_diff > 1 && days_diff <= 7) {
        return QString("In %1 days").arg(days_diff);  // Should be translated
    } else if (days_diff < -1 && days_diff >= -7) {
        return QString("%1 days ago").arg(-days_diff);  // Should be translated
    }

    return QString(); // Use regular date formatting
}

QString LocaleManager::getRelativeTimeString(const QDateTime& datetime) const {
    QDateTime now = QDateTime::currentDateTime();
    qint64 seconds_diff = now.secsTo(datetime);
    qint64 abs_diff = qAbs(seconds_diff);

    if (abs_diff < 60) {
        return "Just now";  // Should be translated
    } else if (abs_diff < 3600) {
        int minutes = abs_diff / 60;
        if (seconds_diff > 0) {
            return QString("In %1 minutes").arg(minutes);  // Should be translated
        } else {
            return QString("%1 minutes ago").arg(minutes);  // Should be translated
        }
    } else if (abs_diff < 86400) {
        int hours = abs_diff / 3600;
        if (seconds_diff > 0) {
            return QString("In %1 hours").arg(hours);  // Should be translated
        } else {
            return QString("%1 hours ago").arg(hours);  // Should be translated
        }
    } else {
        // Use relative date for longer periods
        return formatRelativeDate(datetime.date());
    }
}

} // namespace I18n
} // namespace DeclarativeUI
