#pragma once

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QProgressBar>
#include <QGroupBox>
#include <QCheckBox>
#include <QLineEdit>
#include <QComboBox>
#include <QSpinBox>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QSplitter>
#include <QTabWidget>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QHeaderView>
#include <QApplication>
#include <QDesktopServices>
#include <QUrl>
#include <QDir>
#include <QDebug>
#include <memory>

#include "../src/Documentation/DocumentationGenerator.hpp"

/**
 * @brief Comprehensive example demonstrating the Documentation Generator
 * 
 * This example showcases:
 * - Interactive documentation generation configuration
 * - Real-time progress monitoring and feedback
 * - Generated documentation preview and browsing
 * - Multiple output format support (HTML, Markdown, JSON)
 * - Component gallery and API reference generation
 * - Live documentation server with hot reload
 * - Quality metrics and validation reporting
 * - Incremental documentation updates
 */
class DocumentationGeneratorExample : public QMainWindow {
    Q_OBJECT

public:
    explicit DocumentationGeneratorExample(QWidget* parent = nullptr);
    ~DocumentationGeneratorExample() = default;

private slots:
    // **Generator Control**
    void generateDocumentation();
    void stopGeneration();
    void openOutputDirectory();
    void startLiveServer();
    void stopLiveServer();
    void validateDocumentation();
    
    // **Configuration Management**
    void loadConfiguration();
    void saveConfiguration();
    void resetConfiguration();
    void updateConfiguration();
    
    // **Progress and Status**
    void onGenerationStarted();
    void onGenerationProgress(int percentage, const QString& current_task);
    void onGenerationFinished(bool success);
    void onErrorOccurred(const QString& error_message);
    void onWarningIssued(const QString& warning_message);
    void onFileProcessed(const QString& file_path);
    void onComponentExtracted(const DeclarativeUI::Documentation::DocumentationGenerator::ComponentInfo& component);
    void onExampleFound(const DeclarativeUI::Documentation::DocumentationGenerator::ExampleInfo& example);
    
    // **Preview and Navigation**
    void previewDocumentation();
    void refreshPreview();
    void navigateToItem(QTreeWidgetItem* item, int column);
    void searchDocumentation();
    
    // **Quality and Metrics**
    void showQualityReport();
    void showStatistics();
    void exportDocumentation();

private:
    // **UI Setup Methods**
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupConfigurationPanel();
    void setupProgressPanel();
    void setupPreviewPanel();
    void setupLogPanel();
    
    // **Configuration Helpers**
    void populateConfigurationUI();
    void updateConfigurationFromUI();
    DeclarativeUI::Documentation::DocumentationGenerator::GeneratorConfig getConfigurationFromUI();
    void setConfigurationToUI(const DeclarativeUI::Documentation::DocumentationGenerator::GeneratorConfig& config);
    
    // **Preview Helpers**
    void populateDocumentationTree();
    void addDocumentationItems(QTreeWidgetItem* parent, const QString& category, 
                              const std::vector<DeclarativeUI::Documentation::DocumentationGenerator::DocumentationItem>& items);
    void addComponentItems(QTreeWidgetItem* parent, 
                          const std::vector<DeclarativeUI::Documentation::DocumentationGenerator::ComponentInfo>& components);
    void addExampleItems(QTreeWidgetItem* parent, 
                        const std::vector<DeclarativeUI::Documentation::DocumentationGenerator::ExampleInfo>& examples);
    
    // **Utility Methods**
    void logMessage(const QString& message, const QString& type = "Info");
    void updateProgress(int percentage, const QString& task);
    void showNotification(const QString& title, const QString& message, bool is_error = false);
    QString formatFileSize(qint64 bytes);
    QString formatDuration(qint64 milliseconds);

    // **Core Components**
    std::unique_ptr<DeclarativeUI::Documentation::DocumentationGenerator> generator_;
    
    // **Main UI Components**
    QWidget* central_widget_;
    QSplitter* main_splitter_;
    QTabWidget* left_tabs_;
    QTabWidget* right_tabs_;
    
    // **Configuration Panel**
    QWidget* config_widget_;
    QLineEdit* project_name_edit_;
    QLineEdit* project_version_edit_;
    QTextEdit* project_description_edit_;
    QLineEdit* output_directory_edit_;
    QPushButton* browse_output_button_;
    QLineEdit* source_directories_edit_;
    QLineEdit* example_directories_edit_;
    QComboBox* theme_combo_;
    QCheckBox* generate_api_checkbox_;
    QCheckBox* generate_components_checkbox_;
    QCheckBox* generate_examples_checkbox_;
    QCheckBox* generate_search_checkbox_;
    QCheckBox* enable_live_demos_checkbox_;
    QCheckBox* parse_doxygen_checkbox_;
    QCheckBox* incremental_updates_checkbox_;
    
    // **Progress Panel**
    QWidget* progress_widget_;
    QProgressBar* progress_bar_;
    QLabel* current_task_label_;
    QLabel* files_processed_label_;
    QLabel* components_found_label_;
    QLabel* examples_found_label_;
    QPushButton* generate_button_;
    QPushButton* stop_button_;
    QPushButton* validate_button_;
    
    // **Preview Panel**
    QWidget* preview_widget_;
    QTreeWidget* documentation_tree_;
    QPushButton* preview_button_;
    QPushButton* refresh_button_;
    QPushButton* open_output_button_;
    QLineEdit* search_edit_;
    QPushButton* search_button_;
    
    // **Live Server Panel**
    QWidget* server_widget_;
    QSpinBox* server_port_spin_;
    QPushButton* start_server_button_;
    QPushButton* stop_server_button_;
    QLabel* server_status_label_;
    QPushButton* open_browser_button_;
    
    // **Log Panel**
    QWidget* log_widget_;
    QTextEdit* log_text_;
    QPushButton* clear_log_button_;
    QPushButton* save_log_button_;
    
    // **Statistics Panel**
    QWidget* stats_widget_;
    QLabel* generation_time_label_;
    QLabel* total_files_label_;
    QLabel* classes_documented_label_;
    QLabel* functions_documented_label_;
    QLabel* components_extracted_label_;
    QLabel* examples_found_label_;
    QLabel* warnings_count_label_;
    QLabel* errors_count_label_;
    
    // **Status and State**
    QLabel* status_label_;
    QProgressBar* status_progress_;
    bool generation_in_progress_;
    bool live_server_running_;
    int files_processed_count_;
    int components_found_count_;
    int examples_found_count_;
    QTimer* refresh_timer_;
    
    // **Configuration State**
    QString current_config_file_;
    bool config_modified_;
};

/**
 * @brief Documentation generation stress test and benchmarking
 */
class DocumentationBenchmark : public QObject {
    Q_OBJECT

public:
    explicit DocumentationBenchmark(QObject* parent = nullptr);

    struct BenchmarkResults {
        qint64 total_generation_time_ms;
        qint64 parsing_time_ms;
        qint64 html_generation_time_ms;
        qint64 component_extraction_time_ms;
        qint64 example_extraction_time_ms;
        int total_files_processed;
        int total_items_documented;
        int total_components_found;
        int total_examples_found;
        qint64 output_size_bytes;
        double files_per_second;
        double items_per_second;
        QString performance_grade;
    };

    BenchmarkResults runBenchmark(const QString& source_directory);
    void runStressTest(int iterations = 10);
    void comparePerformance(const QStringList& configurations);

signals:
    void benchmarkProgress(int percentage, const QString& current_test);
    void benchmarkFinished(const BenchmarkResults& results);

private:
    BenchmarkResults calculateResults(qint64 start_time, qint64 end_time, 
                                    const DeclarativeUI::Documentation::DocumentationGenerator* generator);
    QString calculatePerformanceGrade(const BenchmarkResults& results);
    void logBenchmarkResults(const BenchmarkResults& results);
};

/**
 * @brief Interactive documentation quality analyzer
 */
class DocumentationQualityAnalyzer : public QObject {
    Q_OBJECT

public:
    explicit DocumentationQualityAnalyzer(QObject* parent = nullptr);

    struct QualityMetrics {
        double documentation_coverage_percentage;
        int total_items;
        int documented_items;
        int missing_brief_descriptions;
        int missing_detailed_descriptions;
        int missing_parameters;
        int missing_return_values;
        int broken_links;
        int orphaned_files;
        QStringList quality_issues;
        QStringList recommendations;
        QString overall_grade;
    };

    QualityMetrics analyzeQuality(const std::vector<DeclarativeUI::Documentation::DocumentationGenerator::DocumentationItem>& items);
    QStringList generateRecommendations(const QualityMetrics& metrics);
    QString generateQualityReport(const QualityMetrics& metrics);

signals:
    void analysisProgress(int percentage, const QString& current_analysis);
    void analysisFinished(const QualityMetrics& metrics);

private:
    QString calculateOverallGrade(const QualityMetrics& metrics);
    double calculateCoveragePercentage(int documented, int total);
};

/**
 * @brief Live documentation server with hot reload
 */
class LiveDocumentationServer : public QObject {
    Q_OBJECT

public:
    explicit LiveDocumentationServer(QObject* parent = nullptr);

    void startServer(int port, const QString& documentation_root);
    void stopServer();
    bool isRunning() const { return server_running_; }
    int getPort() const { return server_port_; }
    QString getServerUrl() const;

    void enableHotReload(bool enabled);
    void watchDocumentationFiles(const QString& directory);

signals:
    void serverStarted(int port);
    void serverStopped();
    void clientConnected(const QString& client_address);
    void clientDisconnected(const QString& client_address);
    void documentationUpdated(const QString& file_path);
    void hotReloadTriggered();

private slots:
    void onDocumentationFileChanged(const QString& file_path);
    void handleClientRequest(const QString& path, const QJsonObject& parameters);

private:
    void setupServer();
    void broadcastUpdate(const QString& update_type, const QJsonObject& data);
    QString generateServerResponse(const QString& request_path);

    bool server_running_;
    int server_port_;
    QString documentation_root_;
    bool hot_reload_enabled_;
    std::unique_ptr<QFileSystemWatcher> file_watcher_;
    QStringList connected_clients_;
};

#endif // DOCUMENTATIONGENERATOREXAMPLE_HPP
