#include "AccessibilityValidator.hpp"
#include <QApplication>
#include <QWidget>
#include <QDebug>
#include <QJsonDocument>
#include <QFile>
#include <QTextStream>
#include <QTimer>
#include <QDateTime>
#include <QPalette>
#include <QStyleOption>
#include <QAbstractButton>
#include <QLineEdit>
#include <QLabel>
#include <QListWidget>
#include <QTreeWidget>
#include <QTabWidget>
#include <cmath>

namespace DeclarativeUI::Accessibility {

// ValidationIssue implementation
QJsonObject ValidationIssue::toJson() const {
    QJsonObject obj;
    obj["id"] = id;
    obj["title"] = title;
    obj["description"] = description;
    obj["severity"] = static_cast<int>(severity);
    obj["category"] = static_cast<int>(category);
    obj["wcagLevel"] = static_cast<int>(wcagLevel);
    obj["wcagCriterion"] = wcagCriterion;
    obj["location"] = QJsonObject{
        {"x", location.x()},
        {"y", location.y()},
        {"width", location.width()},
        {"height", location.height()}
    };
    
    QJsonArray suggestionsArray;
    for (const QString& suggestion : suggestions) {
        suggestionsArray.append(suggestion);
    }
    obj["suggestions"] = suggestionsArray;
    obj["metadata"] = metadata;
    
    return obj;
}

ValidationIssue ValidationIssue::fromJson(const QJsonObject& json) {
    ValidationIssue issue;
    issue.id = json["id"].toString();
    issue.title = json["title"].toString();
    issue.description = json["description"].toString();
    issue.severity = static_cast<ValidationSeverity>(json["severity"].toInt());
    issue.category = static_cast<ValidationCategory>(json["category"].toInt());
    issue.wcagLevel = static_cast<WCAGLevel>(json["wcagLevel"].toInt());
    issue.wcagCriterion = json["wcagCriterion"].toString();
    
    QJsonObject locationObj = json["location"].toObject();
    issue.location = QRect(
        locationObj["x"].toInt(),
        locationObj["y"].toInt(),
        locationObj["width"].toInt(),
        locationObj["height"].toInt()
    );
    
    QJsonArray suggestionsArray = json["suggestions"].toArray();
    for (const QJsonValue& value : suggestionsArray) {
        issue.suggestions.append(value.toString());
    }
    
    issue.metadata = json["metadata"].toObject();
    return issue;
}

// ValidationReport implementation
int ValidationReport::criticalIssues() const {
    return std::count_if(issues.begin(), issues.end(),
                        [](const ValidationIssue& issue) {
                            return issue.severity == ValidationSeverity::Critical;
                        });
}

int ValidationReport::errorIssues() const {
    return std::count_if(issues.begin(), issues.end(),
                        [](const ValidationIssue& issue) {
                            return issue.severity == ValidationSeverity::Error;
                        });
}

int ValidationReport::warningIssues() const {
    return std::count_if(issues.begin(), issues.end(),
                        [](const ValidationIssue& issue) {
                            return issue.severity == ValidationSeverity::Warning;
                        });
}

int ValidationReport::infoIssues() const {
    return std::count_if(issues.begin(), issues.end(),
                        [](const ValidationIssue& issue) {
                            return issue.severity == ValidationSeverity::Info;
                        });
}

double ValidationReport::complianceScore() const {
    if (issues.isEmpty()) return 1.0;
    
    // Weight issues by severity
    double totalWeight = 0.0;
    double issueWeight = 0.0;
    
    for (const ValidationIssue& issue : issues) {
        double weight = 1.0;
        switch (issue.severity) {
            case ValidationSeverity::Critical: weight = 4.0; break;
            case ValidationSeverity::Error: weight = 3.0; break;
            case ValidationSeverity::Warning: weight = 2.0; break;
            case ValidationSeverity::Info: weight = 1.0; break;
        }
        totalWeight += weight;
        issueWeight += weight;
    }
    
    // Assume a baseline of 100 potential issues for scoring
    double baselineWeight = 100.0;
    return std::max(0.0, (baselineWeight - issueWeight) / baselineWeight);
}

bool ValidationReport::isCompliant(WCAGLevel level) const {
    // Check if there are any critical or error issues for the target level
    for (const ValidationIssue& issue : issues) {
        if (issue.wcagLevel <= level && 
            (issue.severity == ValidationSeverity::Critical || 
             issue.severity == ValidationSeverity::Error)) {
            return false;
        }
    }
    return true;
}

// AccessibilityValidator implementation
AccessibilityValidator::AccessibilityValidator(QObject* parent)
    : QObject(parent)
{
    validation_timer_ = new QTimer(this);
    validation_timer_->setSingleShot(false);
    validation_timer_->setInterval(5000);  // 5 seconds default
    connect(validation_timer_, &QTimer::timeout, this, &AccessibilityValidator::performRealTimeValidation);
    
    qDebug() << "✅ AccessibilityValidator initialized";
}

void AccessibilityValidator::setConfig(const ValidationConfig& config) {
    config_ = config;
    
    // Clear cache when config changes
    issue_cache_.clear();
    cache_timestamps_.clear();
}

ValidationReport AccessibilityValidator::validateWidget(QWidget* widget) {
    if (!widget) {
        return ValidationReport();
    }
    
    validation_in_progress_ = true;
    current_validation_root_ = widget;
    validation_progress_ = 0;
    
    emit validationStarted(widget);
    
    ValidationReport report;
    report.applicationName = QApplication::applicationName();
    report.version = QApplication::applicationVersion();
    report.timestamp = QDateTime::currentDateTime();
    report.targetLevel = config_.targetLevel;
    
    QList<ValidationIssue> issues;
    validateWidgetRecursive(widget, issues);
    report.issues = issues;
    
    validation_in_progress_ = false;
    total_widgets_validated_++;
    total_issues_found_ += issues.size();
    last_validation_time_ = QDateTime::currentDateTime();
    
    emit validationCompleted(report);
    
    qDebug() << "✅ Validation completed for widget" << widget->objectName() 
             << "- Found" << issues.size() << "issues";
    
    return report;
}

ValidationReport AccessibilityValidator::validateApplication() {
    QWidget* mainWindow = nullptr;
    
    // Find the main window
    for (QWidget* widget : QApplication::topLevelWidgets()) {
        if (widget->isWindow() && widget->isVisible()) {
            mainWindow = widget;
            break;
        }
    }
    
    if (!mainWindow) {
        qWarning() << "No main window found for application validation";
        return ValidationReport();
    }
    
    return validateWidgetTree(mainWindow);
}

ValidationReport AccessibilityValidator::validateWidgetTree(QWidget* root) {
    return validateWidget(root);
}

QList<ValidationIssue> AccessibilityValidator::checkColorContrast(QWidget* widget) {
    QList<ValidationIssue> issues;
    
    if (!config_.checkColorContrast || !widget) {
        return issues;
    }
    
    // Get widget colors
    QColor foreground = getWidgetForegroundColor(widget);
    QColor background = getWidgetBackgroundColor(widget);
    
    if (!foreground.isValid() || !background.isValid()) {
        return issues;
    }
    
    // Calculate contrast ratio
    double contrastRatio = calculateContrastRatio(foreground, background);
    
    // Check against WCAG requirements
    bool meetsAA = contrastRatio >= 4.5;
    bool meetsAAA = contrastRatio >= 7.0;
    
    if (!meetsAA && config_.targetLevel >= WCAGLevel::AA) {
        ValidationIssue issue = createIssue(
            "color-contrast-aa",
            "Insufficient Color Contrast",
            QString("Color contrast ratio is %1:1, but WCAG AA requires at least 4.5:1")
                .arg(contrastRatio, 0, 'f', 2),
            ValidationSeverity::Error,
            ValidationCategory::ColorContrast,
            WCAGLevel::AA,
            "1.4.3",
            widget
        );
        
        issue.suggestions << "Increase contrast between text and background colors"
                         << "Use darker text on light backgrounds or lighter text on dark backgrounds"
                         << QString("Suggested foreground color: %1")
                            .arg(suggestContrastColor(background, WCAGLevel::AA).name());
        
        issues.append(issue);
    }
    
    if (!meetsAAA && config_.targetLevel >= WCAGLevel::AAA) {
        ValidationIssue issue = createIssue(
            "color-contrast-aaa",
            "Enhanced Color Contrast Not Met",
            QString("Color contrast ratio is %1:1, but WCAG AAA requires at least 7.0:1")
                .arg(contrastRatio, 0, 'f', 2),
            ValidationSeverity::Warning,
            ValidationCategory::ColorContrast,
            WCAGLevel::AAA,
            "1.4.6",
            widget
        );
        
        issue.suggestions << "Further increase contrast for enhanced accessibility"
                         << QString("Suggested foreground color: %1")
                            .arg(suggestContrastColor(background, WCAGLevel::AAA).name());
        
        issues.append(issue);
    }
    
    return issues;
}

QList<ValidationIssue> AccessibilityValidator::checkKeyboardNavigation(QWidget* widget) {
    QList<ValidationIssue> issues;
    
    if (!config_.checkKeyboardNavigation || !widget) {
        return issues;
    }
    
    // Check if widget is focusable when it should be
    bool shouldBeFocusable = qobject_cast<QAbstractButton*>(widget) ||
                            qobject_cast<QLineEdit*>(widget) ||
                            widget->property("clickable").toBool();
    
    if (shouldBeFocusable && !isWidgetFocusable(widget)) {
        ValidationIssue issue = createIssue(
            "keyboard-focusable",
            "Interactive Element Not Keyboard Focusable",
            "This interactive element cannot be reached using keyboard navigation",
            ValidationSeverity::Error,
            ValidationCategory::KeyboardNavigation,
            WCAGLevel::A,
            "2.1.1",
            widget
        );
        
        issue.suggestions << "Set focusPolicy to Qt::TabFocus or Qt::StrongFocus"
                         << "Ensure the widget can receive keyboard focus"
                         << "Add to the tab order if necessary";
        
        issues.append(issue);
    }
    
    // Check for keyboard traps
    if (isWidgetFocusable(widget)) {
        // This would require more complex analysis of the widget tree
        // For now, we'll do a basic check
        if (widget->focusPolicy() == Qt::ClickFocus) {
            ValidationIssue issue = createIssue(
                "keyboard-trap-risk",
                "Potential Keyboard Trap",
                "Widget uses ClickFocus which may create keyboard navigation issues",
                ValidationSeverity::Warning,
                ValidationCategory::KeyboardNavigation,
                WCAGLevel::A,
                "2.1.2",
                widget
            );
            
            issue.suggestions << "Consider using TabFocus or StrongFocus instead"
                             << "Ensure users can navigate away using keyboard"
                             << "Test keyboard navigation thoroughly";
            
            issues.append(issue);
        }
    }
    
    return issues;
}

QList<ValidationIssue> AccessibilityValidator::checkScreenReaderSupport(QWidget* widget) {
    QList<ValidationIssue> issues;
    
    if (!config_.checkScreenReaderSupport || !widget) {
        return issues;
    }
    
    // Check for accessible name
    if (!hasAccessibleName(widget)) {
        // Only flag this for interactive or important elements
        bool needsName = qobject_cast<QAbstractButton*>(widget) ||
                        qobject_cast<QLineEdit*>(widget) ||
                        widget->property("important").toBool();
        
        if (needsName) {
            ValidationIssue issue = createIssue(
                "accessible-name-missing",
                "Missing Accessible Name",
                "This element lacks an accessible name for screen readers",
                ValidationSeverity::Error,
                ValidationCategory::ScreenReader,
                WCAGLevel::A,
                "4.1.2",
                widget
            );
            
            issue.suggestions << "Set accessibleName property"
                             << "Add a descriptive label"
                             << "Use setAccessibleName() method"
                             << "Ensure the name describes the element's purpose";
            
            issues.append(issue);
        }
    }
    
    // Check for accessible description where helpful
    if (!hasAccessibleDescription(widget)) {
        bool couldUseDescription = qobject_cast<QLineEdit*>(widget) ||
                                  widget->property("complex").toBool();
        
        if (couldUseDescription) {
            ValidationIssue issue = createIssue(
                "accessible-description-missing",
                "Missing Accessible Description",
                "This element could benefit from an accessible description",
                ValidationSeverity::Info,
                ValidationCategory::ScreenReader,
                WCAGLevel::AA,
                "3.3.2",
                widget
            );
            
            issue.suggestions << "Set accessibleDescription property"
                             << "Add helpful context for screen reader users"
                             << "Describe the element's function or expected input";
            
            issues.append(issue);
        }
    }
    
    return issues;
}

QList<ValidationIssue> AccessibilityValidator::checkFocusIndicators(QWidget* widget) {
    QList<ValidationIssue> issues;
    
    if (!config_.checkFocusIndicators || !widget || !isWidgetFocusable(widget)) {
        return issues;
    }
    
    // Check if focus indicator is visible
    if (!hasFocusIndicator(widget)) {
        ValidationIssue issue = createIssue(
            "focus-indicator-missing",
            "Missing Focus Indicator",
            "This focusable element lacks a visible focus indicator",
            ValidationSeverity::Error,
            ValidationCategory::FocusManagement,
            WCAGLevel::AA,
            "2.4.7",
            widget
        );
        
        issue.suggestions << "Add a visible focus indicator (border, outline, etc.)"
                         << "Use CSS :focus pseudo-class"
                         << "Ensure focus indicator has sufficient contrast"
                         << "Make focus indicator clearly visible";
        
        issues.append(issue);
    }
    
    return issues;
}

void AccessibilityValidator::validateWidgetRecursive(QWidget* widget, QList<ValidationIssue>& issues) {
    if (!widget || !widget->isVisible()) {
        return;
    }
    
    // Check cache first
    if (issue_cache_.contains(widget)) {
        QDateTime cacheTime = cache_timestamps_.value(widget);
        if (cacheTime.secsTo(QDateTime::currentDateTime()) < 60) {  // 1 minute cache
            issues.append(issue_cache_.value(widget));
            return;
        }
    }
    
    QList<ValidationIssue> widgetIssues;
    
    // Run all enabled checks
    if (config_.checkColorContrast) {
        widgetIssues.append(checkColorContrast(widget));
    }
    
    if (config_.checkKeyboardNavigation) {
        widgetIssues.append(checkKeyboardNavigation(widget));
    }
    
    if (config_.checkScreenReaderSupport) {
        widgetIssues.append(checkScreenReaderSupport(widget));
    }
    
    if (config_.checkFocusIndicators) {
        widgetIssues.append(checkFocusIndicators(widget));
    }
    
    // Cache results
    issue_cache_[widget] = widgetIssues;
    cache_timestamps_[widget] = QDateTime::currentDateTime();
    
    // Add to main issues list
    issues.append(widgetIssues);
    
    // Emit individual issues
    for (const ValidationIssue& issue : widgetIssues) {
        emit issueFound(issue);
    }
    
    // Recursively validate children
    for (QObject* child : widget->children()) {
        QWidget* childWidget = qobject_cast<QWidget*>(child);
        if (childWidget) {
            validateWidgetRecursive(childWidget, issues);
        }
    }
    
    // Update progress
    validation_progress_++;
    emit validationProgress(validation_progress_);
}

ValidationIssue AccessibilityValidator::createIssue(const QString& id, const QString& title, 
                                                   const QString& description, ValidationSeverity severity,
                                                   ValidationCategory category, WCAGLevel wcagLevel,
                                                   const QString& wcagCriterion, QWidget* widget) {
    ValidationIssue issue;
    issue.id = id;
    issue.title = title;
    issue.description = description;
    issue.severity = severity;
    issue.category = category;
    issue.wcagLevel = wcagLevel;
    issue.wcagCriterion = wcagCriterion;
    issue.widget = widget;
    
    if (widget) {
        issue.location = getWidgetBounds(widget);
        issue.metadata["widgetClass"] = widget->metaObject()->className();
        issue.metadata["widgetName"] = widget->objectName();
    }
    
    return issue;
}

double AccessibilityValidator::calculateContrastRatio(const QColor& foreground, const QColor& background) {
    // Calculate relative luminance
    auto getLuminance = [](const QColor& color) {
        auto toLinear = [](double value) {
            value /= 255.0;
            return (value <= 0.03928) ? value / 12.92 : std::pow((value + 0.055) / 1.055, 2.4);
        };
        
        double r = toLinear(color.red());
        double g = toLinear(color.green());
        double b = toLinear(color.blue());
        
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };
    
    double lum1 = getLuminance(foreground);
    double lum2 = getLuminance(background);
    
    double lighter = std::max(lum1, lum2);
    double darker = std::min(lum1, lum2);
    
    return (lighter + 0.05) / (darker + 0.05);
}

bool AccessibilityValidator::meetsContrastRequirement(const QColor& foreground, const QColor& background, WCAGLevel level) {
    double ratio = calculateContrastRatio(foreground, background);
    
    switch (level) {
        case WCAGLevel::A:
            return ratio >= 3.0;  // Large text requirement
        case WCAGLevel::AA:
            return ratio >= 4.5;
        case WCAGLevel::AAA:
            return ratio >= 7.0;
    }
    
    return false;
}

QColor AccessibilityValidator::suggestContrastColor(const QColor& background, WCAGLevel level, bool preferDark) {
    double targetRatio = (level == WCAGLevel::AAA) ? 7.0 : 4.5;
    
    // Try black or white first
    QColor black(0, 0, 0);
    QColor white(255, 255, 255);
    
    if (calculateContrastRatio(black, background) >= targetRatio) {
        return preferDark ? black : white;
    }
    
    if (calculateContrastRatio(white, background) >= targetRatio) {
        return preferDark ? white : black;
    }
    
    // If neither works, adjust the background luminance
    return preferDark ? black : white;  // Fallback
}

bool AccessibilityValidator::isWidgetFocusable(QWidget* widget) {
    return widget && widget->isEnabled() && widget->isVisible() && 
           widget->focusPolicy() != Qt::NoFocus;
}

bool AccessibilityValidator::hasAccessibleName(QWidget* widget) {
    return widget && !widget->accessibleName().isEmpty();
}

bool AccessibilityValidator::hasAccessibleDescription(QWidget* widget) {
    return widget && !widget->accessibleDescription().isEmpty();
}

QColor AccessibilityValidator::getWidgetForegroundColor(QWidget* widget) {
    if (!widget) return QColor();
    
    QPalette palette = widget->palette();
    return palette.color(QPalette::WindowText);
}

QColor AccessibilityValidator::getWidgetBackgroundColor(QWidget* widget) {
    if (!widget) return QColor();
    
    QPalette palette = widget->palette();
    return palette.color(QPalette::Window);
}

bool AccessibilityValidator::hasFocusIndicator(QWidget* widget) {
    if (!widget) return false;
    
    // This is a simplified check - in practice, you'd need to analyze the widget's style
    // and check for focus-related styling
    QString styleSheet = widget->styleSheet();
    return styleSheet.contains(":focus") || styleSheet.contains("focus");
}

QRect AccessibilityValidator::getWidgetBounds(QWidget* widget) {
    if (!widget) return QRect();
    
    QPoint globalPos = widget->mapToGlobal(QPoint(0, 0));
    return QRect(globalPos, widget->size());
}

void AccessibilityValidator::performRealTimeValidation() {
    if (!real_time_validation_enabled_ || tracked_widgets_.isEmpty()) {
        return;
    }

    for (QWidget* widget : tracked_widgets_) {
        if (widget && widget->isVisible()) {
            QList<ValidationIssue> issues;
            validateWidgetRecursive(widget, issues);

            for (const ValidationIssue& issue : issues) {
                emit realTimeIssueDetected(issue);
            }
        }
    }
}

// **Missing method implementations**
void AccessibilityValidator::enableRealTimeValidation(bool enabled) {
    real_time_validation_enabled_ = enabled;
    if (enabled) {
        validation_timer_->start();
    } else {
        validation_timer_->stop();
    }
}

void AccessibilityValidator::onWidgetChanged(QWidget* widget) {
    // Stub implementation - handle widget changes
    Q_UNUSED(widget)
}

} // namespace DeclarativeUI::Accessibility
