#include "Dropdown.hpp"
#include <QApplication>
#include <QScreen>
#include <QKeyEvent>
#include <QMouseEvent>
#include <QDebug>
#include <QCheckBox>
#include <QTimer>
#include <algorithm>

namespace DeclarativeUI::Components {

Dropdown::Dropdown(QObject* parent)
    : UIElement(parent)
    , dropdown_frame_(nullptr)
    , main_layout_(nullptr)
    , search_edit_(nullptr)
    , dropdown_button_(nullptr)
    , clear_button_(nullptr)
    , dropdown_popup_(nullptr)
    , popup_layout_(nullptr)
    , scroll_area_(nullptr)
    , items_list_(nullptr)
    , loading_label_(nullptr)
    , select_all_button_(nullptr)
    , popup_animation_(nullptr)
    , popup_opacity_(nullptr)
    , placeholder_text_("Select an option...")
    , searchable_(false)
    , search_mode_(SearchMode::Contains)
    , selection_mode_(SelectionMode::Single)
    , max_height_(300)
    , max_visible_items_(10)
    , clearable_(false)
    , disabled_(false)
    , loading_(false)
    , virtual_scrolling_(false)
    , item_height_(32)
    , item_spacing_(2)
    , dropdown_open_(false)
{
    setupUI();
    setupAnimations();
    connectSignals();
}

void Dropdown::initialize() {
    // Dropdown is initialized in constructor, no additional setup needed
    // This method satisfies the UIElement interface requirement
}

Dropdown& Dropdown::placeholder(const QString& text) {
    placeholder_text_ = text;
    updatePlaceholder();
    return *this;
}

Dropdown& Dropdown::searchable(bool enable) {
    searchable_ = enable;
    if (search_edit_) {
        search_edit_->setVisible(enable);
    }
    return *this;
}

Dropdown& Dropdown::searchMode(SearchMode mode) {
    search_mode_ = mode;
    return *this;
}

Dropdown& Dropdown::selectionMode(SelectionMode mode) {
    selection_mode_ = mode;
    updateItemsList();
    return *this;
}

Dropdown& Dropdown::maxHeight(int height) {
    max_height_ = height;
    if (dropdown_popup_) {
        dropdown_popup_->setMaximumHeight(height);
    }
    return *this;
}

Dropdown& Dropdown::maxVisibleItems(int count) {
    max_visible_items_ = count;
    return *this;
}

Dropdown& Dropdown::clearable(bool enable) {
    clearable_ = enable;
    if (clear_button_) {
        clear_button_->setVisible(enable && !selected_values_.isEmpty());
    }
    return *this;
}

Dropdown& Dropdown::disabled(bool disable) {
    disabled_ = disable;
    if (dropdown_frame_) {
        dropdown_frame_->setEnabled(!disable);
    }
    return *this;
}

Dropdown& Dropdown::loading(bool show_loading) {
    loading_ = show_loading;
    if (loading_label_) {
        loading_label_->setVisible(show_loading);
    }
    if (items_list_) {
        items_list_->setVisible(!show_loading);
    }
    return *this;
}

Dropdown& Dropdown::virtualScrolling(bool enable) {
    virtual_scrolling_ = enable;
    return *this;
}

Dropdown& Dropdown::addItem(const QString& text, const QVariant& value) {
    DropdownItem item;
    item.text = text;
    item.value = value.isValid() ? value : text;
    return addItem(item);
}

Dropdown& Dropdown::addItem(const DropdownItem& item) {
    items_.append(item);
    updateItemsList();
    return *this;
}

Dropdown& Dropdown::addItems(const QStringList& items) {
    for (const QString& text : items) {
        addItem(text);
    }
    return *this;
}

Dropdown& Dropdown::addItems(const QList<DropdownItem>& items) {
    for (const DropdownItem& item : items) {
        addItem(item);
    }
    return *this;
}

Dropdown& Dropdown::addSeparator() {
    DropdownItem separator;
    separator.separator = true;
    separator.text = "---";
    return addItem(separator);
}

Dropdown& Dropdown::removeItem(int index) {
    if (index >= 0 && index < items_.size()) {
        items_.removeAt(index);
        updateItemsList();
    }
    return *this;
}

Dropdown& Dropdown::removeItem(const QVariant& value) {
    for (int i = 0; i < items_.size(); ++i) {
        if (items_[i].value == value) {
            items_.removeAt(i);
            updateItemsList();
            break;
        }
    }
    return *this;
}

Dropdown& Dropdown::clearItems() {
    items_.clear();
    selected_values_.clear();
    updateItemsList();
    updatePlaceholder();
    return *this;
}

Dropdown& Dropdown::setSelectedIndex(int index) {
    if (index >= 0 && index < items_.size()) {
        selected_values_.clear();
        selected_values_.append(items_[index].value);
        updateSelection();
    }
    return *this;
}

Dropdown& Dropdown::setSelectedValue(const QVariant& value) {
    selected_values_.clear();
    selected_values_.append(value);
    updateSelection();
    return *this;
}

Dropdown& Dropdown::setSelectedValues(const QVariantList& values) {
    selected_values_ = values;
    updateSelection();
    return *this;
}

Dropdown& Dropdown::selectAll() {
    if (selection_mode_ != SelectionMode::Single) {
        selected_values_.clear();
        for (const auto& item : items_) {
            if (!item.separator && item.enabled) {
                selected_values_.append(item.value);
            }
        }
        updateSelection();
    }
    return *this;
}

Dropdown& Dropdown::clearSelection() {
    selected_values_.clear();
    updateSelection();
    updatePlaceholder();
    return *this;
}

Dropdown& Dropdown::onSelectionChanged(std::function<void(const QVariantList&)> callback) {
    selection_callback_ = callback;
    return *this;
}

Dropdown& Dropdown::onItemClicked(std::function<void(const DropdownItem&)> callback) {
    item_click_callback_ = callback;
    return *this;
}

Dropdown& Dropdown::onSearchChanged(std::function<void(const QString&)> callback) {
    search_callback_ = callback;
    return *this;
}

Dropdown& Dropdown::onDropdownOpened(std::function<void()> callback) {
    opened_callback_ = callback;
    return *this;
}

Dropdown& Dropdown::onDropdownClosed(std::function<void()> callback) {
    closed_callback_ = callback;
    return *this;
}

Dropdown& Dropdown::itemRenderer(std::function<QWidget*(const DropdownItem&)> renderer) {
    item_renderer_ = renderer;
    updateItemsList();
    return *this;
}

Dropdown& Dropdown::itemHeight(int height) {
    item_height_ = height;
    return *this;
}

Dropdown& Dropdown::itemSpacing(int spacing) {
    item_spacing_ = spacing;
    return *this;
}

Dropdown& Dropdown::dataSource(std::function<QList<DropdownItem>()> source) {
    data_source_ = source;
    if (source) {
        auto items = source();
        items_ = items;
        updateItemsList();
    }
    return *this;
}

Dropdown& Dropdown::asyncDataSource(std::function<void(std::function<void(const QList<DropdownItem>&)>)> source) {
    if (source) {
        loading(true);
        source([this](const QList<DropdownItem>& items) {
            items_ = items;
            loading(false);
            updateItemsList();
        });
    }
    return *this;
}

// Getters
QVariantList Dropdown::getSelectedValues() const {
    return selected_values_;
}

QList<int> Dropdown::getSelectedIndices() const {
    QList<int> indices;
    for (const QVariant& value : selected_values_) {
        for (int i = 0; i < items_.size(); ++i) {
            if (items_[i].value == value) {
                indices.append(i);
                break;
            }
        }
    }
    return indices;
}

QList<Dropdown::DropdownItem> Dropdown::getItems() const {
    return items_;
}

QString Dropdown::getSearchText() const {
    return current_search_;
}

bool Dropdown::isOpen() const {
    return dropdown_open_;
}

Dropdown::SelectionMode Dropdown::getSelectionMode() const {
    return selection_mode_;
}

Dropdown::SearchMode Dropdown::getSearchMode() const {
    return search_mode_;
}

// Static convenience methods
Dropdown* Dropdown::simple(const QStringList& items) {
    auto* dropdown = new Dropdown();
    return &dropdown->addItems(items);
}

Dropdown* Dropdown::searchable(const QStringList& items) {
    auto* dropdown = new Dropdown();
    return &dropdown->addItems(items).searchable(true);
}

Dropdown* Dropdown::multiSelect(const QStringList& items) {
    auto* dropdown = new Dropdown();
    return &dropdown->addItems(items).selectionMode(SelectionMode::Multiple);
}

// Private slots
void Dropdown::onToggleDropdown() {
    if (dropdown_open_) {
        hideDropdown();
    } else {
        showDropdown();
    }
}

void Dropdown::onSearchTextChanged(const QString& text) {
    current_search_ = text;
    filterItems(text);

    if (search_callback_) {
        search_callback_(text);
    }

    emit searchTextChanged(text);
}

void Dropdown::onItemSelectionChanged() {
    updateSelection();

    if (selection_callback_) {
        selection_callback_(selected_values_);
    }

    emit selectionChanged(selected_values_);

    // Close dropdown for single selection mode
    if (selection_mode_ == SelectionMode::Single && !selected_values_.isEmpty()) {
        hideDropdown();
    }
}

void Dropdown::onClearButtonClicked() {
    clearSelection();
}

void Dropdown::onSelectAllClicked() {
    selectAll();
}

// Private helper methods
void Dropdown::setupUI() {
    // Create main frame
    dropdown_frame_ = new QFrame();
    dropdown_frame_->setFrameStyle(QFrame::Box);
    dropdown_frame_->setStyleSheet(R"(
        QFrame {
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
        }
        QFrame:focus {
            border-color: #007bff;
        }
    )");

    // Create main layout
    main_layout_ = new QHBoxLayout(dropdown_frame_);
    main_layout_->setContentsMargins(8, 4, 4, 4);
    main_layout_->setSpacing(4);

    // Create search/display edit
    search_edit_ = new QLineEdit();
    search_edit_->setPlaceholderText(placeholder_text_);
    search_edit_->setReadOnly(!searchable_);
    main_layout_->addWidget(search_edit_);

    // Create clear button
    clear_button_ = new QPushButton("×");
    clear_button_->setFixedSize(20, 20);
    clear_button_->setVisible(false);
    clear_button_->setStyleSheet("QPushButton { border: none; font-weight: bold; }");
    main_layout_->addWidget(clear_button_);

    // Create dropdown button
    dropdown_button_ = new QPushButton("▼");
    dropdown_button_->setFixedSize(20, 20);
    dropdown_button_->setStyleSheet("QPushButton { border: none; }");
    main_layout_->addWidget(dropdown_button_);

    // Create popup frame
    dropdown_popup_ = new QFrame();
    dropdown_popup_->setWindowFlags(Qt::Popup | Qt::FramelessWindowHint);
    dropdown_popup_->setFrameStyle(QFrame::Box);
    dropdown_popup_->setMaximumHeight(max_height_);
    dropdown_popup_->setStyleSheet(R"(
        QFrame {
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
        }
    )");

    // Create popup layout
    popup_layout_ = new QVBoxLayout(dropdown_popup_);
    popup_layout_->setContentsMargins(0, 0, 0, 0);
    popup_layout_->setSpacing(0);

    // Create select all button for multi-select
    select_all_button_ = new QPushButton("Select All");
    select_all_button_->setVisible(false);
    popup_layout_->addWidget(select_all_button_);

    // Create scroll area
    scroll_area_ = new QScrollArea();
    scroll_area_->setWidgetResizable(true);
    scroll_area_->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    popup_layout_->addWidget(scroll_area_);

    // Create items list
    items_list_ = new QListWidget();
    items_list_->setFrameStyle(QFrame::NoFrame);
    scroll_area_->setWidget(items_list_);

    // Create loading label
    loading_label_ = new QLabel("Loading...");
    loading_label_->setAlignment(Qt::AlignCenter);
    loading_label_->setVisible(false);
    popup_layout_->addWidget(loading_label_);

    // Set widget reference for UIElement
    setWidget(dropdown_frame_);
}

void Dropdown::setupAnimations() {
    // Create opacity effect for popup
    popup_opacity_ = new QGraphicsOpacityEffect();
    dropdown_popup_->setGraphicsEffect(popup_opacity_);

    // Create popup animation
    popup_animation_ = new QPropertyAnimation(popup_opacity_, "opacity", this);
    popup_animation_->setDuration(200);
}

void Dropdown::connectSignals() {
    if (dropdown_button_) {
        connect(dropdown_button_, &QPushButton::clicked, this, &Dropdown::onToggleDropdown);
    }

    if (search_edit_) {
        connect(search_edit_, &QLineEdit::textChanged, this, &Dropdown::onSearchTextChanged);
    }

    if (clear_button_) {
        connect(clear_button_, &QPushButton::clicked, this, &Dropdown::onClearButtonClicked);
    }

    if (select_all_button_) {
        connect(select_all_button_, &QPushButton::clicked, this, &Dropdown::onSelectAllClicked);
    }

    if (items_list_) {
        connect(items_list_, &QListWidget::itemClicked, this, &Dropdown::onItemSelectionChanged);
    }
}

void Dropdown::updateItemsList() {
    if (!items_list_) return;

    items_list_->clear();

    const auto& items_to_show = current_search_.isEmpty() ? items_ : filtered_items_;

    for (int i = 0; i < items_to_show.size(); ++i) {
        const auto& item = items_to_show[i];

        if (item.separator) {
            // Add separator
            auto* separator_item = new QListWidgetItem();
            separator_item->setSizeHint(QSize(0, 1));
            separator_item->setFlags(Qt::NoItemFlags);
            items_list_->addItem(separator_item);
            continue;
        }

        auto* list_item = new QListWidgetItem();
        list_item->setText(item.text);
        list_item->setData(Qt::UserRole, item.value);
        list_item->setToolTip(item.tooltip);
        list_item->setIcon(item.icon);

        if (!item.enabled) {
            list_item->setFlags(Qt::NoItemFlags);
        } else if (selection_mode_ == SelectionMode::Multiple || selection_mode_ == SelectionMode::Checkboxes) {
            list_item->setFlags(Qt::ItemIsEnabled | Qt::ItemIsUserCheckable);
            list_item->setCheckState(selected_values_.contains(item.value) ? Qt::Checked : Qt::Unchecked);
        } else {
            list_item->setFlags(Qt::ItemIsEnabled | Qt::ItemIsSelectable);
            if (selected_values_.contains(item.value)) {
                list_item->setSelected(true);
            }
        }

        items_list_->addItem(list_item);
    }

    // Update select all button visibility
    if (select_all_button_) {
        select_all_button_->setVisible(selection_mode_ != SelectionMode::Single && !items_.isEmpty());
    }
}

void Dropdown::filterItems(const QString& search_text) {
    filtered_items_.clear();

    if (search_text.isEmpty()) {
        updateItemsList();
        return;
    }

    for (const auto& item : items_) {
        if (matchesSearch(item, search_text)) {
            filtered_items_.append(item);
        }
    }

    updateItemsList();
}

void Dropdown::updateSelection() {
    updatePlaceholder();

    if (clear_button_) {
        clear_button_->setVisible(clearable_ && !selected_values_.isEmpty());
    }
}

void Dropdown::updatePlaceholder() {
    if (!search_edit_) return;

    QString display_text = getDisplayText();

    if (searchable_ && dropdown_open_) {
        search_edit_->setPlaceholderText(display_text.isEmpty() ? placeholder_text_ : display_text);
        search_edit_->clear();
    } else {
        search_edit_->setText(display_text);
        search_edit_->setPlaceholderText(display_text.isEmpty() ? placeholder_text_ : "");
    }
}

void Dropdown::showDropdown() {
    if (!dropdown_popup_ || dropdown_open_) return;

    dropdown_open_ = true;
    positionPopup();

    // Show popup with animation
    popup_opacity_->setOpacity(0.0);
    dropdown_popup_->show();

    popup_animation_->setStartValue(0.0);
    popup_animation_->setEndValue(1.0);
    popup_animation_->start();

    // Update search edit for searchable mode
    if (searchable_) {
        search_edit_->setReadOnly(false);
        search_edit_->setFocus();
        updatePlaceholder();
    }

    if (opened_callback_) {
        opened_callback_();
    }

    emit dropdownOpened();

    qDebug() << "📋 Dropdown opened";
}

void Dropdown::hideDropdown() {
    if (!dropdown_popup_ || !dropdown_open_) return;

    dropdown_open_ = false;

    // Hide popup with animation
    popup_animation_->setStartValue(1.0);
    popup_animation_->setEndValue(0.0);

    connect(popup_animation_, &QPropertyAnimation::finished, this, [this]() {
        dropdown_popup_->hide();
        disconnect(popup_animation_, &QPropertyAnimation::finished, this, nullptr);
    });

    popup_animation_->start();

    // Update search edit
    if (searchable_) {
        search_edit_->setReadOnly(true);
        current_search_.clear();
        filterItems("");
        updatePlaceholder();
    }

    if (closed_callback_) {
        closed_callback_();
    }

    emit dropdownClosed();

    qDebug() << "📋 Dropdown closed";
}

void Dropdown::positionPopup() {
    if (!dropdown_popup_ || !dropdown_frame_) return;

    QPoint global_pos = dropdown_frame_->mapToGlobal(QPoint(0, dropdown_frame_->height()));
    dropdown_popup_->move(global_pos);
    dropdown_popup_->setFixedWidth(dropdown_frame_->width());
}

QWidget* Dropdown::createItemWidget(const DropdownItem& item, int index) {
    Q_UNUSED(index);

    if (item_renderer_) {
        return item_renderer_(item);
    }

    // Default item widget
    auto* widget = new QWidget();
    auto* layout = new QHBoxLayout(widget);
    layout->setContentsMargins(8, 4, 8, 4);

    if (!item.icon.isNull()) {
        auto* icon_label = new QLabel();
        icon_label->setPixmap(item.icon.pixmap(16, 16));
        layout->addWidget(icon_label);
    }

    auto* text_label = new QLabel(item.text);
    layout->addWidget(text_label);

    layout->addStretch();

    widget->setFixedHeight(item_height_);

    return widget;
}

bool Dropdown::matchesSearch(const DropdownItem& item, const QString& search) const {
    if (item.separator) return false;

    QString item_text = item.text.toLower();
    QString search_text = search.toLower();

    switch (search_mode_) {
        case SearchMode::None:
            return true;
        case SearchMode::StartsWith:
            return item_text.startsWith(search_text);
        case SearchMode::Contains:
            return item_text.contains(search_text);
        case SearchMode::Fuzzy:
            // Simple fuzzy matching - check if all characters appear in order
            int search_index = 0;
            for (int i = 0; i < item_text.length() && search_index < search_text.length(); ++i) {
                if (item_text[i] == search_text[search_index]) {
                    search_index++;
                }
            }
            return search_index == search_text.length();
    }

    return false;
}

QString Dropdown::getDisplayText() const {
    if (selected_values_.isEmpty()) {
        return "";
    }

    if (selection_mode_ == SelectionMode::Single) {
        // Find the selected item text
        for (const auto& item : items_) {
            if (item.value == selected_values_.first()) {
                return item.text;
            }
        }
        return selected_values_.first().toString();
    } else {
        // Multiple selection - show count or first few items
        if (selected_values_.size() == 1) {
            for (const auto& item : items_) {
                if (item.value == selected_values_.first()) {
                    return item.text;
                }
            }
            return selected_values_.first().toString();
        } else {
            return QString("%1 items selected").arg(selected_values_.size());
        }
    }
}

void Dropdown::applyStyles() {
    // Apply modern styling to components
    if (dropdown_frame_) {
        dropdown_frame_->setStyleSheet(R"(
            QFrame {
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
                padding: 2px;
            }
            QFrame:hover {
                border-color: #007bff;
            }
            QFrame:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }
        )");
    }
}

} // namespace DeclarativeUI::Components
