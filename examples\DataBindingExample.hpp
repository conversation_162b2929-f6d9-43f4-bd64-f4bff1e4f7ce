#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QLineEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QDateEdit>
#include <QTextEdit>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QProgressBar>
#include <QSlider>
#include <QListWidget>
#include <QSplitter>
#include <memory>

#include "../src/DataBinding/DataBindingEngine.hpp"
#include "../src/DataBinding/BindingUtils.hpp"

/**
 * @brief Comprehensive example demonstrating data binding and validation features
 */
class DataBindingExample : public QWidget {
    Q_OBJECT

public:
    explicit DataBindingExample(QWidget* parent = nullptr);
    ~DataBindingExample() override = default;

private slots:
    void onValidateForm();
    void onClearForm();
    void onLoadSampleData();
    void onSaveData();
    void onFieldValidationChanged(const QString& field, bool is_valid);
    void onFormValidationChanged(bool is_valid);
    void onBindingError(QWidget* widget, const QString& error);

private:
    void setupUI();
    void setupDataBinding();
    void setupValidation();
    void setupFormBinding();
    void updateValidationStatus();
    void showValidationErrors();
    
    // UI Components
    QVBoxLayout* main_layout_;
    QSplitter* main_splitter_;
    
    // Form section
    QGroupBox* form_group_;
    QFormLayout* form_layout_;
    QLineEdit* name_edit_;
    QLineEdit* email_edit_;
    QLineEdit* phone_edit_;
    QSpinBox* age_spin_;
    QDoubleSpinBox* salary_spin_;
    QComboBox* department_combo_;
    QCheckBox* active_check_;
    QDateEdit* start_date_edit_;
    QTextEdit* notes_edit_;
    
    // Validation controls
    QGroupBox* validation_group_;
    QVBoxLayout* validation_layout_;
    QPushButton* validate_button_;
    QPushButton* clear_button_;
    QPushButton* load_sample_button_;
    QPushButton* save_button_;
    QLabel* validation_status_label_;
    QListWidget* errors_list_;
    
    // Data binding controls
    QGroupBox* binding_group_;
    QVBoxLayout* binding_layout_;
    QLabel* bound_widgets_label_;
    QListWidget* bound_widgets_list_;
    QProgressBar* form_completeness_bar_;
    QSlider* salary_slider_;
    QLabel* salary_display_label_;
    
    // Data and binding
    std::shared_ptr<DeclarativeUI::DataBinding::FormData> form_data_;
    std::shared_ptr<DeclarativeUI::DataBinding::ValidationEngine> validation_engine_;
    DeclarativeUI::DataBinding::DataBindingEngine* binding_engine_;
    
    // Validation tracking
    std::unordered_map<QString, bool> field_validation_status_;
};

/**
 * @brief Advanced data binding example with custom transformers
 */
class AdvancedBindingExample : public QWidget {
    Q_OBJECT

public:
    explicit AdvancedBindingExample(QWidget* parent = nullptr);
    ~AdvancedBindingExample() override = default;

private slots:
    void onTemperatureChanged();
    void onCurrencyChanged();
    void onDateFormatChanged();

private:
    void setupUI();
    void setupAdvancedBinding();
    void setupCustomTransformers();
    
    // Temperature conversion example
    QGroupBox* temperature_group_;
    QHBoxLayout* temperature_layout_;
    QDoubleSpinBox* celsius_spin_;
    QDoubleSpinBox* fahrenheit_spin_;
    QDoubleSpinBox* kelvin_spin_;
    QLabel* temperature_status_;
    
    // Currency conversion example
    QGroupBox* currency_group_;
    QFormLayout* currency_layout_;
    QDoubleSpinBox* usd_spin_;
    QDoubleSpinBox* eur_spin_;
    QDoubleSpinBox* gbp_spin_;
    QComboBox* base_currency_combo_;
    
    // Date format example
    QGroupBox* date_group_;
    QVBoxLayout* date_layout_;
    QDateEdit* date_edit_;
    QLineEdit* iso_format_edit_;
    QLineEdit* us_format_edit_;
    QLineEdit* eu_format_edit_;
    QLineEdit* relative_format_edit_;
    
    // Binding engine
    DeclarativeUI::DataBinding::DataBindingEngine* binding_engine_;
};

/**
 * @brief Real-time validation example
 */
class RealTimeValidationExample : public QWidget {
    Q_OBJECT

public:
    explicit RealTimeValidationExample(QWidget* parent = nullptr);
    ~RealTimeValidationExample() override = default;

private slots:
    void onPasswordStrengthChanged();
    void onConfirmPasswordChanged();
    void onUsernameAvailabilityCheck();

private:
    void setupUI();
    void setupRealTimeValidation();
    void updatePasswordStrength(const QString& password);
    void updatePasswordMatch();
    void simulateUsernameCheck(const QString& username);
    
    // Password strength example
    QGroupBox* password_group_;
    QFormLayout* password_layout_;
    QLineEdit* username_edit_;
    QLabel* username_status_;
    QLineEdit* password_edit_;
    QProgressBar* strength_bar_;
    QLabel* strength_label_;
    QLineEdit* confirm_password_edit_;
    QLabel* match_label_;
    
    // Real-time feedback
    QGroupBox* feedback_group_;
    QVBoxLayout* feedback_layout_;
    QLabel* requirements_label_;
    QListWidget* requirements_list_;
    
    // Validation components
    std::shared_ptr<DeclarativeUI::DataBinding::ValidationEngine> validation_engine_;
    DeclarativeUI::DataBinding::DataBindingEngine* binding_engine_;
};

/**
 * @brief Form wizard example with step-by-step validation
 */
class FormWizardExample : public QWidget {
    Q_OBJECT

public:
    explicit FormWizardExample(QWidget* parent = nullptr);
    ~FormWizardExample() override = default;

private slots:
    void onNextStep();
    void onPreviousStep();
    void onStepChanged(int step);
    void onFinishWizard();

private:
    void setupUI();
    void setupWizardSteps();
    void setupStepValidation();
    void updateStepButtons();
    bool validateCurrentStep();
    void showStep(int step);
    
    // Wizard structure
    QVBoxLayout* main_layout_;
    QLabel* step_title_;
    QWidget* step_container_;
    QHBoxLayout* button_layout_;
    QPushButton* previous_button_;
    QPushButton* next_button_;
    QPushButton* finish_button_;
    QProgressBar* progress_bar_;
    
    // Step widgets
    std::vector<QWidget*> step_widgets_;
    std::vector<QString> step_titles_;
    std::vector<std::shared_ptr<DeclarativeUI::DataBinding::FormData>> step_form_data_;
    
    int current_step_;
    DeclarativeUI::DataBinding::DataBindingEngine* binding_engine_;
};

#endif // DATABINDINGEXAMPLE_HPP
