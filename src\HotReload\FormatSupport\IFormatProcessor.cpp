#include "IFormatProcessor.hpp"
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QCryptographicHash>
#include <QDebug>

namespace DeclarativeUI::HotReload::FormatSupport {

QString IFormatProcessor::getFileExtension(const QString& file_path) const {
    QFileInfo file_info(file_path);
    return file_info.suffix().toLower();
}

QString IFormatProcessor::readFileContent(const QString& file_path) const {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for reading:" << file_path;
        return QString();
    }
    
    QTextStream stream(&file);
    stream.setEncoding(QStringConverter::Utf8);
    return stream.readAll();
}

bool IFormatProcessor::writeFileContent(const QString& file_path, const QString& content) const {
    QFile file(file_path);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for writing:" << file_path;
        return false;
    }
    
    QTextStream stream(&file);
    stream.setEncoding(QStringConverter::Utf8);
    stream << content;
    return true;
}

QJsonObject IFormatProcessor::createMetadata(const QString& file_path) const {
    QFileInfo file_info(file_path);
    QJsonObject metadata;
    
    metadata["file_path"] = file_path;
    metadata["file_name"] = file_info.fileName();
    metadata["file_extension"] = file_info.suffix();
    metadata["file_size"] = file_info.size();
    metadata["last_modified"] = file_info.lastModified().toString(Qt::ISODate);
    metadata["processor"] = getFormatName();
    metadata["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return metadata;
}

QString IFormatProcessor::getCacheKey(const QString& file_path, const ProcessingConfig& config) const {
    QFileInfo file_info(file_path);
    
    // Create a hash based on file path, modification time, and config
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(file_path.toUtf8());
    hash.addData(QString::number(file_info.lastModified().toMSecsSinceEpoch()).toUtf8());
    hash.addData(QString::number(config.enable_transpilation).toUtf8());
    hash.addData(QString::number(config.enable_minification).toUtf8());
    hash.addData(config.output_format.toUtf8());
    
    return QString(hash.result().toHex());
}

bool IFormatProcessor::shouldInvalidateCache(const QString& file_path, const QJsonObject& cached_metadata) const {
    QFileInfo file_info(file_path);
    
    // Check if file has been modified since cache was created
    if (cached_metadata.contains("last_modified")) {
        QDateTime cached_time = QDateTime::fromString(cached_metadata["last_modified"].toString(), Qt::ISODate);
        if (file_info.lastModified() > cached_time) {
            return true;
        }
    }
    
    // Check if file size has changed
    if (cached_metadata.contains("file_size")) {
        qint64 cached_size = cached_metadata["file_size"].toInteger();
        if (file_info.size() != cached_size) {
            return true;
        }
    }
    
    return false;
}

void IFormatProcessor::startPerformanceMeasurement() {
    performance_timer_.start();
}

qint64 IFormatProcessor::endPerformanceMeasurement() {
    return performance_timer_.elapsed();
}

} // namespace DeclarativeUI::HotReload::FormatSupport
