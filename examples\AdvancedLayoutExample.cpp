#include "AdvancedLayoutExample.hpp"
#include <QApplication>
#include <QMenuBar>
#include <QStatusBar>
#include <QSplitter>
#include <QScrollArea>
#include <QFileDialog>
#include <QMessageBox>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QPainter>
#include <QMouseEvent>
#include <QElapsedTimer>
#include <QScreen>
#include <QInputDevice>
#include <QDebug>

AdvancedLayoutExample::AdvancedLayoutExample(QWidget* parent)
    : QMainWindow(parent)
    , layout_engine_(std::make_unique<DeclarativeUI::Layout::AdvancedLayoutEngine>(this))
    , demo_tabs_(nullptr)
    , status_widget_(nullptr)
    , status_label_(nullptr)
    , update_timer_(new QTimer(this))
    , layout_update_count_(0)
    , last_update_time_(0)
{
    setWindowTitle("Advanced Layout Engine Demo - DeclarativeUI Framework");
    setMinimumSize(1200, 800);
    resize(1600, 1000);
    
    setupUI();
    setupControlPanels();
    setupStatusBar();
    
    // Connect layout engine signals
    connect(layout_engine_.get(), &DeclarativeUI::Layout::AdvancedLayoutEngine::breakpointChanged,
            this, &AdvancedLayoutExample::onBreakpointChanged);
    connect(layout_engine_.get(), &DeclarativeUI::Layout::AdvancedLayoutEngine::layoutUpdated,
            this, &AdvancedLayoutExample::onLayoutUpdated);
    
    // Setup update timer for performance monitoring
    update_timer_->setInterval(100);  // Update every 100ms
    connect(update_timer_, &QTimer::timeout, this, &AdvancedLayoutExample::updateLayoutStatus);
    update_timer_->start();
    
    // Enable debug mode by default
    layout_engine_->enableDebugMode(true);
    
    qDebug() << "🏗️ Advanced Layout Example initialized";
}

AdvancedLayoutExample::~AdvancedLayoutExample() {
    qDebug() << "🏗️ Advanced Layout Example destroyed";
}

void AdvancedLayoutExample::setupUI() {
    auto* central_widget = new QWidget(this);
    setCentralWidget(central_widget);
    
    auto* main_layout = new QHBoxLayout(central_widget);
    main_layout->setSpacing(10);
    main_layout->setContentsMargins(10, 10, 10, 10);
    
    // Create main splitter
    auto* main_splitter = new QSplitter(Qt::Horizontal, this);
    main_layout->addWidget(main_splitter);
    
    // Create demo tabs
    demo_tabs_ = new QTabWidget();
    demo_tabs_->setMinimumWidth(800);
    main_splitter->addWidget(demo_tabs_);
    
    // Setup individual layout demos
    setupGridLayoutDemo();
    setupFlexLayoutDemo();
    setupResponsiveLayoutDemo();
    setupConstraintLayoutDemo();
    
    // Create control panel container
    auto* control_container = new QWidget();
    control_container->setMaximumWidth(400);
    control_container->setMinimumWidth(350);
    main_splitter->addWidget(control_container);
    
    auto* control_layout = new QVBoxLayout(control_container);
    control_layout->setSpacing(10);
    control_layout->setContentsMargins(10, 10, 10, 10);
    
    // Add control panels (will be created in setupControlPanels)
    
    // Set splitter proportions
    main_splitter->setSizes({1000, 400});
}

void AdvancedLayoutExample::setupGridLayoutDemo() {
    // Create grid demo container
    grid_demo_container_ = new QWidget();
    grid_demo_container_->setObjectName("GridDemoContainer");
    grid_demo_container_->setMinimumSize(600, 400);
    
    // Create scroll area for the demo
    auto* scroll_area = new QScrollArea();
    scroll_area->setWidget(grid_demo_container_);
    scroll_area->setWidgetResizable(true);
    
    demo_tabs_->addTab(scroll_area, "🔲 CSS Grid Layout");
    
    // Create grid layout
    grid_layout_ = layout_engine_->createGridLayout(grid_demo_container_);
    
    // Setup grid with 3x3 template
    std::vector<DeclarativeUI::Layout::GridLayout::GridTrack> row_tracks = {
        {DeclarativeUI::Layout::GridLayout::GridSizing::Fixed, 100.0},
        {DeclarativeUI::Layout::GridLayout::GridSizing::Fraction, 1.0},
        {DeclarativeUI::Layout::GridLayout::GridSizing::Fixed, 80.0}
    };
    
    std::vector<DeclarativeUI::Layout::GridLayout::GridTrack> col_tracks = {
        {DeclarativeUI::Layout::GridLayout::GridSizing::Fixed, 150.0},
        {DeclarativeUI::Layout::GridLayout::GridSizing::Fraction, 2.0},
        {DeclarativeUI::Layout::GridLayout::GridSizing::Fraction, 1.0}
    };
    
    grid_layout_->templateRows(row_tracks)
                 .templateColumns(col_tracks)
                 .gap(16)
                 .justifyContent(DeclarativeUI::Layout::GridLayout::GridAlignment::Start)
                 .alignContent(DeclarativeUI::Layout::GridLayout::GridAlignment::Start);
    
    createGridDemoContent();
}

void AdvancedLayoutExample::setupFlexLayoutDemo() {
    // Create flex demo container
    flex_demo_container_ = new QWidget();
    flex_demo_container_->setObjectName("FlexDemoContainer");
    flex_demo_container_->setMinimumSize(600, 400);
    
    auto* scroll_area = new QScrollArea();
    scroll_area->setWidget(flex_demo_container_);
    scroll_area->setWidgetResizable(true);
    
    demo_tabs_->addTab(scroll_area, "📦 Flexbox Layout");
    
    // Create flex layout
    flex_layout_ = layout_engine_->createFlexLayout(flex_demo_container_);
    
    flex_layout_->direction(DeclarativeUI::Layout::FlexLayout::FlexDirection::Row)
                 .wrap(DeclarativeUI::Layout::FlexLayout::FlexWrap::Wrap)
                 .justifyContent(DeclarativeUI::Layout::FlexLayout::FlexAlignment::Start)
                 .alignItems(DeclarativeUI::Layout::FlexLayout::FlexAlignment::Stretch)
                 .gap(12);
    
    createFlexDemoContent();
}

void AdvancedLayoutExample::setupResponsiveLayoutDemo() {
    // Create responsive demo container
    responsive_demo_container_ = new QWidget();
    responsive_demo_container_->setObjectName("ResponsiveDemoContainer");
    responsive_demo_container_->setMinimumSize(600, 400);
    
    auto* scroll_area = new QScrollArea();
    scroll_area->setWidget(responsive_demo_container_);
    scroll_area->setWidgetResizable(true);
    
    demo_tabs_->addTab(scroll_area, "📱 Responsive Layout");
    
    // Create responsive layout
    responsive_layout_ = layout_engine_->createResponsiveLayout(responsive_demo_container_);
    
    // Setup responsive properties
    responsive_layout_->responsiveSpacing({
        {"xs", 8},
        {"sm", 12},
        {"md", 16},
        {"lg", 20},
        {"xl", 24}
    });
    
    responsive_layout_->responsiveMargins({
        {"xs", QMargins(8, 8, 8, 8)},
        {"sm", QMargins(12, 12, 12, 12)},
        {"md", QMargins(16, 16, 16, 16)},
        {"lg", QMargins(20, 20, 20, 20)},
        {"xl", QMargins(24, 24, 24, 24)}
    });
    
    createResponsiveDemoContent();
}

void AdvancedLayoutExample::setupConstraintLayoutDemo() {
    // Create constraint demo container
    constraint_demo_container_ = new QWidget();
    constraint_demo_container_->setObjectName("ConstraintDemoContainer");
    constraint_demo_container_->setMinimumSize(600, 400);
    
    auto* scroll_area = new QScrollArea();
    scroll_area->setWidget(constraint_demo_container_);
    scroll_area->setWidgetResizable(true);
    
    demo_tabs_->addTab(scroll_area, "🔗 Constraint Layout");
    
    // Create constraint layout
    constraint_layout_ = layout_engine_->createConstraintLayout(constraint_demo_container_);
    
    createConstraintDemoContent();
}

void AdvancedLayoutExample::createGridDemoContent() {
    grid_demo_widgets_.clear();
    
    // Create demo widgets for grid
    auto* header = new LayoutDemoWidget("Header", QColor(52, 152, 219));
    auto* sidebar = new LayoutDemoWidget("Sidebar", QColor(155, 89, 182));
    auto* main_content = new LayoutDemoWidget("Main Content", QColor(46, 204, 113));
    auto* aside = new LayoutDemoWidget("Aside", QColor(241, 196, 15));
    auto* footer = new LayoutDemoWidget("Footer", QColor(231, 76, 60));
    
    grid_demo_widgets_ = {header, sidebar, main_content, aside, footer};
    
    // Add widgets to grid layout
    grid_layout_->addItem(header, 1, 1, 1, 3)      // Header spans all columns
                 .addItem(sidebar, 2, 1)            // Sidebar in first column
                 .addItem(main_content, 2, 2)       // Main content in second column
                 .addItem(aside, 2, 3)              // Aside in third column
                 .addItem(footer, 3, 1, 1, 3);      // Footer spans all columns
    
    // Define named areas
    grid_layout_->defineArea("header", 1, 2, 1, 4)
                 .defineArea("sidebar", 2, 3, 1, 2)
                 .defineArea("main", 2, 3, 2, 3)
                 .defineArea("aside", 2, 3, 3, 4)
                 .defineArea("footer", 3, 4, 1, 4);
    
    grid_layout_->updateLayout();
}

void AdvancedLayoutExample::createFlexDemoContent() {
    flex_demo_widgets_.clear();
    
    // Create demo widgets for flex
    auto* item1 = new LayoutDemoWidget("Flex Item 1", QColor(52, 152, 219));
    auto* item2 = new LayoutDemoWidget("Flex Item 2", QColor(155, 89, 182));
    auto* item3 = new LayoutDemoWidget("Flex Item 3", QColor(46, 204, 113));
    auto* item4 = new LayoutDemoWidget("Flex Item 4", QColor(241, 196, 15));
    auto* item5 = new LayoutDemoWidget("Flex Item 5", QColor(231, 76, 60));
    auto* item6 = new LayoutDemoWidget("Flex Item 6", QColor(230, 126, 34));
    
    flex_demo_widgets_ = {item1, item2, item3, item4, item5, item6};
    
    // Add widgets to flex layout with different flex properties
    flex_layout_->addItem(item1, 1.0, 1.0, -1)    // flex: 1 1 auto
                 .addItem(item2, 2.0, 1.0, -1)    // flex: 2 1 auto
                 .addItem(item3, 1.0, 1.0, 200)   // flex: 1 1 200px
                 .addItem(item4, 0.0, 1.0, 150)   // flex: 0 1 150px
                 .addItem(item5, 1.0, 0.0, -1)    // flex: 1 0 auto
                 .addItem(item6, 0.0, 0.0, 100);  // flex: 0 0 100px
    
    flex_layout_->updateLayout();
}

void AdvancedLayoutExample::createResponsiveDemoContent() {
    responsive_demo_widgets_.clear();
    
    // Create demo widgets for responsive layout
    auto* nav = new LayoutDemoWidget("Navigation", QColor(52, 152, 219));
    auto* hero = new LayoutDemoWidget("Hero Section", QColor(155, 89, 182));
    auto* content1 = new LayoutDemoWidget("Content 1", QColor(46, 204, 113));
    auto* content2 = new LayoutDemoWidget("Content 2", QColor(241, 196, 15));
    auto* content3 = new LayoutDemoWidget("Content 3", QColor(231, 76, 60));
    auto* footer = new LayoutDemoWidget("Footer", QColor(149, 165, 166));
    
    responsive_demo_widgets_ = {nav, hero, content1, content2, content3, footer};
    
    // Add widgets to container
    for (auto* widget : responsive_demo_widgets_) {
        widget->setParent(responsive_demo_container_);
        widget->show();
    }
    
    // Setup responsive behavior for different breakpoints
    responsive_layout_->setLayoutForBreakpoint("xs", [this]() {
        // Mobile: single column layout
        int y = 10;
        for (auto* widget : responsive_demo_widgets_) {
            widget->setGeometry(10, y, responsive_demo_container_->width() - 20, 80);
            y += 90;
        }
    });
    
    responsive_layout_->setLayoutForBreakpoint("md", [this]() {
        // Tablet: two column layout
        int container_width = responsive_demo_container_->width();
        int col_width = (container_width - 30) / 2;
        
        responsive_demo_widgets_[0]->setGeometry(10, 10, container_width - 20, 60);  // Nav full width
        responsive_demo_widgets_[1]->setGeometry(10, 80, container_width - 20, 100); // Hero full width
        
        // Content in two columns
        responsive_demo_widgets_[2]->setGeometry(10, 190, col_width, 120);
        responsive_demo_widgets_[3]->setGeometry(20 + col_width, 190, col_width, 120);
        responsive_demo_widgets_[4]->setGeometry(10, 320, container_width - 20, 80);
        responsive_demo_widgets_[5]->setGeometry(10, 410, container_width - 20, 60);
    });
    
    responsive_layout_->setLayoutForBreakpoint("lg", [this]() {
        // Desktop: three column layout
        int container_width = responsive_demo_container_->width();
        int col_width = (container_width - 40) / 3;
        
        responsive_demo_widgets_[0]->setGeometry(10, 10, container_width - 20, 60);  // Nav full width
        responsive_demo_widgets_[1]->setGeometry(10, 80, container_width - 20, 120); // Hero full width
        
        // Content in three columns
        responsive_demo_widgets_[2]->setGeometry(10, 210, col_width, 150);
        responsive_demo_widgets_[3]->setGeometry(20 + col_width, 210, col_width, 150);
        responsive_demo_widgets_[4]->setGeometry(30 + 2 * col_width, 210, col_width, 150);
        responsive_demo_widgets_[5]->setGeometry(10, 370, container_width - 20, 60);
    });
    
    // Apply initial layout
    responsive_layout_->updateForCurrentBreakpoint(responsive_demo_container_->width());
}

void AdvancedLayoutExample::createConstraintDemoContent() {
    constraint_demo_widgets_.clear();

    // Create demo widgets for constraint layout
    auto* red_box = new LayoutDemoWidget("Red Box", QColor(231, 76, 60));
    auto* blue_box = new LayoutDemoWidget("Blue Box", QColor(52, 152, 219));
    auto* green_box = new LayoutDemoWidget("Green Box", QColor(46, 204, 113));
    auto* yellow_box = new LayoutDemoWidget("Yellow Box", QColor(241, 196, 15));

    constraint_demo_widgets_ = {red_box, blue_box, green_box, yellow_box};

    // Add widgets to container
    for (auto* widget : constraint_demo_widgets_) {
        widget->setParent(constraint_demo_container_);
        widget->show();
    }

    // Setup initial constraints
    constraint_layout_->centerHorizontally(red_box)
                      .centerVertically(red_box)
                      .setWidth(red_box, 100)
                      .setHeight(red_box, 100);

    constraint_layout_->alignLeft(blue_box, red_box, -120)
                      .alignTop(blue_box, red_box)
                      .setWidth(blue_box, 80)
                      .setHeight(blue_box, 80);

    constraint_layout_->alignRight(green_box, red_box, 120)
                      .alignTop(green_box, red_box)
                      .setWidth(green_box, 80)
                      .setHeight(green_box, 80);

    constraint_layout_->centerHorizontally(yellow_box, red_box)
                      .alignTop(yellow_box, red_box, 120)
                      .setWidth(yellow_box, 120)
                      .setHeight(yellow_box, 60);

    constraint_layout_->updateLayout();
}

void AdvancedLayoutExample::setupControlPanels() {
    auto* control_container = centralWidget()->findChild<QWidget*>();
    auto* control_layout = qobject_cast<QVBoxLayout*>(control_container->layout());

    if (!control_layout) return;

    // **Grid Layout Controls**
    grid_controls_ = new QGroupBox("🔲 Grid Layout Controls");
    control_layout->addWidget(grid_controls_);

    auto* grid_layout = new QVBoxLayout(grid_controls_);

    // Row gap control
    auto* row_gap_layout = new QHBoxLayout();
    row_gap_layout->addWidget(new QLabel("Row Gap:"));
    grid_row_gap_slider_ = new QSlider(Qt::Horizontal);
    grid_row_gap_slider_->setRange(0, 50);
    grid_row_gap_slider_->setValue(16);
    row_gap_layout->addWidget(grid_row_gap_slider_);
    auto* row_gap_label = new QLabel("16px");
    row_gap_layout->addWidget(row_gap_label);
    grid_layout->addLayout(row_gap_layout);

    connect(grid_row_gap_slider_, &QSlider::valueChanged, [this, row_gap_label](int value) {
        row_gap_label->setText(QString("%1px").arg(value));
        onGridLayoutChanged();
    });

    // Column gap control
    auto* col_gap_layout = new QHBoxLayout();
    col_gap_layout->addWidget(new QLabel("Col Gap:"));
    grid_col_gap_slider_ = new QSlider(Qt::Horizontal);
    grid_col_gap_slider_->setRange(0, 50);
    grid_col_gap_slider_->setValue(16);
    col_gap_layout->addWidget(grid_col_gap_slider_);
    auto* col_gap_label = new QLabel("16px");
    col_gap_layout->addWidget(col_gap_label);
    grid_layout->addLayout(col_gap_layout);

    connect(grid_col_gap_slider_, &QSlider::valueChanged, [this, col_gap_label](int value) {
        col_gap_label->setText(QString("%1px").arg(value));
        onGridLayoutChanged();
    });

    // Justify content
    grid_justify_content_ = new QComboBox();
    grid_justify_content_->addItems({"Start", "End", "Center", "Stretch", "Space Between", "Space Around", "Space Evenly"});
    grid_layout->addWidget(new QLabel("Justify Content:"));
    grid_layout->addWidget(grid_justify_content_);
    connect(grid_justify_content_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AdvancedLayoutExample::onGridLayoutChanged);

    // Align content
    grid_align_content_ = new QComboBox();
    grid_align_content_->addItems({"Start", "End", "Center", "Stretch", "Space Between", "Space Around", "Space Evenly"});
    grid_layout->addWidget(new QLabel("Align Content:"));
    grid_layout->addWidget(grid_align_content_);
    connect(grid_align_content_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AdvancedLayoutExample::onGridLayoutChanged);

    // **Flex Layout Controls**
    flex_controls_ = new QGroupBox("📦 Flex Layout Controls");
    control_layout->addWidget(flex_controls_);

    auto* flex_layout_controls = new QVBoxLayout(flex_controls_);

    // Flex direction
    flex_direction_ = new QComboBox();
    flex_direction_->addItems({"Row", "Row Reverse", "Column", "Column Reverse"});
    flex_layout_controls->addWidget(new QLabel("Direction:"));
    flex_layout_controls->addWidget(flex_direction_);
    connect(flex_direction_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AdvancedLayoutExample::onFlexLayoutChanged);

    // Flex wrap
    flex_wrap_ = new QComboBox();
    flex_wrap_->addItems({"No Wrap", "Wrap", "Wrap Reverse"});
    flex_wrap_->setCurrentIndex(1);  // Default to Wrap
    flex_layout_controls->addWidget(new QLabel("Wrap:"));
    flex_layout_controls->addWidget(flex_wrap_);
    connect(flex_wrap_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AdvancedLayoutExample::onFlexLayoutChanged);

    // Justify content
    flex_justify_content_ = new QComboBox();
    flex_justify_content_->addItems({"Start", "End", "Center", "Space Between", "Space Around", "Space Evenly"});
    flex_layout_controls->addWidget(new QLabel("Justify Content:"));
    flex_layout_controls->addWidget(flex_justify_content_);
    connect(flex_justify_content_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AdvancedLayoutExample::onFlexLayoutChanged);

    // Gap control
    auto* flex_gap_layout = new QHBoxLayout();
    flex_gap_layout->addWidget(new QLabel("Gap:"));
    flex_gap_slider_ = new QSlider(Qt::Horizontal);
    flex_gap_slider_->setRange(0, 50);
    flex_gap_slider_->setValue(12);
    flex_gap_layout->addWidget(flex_gap_slider_);
    auto* flex_gap_label = new QLabel("12px");
    flex_gap_layout->addWidget(flex_gap_label);
    flex_layout_controls->addLayout(flex_gap_layout);

    connect(flex_gap_slider_, &QSlider::valueChanged, [this, flex_gap_label](int value) {
        flex_gap_label->setText(QString("%1px").arg(value));
        onFlexLayoutChanged();
    });

    // **Responsive Layout Controls**
    responsive_controls_ = new QGroupBox("📱 Responsive Layout Controls");
    control_layout->addWidget(responsive_controls_);

    auto* responsive_layout_controls = new QVBoxLayout(responsive_controls_);

    // Current breakpoint display
    current_breakpoint_label_ = new QLabel("Current: md");
    current_breakpoint_label_->setStyleSheet("font-weight: bold; color: #2980b9;");
    responsive_layout_controls->addWidget(current_breakpoint_label_);

    // Container width slider
    auto* width_layout = new QHBoxLayout();
    width_layout->addWidget(new QLabel("Container Width:"));
    container_width_slider_ = new QSlider(Qt::Horizontal);
    container_width_slider_->setRange(300, 1200);
    container_width_slider_->setValue(800);
    width_layout->addWidget(container_width_slider_);
    auto* width_label = new QLabel("800px");
    width_layout->addWidget(width_label);
    responsive_layout_controls->addLayout(width_layout);

    connect(container_width_slider_, &QSlider::valueChanged, [this, width_label](int value) {
        width_label->setText(QString("%1px").arg(value));
        if (responsive_demo_container_) {
            responsive_demo_container_->resize(value, responsive_demo_container_->height());
            onResponsiveLayoutChanged();
        }
    });

    // Auto responsive checkbox
    auto_responsive_ = new QCheckBox("Auto Responsive");
    auto_responsive_->setChecked(true);
    responsive_layout_controls->addWidget(auto_responsive_);
    connect(auto_responsive_, &QCheckBox::toggled, this, &AdvancedLayoutExample::onResponsiveLayoutChanged);

    // **Global Controls**
    global_controls_ = new QGroupBox("🌐 Global Controls");
    control_layout->addWidget(global_controls_);

    auto* global_layout = new QVBoxLayout(global_controls_);

    // Control buttons
    auto* button_layout = new QHBoxLayout();
    reset_button_ = new QPushButton("Reset All");
    debug_button_ = new QPushButton("Debug Mode");
    debug_button_->setCheckable(true);
    debug_button_->setChecked(true);
    button_layout->addWidget(reset_button_);
    button_layout->addWidget(debug_button_);
    global_layout->addLayout(button_layout);

    connect(reset_button_, &QPushButton::clicked, this, &AdvancedLayoutExample::resetAllLayouts);
    connect(debug_button_, &QPushButton::toggled, this, &AdvancedLayoutExample::toggleDebugMode);

    // Animation controls
    enable_animations_ = new QCheckBox("Enable Animations");
    enable_animations_->setChecked(true);
    global_layout->addWidget(enable_animations_);

    auto* duration_layout = new QHBoxLayout();
    duration_layout->addWidget(new QLabel("Duration:"));
    animation_duration_ = new QSlider(Qt::Horizontal);
    animation_duration_->setRange(100, 1000);
    animation_duration_->setValue(300);
    duration_layout->addWidget(animation_duration_);
    auto* duration_label = new QLabel("300ms");
    duration_layout->addWidget(duration_label);
    global_layout->addLayout(duration_layout);

    connect(animation_duration_, &QSlider::valueChanged, [duration_label](int value) {
        duration_label->setText(QString("%1ms").arg(value));
    });

    // Add stretch to push everything to the top
    control_layout->addStretch();
}

void AdvancedLayoutExample::setupStatusBar() {
    status_widget_ = new QWidget();
    auto* status_layout = new QHBoxLayout(status_widget_);
    status_layout->setContentsMargins(5, 2, 5, 2);

    status_label_ = new QLabel("Ready");
    status_layout->addWidget(status_label_);

    status_layout->addStretch();

    performance_label_ = new QLabel("Updates: 0");
    status_layout->addWidget(performance_label_);

    statusBar()->addPermanentWidget(status_widget_);
}

void AdvancedLayoutExample::onGridLayoutChanged() {
    if (!grid_layout_) return;

    // Update grid properties based on controls
    int row_gap = grid_row_gap_slider_->value();
    int col_gap = grid_col_gap_slider_->value();

    auto justify_content = static_cast<DeclarativeUI::Layout::GridLayout::GridAlignment>(grid_justify_content_->currentIndex());
    auto align_content = static_cast<DeclarativeUI::Layout::GridLayout::GridAlignment>(grid_align_content_->currentIndex());

    grid_layout_->gap(row_gap, col_gap)
                 .justifyContent(justify_content)
                 .alignContent(align_content)
                 .updateLayout();

    layout_update_count_++;
}

void AdvancedLayoutExample::onFlexLayoutChanged() {
    if (!flex_layout_) return;

    // Update flex properties based on controls
    auto direction = static_cast<DeclarativeUI::Layout::FlexLayout::FlexDirection>(flex_direction_->currentIndex());
    auto wrap = static_cast<DeclarativeUI::Layout::FlexLayout::FlexWrap>(flex_wrap_->currentIndex());
    auto justify_content = static_cast<DeclarativeUI::Layout::FlexLayout::FlexAlignment>(flex_justify_content_->currentIndex());
    int gap = flex_gap_slider_->value();

    flex_layout_->direction(direction)
                 .wrap(wrap)
                 .justifyContent(justify_content)
                 .gap(gap)
                 .updateLayout();

    layout_update_count_++;
}

void AdvancedLayoutExample::onResponsiveLayoutChanged() {
    if (!responsive_layout_ || !responsive_demo_container_) return;

    if (auto_responsive_->isChecked()) {
        responsive_layout_->updateForCurrentBreakpoint(responsive_demo_container_->width());
    }

    layout_update_count_++;
}

void AdvancedLayoutExample::onConstraintLayoutChanged() {
    if (!constraint_layout_) return;

    constraint_layout_->updateLayout();
    layout_update_count_++;
}

void AdvancedLayoutExample::onBreakpointChanged(int breakpoint) {
    QString breakpoint_name;
    if (breakpoint == 0) breakpoint_name = "xs";
    else if (breakpoint == 1) breakpoint_name = "sm";
    else if (breakpoint == 2) breakpoint_name = "md";
    else if (breakpoint == 3) breakpoint_name = "lg";
    else breakpoint_name = "xl";

    current_breakpoint_label_->setText(QString("Current: %1").arg(breakpoint_name));
    status_label_->setText(QString("Breakpoint changed to: %1").arg(breakpoint_name));

    qDebug() << "📱 Breakpoint changed to:" << breakpoint_name;
}

void AdvancedLayoutExample::onLayoutUpdated(QWidget* container) {
    if (container) {
        status_label_->setText(QString("Layout updated: %1").arg(container->objectName()));
        layout_update_count_++;
    }
}

void AdvancedLayoutExample::resetAllLayouts() {
    // Reset grid layout
    if (grid_layout_) {
        grid_row_gap_slider_->setValue(16);
        grid_col_gap_slider_->setValue(16);
        grid_justify_content_->setCurrentIndex(0);
        grid_align_content_->setCurrentIndex(0);
        onGridLayoutChanged();
    }

    // Reset flex layout
    if (flex_layout_) {
        flex_direction_->setCurrentIndex(0);
        flex_wrap_->setCurrentIndex(1);
        flex_justify_content_->setCurrentIndex(0);
        flex_gap_slider_->setValue(12);
        onFlexLayoutChanged();
    }

    // Reset responsive layout
    if (responsive_layout_) {
        container_width_slider_->setValue(800);
        auto_responsive_->setChecked(true);
        onResponsiveLayoutChanged();
    }

    // Reset constraint layout
    if (constraint_layout_) {
        // Recreate constraint demo content
        createConstraintDemoContent();
    }

    status_label_->setText("All layouts reset to defaults");
    layout_update_count_ = 0;
}

void AdvancedLayoutExample::toggleDebugMode() {
    bool debug_enabled = debug_button_->isChecked();
    layout_engine_->enableDebugMode(debug_enabled);

    status_label_->setText(QString("Debug mode: %1").arg(debug_enabled ? "enabled" : "disabled"));
}

void AdvancedLayoutExample::exportLayoutConfiguration() {
    QString filename = QFileDialog::getSaveFileName(this, "Export Layout Configuration",
                                                   "layout_config.json", "JSON Files (*.json)");
    if (filename.isEmpty()) return;

    // Create configuration object
    LayoutConfiguration config;

    // TODO: Populate configuration from current layout settings

    if (LayoutConfigurationManager::saveConfiguration(config, filename)) {
        status_label_->setText("Configuration exported successfully");
        QMessageBox::information(this, "Export", "Layout configuration exported successfully!");
    } else {
        QMessageBox::warning(this, "Export Error", "Failed to export layout configuration.");
    }
}

void AdvancedLayoutExample::importLayoutConfiguration() {
    QString filename = QFileDialog::getOpenFileName(this, "Import Layout Configuration",
                                                   "", "JSON Files (*.json)");
    if (filename.isEmpty()) return;

    LayoutConfiguration config;
    if (LayoutConfigurationManager::loadConfiguration(config, filename)) {
        // TODO: Apply configuration to current layouts

        status_label_->setText("Configuration imported successfully");
        QMessageBox::information(this, "Import", "Layout configuration imported successfully!");
    } else {
        QMessageBox::warning(this, "Import Error", "Failed to import layout configuration.");
    }
}

void AdvancedLayoutExample::updateLayoutStatus() {
    performance_label_->setText(QString("Updates: %1").arg(layout_update_count_));

    // Update performance metrics
    static QElapsedTimer timer;
    if (!timer.isValid()) {
        timer.start();
    }

    qint64 current_time = timer.elapsed();
    if (current_time - last_update_time_ > 1000) {  // Update every second
        last_update_time_ = current_time;

        // Additional performance monitoring could go here
    }
}

void AdvancedLayoutExample::resizeEvent(QResizeEvent* event) {
    QMainWindow::resizeEvent(event);

    // Update responsive layouts when window is resized
    if (auto_responsive_ && auto_responsive_->isChecked()) {
        onResponsiveLayoutChanged();
    }
}

// **LayoutDemoWidget Implementation**

LayoutDemoWidget::LayoutDemoWidget(const QString& text, const QColor& color, QWidget* parent)
    : QWidget(parent), text_(text), color_(color)
{
    setMinimumSize(80, 60);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    setToolTip(text);
}

void LayoutDemoWidget::setText(const QString& text) {
    text_ = text;
    setToolTip(text);
    update();
}

void LayoutDemoWidget::setColor(const QColor& color) {
    color_ = color;
    update();
}

void LayoutDemoWidget::setFlexProperties(double grow, double shrink, int basis) {
    flex_grow_ = grow;
    flex_shrink_ = shrink;
    flex_basis_ = basis;
    emit propertiesChanged();
}

void LayoutDemoWidget::setGridPosition(int row, int col, int row_span, int col_span) {
    grid_row_ = row;
    grid_col_ = col;
    grid_row_span_ = row_span;
    grid_col_span_ = col_span;
    emit propertiesChanged();
}

void LayoutDemoWidget::paintEvent(QPaintEvent* event) {
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Draw background
    QColor bg_color = color_;
    if (is_selected_) {
        bg_color = bg_color.lighter(120);
    }

    painter.fillRect(rect(), bg_color);

    // Draw border
    painter.setPen(QPen(color_.darker(150), 2));
    painter.drawRect(rect().adjusted(1, 1, -1, -1));

    // Draw text
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 10, QFont::Bold));
    painter.drawText(rect(), Qt::AlignCenter | Qt::TextWordWrap, text_);

    // Draw flex properties if relevant
    if (flex_grow_ > 0 || flex_shrink_ != 1.0 || flex_basis_ >= 0) {
        QString flex_text = QString("flex: %1 %2 %3")
                           .arg(flex_grow_)
                           .arg(flex_shrink_)
                           .arg(flex_basis_ >= 0 ? QString::number(flex_basis_) + "px" : "auto");

        painter.setPen(Qt::yellow);
        painter.setFont(QFont("Arial", 8));
        painter.drawText(rect().adjusted(5, 5, -5, -5), Qt::AlignBottom | Qt::AlignLeft, flex_text);
    }
}

void LayoutDemoWidget::mousePressEvent(QMouseEvent* event) {
    if (event->button() == Qt::LeftButton) {
        is_selected_ = !is_selected_;
        update();
        emit clicked();
    }
    QWidget::mousePressEvent(event);
}

// **LayoutConfigurationManager Implementation**

bool LayoutConfigurationManager::saveConfiguration(const LayoutConfiguration& config, const QString& filename) {
    QJsonObject root;

    root["grid"] = gridConfigToJson(config.grid);
    root["flex"] = flexConfigToJson(config.flex);
    root["responsive"] = responsiveConfigToJson(config.responsive);
    root["constraint"] = constraintConfigToJson(config.constraint);

    QJsonObject global;
    global["debug_mode"] = config.debug_mode;
    global["animations_enabled"] = config.animations_enabled;
    global["animation_duration"] = config.animation_duration;
    root["global"] = global;

    QJsonDocument doc(root);

    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    file.write(doc.toJson());
    return true;
}

bool LayoutConfigurationManager::loadConfiguration(LayoutConfiguration& config, const QString& filename) {
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);

    if (error.error != QJsonParseError::NoError) {
        return false;
    }

    QJsonObject root = doc.object();

    if (root.contains("grid")) {
        gridConfigFromJson(config.grid, root["grid"].toObject());
    }

    if (root.contains("flex")) {
        flexConfigFromJson(config.flex, root["flex"].toObject());
    }

    if (root.contains("responsive")) {
        responsiveConfigFromJson(config.responsive, root["responsive"].toObject());
    }

    if (root.contains("constraint")) {
        constraintConfigFromJson(config.constraint, root["constraint"].toObject());
    }

    if (root.contains("global")) {
        QJsonObject global = root["global"].toObject();
        config.debug_mode = global["debug_mode"].toBool();
        config.animations_enabled = global["animations_enabled"].toBool();
        config.animation_duration = global["animation_duration"].toInt();
    }

    return true;
}

QString LayoutConfigurationManager::configurationToJson(const LayoutConfiguration& config) {
    QJsonObject root;

    root["grid"] = gridConfigToJson(config.grid);
    root["flex"] = flexConfigToJson(config.flex);
    root["responsive"] = responsiveConfigToJson(config.responsive);
    root["constraint"] = constraintConfigToJson(config.constraint);

    QJsonObject global;
    global["debug_mode"] = config.debug_mode;
    global["animations_enabled"] = config.animations_enabled;
    global["animation_duration"] = config.animation_duration;
    root["global"] = global;

    QJsonDocument doc(root);
    return doc.toJson();
}

bool LayoutConfigurationManager::configurationFromJson(LayoutConfiguration& config, const QString& json) {
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8(), &error);

    if (error.error != QJsonParseError::NoError) {
        return false;
    }

    QJsonObject root = doc.object();

    if (root.contains("grid")) {
        gridConfigFromJson(config.grid, root["grid"].toObject());
    }

    if (root.contains("flex")) {
        flexConfigFromJson(config.flex, root["flex"].toObject());
    }

    if (root.contains("responsive")) {
        responsiveConfigFromJson(config.responsive, root["responsive"].toObject());
    }

    if (root.contains("constraint")) {
        constraintConfigFromJson(config.constraint, root["constraint"].toObject());
    }

    if (root.contains("global")) {
        QJsonObject global = root["global"].toObject();
        config.debug_mode = global["debug_mode"].toBool();
        config.animations_enabled = global["animations_enabled"].toBool();
        config.animation_duration = global["animation_duration"].toInt();
    }

    return true;
}

QJsonObject LayoutConfigurationManager::gridConfigToJson(const LayoutConfiguration::GridConfig& config) {
    QJsonObject obj;

    obj["row_gap"] = config.row_gap;
    obj["col_gap"] = config.col_gap;
    obj["justify_content"] = static_cast<int>(config.justify_content);
    obj["align_content"] = static_cast<int>(config.align_content);
    obj["justify_items"] = static_cast<int>(config.justify_items);
    obj["align_items"] = static_cast<int>(config.align_items);

    QJsonArray areas;
    for (const QString& area : config.template_areas) {
        areas.append(area);
    }
    obj["template_areas"] = areas;

    return obj;
}

QJsonObject LayoutConfigurationManager::flexConfigToJson(const LayoutConfiguration::FlexConfig& config) {
    QJsonObject obj;

    obj["direction"] = static_cast<int>(config.direction);
    obj["wrap"] = static_cast<int>(config.wrap);
    obj["justify_content"] = static_cast<int>(config.justify_content);
    obj["align_items"] = static_cast<int>(config.align_items);
    obj["align_content"] = static_cast<int>(config.align_content);
    obj["gap"] = config.gap;

    return obj;
}

QJsonObject LayoutConfigurationManager::responsiveConfigToJson(const LayoutConfiguration::ResponsiveConfig& config) {
    QJsonObject obj;

    QJsonArray breakpoints;
    for (const auto& bp : config.breakpoints) {
        QJsonObject bp_obj;
        bp_obj["name"] = bp.first;
        bp_obj["min_width"] = bp.second.first;
        bp_obj["max_width"] = bp.second.second;
        breakpoints.append(bp_obj);
    }
    obj["breakpoints"] = breakpoints;

    return obj;
}

QJsonObject LayoutConfigurationManager::constraintConfigToJson(const LayoutConfiguration::ConstraintConfig& config) {
    QJsonObject obj;

    QJsonArray constraints;
    for (const auto& constraint : config.constraints) {
        QJsonObject c_obj;
        c_obj["identifier"] = constraint.identifier;
        c_obj["first_attribute"] = static_cast<int>(constraint.first_attribute);
        c_obj["relation"] = static_cast<int>(constraint.relation);
        c_obj["second_attribute"] = static_cast<int>(constraint.second_attribute);
        c_obj["multiplier"] = constraint.multiplier;
        c_obj["constant"] = constraint.constant;
        c_obj["priority"] = static_cast<int>(constraint.priority);
        c_obj["active"] = constraint.active;
        constraints.append(c_obj);
    }
    obj["constraints"] = constraints;

    return obj;
}

void LayoutConfigurationManager::gridConfigFromJson(LayoutConfiguration::GridConfig& config, const QJsonObject& json) {
    config.row_gap = json["row_gap"].toInt();
    config.col_gap = json["col_gap"].toInt();
    config.justify_content = static_cast<DeclarativeUI::Layout::GridLayout::GridAlignment>(json["justify_content"].toInt());
    config.align_content = static_cast<DeclarativeUI::Layout::GridLayout::GridAlignment>(json["align_content"].toInt());
    config.justify_items = static_cast<DeclarativeUI::Layout::GridLayout::GridAlignment>(json["justify_items"].toInt());
    config.align_items = static_cast<DeclarativeUI::Layout::GridLayout::GridAlignment>(json["align_items"].toInt());

    config.template_areas.clear();
    QJsonArray areas = json["template_areas"].toArray();
    for (const QJsonValue& area : areas) {
        config.template_areas.push_back(area.toString());
    }
}

void LayoutConfigurationManager::flexConfigFromJson(LayoutConfiguration::FlexConfig& config, const QJsonObject& json) {
    config.direction = static_cast<DeclarativeUI::Layout::FlexLayout::FlexDirection>(json["direction"].toInt());
    config.wrap = static_cast<DeclarativeUI::Layout::FlexLayout::FlexWrap>(json["wrap"].toInt());
    config.justify_content = static_cast<DeclarativeUI::Layout::FlexLayout::FlexAlignment>(json["justify_content"].toInt());
    config.align_items = static_cast<DeclarativeUI::Layout::FlexLayout::FlexAlignment>(json["align_items"].toInt());
    config.align_content = static_cast<DeclarativeUI::Layout::FlexLayout::FlexAlignment>(json["align_content"].toInt());
    config.gap = json["gap"].toInt();
}

void LayoutConfigurationManager::responsiveConfigFromJson(LayoutConfiguration::ResponsiveConfig& config, const QJsonObject& json) {
    config.breakpoints.clear();
    QJsonArray breakpoints = json["breakpoints"].toArray();
    for (const QJsonValue& bp : breakpoints) {
        QJsonObject bp_obj = bp.toObject();
        QString name = bp_obj["name"].toString();
        int min_width = bp_obj["min_width"].toInt();
        int max_width = bp_obj["max_width"].toInt();
        config.breakpoints.emplace_back(name, std::make_pair(min_width, max_width));
    }
}

void LayoutConfigurationManager::constraintConfigFromJson(LayoutConfiguration::ConstraintConfig& config, const QJsonObject& json) {
    config.constraints.clear();
    QJsonArray constraints = json["constraints"].toArray();
    for (const QJsonValue& c : constraints) {
        QJsonObject c_obj = c.toObject();
        DeclarativeUI::Layout::ConstraintLayout::Constraint constraint;
        constraint.identifier = c_obj["identifier"].toString();
        constraint.first_attribute = static_cast<DeclarativeUI::Layout::ConstraintLayout::ConstraintAttribute>(c_obj["first_attribute"].toInt());
        constraint.relation = static_cast<DeclarativeUI::Layout::ConstraintLayout::ConstraintType>(c_obj["relation"].toInt());
        constraint.second_attribute = static_cast<DeclarativeUI::Layout::ConstraintLayout::ConstraintAttribute>(c_obj["second_attribute"].toInt());
        constraint.multiplier = c_obj["multiplier"].toDouble();
        constraint.constant = c_obj["constant"].toDouble();
        constraint.priority = static_cast<DeclarativeUI::Layout::ConstraintLayout::ConstraintPriority>(c_obj["priority"].toInt());
        constraint.active = c_obj["active"].toBool();
        config.constraints.push_back(constraint);
    }
}

#include "AdvancedLayoutExample.moc"
