#pragma once

#include "../../../src/Plugins/PluginInterface.hpp"
#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <memory>

using namespace DeclarativeUI::Plugins;

/**
 * @brief Example plugin demonstrating all plugin capabilities
 */
class ExamplePlugin : public QObject, 
                     public IPlugin,
                     public IComponentPlugin,
                     public ICommandPlugin,
                     public IThemePlugin {
    Q_OBJECT
    Q_INTERFACES(DeclarativeUI::Plugins::IPlugin)
    Q_INTERFACES(DeclarativeUI::Plugins::IComponentPlugin)
    Q_INTERFACES(DeclarativeUI::Plugins::ICommandPlugin)
    Q_INTERFACES(DeclarativeUI::Plugins::IThemePlugin)
    
public:
    explicit ExamplePlugin(QObject* parent = nullptr);
    ~ExamplePlugin() override;
    
    // IPlugin interface
    PluginMetadata getMetadata() const override;
    PluginCapabilities getCapabilities() const override;
    PluginState getState() const override;
    
    bool initialize() override;
    bool activate() override;
    bool deactivate() override;
    void cleanup() override;
    
    QStringList getRequiredPlugins() const override;
    QJsonObject getDefaultConfiguration() const override;
    bool configure(const QJsonObject& config) override;
    QJsonObject getConfiguration() const override;
    
    QString getLastError() const override;
    
    // IComponentPlugin interface
    QStringList getProvidedComponents() const override;
    std::unique_ptr<QObject> createComponent(const QString& componentType) override;
    QJsonObject getComponentSchema(const QString& componentType) const override;
    QStringList getComponentsByCategory(const QString& category) const override;
    QStringList getComponentsByTag(const QString& tag) const override;
    
    // ICommandPlugin interface
    QStringList getProvidedCommands() const override;
    std::unique_ptr<QObject> createCommand(const QString& commandType) override;
    QJsonObject getCommandSchema(const QString& commandType) const override;
    QStringList getCommandsByCategory(const QString& category) const override;
    QStringList getCommandsByTag(const QString& tag) const override;
    
    // IThemePlugin interface
    QStringList getProvidedThemes() const override;
    QJsonObject getThemeDefinition(const QString& themeName) const override;
    QString getThemeStyleSheet(const QString& themeName) const override;
    QStringList getThemesByTag(const QString& tag) const override;
    QStringList getDarkThemes() const override;
    QStringList getLightThemes() const override;
    
private:
    void setupComponents();
    void setupCommands();
    void setupThemes();
    
    void setError(const QString& error);
    
private:
    PluginState state_ = PluginState::Unloaded;
    QJsonObject configuration_;
    QString last_error_;
    
    // Component definitions
    QHash<QString, QJsonObject> component_schemas_;
    QHash<QString, QString> component_categories_;
    QHash<QString, QStringList> component_tags_;
    
    // Command definitions
    QHash<QString, QJsonObject> command_schemas_;
    QHash<QString, QString> command_categories_;
    QHash<QString, QStringList> command_tags_;
    
    // Theme definitions
    QHash<QString, QJsonObject> theme_definitions_;
    QHash<QString, QString> theme_stylesheets_;
    QHash<QString, QStringList> theme_tags_;
    QStringList dark_themes_;
    QStringList light_themes_;
};

// Example components
class ExampleButton : public QObject {
    Q_OBJECT
    
public:
    explicit ExampleButton(QObject* parent = nullptr);
    
    Q_PROPERTY(QString text READ text WRITE setText NOTIFY textChanged)
    Q_PROPERTY(QString color READ color WRITE setColor NOTIFY colorChanged)
    Q_PROPERTY(bool enabled READ isEnabled WRITE setEnabled NOTIFY enabledChanged)
    
    QString text() const { return text_; }
    void setText(const QString& text);
    
    QString color() const { return color_; }
    void setColor(const QString& color);
    
    bool isEnabled() const { return enabled_; }
    void setEnabled(bool enabled);
    
public slots:
    void click();
    
signals:
    void textChanged(const QString& text);
    void colorChanged(const QString& color);
    void enabledChanged(bool enabled);
    void clicked();
    
private:
    QString text_ = "Button";
    QString color_ = "#007ACC";
    bool enabled_ = true;
};

class ExampleCard : public QObject {
    Q_OBJECT
    
public:
    explicit ExampleCard(QObject* parent = nullptr);
    
    Q_PROPERTY(QString title READ title WRITE setTitle NOTIFY titleChanged)
    Q_PROPERTY(QString content READ content WRITE setContent NOTIFY contentChanged)
    Q_PROPERTY(bool elevated READ isElevated WRITE setElevated NOTIFY elevatedChanged)
    
    QString title() const { return title_; }
    void setTitle(const QString& title);
    
    QString content() const { return content_; }
    void setContent(const QString& content);
    
    bool isElevated() const { return elevated_; }
    void setElevated(bool elevated);
    
signals:
    void titleChanged(const QString& title);
    void contentChanged(const QString& content);
    void elevatedChanged(bool elevated);
    
private:
    QString title_ = "Card Title";
    QString content_ = "Card content goes here...";
    bool elevated_ = true;
};

// Example commands
class ShowNotificationCommand : public QObject {
    Q_OBJECT
    
public:
    explicit ShowNotificationCommand(QObject* parent = nullptr);
    
    Q_PROPERTY(QString message READ message WRITE setMessage NOTIFY messageChanged)
    Q_PROPERTY(QString type READ type WRITE setType NOTIFY typeChanged)
    Q_PROPERTY(int duration READ duration WRITE setDuration NOTIFY durationChanged)
    
    QString message() const { return message_; }
    void setMessage(const QString& message);
    
    QString type() const { return type_; }
    void setType(const QString& type);
    
    int duration() const { return duration_; }
    void setDuration(int duration);
    
public slots:
    void execute();
    
signals:
    void messageChanged(const QString& message);
    void typeChanged(const QString& type);
    void durationChanged(int duration);
    void executed();
    
private:
    QString message_ = "Notification";
    QString type_ = "info";
    int duration_ = 3000;
};

class ToggleThemeCommand : public QObject {
    Q_OBJECT
    
public:
    explicit ToggleThemeCommand(QObject* parent = nullptr);
    
    Q_PROPERTY(QString currentTheme READ currentTheme WRITE setCurrentTheme NOTIFY currentThemeChanged)
    Q_PROPERTY(QString lightTheme READ lightTheme WRITE setLightTheme NOTIFY lightThemeChanged)
    Q_PROPERTY(QString darkTheme READ darkTheme WRITE setDarkTheme NOTIFY darkThemeChanged)
    
    QString currentTheme() const { return current_theme_; }
    void setCurrentTheme(const QString& theme);
    
    QString lightTheme() const { return light_theme_; }
    void setLightTheme(const QString& theme);
    
    QString darkTheme() const { return dark_theme_; }
    void setDarkTheme(const QString& theme);
    
public slots:
    void execute();
    void toggle();
    
signals:
    void currentThemeChanged(const QString& theme);
    void lightThemeChanged(const QString& theme);
    void darkThemeChanged(const QString& theme);
    void executed();
    void themeToggled(const QString& newTheme);
    
private:
    QString current_theme_ = "ExampleLight";
    QString light_theme_ = "ExampleLight";
    QString dark_theme_ = "ExampleDark";
};

// Plugin factory function
extern "C" {
    Q_DECL_EXPORT IPlugin* createPlugin();
}
