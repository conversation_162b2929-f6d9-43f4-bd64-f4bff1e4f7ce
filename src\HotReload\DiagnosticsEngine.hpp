#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QTimer>
#include <QMutex>
#include <QReadWriteLock>
#include <memory>
#include <deque>
#include <unordered_map>
#include <functional>

namespace DeclarativeUI::HotReload {

/**
 * @brief Diagnostic information for errors and issues
 */
struct DiagnosticInfo {
    enum Severity {
        Info = 0,
        Warning = 1,
        Error = 2,
        Critical = 3
    };
    
    enum Category {
        FileSystem = 0,
        Compilation = 1,
        Validation = 2,
        Performance = 3,
        Memory = 4,
        Network = 5,
        Configuration = 6,
        Unknown = 7
    };
    
    QString id;
    Severity severity = Error;
    Category category = Unknown;
    QString title;
    QString description;
    QString file_path;
    int line_number = -1;
    int column_number = -1;
    QDateTime timestamp;
    QJsonObject metadata;
    QStringList suggested_fixes;
    QString error_code;
    
    // Recovery information
    bool auto_recoverable = false;
    QString recovery_action;
    int recovery_attempts = 0;
    bool recovery_successful = false;
    
    DiagnosticInfo() : timestamp(QDateTime::currentDateTime()) {}
    
    static DiagnosticInfo createError(const QString& title, const QString& description, const QString& file_path = QString()) {
        DiagnosticInfo info;
        info.severity = Error;
        info.title = title;
        info.description = description;
        info.file_path = file_path;
        return info;
    }
    
    static DiagnosticInfo createWarning(const QString& title, const QString& description, const QString& file_path = QString()) {
        DiagnosticInfo info;
        info.severity = Warning;
        info.title = title;
        info.description = description;
        info.file_path = file_path;
        return info;
    }
    
    QJsonObject toJson() const;
    static DiagnosticInfo fromJson(const QJsonObject& json);
};

/**
 * @brief Recovery strategy for automatic error handling
 */
struct RecoveryStrategy {
    QString name;
    QString description;
    std::function<bool(const DiagnosticInfo&)> condition_check;
    std::function<bool(const DiagnosticInfo&)> recovery_action;
    int max_attempts = 3;
    int delay_ms = 1000;
    bool enabled = true;
    
    RecoveryStrategy() = default;
    RecoveryStrategy(const QString& name, const QString& description) 
        : name(name), description(description) {}
};

/**
 * @brief Advanced diagnostics and error recovery engine
 * 
 * Provides comprehensive error handling with features:
 * - Automatic error detection and classification
 * - Smart recovery strategies
 * - Detailed diagnostic reporting
 * - Error pattern analysis
 * - Performance impact assessment
 * - Debugging assistance tools
 */
class DiagnosticsEngine : public QObject {
    Q_OBJECT

public:
    explicit DiagnosticsEngine(QObject* parent = nullptr);
    ~DiagnosticsEngine() override = default;

    // Error reporting and management
    void reportError(const DiagnosticInfo& diagnostic);
    void reportError(const QString& title, const QString& description, const QString& file_path = QString());
    void reportWarning(const QString& title, const QString& description, const QString& file_path = QString());
    void reportInfo(const QString& title, const QString& description, const QString& file_path = QString());
    
    // Error retrieval and querying
    QList<DiagnosticInfo> getAllDiagnostics() const;
    QList<DiagnosticInfo> getDiagnosticsByFile(const QString& file_path) const;
    QList<DiagnosticInfo> getDiagnosticsBySeverity(DiagnosticInfo::Severity severity) const;
    QList<DiagnosticInfo> getDiagnosticsByCategory(DiagnosticInfo::Category category) const;
    QList<DiagnosticInfo> getRecentDiagnostics(int minutes = 60) const;
    
    // Recovery management
    void registerRecoveryStrategy(const RecoveryStrategy& strategy);
    void enableAutoRecovery(bool enabled);
    bool isAutoRecoveryEnabled() const;
    void attemptRecovery(const QString& diagnostic_id);
    void attemptRecoveryForFile(const QString& file_path);
    
    // Analysis and insights
    QJsonObject generateDiagnosticReport() const;
    QStringList getCommonErrorPatterns() const;
    QStringList getSuggestedFixes(const QString& diagnostic_id) const;
    QString getErrorCodeDescription(const QString& error_code) const;
    
    // Configuration
    void setMaxDiagnosticHistory(int max_count);
    int getMaxDiagnosticHistory() const;
    void setAutoRecoveryDelay(int delay_ms);
    int getAutoRecoveryDelay() const;
    
    // Cleanup and maintenance
    void clearDiagnostics();
    void clearDiagnosticsForFile(const QString& file_path);
    void clearOldDiagnostics(int days = 7);
    
    // Export and import
    bool exportDiagnostics(const QString& file_path) const;
    bool importDiagnostics(const QString& file_path);

signals:
    void diagnosticReported(const DiagnosticInfo& diagnostic);
    void errorRecovered(const QString& diagnostic_id, const QString& recovery_action);
    void recoveryFailed(const QString& diagnostic_id, const QString& reason);
    void criticalErrorDetected(const DiagnosticInfo& diagnostic);
    void errorPatternDetected(const QString& pattern, int frequency);

private:
    mutable QReadWriteLock diagnostics_lock_;
    std::deque<DiagnosticInfo> diagnostics_history_;
    std::unordered_map<QString, DiagnosticInfo> active_diagnostics_;
    
    // Recovery system
    std::vector<RecoveryStrategy> recovery_strategies_;
    QMutex recovery_lock_;
    bool auto_recovery_enabled_;
    int auto_recovery_delay_ms_;
    std::unique_ptr<QTimer> recovery_timer_;
    
    // Configuration
    int max_diagnostic_history_;
    
    // Pattern analysis
    std::unordered_map<std::string, int> error_patterns_;
    std::unordered_map<std::string, std::string> error_code_descriptions_;
    
    // Helper methods
    void initializeBuiltinRecoveryStrategies();
    void initializeErrorCodeDescriptions();
    QString generateDiagnosticId() const;
    void updateErrorPatterns(const DiagnosticInfo& diagnostic);
    void scheduleRecoveryAttempt(const QString& diagnostic_id);
    bool executeRecoveryStrategy(const RecoveryStrategy& strategy, const DiagnosticInfo& diagnostic);
    void pruneOldDiagnostics();
    QJsonArray diagnosticsToJsonArray(const QList<DiagnosticInfo>& diagnostics) const;
    
private slots:
    void onRecoveryTimerTimeout();
};

} // namespace DeclarativeUI::HotReload
