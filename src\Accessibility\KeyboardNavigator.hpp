#pragma once

#include <QObject>
#include <QWidget>
#include <QKeyEvent>
#include <QApplication>
#include <QTimer>
#include <QHash>
#include <QList>
#include <QRect>
#include <functional>

namespace DeclarativeUI::Accessibility {

/**
 * @brief Navigation direction for keyboard navigation
 */
enum class NavigationDirection {
    Next,       // Tab or Right Arrow
    Previous,   // Shift+Tab or Left Arrow
    Up,         // Up Arrow
    Down,       // Down Arrow
    Left,       // Left Arrow
    Right,      // Right Arrow
    Home,       // Home key
    End,        // End key
    PageUp,     // Page Up
    PageDown    // Page Down
};

/**
 * @brief Navigation strategy for different widget types
 */
enum class NavigationStrategy {
    Linear,     // Simple linear navigation (default)
    Grid,       // 2D grid navigation
    Tree,       // Hierarchical tree navigation
    Circular,   // Circular navigation (wraps around)
    Custom      // Custom navigation logic
};

/**
 * @brief Focus indicator style
 */
struct FocusIndicatorStyle {
    QColor borderColor = QColor(0, 123, 255);  // Blue
    int borderWidth = 2;
    int borderRadius = 4;
    QColor backgroundColor = QColor(0, 123, 255, 30);  // Semi-transparent blue
    QString customStyleSheet;
    bool animated = true;
    int animationDuration = 200;
};

/**
 * @brief Navigation group for organizing related widgets
 */
struct NavigationGroup {
    QString name;
    QList<QWidget*> widgets;
    NavigationStrategy strategy = NavigationStrategy::Linear;
    bool wrapAround = false;
    bool skipDisabled = true;
    bool skipHidden = true;
    std::function<QWidget*(QWidget*, NavigationDirection)> customNavigator;
};

/**
 * @brief Advanced keyboard navigation system
 */
class KeyboardNavigator : public QObject {
    Q_OBJECT
    
public:
    explicit KeyboardNavigator(QObject* parent = nullptr);
    ~KeyboardNavigator() override;
    
    // Global navigation settings
    void setEnabled(bool enabled);
    bool isEnabled() const { return enabled_; }
    
    void setFocusIndicatorEnabled(bool enabled);
    bool isFocusIndicatorEnabled() const { return focus_indicator_enabled_; }
    
    void setFocusIndicatorStyle(const FocusIndicatorStyle& style);
    FocusIndicatorStyle getFocusIndicatorStyle() const { return focus_style_; }
    
    // Widget registration and management
    void registerWidget(QWidget* widget, const QString& group = "default");
    void unregisterWidget(QWidget* widget);
    void setWidgetNavigationOrder(const QList<QWidget*>& widgets, const QString& group = "default");
    
    // Navigation group management
    void createNavigationGroup(const QString& name, NavigationStrategy strategy = NavigationStrategy::Linear);
    void setGroupStrategy(const QString& group, NavigationStrategy strategy);
    void setGroupWrapAround(const QString& group, bool wrapAround);
    void setCustomNavigator(const QString& group, std::function<QWidget*(QWidget*, NavigationDirection)> navigator);
    
    // Navigation operations
    bool navigateToNext(QWidget* current = nullptr);
    bool navigateToPrevious(QWidget* current = nullptr);
    bool navigateInDirection(NavigationDirection direction, QWidget* current = nullptr);
    bool navigateToWidget(QWidget* target);
    bool navigateToFirst(const QString& group = "default");
    bool navigateToLast(const QString& group = "default");
    
    // Focus management
    QWidget* getCurrentFocus() const;
    QWidget* getNextFocusableWidget(QWidget* current, NavigationDirection direction) const;
    QList<QWidget*> getFocusableWidgets(const QString& group = "default") const;
    
    // Skip links and shortcuts
    void addSkipLink(const QString& text, QWidget* target, const QKeySequence& shortcut = QKeySequence());
    void removeSkipLink(const QString& text);
    void activateSkipLink(const QString& text);
    QStringList getSkipLinks() const;
    
    // Spatial navigation (for grid layouts)
    void enableSpatialNavigation(bool enabled = true);
    bool isSpatialNavigationEnabled() const { return spatial_navigation_enabled_; }
    void setSpatialNavigationThreshold(int threshold) { spatial_threshold_ = threshold; }
    
    // Accessibility shortcuts
    void addGlobalShortcut(const QKeySequence& key, std::function<void()> action, const QString& description = QString());
    void removeGlobalShortcut(const QKeySequence& key);
    void setEscapeKeyHandler(std::function<void()> handler);
    
    // Focus trapping (for modal dialogs)
    void trapFocus(QWidget* container);
    void releaseFocusTrap();
    bool isFocusTrapped() const { return focus_trapped_; }
    
    // Validation and debugging
    QStringList validateNavigation(const QString& group = "default") const;
    void dumpNavigationStructure() const;
    
signals:
    void focusChanged(QWidget* from, QWidget* to);
    void navigationFailed(NavigationDirection direction, QWidget* current);
    void skipLinkActivated(const QString& text, QWidget* target);
    void focusTrapped(QWidget* container);
    void focusReleased();
    
protected:
    bool eventFilter(QObject* obj, QEvent* event) override;
    
private slots:
    void onApplicationFocusChanged(QWidget* old, QWidget* now);
    void onWidgetDestroyed(QObject* obj);
    void updateFocusIndicator();
    
private:
    // Core navigation logic
    QWidget* findNextWidget(QWidget* current, NavigationDirection direction, const QString& group) const;
    QWidget* findNextLinear(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const;
    QWidget* findNextGrid(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const;
    QWidget* findNextSpatial(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const;
    QWidget* findNextTree(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const;
    
    // Widget filtering
    bool isWidgetFocusable(QWidget* widget) const;
    bool isWidgetInGroup(QWidget* widget, const QString& group) const;
    QString getWidgetGroup(QWidget* widget) const;
    
    // Focus indicator management
    void createFocusIndicator();
    void showFocusIndicator(QWidget* widget);
    void hideFocusIndicator();
    void animateFocusIndicator(QWidget* widget);
    
    // Spatial navigation helpers
    QRect getWidgetRect(QWidget* widget) const;
    double calculateDistance(const QRect& from, const QRect& to, NavigationDirection direction) const;
    bool isInDirection(const QRect& from, const QRect& to, NavigationDirection direction) const;
    
    // Key event handling
    bool handleKeyEvent(QKeyEvent* event);
    NavigationDirection keyToDirection(int key, Qt::KeyboardModifiers modifiers) const;
    
    // Skip link management
    void createSkipLinkWidget();
    void showSkipLinks();
    void hideSkipLinks();
    
private:
    bool enabled_ = true;
    bool focus_indicator_enabled_ = true;
    bool spatial_navigation_enabled_ = false;
    bool focus_trapped_ = false;
    
    // Navigation groups
    QHash<QString, NavigationGroup> navigation_groups_;
    QHash<QWidget*, QString> widget_to_group_;
    
    // Focus indicator
    QWidget* focus_indicator_ = nullptr;
    FocusIndicatorStyle focus_style_;
    QTimer* focus_animation_timer_ = nullptr;
    
    // Skip links
    struct SkipLink {
        QString text;
        QWidget* target;
        QKeySequence shortcut;
    };
    QList<SkipLink> skip_links_;
    QWidget* skip_link_widget_ = nullptr;
    
    // Focus trapping
    QWidget* focus_trap_container_ = nullptr;
    QList<QWidget*> trapped_widgets_;
    
    // Spatial navigation
    int spatial_threshold_ = 50;  // pixels
    
    // Global shortcuts
    QHash<QKeySequence, std::function<void()>> global_shortcuts_;
    QHash<QKeySequence, QString> shortcut_descriptions_;
    std::function<void()> escape_handler_;
    
    // State tracking
    QWidget* last_focused_widget_ = nullptr;
    QWidget* current_focus_ = nullptr;
};

} // namespace DeclarativeUI::Accessibility
