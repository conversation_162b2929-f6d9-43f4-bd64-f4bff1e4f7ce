#include "CSSProcessor.hpp"
#include <QDebug>
#include <QFileInfo>
#include <QMutexLocker>
#include <QRegularExpressionMatch>
#include <QRegularExpressionMatchIterator>

namespace DeclarativeUI::HotReload::FormatSupport {

CSSProcessor::CSSProcessor(QObject* parent) : IFormatProcessor(parent) {
    initializeRegexPatterns();
    
    // Set default target to application if available
    if (qApp) {
        target_application_ = qApp;
    }
}

bool CSSProcessor::canProcess(const QString& file_path) const {
    QString extension = getFileExtension(file_path);
    return getSupportedExtensions().contains(extension);
}

ProcessingResult CSSProcessor::processFile(const QString& file_path, const ProcessingConfig& config) {
    emit processingStarted(file_path);
    startPerformanceMeasurement();
    
    try {
        if (!validateFile(file_path)) {
            return ProcessingResult::createError("Invalid CSS file: " + file_path);
        }
        
        QString content = readFileContent(file_path);
        if (content.isEmpty()) {
            return ProcessingResult::createError("Failed to read CSS file: " + file_path);
        }
        
        ProcessingResult result = processContent(content, file_path, config);
        result.processing_time_ms = endPerformanceMeasurement();
        result.metadata = createMetadata(file_path);
        
        // Auto-inject if enabled
        if (auto_injection_enabled_ && config.enable_live_injection && result.success) {
            QString identifier = QFileInfo(file_path).baseName();
            if (injectCSS(result.processed_content, identifier)) {
                result.metadata["injected"] = true;
                result.metadata["injection_identifier"] = identifier;
            }
        }
        
        emit processingFinished(file_path, result);
        return result;
        
    } catch (const std::exception& e) {
        ProcessingResult result = ProcessingResult::createError(QString("CSS processing error: %1").arg(e.what()));
        result.processing_time_ms = endPerformanceMeasurement();
        emit processingError(file_path, result.error_message);
        return result;
    }
}

ProcessingResult CSSProcessor::processContent(const QString& content, const QString& file_path, const ProcessingConfig& config) {
    try {
        if (!validateContent(content)) {
            return ProcessingResult::createError("Invalid CSS content");
        }
        
        QString processed_content = content;
        QJsonObject metadata = createMetadata(file_path);
        
        // Process imports
        if (!file_path.isEmpty()) {
            QFileInfo file_info(file_path);
            processed_content = processImports(processed_content, file_info.absolutePath());
        }
        
        // Variable substitution
        if (!variable_substitutions_.isEmpty()) {
            processed_content = substituteVariables(processed_content, variable_substitutions_);
        }
        
        // Autoprefixer
        if (autoprefixer_enabled_ || config.enable_transpilation) {
            processed_content = addAutoprefixes(processed_content);
            metadata["autoprefixed"] = true;
        }
        
        // Minification
        if (minification_enabled_ || config.enable_minification) {
            processed_content = minifyCSS(processed_content);
            metadata["minified"] = true;
        }
        
        metadata["original_size"] = content.length();
        metadata["processed_size"] = processed_content.length();
        metadata["compression_ratio"] = content.isEmpty() ? 0.0 : (double)processed_content.length() / content.length();
        
        return ProcessingResult::createSuccess(processed_content, metadata);
        
    } catch (const std::exception& e) {
        return ProcessingResult::createError(QString("CSS content processing error: %1").arg(e.what()));
    }
}

bool CSSProcessor::validateFile(const QString& file_path) const {
    QFileInfo file_info(file_path);
    if (!file_info.exists() || !file_info.isReadable()) {
        return false;
    }
    
    QString content = readFileContent(file_path);
    return validateContent(content);
}

bool CSSProcessor::validateContent(const QString& content) const {
    if (content.trimmed().isEmpty()) {
        return true; // Empty CSS is valid
    }
    
    return validateCSSContent(content);
}

ProcessingConfig CSSProcessor::getDefaultConfig() const {
    ProcessingConfig config;
    config.enable_live_injection = true;
    config.enable_minification = minification_enabled_;
    config.enable_transpilation = autoprefixer_enabled_;
    config.enable_caching = true;
    config.max_processing_time_ms = 2000; // CSS should be fast
    return config;
}

ProcessingResult CSSProcessor::prepareLiveInjection(const QString& content, const QString& file_path) {
    ProcessingConfig config = getDefaultConfig();
    config.enable_live_injection = true;
    
    ProcessingResult result = processContent(content, file_path, config);
    
    if (result.success) {
        result.metadata["prepared_for_injection"] = true;
        result.metadata["injection_scope"] = injection_scope_;
    }
    
    return result;
}

void CSSProcessor::setTargetWidget(QWidget* widget) {
    target_widget_ = widget;
    injection_scope_ = "widget";
}

void CSSProcessor::setTargetApplication(QApplication* app) {
    target_application_ = app;
    injection_scope_ = "application";
}

void CSSProcessor::enableAutoInjection(bool enabled) {
    auto_injection_enabled_ = enabled;
}

void CSSProcessor::setInjectionScope(const QString& scope) {
    if (scope == "widget" || scope == "application" || scope == "global") {
        injection_scope_ = scope;
    } else {
        qWarning() << "Invalid injection scope:" << scope << ". Using 'application'.";
        injection_scope_ = "application";
    }
}

void CSSProcessor::enableMinification(bool enabled) {
    minification_enabled_ = enabled;
}

void CSSProcessor::enableAutoprefixer(bool enabled) {
    autoprefixer_enabled_ = enabled;
}

void CSSProcessor::setVariableSubstitution(const QVariantMap& variables) {
    variable_substitutions_ = variables;
}

bool CSSProcessor::injectCSS(const QString& css_content, const QString& identifier) {
    QMutexLocker locker(&injection_mutex_);
    
    try {
        QString css_id = identifier.isEmpty() ? QString("css_%1").arg(QDateTime::currentMSecsSinceEpoch()) : identifier;
        
        bool success = false;
        if (injection_scope_ == "widget" && target_widget_) {
            success = injectCSSToWidget(target_widget_, css_content, css_id);
        } else if (injection_scope_ == "application" && target_application_) {
            success = injectCSSToApplication(target_application_, css_content, css_id);
        } else if (injection_scope_ == "global") {
            success = injectCSSGlobally(css_content, css_id);
        } else {
            qWarning() << "No valid injection target available";
            return false;
        }
        
        if (success) {
            InjectedCSS injection;
            injection.identifier = css_id;
            injection.original_content = css_content;
            injection.processed_content = css_content;
            injection.injection_time = QDateTime::currentDateTime();
            injection.file_path = QString(); // Will be set by caller if needed
            
            injected_css_[css_id] = injection;
            
            emit cssInjected(css_id, css_content);
            qDebug() << "🎨 CSS injected successfully:" << css_id;
        } else {
            emit injectionFailed(css_id, "Failed to inject CSS");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        QString error = QString("CSS injection error: %1").arg(e.what());
        emit injectionFailed(identifier, error);
        qWarning() << error;
        return false;
    }
}

bool CSSProcessor::removeInjectedCSS(const QString& identifier) {
    QMutexLocker locker(&injection_mutex_);
    
    auto it = injected_css_.find(identifier);
    if (it == injected_css_.end()) {
        return false;
    }
    
    try {
        // Remove from target
        bool success = false;
        if (injection_scope_ == "widget" && target_widget_) {
            QString current_stylesheet = target_widget_->styleSheet();
            QString updated_stylesheet = removeStyleSheetSection(current_stylesheet, identifier);
            target_widget_->setStyleSheet(updated_stylesheet);
            success = true;
        } else if (injection_scope_ == "application" && target_application_) {
            QString current_stylesheet = target_application_->styleSheet();
            QString updated_stylesheet = removeStyleSheetSection(current_stylesheet, identifier);
            target_application_->setStyleSheet(updated_stylesheet);
            success = true;
        }
        
        if (success) {
            injected_css_.erase(it);
            emit cssRemoved(identifier);
            qDebug() << "🎨 CSS removed successfully:" << identifier;
        }
        
        return success;
        
    } catch (const std::exception& e) {
        qWarning() << "Error removing CSS:" << e.what();
        return false;
    }
}

void CSSProcessor::clearAllInjectedCSS() {
    QMutexLocker locker(&injection_mutex_);
    
    for (const auto& pair : injected_css_) {
        removeInjectedCSS(pair.first);
    }
    
    injected_css_.clear();
}

QStringList CSSProcessor::getInjectedCSSIdentifiers() const {
    QMutexLocker locker(&injection_mutex_);

    QStringList identifiers;
    for (const auto& pair : injected_css_) {
        identifiers.append(pair.first);
    }
    return identifiers;
}

QString CSSProcessor::minifyCSS(const QString& css_content) const {
    QString minified = css_content;

    // Remove comments
    minified.remove(QRegularExpression("/\\*.*?\\*/", QRegularExpression::DotMatchesEverythingOption));

    // Remove unnecessary whitespace
    minified.replace(QRegularExpression("\\s+"), " ");
    minified.replace(QRegularExpression("\\s*{\\s*"), "{");
    minified.replace(QRegularExpression("\\s*}\\s*"), "}");
    minified.replace(QRegularExpression("\\s*;\\s*"), ";");
    minified.replace(QRegularExpression("\\s*:\\s*"), ":");
    minified.replace(QRegularExpression("\\s*,\\s*"), ",");

    // Remove trailing semicolons before closing braces
    minified.replace(QRegularExpression(";\\s*}"), "}");

    return minified.trimmed();
}

QString CSSProcessor::addAutoprefixes(const QString& css_content) const {
    QString prefixed = css_content;

    // Simple autoprefixer implementation for common properties
    QStringList properties_to_prefix = {
        "transform", "transition", "animation", "box-shadow",
        "border-radius", "opacity", "user-select", "appearance"
    };

    for (const QString& property : properties_to_prefix) {
        QRegularExpression regex(QString("(\\s*)%1\\s*:\\s*([^;]+);").arg(property));
        QRegularExpressionMatchIterator it = regex.globalMatch(prefixed);

        QStringList replacements;
        while (it.hasNext()) {
            QRegularExpressionMatch match = it.next();
            QString indent = match.captured(1);
            QString value = match.captured(2);

            QString replacement = QString("%1-webkit-%2: %3;\n%1-moz-%2: %3;\n%1%2: %3;")
                                .arg(indent, property, value);
            replacements.append(replacement);
        }

        // Apply replacements
        for (const QString& replacement : replacements) {
            prefixed.replace(regex, replacement);
        }
    }

    return prefixed;
}

QString CSSProcessor::substituteVariables(const QString& css_content, const QVariantMap& variables) const {
    QString substituted = css_content;

    for (auto it = variables.begin(); it != variables.end(); ++it) {
        QString variable_name = it.key();
        QString variable_value = it.value().toString();

        // Support both CSS custom properties and simple variable substitution
        QRegularExpression css_var_regex(QString("var\\(\\s*--%1\\s*\\)").arg(variable_name));
        QRegularExpression simple_var_regex(QString("\\$%1\\b").arg(variable_name));

        substituted.replace(css_var_regex, variable_value);
        substituted.replace(simple_var_regex, variable_value);
    }

    return substituted;
}

QString CSSProcessor::processImports(const QString& css_content, const QString& base_path) const {
    QString processed = css_content;
    QRegularExpressionMatchIterator it = css_import_regex_.globalMatch(css_content);

    while (it.hasNext()) {
        QRegularExpressionMatch match = it.next();
        QString import_statement = match.captured(0);
        QString import_path = match.captured(1);

        // Resolve relative path
        QString full_path = QDir(base_path).absoluteFilePath(import_path);

        if (QFileInfo::exists(full_path)) {
            QString imported_content = IFormatProcessor::readFileContent(full_path);
            if (!imported_content.isEmpty()) {
                // Recursively process imports in the imported file
                QString imported_dir = QFileInfo(full_path).absolutePath();
                imported_content = processImports(imported_content, imported_dir);

                processed.replace(import_statement, imported_content);
            }
        } else {
            qWarning() << "CSS import not found:" << full_path;
        }
    }

    return processed;
}

bool CSSProcessor::validateCSSContent(const QString& css_content) const {
    // Basic CSS validation
    QStringList errors = findCSSErrors(css_content);
    return errors.isEmpty();
}

QStringList CSSProcessor::findCSSErrors(const QString& css_content) const {
    QStringList errors;

    // Check for balanced braces
    int brace_count = 0;
    for (const QChar& ch : css_content) {
        if (ch == '{') brace_count++;
        else if (ch == '}') brace_count--;

        if (brace_count < 0) {
            errors.append("Unmatched closing brace");
            break;
        }
    }

    if (brace_count > 0) {
        errors.append("Unmatched opening brace");
    }

    // Check for basic syntax errors
    if (css_content.contains(";;")) {
        errors.append("Double semicolon found");
    }

    return errors;
}

bool CSSProcessor::injectCSSToWidget(QWidget* widget, const QString& css_content, const QString& identifier) {
    if (!widget) return false;

    QString current_stylesheet = widget->styleSheet();
    QString updated_stylesheet = generateStyleSheetUpdate(current_stylesheet, css_content, identifier);

    widget->setStyleSheet(updated_stylesheet);
    return true;
}

bool CSSProcessor::injectCSSToApplication(QApplication* app, const QString& css_content, const QString& identifier) {
    if (!app) return false;

    QString current_stylesheet = app->styleSheet();
    QString updated_stylesheet = generateStyleSheetUpdate(current_stylesheet, css_content, identifier);

    app->setStyleSheet(updated_stylesheet);
    return true;
}

bool CSSProcessor::injectCSSGlobally(const QString& css_content, const QString& identifier) {
    // For global injection, we'll use the application stylesheet
    return injectCSSToApplication(qApp, css_content, identifier);
}

QString CSSProcessor::generateStyleSheetUpdate(const QString& existing_stylesheet, const QString& new_css, const QString& identifier) const {
    QString marker_start = createInjectionMarker(identifier + "_START");
    QString marker_end = createInjectionMarker(identifier + "_END");

    // Remove existing injection if present
    QString cleaned_stylesheet = removeStyleSheetSection(existing_stylesheet, identifier);

    // Add new injection
    QString updated_stylesheet = cleaned_stylesheet;
    if (!updated_stylesheet.isEmpty() && !updated_stylesheet.endsWith('\n')) {
        updated_stylesheet += '\n';
    }

    updated_stylesheet += marker_start + '\n';
    updated_stylesheet += new_css + '\n';
    updated_stylesheet += marker_end + '\n';

    return updated_stylesheet;
}

QString CSSProcessor::removeStyleSheetSection(const QString& stylesheet, const QString& identifier) const {
    QString marker_start = createInjectionMarker(identifier + "_START");
    QString marker_end = createInjectionMarker(identifier + "_END");

    QRegularExpression section_regex(
        QString("%1.*?%2").arg(QRegularExpression::escape(marker_start), QRegularExpression::escape(marker_end)),
        QRegularExpression::DotMatchesEverythingOption
    );

    QString cleaned = stylesheet;
    cleaned.remove(section_regex);

    return cleaned;
}

void CSSProcessor::initializeRegexPatterns() {
    css_rule_regex_ = QRegularExpression(R"([^{}]+\{[^{}]*\})");
    css_property_regex_ = QRegularExpression(R"(([^:]+):\s*([^;]+);?)");
    css_import_regex_ = QRegularExpression(R"(@import\s+['""]([^'""]+)['""];?)");
    css_variable_regex_ = QRegularExpression(R"(var\(\s*--([^)]+)\s*\))");
}

QString CSSProcessor::createInjectionMarker(const QString& identifier) const {
    return QString("/* DECLARATIVE_UI_CSS_INJECTION_%1 */").arg(identifier);
}

QPair<QString, QString> CSSProcessor::extractInjectionMarkers() const {
    return qMakePair(
        QString("/* DECLARATIVE_UI_CSS_INJECTION_"),
        QString(" */")
    );
}

// CSSInjectionGuard implementation
CSSInjectionGuard::CSSInjectionGuard(CSSProcessor* processor, const QString& identifier)
    : processor_(processor), identifier_(identifier), auto_cleanup_(true) {
}

CSSInjectionGuard::~CSSInjectionGuard() {
    if (auto_cleanup_ && processor_) {
        processor_->removeInjectedCSS(identifier_);
    }
}

bool CSSInjectionGuard::inject(const QString& css_content) {
    return processor_ ? processor_->injectCSS(css_content, identifier_) : false;
}

void CSSInjectionGuard::release() {
    auto_cleanup_ = false;
}

} // namespace DeclarativeUI::HotReload::FormatSupport
