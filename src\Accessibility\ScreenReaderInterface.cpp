#include "ScreenReaderInterface.hpp"
#include <QApplication>
#include <QWidget>
#include <QDebug>
#include <QJsonDocument>
#include <QAccessibleEvent>
#include <QTimer>
#include <QSettings>
#include <QRegularExpression>

#ifdef Q_OS_WIN
#include <windows.h>
#include <sapi.h>
#elif defined(Q_OS_MACOS)
#include <ApplicationServices/ApplicationServices.h>
#elif defined(Q_OS_LINUX)
#include <speech-dispatcher/libspeechd.h>
#endif

namespace DeclarativeUI::Accessibility {

// ScreenReaderSettings implementation
QJsonObject ScreenReaderSettings::toJson() const {
    QJsonObject obj;
    obj["enabled"] = enabled;
    obj["speechEnabled"] = speechEnabled;
    obj["brailleEnabled"] = brailleEnabled;
    obj["speechRate"] = speechRate;
    obj["speechVolume"] = speechVolume;
    obj["voice"] = voice;
    obj["language"] = language;
    obj["announceKeystrokes"] = announceKeystrokes;
    obj["announceCharacters"] = announceCharacters;
    obj["announceWords"] = announceWords;
    obj["announcePunctuation"] = announcePunctuation;
    obj["verboseMode"] = verboseMode;
    obj["announcementDelay"] = announcementDelay;
    obj["maxAnnouncementLength"] = maxAnnouncementLength;
    return obj;
}

ScreenReaderSettings ScreenReaderSettings::fromJson(const QJsonObject& json) {
    ScreenReaderSettings settings;
    settings.enabled = json["enabled"].toBool();
    settings.speechEnabled = json["speechEnabled"].toBool(true);
    settings.brailleEnabled = json["brailleEnabled"].toBool();
    settings.speechRate = json["speechRate"].toDouble(1.0);
    settings.speechVolume = json["speechVolume"].toDouble(0.8);
    settings.voice = json["voice"].toString();
    settings.language = json["language"].toString("en-US");
    settings.announceKeystrokes = json["announceKeystrokes"].toBool();
    settings.announceCharacters = json["announceCharacters"].toBool();
    settings.announceWords = json["announceWords"].toBool(true);
    settings.announcePunctuation = json["announcePunctuation"].toBool();
    settings.verboseMode = json["verboseMode"].toBool();
    settings.announcementDelay = json["announcementDelay"].toInt(100);
    settings.maxAnnouncementLength = json["maxAnnouncementLength"].toInt(500);
    return settings;
}

// ScreenReaderAccessibleInterface implementation
ScreenReaderAccessibleInterface::ScreenReaderAccessibleInterface(QWidget* widget)
    : widget_(widget)
{
}

bool ScreenReaderAccessibleInterface::isValid() const {
    return widget_ && !widget_->isHidden();
}

QObject* ScreenReaderAccessibleInterface::object() const {
    return widget_;
}

QWindow* ScreenReaderAccessibleInterface::window() const {
    return widget_ ? widget_->window()->windowHandle() : nullptr;
}

QRect ScreenReaderAccessibleInterface::rect() const {
    if (!widget_) return QRect();
    
    QPoint globalPos = widget_->mapToGlobal(QPoint(0, 0));
    return QRect(globalPos, widget_->size());
}

QString ScreenReaderAccessibleInterface::text(QAccessible::Text t) const {
    if (!widget_) return QString();
    
    // Check cache first
    if (cached_text_.contains(t)) {
        return cached_text_[t];
    }
    
    QString result;
    
    switch (t) {
        case QAccessible::Name:
            result = widget_->accessibleName();
            if (result.isEmpty()) {
                result = widget_->objectName();
            }
            if (result.isEmpty() && widget_->property("text").isValid()) {
                result = widget_->property("text").toString();
            }
            break;
            
        case QAccessible::Description:
            result = widget_->accessibleDescription();
            if (result.isEmpty()) {
                result = widget_->toolTip();
            }
            break;
            
        case QAccessible::Value:
            if (widget_->property("value").isValid()) {
                result = widget_->property("value").toString();
            }
            break;
            
        case QAccessible::Help:
            result = widget_->whatsThis();
            break;
            
        default:
            break;
    }
    
    // Cache the result
    cached_text_[t] = result;
    return result;
}

QAccessible::Role ScreenReaderAccessibleInterface::role() const {
    if (!widget_) return QAccessible::NoRole;
    
    // Try to determine role from widget type
    QString className = widget_->metaObject()->className();
    
    if (className.contains("Button")) {
        return QAccessible::Button;
    } else if (className.contains("Edit") || className.contains("LineEdit")) {
        return QAccessible::EditableText;
    } else if (className.contains("Label")) {
        return QAccessible::StaticText;
    } else if (className.contains("List")) {
        return QAccessible::List;
    } else if (className.contains("Tree")) {
        return QAccessible::Tree;
    } else if (className.contains("Table")) {
        return QAccessible::Table;
    } else if (className.contains("Tab")) {
        return QAccessible::PageTabList;
    } else if (className.contains("Menu")) {
        return QAccessible::MenuBar;
    } else if (className.contains("Dialog")) {
        return QAccessible::Dialog;
    }
    
    return QAccessible::Client;
}

QAccessible::State ScreenReaderAccessibleInterface::state() const {
    QAccessible::State state;
    
    if (!widget_) return state;
    
    if (!widget_->isEnabled()) {
        state.disabled = true;
    }
    
    if (!widget_->isVisible()) {
        state.invisible = true;
    }
    
    if (widget_->hasFocus()) {
        state.focused = true;
    }
    
    if (widget_->property("checked").isValid()) {
        state.checkable = true;
        state.checked = widget_->property("checked").toBool();
    }
    
    if (widget_->property("readOnly").toBool()) {
        state.readOnly = true;
    }
    
    return state;
}

QStringList ScreenReaderAccessibleInterface::actionNames() const {
    QStringList actions;
    
    if (!widget_) return actions;
    
    // Standard actions based on widget type
    if (role() == QAccessible::Button) {
        actions << "Press";
    }
    
    if (role() == QAccessible::EditableText) {
        actions << "SetFocus" << "Select";
    }
    
    // Check for custom actions
    QVariant customActions = widget_->property("accessibleActions");
    if (customActions.isValid()) {
        actions.append(customActions.toStringList());
    }
    
    return actions;
}

void ScreenReaderAccessibleInterface::doAction(const QString& actionName) {
    if (!widget_) return;
    
    if (actionName == "Press" && role() == QAccessible::Button) {
        // Simulate button click
        QMetaObject::invokeMethod(widget_, "click", Qt::QueuedConnection);
    } else if (actionName == "SetFocus") {
        widget_->setFocus();
    } else if (actionName == "Select" && role() == QAccessible::EditableText) {
        QMetaObject::invokeMethod(widget_, "selectAll", Qt::QueuedConnection);
    }
    
    // Emit custom action signal if available
    QMetaObject::invokeMethod(widget_, "accessibleActionTriggered",
                             Qt::QueuedConnection,
                             Q_ARG(QString, actionName));
}

QStringList ScreenReaderAccessibleInterface::keyBindingsForAction(const QString& actionName) const {
    QStringList bindings;

    if (actionName == "Press") {
        bindings << "Space" << "Enter";
    } else if (actionName == "SetFocus") {
        bindings << "Tab";
    } else if (actionName == "Select") {
        bindings << "Ctrl+A";
    }

    return bindings;
}

QAccessibleInterface* ScreenReaderAccessibleInterface::childAt(int x, int y) const {
    if (!widget_) return nullptr;

    QWidget* child = widget_->childAt(x, y);
    if (child && child != widget_) {
        return QAccessible::queryAccessibleInterface(child);
    }

    return nullptr;
}

QAccessibleInterface* ScreenReaderAccessibleInterface::parent() const {
    if (!widget_ || !widget_->parentWidget()) return nullptr;
    return QAccessible::queryAccessibleInterface(widget_->parentWidget());
}

QAccessibleInterface* ScreenReaderAccessibleInterface::child(int index) const {
    if (!widget_) return nullptr;

    QList<QWidget*> children = widget_->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
    if (index >= 0 && index < children.size()) {
        return QAccessible::queryAccessibleInterface(children[index]);
    }

    return nullptr;
}

int ScreenReaderAccessibleInterface::childCount() const {
    if (!widget_) return 0;
    return widget_->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly).size();
}

int ScreenReaderAccessibleInterface::indexOfChild(const QAccessibleInterface* child) const {
    if (!widget_ || !child) return -1;

    QWidget* childWidget = qobject_cast<QWidget*>(child->object());
    if (!childWidget) return -1;

    QList<QWidget*> children = widget_->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
    return children.indexOf(childWidget);
}

void ScreenReaderAccessibleInterface::setText(QAccessible::Text t, const QString& text) {
    if (!widget_) return;

    switch (t) {
        case QAccessible::Name:
            widget_->setAccessibleName(text);
            break;
        case QAccessible::Description:
            widget_->setAccessibleDescription(text);
            break;
        default:
            break;
    }

    // Clear cache for this text type
    cached_text_.remove(t);
}

QColor ScreenReaderAccessibleInterface::foregroundColor() const {
    if (!widget_) return QColor();
    return widget_->palette().color(QPalette::WindowText);
}

QColor ScreenReaderAccessibleInterface::backgroundColor() const {
    if (!widget_) return QColor();
    return widget_->palette().color(QPalette::Window);
}

QVector<QPair<QAccessibleInterface*, QAccessible::Relation>> ScreenReaderAccessibleInterface::relations(QAccessible::Relation match) const {
    Q_UNUSED(match)
    // Stub implementation - would need to track widget relationships
    return QVector<QPair<QAccessibleInterface*, QAccessible::Relation>>();
}

QAccessibleInterface* ScreenReaderAccessibleInterface::focusChild() const {
    if (!widget_) return nullptr;

    QWidget* focusWidget = widget_->focusWidget();
    if (focusWidget && focusWidget != widget_) {
        return QAccessible::queryAccessibleInterface(focusWidget);
    }

    return nullptr;
}

QString ScreenReaderAccessibleInterface::getDetailedDescription() const {
    if (!widget_) return QString();
    
    QStringList parts;
    
    // Role
    QString roleName = "element";
    switch (role()) {
        case QAccessible::Button: roleName = "button"; break;
        case QAccessible::EditableText: roleName = "text field"; break;
        case QAccessible::StaticText: roleName = "text"; break;
        case QAccessible::List: roleName = "list"; break;
        default: break;
    }
    parts << roleName;
    
    // Name
    QString name = text(QAccessible::Name);
    if (!name.isEmpty()) {
        parts << name;
    }
    
    // State
    QAccessible::State state = this->state();
    if (state.disabled) parts << "disabled";
    if (state.checked) parts << "checked";
    if (state.focused) parts << "focused";
    
    // Value
    QString value = text(QAccessible::Value);
    if (!value.isEmpty()) {
        parts << "value:" << value;
    }
    
    // Description
    QString desc = text(QAccessible::Description);
    if (!desc.isEmpty()) {
        parts << desc;
    }
    
    return parts.join(" ");
}

// ScreenReaderInterface implementation
ScreenReaderInterface::ScreenReaderInterface(QObject* parent)
    : QObject(parent)
{
    // Initialize announcement timer
    announcement_timer_ = new QTimer(this);
    announcement_timer_->setSingleShot(true);
    connect(announcement_timer_, &QTimer::timeout, this, &ScreenReaderInterface::processAnnouncementQueue);
    
    // Connect to application focus changes
    if (auto app = qobject_cast<QApplication*>(QApplication::instance())) {
        connect(app, &QApplication::focusChanged,
                this, &ScreenReaderInterface::onApplicationFocusChanged);
    }
    
    // Initialize speech engine
    initializeSpeechEngine();
    
    // Setup accessibility hooks
    setupAccessibilityHooks();
    
    qDebug() << "🔊 ScreenReaderInterface initialized";
}

ScreenReaderInterface::~ScreenReaderInterface() {
    // Cleanup speech engine
    if (speech_engine_) {
#ifdef Q_OS_WIN
        // Windows SAPI cleanup would go here
#elif defined(Q_OS_LINUX)
        // Speech dispatcher cleanup would go here
#endif
        speech_engine_ = nullptr;
    }
}

void ScreenReaderInterface::setEnabled(bool enabled) {
    settings_.enabled = enabled;
    
    if (enabled) {
        announce("Screen reader enabled", AnnouncementType::System, AnnouncementPriority::High);
    } else {
        stopSpeaking();
    }
    
    emit settingsChanged(settings_);
    qDebug() << "🔊 Screen reader" << (enabled ? "enabled" : "disabled");
}

void ScreenReaderInterface::announce(const QString& text, 
                                   AnnouncementType type,
                                   AnnouncementPriority priority,
                                   QWidget* source) {
    if (!settings_.enabled || text.isEmpty()) return;
    
    Announcement announcement;
    announcement.text = text;
    announcement.type = type;
    announcement.priority = priority;
    announcement.source = source;
    announcement.timestamp = QDateTime::currentMSecsSinceEpoch();
    announcement.language = settings_.language;
    
    // Check if this should interrupt current speech
    announcement.interrupt = (priority >= AnnouncementPriority::High);
    
    queueAnnouncement(announcement);
}

void ScreenReaderInterface::announceWidget(QWidget* widget, bool detailed) {
    if (!widget || !settings_.enabled) return;
    
    QString text = analyzeWidget(widget, detailed);
    if (!text.isEmpty()) {
        announce(text, AnnouncementType::Navigation, AnnouncementPriority::Normal, widget);
    }
}

void ScreenReaderInterface::announceNavigation(QWidget* from, QWidget* to) {
    if (!to || !settings_.enabled) return;
    
    QString text;
    if (settings_.verboseMode) {
        text = QString("Navigated to %1").arg(analyzeWidget(to, true));
    } else {
        text = analyzeWidget(to, false);
    }
    
    announce(text, AnnouncementType::Navigation, AnnouncementPriority::Normal, to);
}

void ScreenReaderInterface::initializeSpeechEngine() {
    speech_available_ = false;
    
#ifdef Q_OS_WIN
    // Initialize Windows SAPI
    // This would require linking against SAPI libraries
    qDebug() << "🔊 Windows SAPI speech engine would be initialized here";
    speech_available_ = true;
#elif defined(Q_OS_MACOS)
    // Initialize macOS speech synthesis
    qDebug() << "🔊 macOS speech synthesis would be initialized here";
    speech_available_ = true;
#elif defined(Q_OS_LINUX)
    // Initialize Speech Dispatcher
    qDebug() << "🔊 Speech Dispatcher would be initialized here";
    speech_available_ = true;
#else
    qDebug() << "🔊 No speech engine available for this platform";
#endif
}

void ScreenReaderInterface::setupAccessibilityHooks() {
    // Install accessibility factory for enhanced interfaces
    QAccessible::installFactory([](const QString& classname, QObject* object) -> QAccessibleInterface* {
        QWidget* widget = qobject_cast<QWidget*>(object);
        if (widget) {
            return new ScreenReaderAccessibleInterface(widget);
        }
        return nullptr;
    });
}

void ScreenReaderInterface::queueAnnouncement(const Announcement& announcement) {
    // Check if we should interrupt current speech
    if (announcement.interrupt && currently_speaking_) {
        stopSpeaking();
        announcement_queue_.clear();
    }
    
    // Add to queue
    announcement_queue_.enqueue(announcement);
    emit announcementQueued(announcement);
    
    // Start processing if not already running
    if (!announcement_timer_->isActive()) {
        announcement_timer_->start(settings_.announcementDelay);
    }
}

void ScreenReaderInterface::processAnnouncementQueue() {
    if (announcement_queue_.isEmpty() || currently_speaking_) {
        return;
    }
    
    Announcement announcement = announcement_queue_.dequeue();
    current_announcement_ = announcement;
    
    QString processedText = preprocessText(announcement.text);
    
    if (settings_.speechEnabled && speech_available_) {
        speakText(processedText);
    }
    
    emit announcementStarted(processedText);
    
    // Schedule next announcement
    if (!announcement_queue_.isEmpty()) {
        announcement_timer_->start(settings_.announcementDelay);
    }
}

void ScreenReaderInterface::speakText(const QString& text) {
    if (!speech_available_ || text.isEmpty()) return;
    
    currently_speaking_ = true;
    
#ifdef Q_OS_WIN
    // Windows SAPI speech implementation would go here
    qDebug() << "🔊 Speaking (Windows):" << text;
#elif defined(Q_OS_MACOS)
    // macOS speech implementation would go here
    qDebug() << "🔊 Speaking (macOS):" << text;
#elif defined(Q_OS_LINUX)
    // Speech Dispatcher implementation would go here
    qDebug() << "🔊 Speaking (Linux):" << text;
#endif
    
    // Simulate speech completion after a delay
    QTimer::singleShot(text.length() * 50, this, [this]() {
        currently_speaking_ = false;
        emit announcementFinished(current_announcement_.text);
        
        // Process next announcement
        if (!announcement_queue_.isEmpty()) {
            announcement_timer_->start(settings_.announcementDelay);
        }
    });
}

void ScreenReaderInterface::stopSpeaking() {
    if (!currently_speaking_) return;
    
    currently_speaking_ = false;
    
#ifdef Q_OS_WIN
    // Stop Windows SAPI speech
#elif defined(Q_OS_MACOS)
    // Stop macOS speech
#elif defined(Q_OS_LINUX)
    // Stop Speech Dispatcher
#endif
    
    qDebug() << "🔊 Speech stopped";
}

QString ScreenReaderInterface::preprocessText(const QString& text) const {
    QString processed = text;
    
    // Limit length
    if (processed.length() > settings_.maxAnnouncementLength) {
        processed = processed.left(settings_.maxAnnouncementLength) + "...";
    }
    
    // Clean up text
    processed = cleanupText(processed);
    
    // Expand abbreviations if needed
    if (settings_.verboseMode) {
        processed = expandAbbreviations(processed);
    }
    
    return processed;
}

QString ScreenReaderInterface::cleanupText(const QString& text) const {
    QString cleaned = text;
    
    // Remove excessive whitespace
    cleaned = cleaned.simplified();
    
    // Handle punctuation based on settings
    if (!settings_.announcePunctuation) {
        cleaned.remove(QRegularExpression("[.,;:!?]"));
    }
    
    return cleaned;
}

QString ScreenReaderInterface::expandAbbreviations(const QString& text) const {
    QString expanded = text;
    
    // Common abbreviations
    expanded.replace("btn", "button", Qt::CaseInsensitive);
    expanded.replace("txt", "text", Qt::CaseInsensitive);
    expanded.replace("lbl", "label", Qt::CaseInsensitive);
    expanded.replace("chk", "checkbox", Qt::CaseInsensitive);
    
    return expanded;
}

QString ScreenReaderInterface::analyzeWidget(QWidget* widget, bool detailed) const {
    if (!widget) return QString();
    
    ScreenReaderAccessibleInterface accessible_interface(widget);

    if (detailed) {
        return accessible_interface.getDetailedDescription();
    } else {
        QString name = accessible_interface.text(QAccessible::Name);
        if (name.isEmpty()) {
            name = "unnamed element";
        }
        return name;
    }
}

void ScreenReaderInterface::onApplicationFocusChanged(QWidget* old, QWidget* now) {
    if (now && settings_.enabled) {
        announceNavigation(old, now);
    }
}

void ScreenReaderInterface::onWidgetDestroyed(QObject* obj) {
    QWidget* widget = static_cast<QWidget*>(obj);
    
    // Remove from live regions
    live_regions_.remove(widget);
    
    // Remove from tracking maps
    widget_descriptions_.remove(widget);
    widget_roles_.remove(widget);
    widget_states_.remove(widget);
    widget_values_.remove(widget);
}

bool ScreenReaderInterface::isScreenReaderActive() {
    return QAccessible::isActive();
}

QString ScreenReaderInterface::getActiveScreenReader() {
#ifdef Q_OS_WIN
    // Check for common Windows screen readers
    if (FindWindow(L"JAWS", nullptr)) return "JAWS";
    if (FindWindow(L"NVDA_controllerClient", nullptr)) return "NVDA";
    // Check for Windows Narrator process
    if (FindWindow(L"Windows.UI.Core.CoreWindow", L"Narrator")) return "Windows Narrator";
#elif defined(Q_OS_MACOS)
    // Check for VoiceOver
    return "VoiceOver";
#elif defined(Q_OS_LINUX)
    // Check for Orca
    return "Orca";
#endif

    return "Unknown";
}

void ScreenReaderInterface::onSpeechStateChanged(int state) {
    qDebug() << "🔊 Speech state changed:" << state;

    // Handle speech state changes
    switch (state) {
        case 0: // Stopped
            currently_speaking_ = false;
            break;
        case 1: // Speaking
            currently_speaking_ = true;
            break;
        case 2: // Paused
            // Handle pause state
            break;
        default:
            break;
    }

    emit speechStateChanged(state);
}

} // namespace DeclarativeUI::Accessibility
