{"type": "QWidget", "properties": {"windowTitle": "06 - Basic Components | DeclarativeUI", "minimumSize": [500, 450]}, "layout": {"type": "QVBoxLayout", "properties": {"spacing": 20, "contentsMargins": [20, 20, 20, 20]}, "children": [{"type": "QLabel", "properties": {"text": "🧱 Basic Components Showcase", "alignment": "AlignCenter", "styleSheet": "font-size: 20px; font-weight: bold; color: #2c3e50;"}}, {"type": "QLabel", "properties": {"text": "This example demonstrates the three most fundamental UI components:\n• Label - for displaying text\n• LineEdit - for text input\n• Button - for user actions", "alignment": "AlignCenter", "wordWrap": true, "styleSheet": "color: #7f8c8d; margin-bottom: 10px;"}}, {"type": "QGroupBox", "properties": {"title": "📝 Text Input"}, "layout": {"type": "QVBoxLayout", "children": [{"type": "QLabel", "properties": {"text": "Enter a message:"}}, {"type": "QLineEdit", "properties": {"objectName": "messageInput", "placeholderText": "Type your message here..."}}, {"type": "QLabel", "properties": {"objectName": "charCount", "text": "Characters: 0", "styleSheet": "color: #7f8c8d; font-size: 12px;"}}]}}, {"type": "QGroupBox", "properties": {"title": "📢 Message Display"}, "layout": {"type": "QVBoxLayout", "children": [{"type": "QLabel", "properties": {"objectName": "messageDisplay", "text": "💬 Enter a message above...", "alignment": "AlignCenter", "wordWrap": true, "styleSheet": "color: #2c3e50; background-color: #ecf0f1; padding: 15px; border-radius: 8px; font-size: 14px;"}}]}}, {"type": "QGroupBox", "properties": {"title": "🔘 Actions"}, "layout": {"type": "QHBoxLayout", "children": [{"type": "QPushButton", "properties": {"objectName": "submitButton", "text": "📤 Submit Message", "enabled": false, "styleSheet": "QPushButton { background-color: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold; } QPushButton:hover { background-color: #2980b9; } QPushButton:disabled { background-color: #bdc3c7; }"}, "events": {"clicked": "submitMessage"}}, {"type": "QPushButton", "properties": {"text": "🗑️ Clear", "styleSheet": "QPushButton { background-color: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; } QPushButton:hover { background-color: #c0392b; }"}, "events": {"clicked": "clearFields"}}, {"type": "QPushButton", "properties": {"text": "🎨 Change Style", "styleSheet": "QPushButton { background-color: #9b59b6; color: white; padding: 10px 20px; border: none; border-radius: 5px; } QPushButton:hover { background-color: #8e44ad; }"}, "events": {"clicked": "changeStyle"}}, {"type": "spacer", "properties": {"orientation": "horizontal"}}]}}, {"type": "spacer", "properties": {"orientation": "vertical"}}, {"type": "QLabel", "properties": {"text": "💡 These are the building blocks of all DeclarativeUI applications", "alignment": "AlignCenter", "styleSheet": "font-size: 12px; color: #7f8c8d; font-style: italic;"}}]}}