# Contributing to DeclarativeUI Framework

We welcome contributions to the DeclarativeUI Framework! This document provides guidelines for contributing to the project.

## 🚀 Getting Started

### Prerequisites

- **Qt6** (6.2 or later) with Core, Widgets, Network, and Test modules
- **CMake** 3.20 or higher
- **C++20 compatible compiler** (GCC 10+, Clang 12+, MSVC 2019+)
- **Git** for version control

### Setting Up Development Environment

1. **Fork and clone the repository**:

   ```bash
   git clone https://github.com/your-username/qt-hotload-test.git
   cd qt-hotload-test
   ```

2. **Build the project**:

   ```bash
   # Windows
   build.bat --debug
   
   # Linux/macOS
   mkdir build && cd build
   cmake --preset=debug ..
   cmake --build . --config Debug
   ```

3. **Run tests to verify setup**:

   ```bash
   ctest --config Debug --output-on-failure
   ```

## 📋 How to Contribute

### Reporting Issues

- Use the [GitHub Issues](https://github.com/your-username/qt-hotload-test/issues) page
- Search existing issues before creating a new one
- Provide detailed information including:
  - Operating system and version
  - Qt version
  - Compiler version
  - Steps to reproduce
  - Expected vs actual behavior

### Submitting Changes

1. **Create a feature branch**:

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**:
   - Follow the coding standards (see below)
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**:

   ```bash
   # Run all tests
   ctest --output-on-failure
   
   # Run specific test categories
   ctest -R "unit" --output-on-failure
   ctest -R "command" --output-on-failure
   ```

4. **Commit your changes**:

   ```bash
   git add .
   git commit -m "feat: add new component feature"
   ```

5. **Push and create a pull request**:

   ```bash
   git push origin feature/your-feature-name
   ```

## 🎯 Development Guidelines

### Code Style

- **Modern C++20**: Use auto, range-based loops, smart pointers
- **RAII**: All resources managed automatically
- **Const correctness**: Mark methods const when possible
- **Exception safety**: Use RAII and proper exception handling
- **Documentation**: Document public APIs with Doxygen comments

### Commit Message Format

Use conventional commits format:

```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Examples:

- `feat(components): add new DatePicker component`
- `fix(hotreload): resolve file watching issue on Windows`
- `docs(api): update component documentation`

### Adding New Components

1. Create component files in `src/Components/`
2. Implement fluent interface methods
3. Add to component registry for JSON support
4. Create comprehensive tests
5. Add example usage
6. Update documentation

### Adding New Examples

1. Choose appropriate category (`basic/`, `components/`, `command/`, `advanced/`)
2. Create example file with clear comments
3. Register in CMakeLists.txt
4. Test on multiple platforms

### Testing Requirements

- **Unit tests** for all new functionality
- **Integration tests** for cross-component features
- **Performance tests** for optimization changes
- **Memory safety** validation with tools like Valgrind

## 📚 Documentation

- Update relevant documentation in `docs/`
- Follow existing documentation structure
- Include code examples
- Update API reference if needed

## 🔍 Code Review Process

1. All changes require review via pull request
2. Automated tests must pass
3. Code style checks must pass
4. At least one maintainer approval required
5. Documentation updates reviewed

## 🤝 Community Guidelines

- Be respectful and inclusive
- Help others learn and grow
- Provide constructive feedback
- Follow the [Code of Conduct](CODE_OF_CONDUCT.md)

## 📞 Getting Help

- Check the [documentation](docs/)
- Review [examples](examples/)
- Ask questions in GitHub Discussions
- Join our community chat

## 🏆 Recognition

Contributors are recognized in:

- CHANGELOG.md for significant contributions
- README.md contributors section
- Release notes for major features

Thank you for contributing to DeclarativeUI Framework! 🎉
