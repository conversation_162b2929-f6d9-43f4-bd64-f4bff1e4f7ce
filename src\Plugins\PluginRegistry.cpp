#include "PluginRegistry.hpp"
#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>
#include <algorithm>

namespace DeclarativeUI::Plugins {

PluginRegistry* PluginRegistry::instance_ = nullptr;

PluginRegistry& PluginRegistry::instance() {
    if (!instance_) {
        instance_ = new PluginRegistry();
    }
    return *instance_;
}

PluginRegistry::PluginRegistry(QObject* parent)
    : QObject(parent)
{
}

PluginRegistry::~PluginRegistry() {
    unregisterAllPlugins();
}

bool PluginRegistry::registerPlugin(std::unique_ptr<IPlugin> plugin, const QString& filePath) {
    if (!plugin) {
        setError("Cannot register null plugin");
        return false;
    }
    
    if (!validatePluginInterface(plugin.get())) {
        setError("Plugin failed interface validation");
        return false;
    }
    
    PluginMetadata metadata = plugin->getMetadata();
    QString pluginName = metadata.name;
    
    if (isPluginRegistered(pluginName)) {
        setError("Plugin already registered: " + pluginName);
        return false;
    }
    
    if (checkPluginConflicts(pluginName)) {
        setError("Plugin conflicts with existing plugins: " + pluginName);
        return false;
    }
    
    // Create registry entry
    auto entry = new PluginRegistryEntry();
    entry->metadata = metadata;
    entry->capabilities = plugin->getCapabilities();
    entry->filePath = filePath;
    entry->configuration = plugin->getDefaultConfiguration();
    
    // Connect plugin signals
    connect(plugin.get(), &IPlugin::stateChanged,
            this, &PluginRegistry::onPluginStateChanged);
    connect(plugin.get(), &IPlugin::errorOccurred,
            this, &PluginRegistry::onPluginError);
    connect(plugin.get(), &IPlugin::configurationChanged,
            this, &PluginRegistry::onPluginConfigurationChanged);
    
    // Store plugin
    entry->plugin = std::move(plugin);
    IPlugin* pluginPtr = entry->plugin.get();
    plugins_.insert(pluginName, entry);
    
    // Register capabilities
    if (entry->capabilities.testFlag(PluginCapability::Components)) {
        if (auto* componentPlugin = qobject_cast<IComponentPlugin*>(pluginPtr)) {
            registerPluginComponents(pluginName, componentPlugin);
        }
    }
    
    if (entry->capabilities.testFlag(PluginCapability::Commands)) {
        if (auto* commandPlugin = qobject_cast<ICommandPlugin*>(pluginPtr)) {
            registerPluginCommands(pluginName, commandPlugin);
        }
    }
    
    if (entry->capabilities.testFlag(PluginCapability::Themes)) {
        if (auto* themePlugin = qobject_cast<IThemePlugin*>(pluginPtr)) {
            registerPluginThemes(pluginName, themePlugin);
        }
    }
    
    buildDependencyGraph();
    
    emit pluginRegistered(pluginName, pluginPtr);
    qDebug() << "🔌 Plugin registered:" << pluginName;
    
    return true;
}

bool PluginRegistry::unregisterPlugin(const QString& pluginName) {
    auto it = plugins_.find(pluginName);
    if (it == plugins_.end()) {
        setError("Plugin not registered: " + pluginName);
        return false;
    }
    
    auto& entry = it.value();
    
    // Check if other plugins depend on this one
    QStringList dependents = getPluginDependents(pluginName);
    if (!dependents.isEmpty()) {
        setError("Cannot unregister plugin with dependents: " + dependents.join(", "));
        return false;
    }
    
    // Deactivate if active
    if (entry->isActive) {
        deactivatePlugin(pluginName);
    }
    
    // Unregister capabilities
    unregisterPluginComponents(pluginName);
    unregisterPluginCommands(pluginName);
    unregisterPluginThemes(pluginName);
    
    // Cleanup plugin
    if (entry->plugin) {
        entry->plugin->cleanup();
    }

    // Delete the entry and remove from map
    delete entry;
    plugins_.remove(it.key());
    buildDependencyGraph();
    
    emit pluginUnregistered(pluginName);
    qDebug() << "🔌 Plugin unregistered:" << pluginName;
    
    return true;
}

void PluginRegistry::unregisterAllPlugins() {
    QStringList pluginNames = plugins_.keys();
    
    // Sort by dependency order (dependents first)
    std::sort(pluginNames.begin(), pluginNames.end(), [this](const QString& a, const QString& b) {
        return getPluginDependents(a).size() > getPluginDependents(b).size();
    });
    
    for (const QString& name : pluginNames) {
        unregisterPlugin(name);
    }
}

bool PluginRegistry::isPluginRegistered(const QString& pluginName) const {
    return plugins_.contains(pluginName);
}

IPlugin* PluginRegistry::getPlugin(const QString& pluginName) const {
    auto it = plugins_.find(pluginName);
    return (it != plugins_.end()) ? it.value()->plugin.get() : nullptr;
}

QStringList PluginRegistry::getRegisteredPlugins() const {
    return plugins_.keys();
}

QStringList PluginRegistry::getActivePlugins() const {
    QStringList active;
    for (auto it = plugins_.begin(); it != plugins_.end(); ++it) {
        if (it.value()->isActive) {
            active.append(it.key());
        }
    }
    return active;
}

QStringList PluginRegistry::getPluginsByCapability(PluginCapability capability) const {
    QStringList plugins;
    for (auto it = plugins_.begin(); it != plugins_.end(); ++it) {
        if (it.value()->capabilities.testFlag(capability)) {
            plugins.append(it.key());
        }
    }
    return plugins;
}

PluginMetadata PluginRegistry::getPluginMetadata(const QString& pluginName) const {
    auto it = plugins_.find(pluginName);
    return (it != plugins_.end()) ? it.value()->metadata : PluginMetadata{};
}

PluginCapabilities PluginRegistry::getPluginCapabilities(const QString& pluginName) const {
    auto it = plugins_.find(pluginName);
    return (it != plugins_.end()) ? it.value()->capabilities : PluginCapabilities{};
}

PluginState PluginRegistry::getPluginState(const QString& pluginName) const {
    IPlugin* plugin = getPlugin(pluginName);
    return plugin ? plugin->getState() : PluginState::Unloaded;
}

QString PluginRegistry::getPluginFilePath(const QString& pluginName) const {
    auto it = plugins_.find(pluginName);
    return (it != plugins_.end()) ? it.value()->filePath : QString{};
}

bool PluginRegistry::activatePlugin(const QString& pluginName) {
    auto it = plugins_.find(pluginName);
    if (it == plugins_.end()) {
        setError("Plugin not registered: " + pluginName);
        return false;
    }
    
    auto& entry = it.value();
    if (entry->isActive) {
        return true; // Already active
    }
    
    // Check dependencies
    if (!checkDependencies(pluginName)) {
        setError("Plugin dependencies not satisfied: " + pluginName);
        return false;
    }
    
    // Activate plugin
    if (!entry->plugin->activate()) {
        setError("Plugin activation failed: " + entry->plugin->getLastError());
        return false;
    }
    
    entry->isActive = true;
    emit pluginActivated(pluginName);
    qDebug() << "🔌 Plugin activated:" << pluginName;
    
    return true;
}

bool PluginRegistry::deactivatePlugin(const QString& pluginName) {
    auto it = plugins_.find(pluginName);
    if (it == plugins_.end()) {
        setError("Plugin not registered: " + pluginName);
        return false;
    }
    
    auto& entry = it.value();
    if (!entry->isActive) {
        return true; // Already inactive
    }
    
    // Check dependents
    QStringList activeDependents;
    for (const QString& dependent : getPluginDependents(pluginName)) {
        if (isPluginActive(dependent)) {
            activeDependents.append(dependent);
        }
    }
    
    if (!activeDependents.isEmpty()) {
        setError("Cannot deactivate plugin with active dependents: " + activeDependents.join(", "));
        return false;
    }
    
    // Deactivate plugin
    if (!entry->plugin->deactivate()) {
        setError("Plugin deactivation failed: " + entry->plugin->getLastError());
        return false;
    }
    
    entry->isActive = false;
    emit pluginDeactivated(pluginName);
    qDebug() << "🔌 Plugin deactivated:" << pluginName;
    
    return true;
}

bool PluginRegistry::isPluginActive(const QString& pluginName) const {
    auto it = plugins_.find(pluginName);
    return (it != plugins_.end()) ? it.value()->isActive : false;
}

QStringList PluginRegistry::getAvailableComponents() const {
    return component_providers_.keys();
}

QString PluginRegistry::getComponentProvider(const QString& componentType) const {
    return component_providers_.value(componentType);
}

std::unique_ptr<QObject> PluginRegistry::createComponent(const QString& componentType) const {
    QString providerName = getComponentProvider(componentType);
    if (providerName.isEmpty()) {
        return nullptr;
    }
    
    IPlugin* plugin = getPlugin(providerName);
    if (!plugin || !isPluginActive(providerName)) {
        return nullptr;
    }
    
    auto* componentPlugin = qobject_cast<IComponentPlugin*>(plugin);
    if (!componentPlugin) {
        return nullptr;
    }
    
    return componentPlugin->createComponent(componentType);
}

void PluginRegistry::registerPluginComponents(const QString& pluginName, IComponentPlugin* componentPlugin) {
    QStringList components = componentPlugin->getProvidedComponents();
    for (const QString& componentType : components) {
        component_providers_[componentType] = pluginName;
        emit componentRegistered(componentType, pluginName);
    }
}

void PluginRegistry::registerPluginCommands(const QString& pluginName, ICommandPlugin* commandPlugin) {
    QStringList commands = commandPlugin->getProvidedCommands();
    for (const QString& commandType : commands) {
        command_providers_[commandType] = pluginName;
        emit commandRegistered(commandType, pluginName);
    }
}

void PluginRegistry::registerPluginThemes(const QString& pluginName, IThemePlugin* themePlugin) {
    QStringList themes = themePlugin->getProvidedThemes();
    for (const QString& themeName : themes) {
        theme_providers_[themeName] = pluginName;
        emit themeRegistered(themeName, pluginName);
    }
}

void PluginRegistry::unregisterPluginComponents(const QString& pluginName) {
    auto it = component_providers_.begin();
    while (it != component_providers_.end()) {
        if (it.value() == pluginName) {
            it = component_providers_.erase(it);
        } else {
            ++it;
        }
    }
}

void PluginRegistry::unregisterPluginCommands(const QString& pluginName) {
    auto it = command_providers_.begin();
    while (it != command_providers_.end()) {
        if (it.value() == pluginName) {
            it = command_providers_.erase(it);
        } else {
            ++it;
        }
    }
}

void PluginRegistry::unregisterPluginThemes(const QString& pluginName) {
    auto it = theme_providers_.begin();
    while (it != theme_providers_.end()) {
        if (it.value() == pluginName) {
            it = theme_providers_.erase(it);
        } else {
            ++it;
        }
    }
}

bool PluginRegistry::validatePluginInterface(IPlugin* plugin) const {
    if (!plugin) {
        return false;
    }
    
    PluginMetadata metadata = plugin->getMetadata();
    if (!metadata.isValid()) {
        return false;
    }
    
    // Additional validation can be added here
    return true;
}

bool PluginRegistry::checkPluginConflicts(const QString& pluginName) const {
    // Check for name conflicts
    return isPluginRegistered(pluginName);
}

void PluginRegistry::buildDependencyGraph() {
    dependency_graph_.clear();
    dependent_graph_.clear();
    
    for (auto it = plugins_.begin(); it != plugins_.end(); ++it) {
        const QString& pluginName = it.key();
        const QStringList dependencies = it.value()->plugin->getRequiredPlugins();
        
        dependency_graph_[pluginName] = dependencies;
        
        for (const QString& dep : dependencies) {
            dependent_graph_[dep].append(pluginName);
        }
    }
}

QStringList PluginRegistry::getPluginDependencies(const QString& pluginName) const {
    return dependency_graph_.value(pluginName);
}

QStringList PluginRegistry::getPluginDependents(const QString& pluginName) const {
    return dependent_graph_.value(pluginName);
}

bool PluginRegistry::checkDependencies(const QString& pluginName) const {
    QStringList dependencies = getPluginDependencies(pluginName);
    for (const QString& dep : dependencies) {
        if (!isPluginRegistered(dep) || !isPluginActive(dep)) {
            return false;
        }
    }
    return true;
}

void PluginRegistry::onPluginStateChanged(PluginState state) {
    Q_UNUSED(state)
    // Handle plugin state changes if needed
}

void PluginRegistry::onPluginError(const QString& error) {
    IPlugin* plugin = qobject_cast<IPlugin*>(sender());
    if (plugin) {
        QString pluginName = plugin->getMetadata().name;
        plugin_errors_.append(QString("%1: %2").arg(pluginName, error));
        emit pluginError(pluginName, error);
    }
}

void PluginRegistry::onPluginConfigurationChanged(const QJsonObject& config) {
    IPlugin* plugin = qobject_cast<IPlugin*>(sender());
    if (plugin) {
        QString pluginName = plugin->getMetadata().name;
        auto it = plugins_.find(pluginName);
        if (it != plugins_.end()) {
            it.value()->configuration = config;
            emit pluginConfigurationChanged(pluginName, config);
        }
    }
}

void PluginRegistry::setError(const QString& error) {
    last_error_ = error;
    qWarning() << "PluginRegistry error:" << error;
}

} // namespace DeclarativeUI::Plugins
