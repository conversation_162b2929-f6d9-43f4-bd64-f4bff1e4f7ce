#include "TestRunner.hpp"
#include <QApplication>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QStandardPaths>
#include <QRegularExpression>
#include <QXmlStreamWriter>
#include <QDateTime>
#include <algorithm>
#include <iostream>

namespace DeclarativeUI::Testing {

// **TestRunner Implementation**

TestRunner::TestRunner(QObject* parent)
    : QObject(parent)
{
    // Initialize testing utilities
    ui_automation_ = std::make_unique<UIAutomation>(this);
    visual_testing_ = std::make_unique<VisualTesting>(this);
    component_tester_ = std::make_unique<ComponentTester>(this);
    performance_benchmark_ = std::make_unique<PerformanceBenchmark>(this);
    
    // Set default configuration
    config_.name = "DeclarativeUI Test Suite";
    config_.description = "Comprehensive test suite for DeclarativeUI framework";
    config_.output_directory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/test_results";
    config_.timeout_seconds = 30;
    config_.generate_html_report = true;
    
    // Create output directory
    QDir().mkpath(config_.output_directory);
}

TestRunner::~TestRunner() {
    stopExecution();
}

void TestRunner::addTest(std::unique_ptr<TestCase> test) {
    if (!test) {
        qWarning() << "Cannot add null test case";
        return;
    }
    
    // Connect test signals
    connect(test.get(), &TestCase::testStarted, this, &TestRunner::onTestStarted);
    connect(test.get(), &TestCase::testFinished, this, &TestRunner::onTestFinished);
    
    tests_.push_back(std::move(test));
}

void TestRunner::addTestSuite(const QString& suite_name, std::vector<std::unique_ptr<TestCase>> tests) {
    for (auto& test : tests) {
        if (test) {
            connect(test.get(), &TestCase::testStarted, this, &TestRunner::onTestStarted);
            connect(test.get(), &TestCase::testFinished, this, &TestRunner::onTestFinished);
        }
    }
    
    test_suites_[suite_name] = std::move(tests);
}

void TestRunner::removeTest(const QString& test_name) {
    auto it = std::remove_if(tests_.begin(), tests_.end(),
                            [&test_name](const std::unique_ptr<TestCase>& test) {
                                return test && test->getName() == test_name;
                            });
    tests_.erase(it, tests_.end());
}

void TestRunner::clearTests() {
    tests_.clear();
    test_suites_.clear();
    results_.clear();
}

void TestRunner::runAllTests() {
    QMutexLocker locker(&execution_mutex_);
    
    execution_stopped_ = false;
    results_.clear();
    completed_tests_ = 0;
    running_tests_ = 0;
    
    // Collect all tests to run
    std::vector<TestCase*> tests_to_run;
    
    // Add individual tests
    for (const auto& test : tests_) {
        if (shouldRunTest(test.get())) {
            tests_to_run.push_back(test.get());
        }
    }
    
    // Add test suite tests
    for (const auto& [suite_name, suite_tests] : test_suites_) {
        emit suiteStarted(suite_name);
        
        for (const auto& test : suite_tests) {
            if (shouldRunTest(test.get())) {
                tests_to_run.push_back(test.get());
            }
        }
    }
    
    if (tests_to_run.empty()) {
        qWarning() << "No tests to run";
        return;
    }
    
    emit executionStarted(static_cast<int>(tests_to_run.size()));
    execution_timer_.start();
    
    if (parallel_execution_) {
        // Execute tests in parallel
        QThreadPool::globalInstance()->setMaxThreadCount(max_concurrent_tests_);
        
        for (TestCase* test : tests_to_run) {
            if (execution_stopped_) break;
            
            auto* executor = new TestExecutor(test, this);
            QThreadPool::globalInstance()->start(executor);
        }
        
        // Wait for all tests to complete
        while (completed_tests_.loadAcquire() < static_cast<int>(tests_to_run.size()) && !execution_stopped_) {
            QThread::msleep(100);
            QApplication::processEvents();
        }
    } else {
        // Execute tests sequentially
        for (TestCase* test : tests_to_run) {
            if (execution_stopped_) break;
            
            executeTestSequential(test);
            
            if (fail_fast_ && !results_.empty() && results_.back().isFailed()) {
                qDebug() << "Stopping execution due to test failure (fail-fast enabled)";
                break;
            }
        }
    }
    
    total_execution_time_ = execution_timer_.elapsed();
    
    int passed = getPassedCount();
    int failed = getFailedCount();
    
    emit executionFinished(passed, failed, total_execution_time_);
    
    // Generate reports if configured
    if (config_.generate_html_report) {
        QString html_file = config_.output_directory + "/test_report.html";
        generateReport(html_file, "html");
    }
    
    if (config_.generate_junit_xml) {
        QString xml_file = config_.output_directory + "/test_results.xml";
        generateJUnitXML(xml_file);
    }
}

void TestRunner::runTestsByTag(const QStringList& tags) {
    setTagFilter(tags);
    runAllTests();
}

void TestRunner::runTestsByName(const QStringList& test_names) {
    QString pattern = "^(" + test_names.join("|") + ")$";
    setNameFilter(QRegularExpression(pattern));
    runAllTests();
}

void TestRunner::runTestSuite(const QString& suite_name) {
    auto suite_it = test_suites_.find(suite_name);
    if (suite_it == test_suites_.end()) {
        qWarning() << "Test suite not found:" << suite_name;
        return;
    }
    
    QMutexLocker locker(&execution_mutex_);
    
    execution_stopped_ = false;
    results_.clear();
    completed_tests_ = 0;
    
    const auto& suite_tests = suite_it->second;
    std::vector<TestCase*> tests_to_run;
    
    for (const auto& test : suite_tests) {
        if (shouldRunTest(test.get())) {
            tests_to_run.push_back(test.get());
        }
    }
    
    emit suiteStarted(suite_name);
    emit executionStarted(static_cast<int>(tests_to_run.size()));
    execution_timer_.start();
    
    for (TestCase* test : tests_to_run) {
        if (execution_stopped_) break;
        executeTestSequential(test);
    }
    
    total_execution_time_ = execution_timer_.elapsed();
    
    int passed = getPassedCount();
    int failed = getFailedCount();
    
    emit suiteFinished(suite_name, passed, failed);
    emit executionFinished(passed, failed, total_execution_time_);
}

void TestRunner::stopExecution() {
    execution_stopped_ = true;
}

void TestRunner::setConfiguration(const TestSuiteConfig& config) {
    config_ = config;
    
    // Update individual settings
    timeout_seconds_ = config.timeout_seconds;
    retry_count_ = config.retry_count;
    
    // Create output directory
    QDir().mkpath(config_.output_directory);
}

void TestRunner::setParallelExecution(bool enabled) {
    parallel_execution_ = enabled;
}

void TestRunner::setMaxConcurrentTests(int max_concurrent) {
    max_concurrent_tests_ = qMax(1, max_concurrent);
}

void TestRunner::setTimeout(int seconds) {
    timeout_seconds_ = qMax(1, seconds);
}

void TestRunner::setRetryCount(int count) {
    retry_count_ = qMax(0, count);
}

void TestRunner::setFailFast(bool enabled) {
    fail_fast_ = enabled;
}

void TestRunner::setTagFilter(const QStringList& required_tags, const QStringList& excluded_tags) {
    required_tags_ = required_tags;
    excluded_tags_ = excluded_tags;
}

void TestRunner::setNameFilter(const QRegularExpression& pattern) {
    name_filter_ = pattern;
}

void TestRunner::clearFilters() {
    required_tags_.clear();
    excluded_tags_.clear();
    name_filter_ = QRegularExpression();
}

TestResult TestRunner::getResult(const QString& test_name) const {
    QMutexLocker locker(&results_mutex_);
    
    auto it = std::find_if(results_.begin(), results_.end(),
                          [&test_name](const TestResult& result) {
                              return result.test_name == test_name;
                          });
    
    return (it != results_.end()) ? *it : TestResult{};
}

int TestRunner::getPassedCount() const {
    QMutexLocker locker(&results_mutex_);
    return std::count_if(results_.begin(), results_.end(),
                        [](const TestResult& result) { return result.isPassed(); });
}

int TestRunner::getFailedCount() const {
    QMutexLocker locker(&results_mutex_);
    return std::count_if(results_.begin(), results_.end(),
                        [](const TestResult& result) { return result.isFailed(); });
}

int TestRunner::getSkippedCount() const {
    QMutexLocker locker(&results_mutex_);
    return std::count_if(results_.begin(), results_.end(),
                        [](const TestResult& result) { return result.isSkipped(); });
}

int TestRunner::getErrorCount() const {
    QMutexLocker locker(&results_mutex_);
    return std::count_if(results_.begin(), results_.end(),
                        [](const TestResult& result) { return result.isError(); });
}

double TestRunner::getSuccessRate() const {
    QMutexLocker locker(&results_mutex_);
    
    if (results_.empty()) return 0.0;
    
    int passed = getPassedCount();
    return static_cast<double>(passed) / results_.size() * 100.0;
}

void TestRunner::generateReport(const QString& output_file, const QString& format) {
    TestReporter reporter;
    
    if (format.toLower() == "html") {
        reporter.generateHTMLReport(results_, output_file);
    } else if (format.toLower() == "json") {
        reporter.generateJSONReport(results_, output_file);
    } else if (format.toLower() == "xml" || format.toLower() == "junit") {
        reporter.generateJUnitXMLReport(results_, output_file);
    } else if (format.toLower() == "markdown" || format.toLower() == "md") {
        reporter.generateMarkdownReport(results_, output_file);
    } else if (format.toLower() == "csv") {
        reporter.generateCSVReport(results_, output_file);
    } else {
        qWarning() << "Unsupported report format:" << format;
    }
}

void TestRunner::generateJUnitXML(const QString& output_file) {
    generateReport(output_file, "junit");
}

void TestRunner::generateJSON(const QString& output_file) {
    generateReport(output_file, "json");
}

void TestRunner::printSummary() {
    TestReporter reporter;
    reporter.printSummary(results_);
}

void TestRunner::onTestStarted(const QString& test_name) {
    emit testStarted(test_name);
}

void TestRunner::onTestFinished(const TestResult& result) {
    processTestResult(result);
    emit testFinished(result);
    
    int completed = completed_tests_.fetchAndAddAcquire(1) + 1;
    emit progressUpdated(completed, static_cast<int>(tests_.size()));
}

// **TestExecutor Implementation**

TestRunner::TestExecutor::TestExecutor(TestCase* test, TestRunner* runner)
    : test_(test), runner_(runner)
{
    setAutoDelete(true);
}

void TestRunner::TestExecutor::run() {
    if (test_ && runner_) {
        runner_->executeTestParallel(test_);
    }
}

void TestRunner::executeTest(TestCase* test) {
    if (parallel_execution_) {
        executeTestParallel(test);
    } else {
        executeTestSequential(test);
    }
}

void TestRunner::executeTestSequential(TestCase* test) {
    if (!test || execution_stopped_) return;
    
    running_tests_.fetchAndAddAcquire(1);
    
    QElapsedTimer test_timer;
    test_timer.start();
    
    try {
        test->setUp();
        test->run();
        test->tearDown();
        
        // If no result was set, assume success
        TestResult result = test->getResult();
        if (result.status == TestResult::Status::Failed && result.error_message.isEmpty()) {
            result.status = TestResult::Status::Passed;
        }
        result.execution_time_ms = test_timer.elapsed();
        
        processTestResult(result);
        
    } catch (const std::exception& e) {
        TestResult result;
        result.test_name = test->getName();
        result.description = test->getDescription();
        result.status = TestResult::Status::Error;
        result.error_message = QString("Exception: %1").arg(e.what());
        result.execution_time_ms = test_timer.elapsed();
        
        processTestResult(result);
    } catch (...) {
        TestResult result;
        result.test_name = test->getName();
        result.description = test->getDescription();
        result.status = TestResult::Status::Error;
        result.error_message = "Unknown exception occurred";
        result.execution_time_ms = test_timer.elapsed();
        
        processTestResult(result);
    }
    
    running_tests_.fetchAndSubAcquire(1);
}

void TestRunner::executeTestParallel(TestCase* test) {
    executeTestSequential(test);  // Same implementation for now
}

bool TestRunner::shouldRunTest(TestCase* test) const {
    if (!test) return false;
    
    // Check name filter
    if (name_filter_.isValid() && !name_filter_.match(test->getName()).hasMatch()) {
        return false;
    }
    
    // Check tag filters
    QStringList test_tags = test->getTags();
    
    // Check required tags
    if (!required_tags_.isEmpty()) {
        bool has_required_tag = false;
        for (const QString& required_tag : required_tags_) {
            if (test_tags.contains(required_tag)) {
                has_required_tag = true;
                break;
            }
        }
        if (!has_required_tag) return false;
    }
    
    // Check excluded tags
    for (const QString& excluded_tag : excluded_tags_) {
        if (test_tags.contains(excluded_tag)) {
            return false;
        }
    }
    
    return true;
}

void TestRunner::processTestResult(const TestResult& result) {
    QMutexLocker locker(&results_mutex_);
    results_.push_back(result);
}

// **AccessibilityTester Implementation**

AccessibilityTester::AccessibilityTester(QObject* parent)
    : QObject(parent)
    , min_contrast_ratio_(4.5)
    , min_font_size_(12)
    , check_level_("AA")
{
    qDebug() << "♿ AccessibilityTester initialized with WCAG level:" << check_level_;
}

std::vector<AccessibilityTester::AccessibilityResult> AccessibilityTester::checkWidget(QWidget* widget) {
    std::vector<AccessibilityResult> results;

    if (!widget) {
        AccessibilityResult result;
        result.passed = false;
        result.rule_name = "null-widget";
        result.description = "Cannot check accessibility of null widget";
        result.severity = "error";
        result.element_info = "null";
        results.push_back(result);
        return results;
    }

    // Run all accessibility checks
    auto contrastResults = checkColorContrast(widget);
    auto keyboardResults = checkKeyboardNavigation(widget);
    auto screenReaderResults = checkScreenReaderSupport(widget);
    auto focusResults = checkFocusManagement(widget);

    // Combine all results
    results.insert(results.end(), contrastResults.begin(), contrastResults.end());
    results.insert(results.end(), keyboardResults.begin(), keyboardResults.end());
    results.insert(results.end(), screenReaderResults.begin(), screenReaderResults.end());
    results.insert(results.end(), focusResults.begin(), focusResults.end());

    emit accessibilityCheckCompleted(results);

    return results;
}

std::vector<AccessibilityTester::AccessibilityResult> AccessibilityTester::checkColorContrast(QWidget* widget) {
    std::vector<AccessibilityResult> results;

    if (!widget) return results;

    QPalette palette = widget->palette();
    QColor foreground = palette.color(QPalette::WindowText);
    QColor background = palette.color(QPalette::Window);

    double contrast = calculateContrastRatio(foreground, background);

    AccessibilityResult result;
    result.rule_name = "color-contrast";
    result.description = QString("Color contrast ratio: %1:1").arg(contrast, 0, 'f', 2);
    result.element_info = QString("Widget: %1").arg(widget->objectName());
    result.passed = contrast >= min_contrast_ratio_;
    result.severity = result.passed ? "info" : "error";

    if (!result.passed) {
        result.suggestions << QString("Increase contrast to at least %1:1").arg(min_contrast_ratio_)
                          << "Use darker text on light backgrounds"
                          << "Use lighter text on dark backgrounds"
                          << "Consider using high contrast themes";
    }

    result.metadata["contrast_ratio"] = contrast;
    result.metadata["foreground_color"] = foreground.name();
    result.metadata["background_color"] = background.name();
    result.metadata["minimum_required"] = min_contrast_ratio_;

    results.push_back(result);
    return results;
}

std::vector<AccessibilityTester::AccessibilityResult> AccessibilityTester::checkKeyboardNavigation(QWidget* widget) {
    std::vector<AccessibilityResult> results;

    if (!widget) return results;

    AccessibilityResult result;
    result.rule_name = "keyboard-navigation";
    result.element_info = QString("Widget: %1").arg(widget->objectName());
    result.passed = isKeyboardAccessible(widget);
    result.description = result.passed ? "Widget is keyboard accessible" : "Widget is not keyboard accessible";
    result.severity = result.passed ? "info" : "warning";

    if (!result.passed) {
        result.suggestions << "Set appropriate focus policy"
                          << "Ensure widget can receive keyboard focus"
                          << "Implement proper tab order"
                          << "Add keyboard shortcuts where appropriate";
    }

    result.metadata["focus_policy"] = static_cast<int>(widget->focusPolicy());
    result.metadata["can_focus"] = (widget->focusPolicy() != Qt::NoFocus);
    result.metadata["is_enabled"] = widget->isEnabled();
    result.metadata["is_visible"] = widget->isVisible();

    results.push_back(result);
    return results;
}

std::vector<AccessibilityTester::AccessibilityResult> AccessibilityTester::checkScreenReaderSupport(QWidget* widget) {
    std::vector<AccessibilityResult> results;

    if (!widget) return results;

    AccessibilityResult result;
    result.rule_name = "screen-reader-support";
    result.element_info = QString("Widget: %1").arg(widget->objectName());
    result.passed = hasProperLabels(widget) && hasProperRoles(widget);
    result.description = result.passed ? "Widget has proper screen reader support" : "Widget lacks proper screen reader support";
    result.severity = result.passed ? "info" : "error";

    if (!result.passed) {
        result.suggestions << "Set accessible name using setAccessibleName()"
                          << "Set accessible description using setAccessibleDescription()"
                          << "Ensure proper ARIA roles are set"
                          << "Test with actual screen reader software";
    }

    result.metadata["has_accessible_name"] = !widget->accessibleName().isEmpty();
    result.metadata["has_accessible_description"] = !widget->accessibleDescription().isEmpty();
    result.metadata["accessible_name"] = widget->accessibleName();
    result.metadata["accessible_description"] = widget->accessibleDescription();

    results.push_back(result);
    return results;
}

std::vector<AccessibilityTester::AccessibilityResult> AccessibilityTester::checkFocusManagement(QWidget* widget) {
    std::vector<AccessibilityResult> results;

    if (!widget) return results;

    AccessibilityResult result;
    result.rule_name = "focus-management";
    result.element_info = QString("Widget: %1").arg(widget->objectName());

    bool canFocus = widget->focusPolicy() != Qt::NoFocus;
    bool hasVisibleFocus = widget->hasFocus(); // This is a simple check

    result.passed = !canFocus || hasVisibleFocus || !widget->isVisible();
    result.description = result.passed ? "Focus management is appropriate" : "Focus management needs improvement";
    result.severity = result.passed ? "info" : "warning";

    if (!result.passed) {
        result.suggestions << "Ensure focus indicators are visible"
                          << "Implement proper focus order"
                          << "Handle focus trapping in modal dialogs"
                          << "Provide skip links for navigation";
    }

    result.metadata["can_focus"] = canFocus;
    result.metadata["has_focus"] = hasVisibleFocus;
    result.metadata["focus_policy"] = static_cast<int>(widget->focusPolicy());

    results.push_back(result);
    return results;
}

bool AccessibilityTester::checkWCAGCompliance(QWidget* widget, const QString& level) {
    auto results = checkWidget(widget);

    for (const auto& result : results) {
        if (!result.passed && result.severity == "error") {
            return false;
        }

        // For AA level, also check warnings
        if (level == "AA" && !result.passed && result.severity == "warning") {
            return false;
        }
    }

    return true;
}

QJsonObject AccessibilityTester::generateWCAGReport(QWidget* widget) {
    QJsonObject report;
    report["widget_name"] = widget ? widget->objectName() : "null";
    report["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    report["wcag_level"] = check_level_;
    report["compliant"] = checkWCAGCompliance(widget, check_level_);

    auto results = checkWidget(widget);
    QJsonArray issues;

    for (const auto& result : results) {
        QJsonObject issue;
        issue["rule"] = result.rule_name;
        issue["description"] = result.description;
        issue["severity"] = result.severity;
        issue["passed"] = result.passed;
        issue["element"] = result.element_info;

        QJsonArray suggestions;
        for (const QString& suggestion : result.suggestions) {
            suggestions.append(suggestion);
        }
        issue["suggestions"] = suggestions;
        issue["metadata"] = result.metadata;

        issues.append(issue);
    }

    report["issues"] = issues;
    report["total_checks"] = static_cast<int>(results.size());
    report["passed_checks"] = static_cast<int>(std::count_if(results.begin(), results.end(),
                                                            [](const AccessibilityResult& r) { return r.passed; }));

    return report;
}

double AccessibilityTester::calculateContrastRatio(const QColor& foreground, const QColor& background) {
    // Calculate relative luminance
    auto relativeLuminance = [](const QColor& color) -> double {
        double r = color.redF();
        double g = color.greenF();
        double b = color.blueF();

        // Apply gamma correction
        auto gammaCorrect = [](double c) -> double {
            return c <= 0.03928 ? c / 12.92 : std::pow((c + 0.055) / 1.055, 2.4);
        };

        r = gammaCorrect(r);
        g = gammaCorrect(g);
        b = gammaCorrect(b);

        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    double l1 = relativeLuminance(foreground);
    double l2 = relativeLuminance(background);

    // Ensure l1 is the lighter color
    if (l1 < l2) {
        std::swap(l1, l2);
    }

    return (l1 + 0.05) / (l2 + 0.05);
}

bool AccessibilityTester::isKeyboardAccessible(QWidget* widget) {
    if (!widget) return false;

    return widget->focusPolicy() != Qt::NoFocus &&
           widget->isEnabled() &&
           widget->isVisible();
}

bool AccessibilityTester::hasProperLabels(QWidget* widget) {
    if (!widget) return false;

    return !widget->accessibleName().isEmpty() ||
           !widget->accessibleDescription().isEmpty();
}

bool AccessibilityTester::hasProperRoles(QWidget* widget) {
    if (!widget) return false;

    // For Qt widgets, roles are typically handled automatically
    // This is a simplified check
    return true;
}

} // namespace DeclarativeUI::Testing
