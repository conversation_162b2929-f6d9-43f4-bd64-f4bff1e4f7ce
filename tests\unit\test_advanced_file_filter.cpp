#include <QtTest/QtTest>
#include <QTemporaryDir>
#include <QFile>
#include <QTextStream>
#include "../../src/HotReload/AdvancedFileFilter.hpp"

using namespace DeclarativeUI::HotReload;

class TestAdvancedFileFilter : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // **FilterRule tests**
    void testFilterRuleSerialization();
    void testFilterRuleMatching();
    void testFilterRuleExtensionMatching();
    void testFilterRuleGlobMatching();
    void testFilterRuleRegexMatching();
    void testFilterRuleSizeMatching();
    void testFilterRuleDirectoryMatching();

    // **AdvancedFileFilter tests**
    void testBasicFiltering();
    void testRuleManagement();
    void testRulePriority();
    void testCaching();
    void testBatchFiltering();
    void testStatistics();
    void testPresets();
    void testConfigurationSaveLoad();
    void testPerformanceOptimizations();

    // **Integration tests**
    void testRealDirectoryFiltering();
    void testContentFiltering();
    void testExplanationFeature();

private:
    AdvancedFileFilter* filter_;
    QTemporaryDir* temp_dir_;
    
    void createTestFiles();
    QString createTestFile(const QString& name, const QString& content = "");
};

void TestAdvancedFileFilter::initTestCase() {
    // Setup for all tests
}

void TestAdvancedFileFilter::cleanupTestCase() {
    // Cleanup after all tests
}

void TestAdvancedFileFilter::init() {
    filter_ = new AdvancedFileFilter(this);
    temp_dir_ = new QTemporaryDir();
    QVERIFY(temp_dir_->isValid());
    createTestFiles();
}

void TestAdvancedFileFilter::cleanup() {
    delete filter_;
    filter_ = nullptr;
    delete temp_dir_;
    temp_dir_ = nullptr;
}

void TestAdvancedFileFilter::createTestFiles() {
    // Create various test files
    createTestFile("test.qml", "import QtQuick 2.0");
    createTestFile("style.css", "body { color: red; }");
    createTestFile("script.js", "console.log('hello');");
    createTestFile("data.json", "{}");
    createTestFile("temp.tmp", "temporary");
    createTestFile("backup.bak", "backup");
    createTestFile("document.txt", "plain text");
    createTestFile("image.png", "fake png data");
    
    // Create subdirectories
    QDir(temp_dir_->path()).mkpath("build");
    QDir(temp_dir_->path()).mkpath(".git");
    QDir(temp_dir_->path()).mkpath("node_modules");
    
    createTestFile("build/output.exe", "binary");
    createTestFile(".git/config", "git config");
    createTestFile("node_modules/package.json", "{}");
}

QString TestAdvancedFileFilter::createTestFile(const QString& name, const QString& content) {
    QString file_path = temp_dir_->path() + "/" + name;
    QFileInfo file_info(file_path);
    
    // Create directory if needed
    QDir().mkpath(file_info.absolutePath());
    
    QFile file(file_path);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream stream(&file);
        stream << content;
    }
    return file_path;
}

void TestAdvancedFileFilter::testFilterRuleSerialization() {
    FilterRule rule;
    rule.type = FilterRule::Type::Include;
    rule.match_type = FilterRule::MatchType::Extension;
    rule.pattern = "qml";
    rule.description = "QML files";
    rule.priority = 100;
    
    QJsonObject json = rule.toJson();
    FilterRule deserialized = FilterRule::fromJson(json);
    
    QCOMPARE(static_cast<int>(deserialized.type), static_cast<int>(rule.type));
    QCOMPARE(static_cast<int>(deserialized.match_type), static_cast<int>(rule.match_type));
    QCOMPARE(deserialized.pattern, rule.pattern);
    QCOMPARE(deserialized.description, rule.description);
    QCOMPARE(deserialized.priority, rule.priority);
}

void TestAdvancedFileFilter::testFilterRuleExtensionMatching() {
    FilterRule rule;
    rule.type = FilterRule::Type::Include;
    rule.match_type = FilterRule::MatchType::Extension;
    rule.pattern = "qml";
    
    QFileInfo qml_file("test.qml");
    QFileInfo js_file("test.js");
    
    QVERIFY(rule.matches("test.qml", qml_file));
    QVERIFY(!rule.matches("test.js", js_file));
}

void TestAdvancedFileFilter::testFilterRuleGlobMatching() {
    FilterRule rule;
    rule.type = FilterRule::Type::Exclude;
    rule.match_type = FilterRule::MatchType::Glob;
    rule.pattern = "*.tmp";
    
    QFileInfo tmp_file("test.tmp");
    QFileInfo qml_file("test.qml");
    
    QVERIFY(rule.matches("test.tmp", tmp_file));
    QVERIFY(!rule.matches("test.qml", qml_file));
}

void TestAdvancedFileFilter::testFilterRuleRegexMatching() {
    FilterRule rule;
    rule.type = FilterRule::Type::Include;
    rule.match_type = FilterRule::MatchType::Regex;
    rule.pattern = ".*\\.(qml|js)$";
    
    QFileInfo qml_file("test.qml");
    QFileInfo js_file("test.js");
    QFileInfo txt_file("test.txt");
    
    QVERIFY(rule.matches("test.qml", qml_file));
    QVERIFY(rule.matches("test.js", js_file));
    QVERIFY(!rule.matches("test.txt", txt_file));
}

void TestAdvancedFileFilter::testBasicFiltering() {
    // Clear default rules and add specific ones
    filter_->clearRules();
    
    FilterRule include_qml;
    include_qml.type = FilterRule::Type::Include;
    include_qml.match_type = FilterRule::MatchType::Extension;
    include_qml.pattern = "qml";
    include_qml.priority = 100;
    filter_->addRule(include_qml);
    
    FilterRule exclude_tmp;
    exclude_tmp.type = FilterRule::Type::Exclude;
    exclude_tmp.match_type = FilterRule::MatchType::Glob;
    exclude_tmp.pattern = "*.tmp";
    exclude_tmp.priority = 200;
    filter_->addRule(exclude_tmp);
    
    QString qml_file = temp_dir_->path() + "/test.qml";
    QString tmp_file = temp_dir_->path() + "/temp.tmp";
    QString txt_file = temp_dir_->path() + "/document.txt";
    
    QVERIFY(filter_->shouldIncludeFile(qml_file));
    QVERIFY(!filter_->shouldIncludeFile(tmp_file));
    QVERIFY(!filter_->shouldIncludeFile(txt_file)); // No include rule matches
}

void TestAdvancedFileFilter::testRuleManagement() {
    int initial_count = filter_->getRules().size();
    
    FilterRule test_rule;
    test_rule.pattern = "test_pattern";
    test_rule.description = "Test rule";
    
    filter_->addRule(test_rule);
    QCOMPARE(filter_->getRules().size(), initial_count + 1);
    
    filter_->removeRule("test_pattern");
    QCOMPARE(filter_->getRules().size(), initial_count);
}

void TestAdvancedFileFilter::testRulePriority() {
    filter_->clearRules();
    
    // Add low priority include rule
    FilterRule include_all;
    include_all.type = FilterRule::Type::Include;
    include_all.match_type = FilterRule::MatchType::Glob;
    include_all.pattern = "*";
    include_all.priority = 10;
    filter_->addRule(include_all);
    
    // Add high priority exclude rule
    FilterRule exclude_tmp;
    exclude_tmp.type = FilterRule::Type::Exclude;
    exclude_tmp.match_type = FilterRule::MatchType::Glob;
    exclude_tmp.pattern = "*.tmp";
    exclude_tmp.priority = 100;
    filter_->addRule(exclude_tmp);
    
    QString tmp_file = temp_dir_->path() + "/test.tmp";
    QString qml_file = temp_dir_->path() + "/test.qml";
    
    // High priority exclude should override low priority include
    QVERIFY(!filter_->shouldIncludeFile(tmp_file));
    QVERIFY(filter_->shouldIncludeFile(qml_file));
}

void TestAdvancedFileFilter::testCaching() {
    filter_->enableCaching(true);
    filter_->resetStatistics();
    
    QString test_file = temp_dir_->path() + "/test.qml";
    
    // First call should be a cache miss
    bool result1 = filter_->shouldIncludeFile(test_file);
    FilterStats stats1 = filter_->getStatistics();
    QVERIFY(stats1.cache_misses > 0);
    
    // Second call should be a cache hit
    bool result2 = filter_->shouldIncludeFile(test_file);
    FilterStats stats2 = filter_->getStatistics();
    QVERIFY(stats2.cache_hits > 0);
    
    QCOMPARE(result1, result2);
}

void TestAdvancedFileFilter::testBatchFiltering() {
    QStringList test_files = {
        temp_dir_->path() + "/test.qml",
        temp_dir_->path() + "/style.css",
        temp_dir_->path() + "/temp.tmp",
        temp_dir_->path() + "/document.txt"
    };
    
    QStringList filtered = filter_->filterFiles(test_files);
    
    // Should include QML and CSS files, exclude TMP and TXT
    QVERIFY(filtered.contains(temp_dir_->path() + "/test.qml"));
    QVERIFY(filtered.contains(temp_dir_->path() + "/style.css"));
    QVERIFY(!filtered.contains(temp_dir_->path() + "/temp.tmp"));
    QVERIFY(!filtered.contains(temp_dir_->path() + "/document.txt"));
}

void TestAdvancedFileFilter::testStatistics() {
    filter_->resetStatistics();
    
    QString qml_file = temp_dir_->path() + "/test.qml";
    QString tmp_file = temp_dir_->path() + "/temp.tmp";
    
    filter_->shouldIncludeFile(qml_file);
    filter_->shouldIncludeFile(tmp_file);
    
    FilterStats stats = filter_->getStatistics();
    QCOMPARE(stats.total_files_processed, qint64(2));
    QVERIFY(stats.files_included > 0);
    QVERIFY(stats.files_excluded > 0);
}

void TestAdvancedFileFilter::testPresets() {
    auto web_filter = FilterPresets::createWebDevelopmentFilter();
    QVERIFY(web_filter != nullptr);
    QVERIFY(web_filter->shouldIncludeFile("test.html"));
    QVERIFY(web_filter->shouldIncludeFile("style.css"));
    QVERIFY(web_filter->shouldIncludeFile("script.js"));
    
    auto qt_filter = FilterPresets::createQtDevelopmentFilter();
    QVERIFY(qt_filter != nullptr);
    QVERIFY(qt_filter->shouldIncludeFile("main.qml"));
    QVERIFY(qt_filter->shouldIncludeFile("widget.ui"));
    QVERIFY(qt_filter->shouldIncludeFile("main.cpp"));
    
    auto minimal_filter = FilterPresets::createMinimalFilter();
    QVERIFY(minimal_filter != nullptr);
    QVERIFY(minimal_filter->shouldIncludeFile("data.json"));
    QVERIFY(minimal_filter->shouldIncludeFile("main.qml"));
}

void TestAdvancedFileFilter::testConfigurationSaveLoad() {
    // Configure filter
    filter_->enableCaching(false);
    filter_->enableContentFiltering(true);
    filter_->setMaxFileSize(1024);
    
    // Save configuration
    QString config_file = temp_dir_->path() + "/filter_config.json";
    QVERIFY(filter_->saveToFile(config_file));
    
    // Create new filter and load configuration
    AdvancedFileFilter new_filter;
    QVERIFY(new_filter.loadFromFile(config_file));
    
    // Verify configuration was loaded
    QJsonObject original_config = filter_->toJson();
    QJsonObject loaded_config = new_filter.toJson();
    
    QCOMPARE(loaded_config["caching_enabled"].toBool(), 
             original_config["caching_enabled"].toBool());
    QCOMPARE(loaded_config["content_filtering_enabled"].toBool(), 
             original_config["content_filtering_enabled"].toBool());
}

void TestAdvancedFileFilter::testExplanationFeature() {
    QString test_file = temp_dir_->path() + "/test.qml";
    QString explanation = filter_->explainDecision(test_file);

    QVERIFY(!explanation.isEmpty());
    QVERIFY(explanation.contains("test.qml"));
    QVERIFY(explanation.contains("Final decision"));
}

void TestAdvancedFileFilter::testFilterRuleMatching() {
    // Test basic FilterRule matching functionality
    FilterRule rule;
    rule.pattern = "*.qml";
    rule.match_type = FilterRule::MatchType::Glob;
    rule.type = FilterRule::Type::Include;

    QFileInfo qml_info("test.qml");
    QFileInfo cpp_info("test.cpp");

    QVERIFY(rule.matches("test.qml", qml_info));
    QVERIFY(!rule.matches("test.cpp", cpp_info));
}

void TestAdvancedFileFilter::testFilterRuleSizeMatching() {
    // Test FilterRule size-based matching
    FilterRule rule;
    rule.match_type = FilterRule::MatchType::Size;
    rule.type = FilterRule::Type::Include;
    rule.min_size_bytes = 100;
    rule.max_size_bytes = 1000;

    // Note: This is a simplified test since actual size checking
    // would require file system operations
    QVERIFY(rule.min_size_bytes == 100);
    QVERIFY(rule.max_size_bytes == 1000);
}

void TestAdvancedFileFilter::testFilterRuleDirectoryMatching() {
    // Test FilterRule directory matching
    FilterRule rule;
    rule.pattern = "build/*";
    rule.match_type = FilterRule::MatchType::Glob;
    rule.type = FilterRule::Type::Exclude;

    QFileInfo build_info("build/test.o");
    QFileInfo src_info("src/test.cpp");

    QVERIFY(rule.matches("build/test.o", build_info));
    QVERIFY(!rule.matches("src/test.cpp", src_info));
}

void TestAdvancedFileFilter::testPerformanceOptimizations() {
    // Test performance optimization features
    filter_->enableCaching(true);
    filter_->setCacheSize(1000);

    // Process same file multiple times to test caching
    QString test_file = createTestFile("perf_test.qml");

    for (int i = 0; i < 10; ++i) {
        filter_->shouldIncludeFile(test_file);
    }

    FilterStats stats = filter_->getStatistics();
    QVERIFY(stats.cache_hits > 0);
}

void TestAdvancedFileFilter::testRealDirectoryFiltering() {
    // Test filtering on real directory structure
    createTestFiles();

    QStringList files = {
        temp_dir_->path() + "/test.qml",
        temp_dir_->path() + "/test.cpp",
        temp_dir_->path() + "/test.txt"
    };

    auto results = filter_->filterFiles(files);
    QVERIFY(results.size() > 0);

    // Should include QML files by default
    bool found_qml = false;
    for (const auto& file : results) {
        if (file.endsWith(".qml")) {
            found_qml = true;
            break;
        }
    }
    QVERIFY(found_qml);
}

void TestAdvancedFileFilter::testContentFiltering() {
    // Test content-based filtering
    filter_->enableContentFiltering(true);

    QString qml_file = createTestFile("content_test.qml", "import QtQuick 2.0\nRectangle {}");
    QString cpp_file = createTestFile("content_test.cpp", "#include <iostream>");

    QVERIFY(filter_->shouldIncludeFile(qml_file));

    FilterStats stats = filter_->getStatistics();
    QVERIFY(stats.total_files_processed > 0);
}

QTEST_MAIN(TestAdvancedFileFilter)
#include "test_advanced_file_filter.moc"
