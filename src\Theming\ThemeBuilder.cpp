#include "ThemeManager.hpp"
#include <QDebug>

namespace DeclarativeUI::Theming {

ThemeBuilder::ThemeBuilder(const QString& name) {
    theme_.name = name;
    theme_.version = "1.0";
    theme_.mode = ThemeManager::ThemeMode::Light;
    theme_.scheme = ThemeManager::ColorScheme::Default;
    
    // Initialize with default values
    theme_.colors = ThemeManager::instance().createLightPalette(ThemeManager::ColorScheme::Default);
    theme_.typography = ThemeManager::instance().createDefaultTypography();
    theme_.spacing = ThemeManager::Spacing{4, 8, 16, 24, 32, 48};
    theme_.border_radius = ThemeManager::BorderRadius{0, 2, 4, 8, 12, 9999};
    theme_.shadows = ThemeManager::Shadows{
        "none",
        "0 1px 2px rgba(0, 0, 0, 0.05)",
        "0 4px 6px rgba(0, 0, 0, 0.1)",
        "0 10px 15px rgba(0, 0, 0, 0.1)",
        "0 20px 25px rgba(0, 0, 0, 0.1)"
    };
}

ThemeBuilder& ThemeBuilder::mode(ThemeManager::ThemeMode mode) {
    theme_.mode = mode;
    
    // Update colors based on mode
    if (mode == ThemeManager::ThemeMode::Dark) {
        theme_.colors = ThemeManager::instance().createDarkPalette(theme_.scheme);
    } else if (mode == ThemeManager::ThemeMode::Light) {
        theme_.colors = ThemeManager::instance().createLightPalette(theme_.scheme);
    }
    
    return *this;
}

ThemeBuilder& ThemeBuilder::scheme(ThemeManager::ColorScheme scheme) {
    theme_.scheme = scheme;
    
    // Update colors based on scheme and current mode
    if (theme_.mode == ThemeManager::ThemeMode::Dark) {
        theme_.colors = ThemeManager::instance().createDarkPalette(scheme);
    } else {
        theme_.colors = ThemeManager::instance().createLightPalette(scheme);
    }
    
    return *this;
}

ThemeBuilder& ThemeBuilder::author(const QString& author) {
    theme_.author = author;
    return *this;
}

ThemeBuilder& ThemeBuilder::description(const QString& description) {
    theme_.description = description;
    return *this;
}

ThemeBuilder& ThemeBuilder::version(const QString& version) {
    theme_.version = version;
    return *this;
}

// Color customization methods
ThemeBuilder& ThemeBuilder::primaryColor(const QColor& color) {
    theme_.colors.primary = color;
    
    // Generate light and dark variants
    theme_.colors.primary_light = color.lighter(120);
    theme_.colors.primary_dark = color.darker(120);
    
    return *this;
}

ThemeBuilder& ThemeBuilder::secondaryColor(const QColor& color) {
    theme_.colors.secondary = color;
    
    // Generate light and dark variants
    theme_.colors.secondary_light = color.lighter(120);
    theme_.colors.secondary_dark = color.darker(120);
    
    return *this;
}

ThemeBuilder& ThemeBuilder::backgroundColor(const QColor& color) {
    theme_.colors.background = color;
    
    // Adjust surface and card colors based on background
    theme_.colors.surface = color.lighter(105);
    theme_.colors.card = color.lighter(102);
    
    return *this;
}

ThemeBuilder& ThemeBuilder::textColor(const QColor& color) {
    theme_.colors.text_primary = color;
    
    // Generate secondary and disabled text colors
    theme_.colors.text_secondary = color.lighter(150);
    theme_.colors.text_disabled = color.lighter(200);
    
    return *this;
}

ThemeBuilder& ThemeBuilder::accentColor(const QColor& color) {
    theme_.colors.focus = color;
    theme_.colors.active = color;
    theme_.colors.hover = color.lighter(110);
    
    return *this;
}

// Typography customization methods
ThemeBuilder& ThemeBuilder::headingFont(const QFont& font) {
    theme_.typography.heading1 = font;
    
    // Create variants for other heading levels
    QFont h2 = font;
    h2.setPointSize(font.pointSize() * 0.8);
    theme_.typography.heading2 = h2;
    
    QFont h3 = font;
    h3.setPointSize(font.pointSize() * 0.7);
    theme_.typography.heading3 = h3;
    
    QFont h4 = font;
    h4.setPointSize(font.pointSize() * 0.6);
    theme_.typography.heading4 = h4;
    
    QFont h5 = font;
    h5.setPointSize(font.pointSize() * 0.55);
    theme_.typography.heading5 = h5;
    
    QFont h6 = font;
    h6.setPointSize(font.pointSize() * 0.5);
    theme_.typography.heading6 = h6;
    
    return *this;
}

ThemeBuilder& ThemeBuilder::bodyFont(const QFont& font) {
    theme_.typography.body1 = font;
    
    // Create body2 variant
    QFont body2 = font;
    body2.setPointSize(font.pointSize() * 0.9);
    theme_.typography.body2 = body2;
    
    // Create caption variant
    QFont caption = font;
    caption.setPointSize(font.pointSize() * 0.8);
    theme_.typography.caption = caption;
    
    // Create overline variant
    QFont overline = font;
    overline.setPointSize(font.pointSize() * 0.75);
    overline.setCapitalization(QFont::AllUppercase);
    theme_.typography.overline = overline;
    
    return *this;
}

ThemeBuilder& ThemeBuilder::buttonFont(const QFont& font) {
    theme_.typography.button = font;
    return *this;
}

// Spacing and layout methods
ThemeBuilder& ThemeBuilder::baseSpacing(int spacing) {
    theme_.spacing.xs = spacing;
    theme_.spacing.sm = spacing * 2;
    theme_.spacing.md = spacing * 4;
    theme_.spacing.lg = spacing * 6;
    theme_.spacing.xl = spacing * 8;
    theme_.spacing.xxl = spacing * 12;
    
    return *this;
}

ThemeBuilder& ThemeBuilder::borderRadius(int radius) {
    theme_.border_radius.sm = radius;
    theme_.border_radius.md = radius * 2;
    theme_.border_radius.lg = radius * 4;
    theme_.border_radius.xl = radius * 6;
    
    return *this;
}

// Custom properties methods
ThemeBuilder& ThemeBuilder::customProperty(const QString& key, const QVariant& value) {
    theme_.custom_properties[key] = QJsonValue::fromVariant(value);
    return *this;
}

ThemeBuilder& ThemeBuilder::cssOverride(const QString& css) {
    theme_.css_overrides = css;
    return *this;
}

// Build the theme
ThemeManager::Theme ThemeBuilder::build() const {
    // Validate the theme before returning
    ThemeManager::Theme validated_theme = theme_;
    
    // Ensure required fields are set
    if (validated_theme.name.isEmpty()) {
        validated_theme.name = "Custom Theme";
    }
    
    if (validated_theme.version.isEmpty()) {
        validated_theme.version = "1.0";
    }
    
    // Validate colors
    if (!validated_theme.colors.primary.isValid()) {
        validated_theme.colors.primary = QColor("#3498db");
    }
    
    if (!validated_theme.colors.background.isValid()) {
        validated_theme.colors.background = QColor("#ffffff");
    }
    
    if (!validated_theme.colors.text_primary.isValid()) {
        validated_theme.colors.text_primary = QColor("#212529");
    }
    
    // Validate typography
    if (validated_theme.typography.body1.family().isEmpty()) {
        validated_theme.typography.body1 = QFont("Arial", 14);
    }
    
    qDebug() << "🎨 Theme built:" << validated_theme.name;
    return validated_theme;
}

} // namespace DeclarativeUI::Theming
