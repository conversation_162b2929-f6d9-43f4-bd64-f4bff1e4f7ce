#include "ThemeManager.hpp"
#include <QApplication>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonArray>
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QSettings>
#include <QStyleFactory>
#include <QPalette>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>

#ifdef Q_OS_WIN
#include <windows.h>
#include <dwmapi.h>
#pragma comment(lib, "dwmapi.lib")
#endif

#ifdef Q_OS_MACOS
#include <CoreFoundation/CoreFoundation.h>
#endif

namespace DeclarativeUI::Theming {

// Static instance for singleton pattern
static ThemeManager* s_instance = nullptr;

ThemeManager::ThemeManager(QObject* parent)
    : QObject(parent)
    , current_mode_(ThemeMode::Auto)
    , current_scheme_(ColorScheme::Default)
    , dynamic_theming_enabled_(true)
    , follow_system_theme_(true)
    , transition_duration_(300)
    , transition_timer_(new QTimer(this))
    , theme_animation_(nullptr)
    , transition_effect_(nullptr)
{
    // Initialize built-in themes
    setupBuiltinThemes();
    
    // Setup system theme monitoring
    setupSystemThemeMonitoring();
    
    // Initialize with auto theme (follows system)
    applyAutoTheme();
    
    qDebug() << "🎨 ThemeManager initialized with" << registered_themes_.size() << "themes";
}

ThemeManager& ThemeManager::instance() {
    if (!s_instance) {
        s_instance = new ThemeManager();
    }
    return *s_instance;
}

void ThemeManager::setTheme(const Theme& theme) {
    // Validate theme before applying
    Theme validated_theme = theme;
    validateAndFixTheme(validated_theme);
    
    // Store previous theme for animation
    Theme previous_theme = current_theme_;
    
    // Apply the theme
    applyThemeInternal(validated_theme, dynamic_theming_enabled_);
    
    qDebug() << "🎨 Theme applied:" << validated_theme.name;
}

void ThemeManager::setThemeMode(ThemeMode mode) {
    if (current_mode_ == mode) return;
    
    current_mode_ = mode;
    
    switch (mode) {
        case ThemeMode::Light:
            applyLightTheme();
            break;
        case ThemeMode::Dark:
            applyDarkTheme();
            break;
        case ThemeMode::Auto:
            applyAutoTheme();
            break;
        case ThemeMode::Custom:
            // Keep current custom theme
            break;
    }
    
    emit themeModeChanged(mode);
    qDebug() << "🎨 Theme mode changed to:" << static_cast<int>(mode);
}

void ThemeManager::setColorScheme(ColorScheme scheme) {
    if (current_scheme_ == scheme) return;
    
    current_scheme_ = scheme;
    
    // Update current theme with new color scheme
    Theme updated_theme = current_theme_;
    updated_theme.scheme = scheme;
    
    // Regenerate colors based on current mode and new scheme
    if (current_mode_ == ThemeMode::Light) {
        updated_theme.colors = createLightPalette(scheme);
    } else if (current_mode_ == ThemeMode::Dark) {
        updated_theme.colors = createDarkPalette(scheme);
    }
    
    setTheme(updated_theme);
    emit colorSchemeChanged(scheme);
    
    qDebug() << "🎨 Color scheme changed to:" << static_cast<int>(scheme);
}

void ThemeManager::switchTheme(const QString& theme_name, bool animated) {
    auto it = registered_themes_.find(theme_name);
    if (it == registered_themes_.end()) {
        qWarning() << "🎨 Theme not found:" << theme_name;
        return;
    }
    
    bool old_animation_setting = dynamic_theming_enabled_;
    dynamic_theming_enabled_ = animated;
    
    setTheme(it->second);
    
    dynamic_theming_enabled_ = old_animation_setting;
}

void ThemeManager::applyLightTheme() {
    Theme light_theme;
    light_theme.name = "Light";
    light_theme.mode = ThemeMode::Light;
    light_theme.scheme = current_scheme_;
    light_theme.colors = createLightPalette(current_scheme_);
    light_theme.typography = createDefaultTypography();
    light_theme.spacing = Spacing{4, 8, 16, 24, 32, 48};
    light_theme.border_radius = BorderRadius{2, 4, 6, 8, 12};
    light_theme.shadows = Shadows{
        "0 1px 3px rgba(0,0,0,0.12)",
        "0 2px 6px rgba(0,0,0,0.16)",
        "0 4px 12px rgba(0,0,0,0.24)",
        "0 8px 24px rgba(0,0,0,0.32)"
    };
    
    setTheme(light_theme);
}

void ThemeManager::applyDarkTheme() {
    Theme dark_theme;
    dark_theme.name = "Dark";
    dark_theme.mode = ThemeMode::Dark;
    dark_theme.scheme = current_scheme_;
    dark_theme.colors = createDarkPalette(current_scheme_);
    dark_theme.typography = createDefaultTypography();
    dark_theme.spacing = Spacing{4, 8, 16, 24, 32, 48};
    dark_theme.border_radius = BorderRadius{2, 4, 6, 8, 12};
    dark_theme.shadows = Shadows{
        "0 1px 3px rgba(0,0,0,0.24)",
        "0 2px 6px rgba(0,0,0,0.32)",
        "0 4px 12px rgba(0,0,0,0.48)",
        "0 8px 24px rgba(0,0,0,0.64)"
    };
    
    setTheme(dark_theme);
}

void ThemeManager::applyAutoTheme() {
    if (isSystemDarkMode()) {
        applyDarkTheme();
    } else {
        applyLightTheme();
    }
}

void ThemeManager::toggleTheme(bool animated) {
    bool old_animation_setting = dynamic_theming_enabled_;
    dynamic_theming_enabled_ = animated;
    
    if (current_mode_ == ThemeMode::Light) {
        setThemeMode(ThemeMode::Dark);
    } else if (current_mode_ == ThemeMode::Dark) {
        setThemeMode(ThemeMode::Light);
    } else {
        // For Auto or Custom, toggle between light and dark
        if (isDarkMode()) {
            applyLightTheme();
        } else {
            applyDarkTheme();
        }
    }
    
    dynamic_theming_enabled_ = old_animation_setting;
}

void ThemeManager::registerTheme(const Theme& theme) {
    registered_themes_[theme.name] = theme;
    qDebug() << "🎨 Theme registered:" << theme.name;
}

void ThemeManager::unregisterTheme(const QString& name) {
    auto it = registered_themes_.find(name);
    if (it != registered_themes_.end()) {
        registered_themes_.erase(it);
        qDebug() << "🎨 Theme unregistered:" << name;
    }
}

ThemeManager::Theme ThemeManager::getCurrentTheme() const {
    return current_theme_;
}

bool ThemeManager::saveThemeToFile(const Theme& theme, const QString& filename) {
    // Save theme to file
    QJsonObject theme_json = themeToJson(theme);
    QJsonDocument doc(theme_json);
    QFile file(filename);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        qDebug() << "🎨 Theme saved to:" << filename;
        return true;
    } else {
        qWarning() << "🎨 Failed to save theme:" << filename;
        return false;
    }
}

bool ThemeManager::loadThemeFromFile(const QString& filename) {
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "🎨 Failed to load theme:" << filename;
        return false;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    Theme theme = themeFromJson(doc.object());

    // Register the loaded theme
    registerTheme(theme);

    qDebug() << "🎨 Theme loaded from:" << filename;
    return true;
}

void ThemeManager::applyThemeToWidget(QWidget* widget, const QString& component_type) {
    if (!widget) return;
    
    QString stylesheet = generateWidgetStyleSheet(widget);
    if (!component_type.isEmpty()) {
        stylesheet += generateComponentStyleSheet(component_type);
    }
    
    widget->setStyleSheet(stylesheet);
    
    // Apply theme properties if any
    auto it = widget_theme_properties_.find(widget);
    if (it != widget_theme_properties_.end()) {
        const QJsonObject& properties = it->second;
        for (auto prop_it = properties.begin(); prop_it != properties.end(); ++prop_it) {
            widget->setProperty(prop_it.key().toUtf8().constData(), prop_it.value().toVariant());
        }
    }
}

void ThemeManager::setWidgetThemeClass(QWidget* widget, const QString& theme_class) {
    if (!widget) return;
    
    widget_theme_classes_[widget] = theme_class;
    applyThemeToWidget(widget);
}

void ThemeManager::addWidgetThemeProperty(QWidget* widget, const QString& property, const QVariant& value) {
    if (!widget) return;
    
    widget_theme_properties_[widget][property] = QJsonValue::fromVariant(value);
    widget->setProperty(property.toUtf8().constData(), value);
}

void ThemeManager::removeWidgetThemeProperty(QWidget* widget, const QString& property) {
    if (!widget) return;
    
    auto it = widget_theme_properties_.find(widget);
    if (it != widget_theme_properties_.end()) {
        it->second.remove(property);
        if (it->second.isEmpty()) {
            widget_theme_properties_.erase(it);
        }
    }
}

void ThemeManager::addGlobalStyle(const QString& selector, const QString& style) {
    global_styles_[selector] = style;
    applyWidgetStyles(); // Reapply styles to all widgets
}

void ThemeManager::removeGlobalStyle(const QString& selector) {
    auto it = global_styles_.find(selector);
    if (it != global_styles_.end()) {
        global_styles_.erase(it);
        applyWidgetStyles(); // Reapply styles to all widgets
    }
}

void ThemeManager::addComponentStyle(const QString& component, const QString& style) {
    component_styles_[component] = style;
}

QString ThemeManager::generateStyleSheet() const {
    QString stylesheet;
    
    // Add global styles
    for (const auto& [selector, style] : global_styles_) {
        stylesheet += QString("%1 { %2 }\n").arg(selector, style);
    }
    
    // Add component styles
    for (const auto& [component, style] : component_styles_) {
        stylesheet += QString(".%1 { %2 }\n").arg(component, style);
    }
    
    return stylesheet;
}

QString ThemeManager::generateComponentStyleSheet(const QString& component) const {
    auto it = component_styles_.find(component);
    if (it != component_styles_.end()) {
        return it->second;
    }
    return QString();
}

void ThemeManager::enableDynamicTheming(bool enable) {
    dynamic_theming_enabled_ = enable;
    qDebug() << "🎨 Dynamic theming" << (enable ? "enabled" : "disabled");
}

void ThemeManager::setThemeTransitionDuration(int milliseconds) {
    transition_duration_ = milliseconds;
    qDebug() << "🎨 Theme transition duration set to" << milliseconds << "ms";
}

void ThemeManager::addThemeChangeListener(std::function<void(const Theme&)> listener) {
    theme_listeners_.append(listener);
}

void ThemeManager::removeThemeChangeListener(std::function<void(const Theme&)> listener) {
    // Note: This is a simplified implementation
    // In practice, you'd need a more sophisticated way to identify and remove specific listeners
    qDebug() << "🎨 Theme change listener removal requested (simplified implementation)";
}

void ThemeManager::followSystemTheme(bool enable) {
    follow_system_theme_ = enable;
    
    if (enable) {
        setThemeMode(ThemeMode::Auto);
    }
    
    qDebug() << "🎨 Follow system theme" << (enable ? "enabled" : "disabled");
}

void ThemeManager::detectSystemTheme() {
    bool is_dark = isSystemDarkMode();
    
    if (follow_system_theme_ && current_mode_ == ThemeMode::Auto) {
        if (is_dark && !isDarkMode()) {
            applyDarkTheme();
        } else if (!is_dark && isDarkMode()) {
            applyLightTheme();
        }
    }
    
    emit systemThemeChanged(is_dark);
}

bool ThemeManager::isSystemDarkMode() const {
#ifdef Q_OS_WIN
    // Windows 10/11 dark mode detection
    QSettings settings("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Themes\\Personalize", QSettings::NativeFormat);
    return settings.value("AppsUseLightTheme", 1).toInt() == 0;
#elif defined(Q_OS_MACOS)
    // macOS dark mode detection would require Objective-C code
    // For now, return false as a fallback
    return false;
#elif defined(Q_OS_LINUX)
    // Linux dark mode detection via gsettings
    QSettings settings("org.gnome.desktop.interface", QSettings::NativeFormat);
    QString theme = settings.value("gtk-theme").toString();
    return theme.contains("dark", Qt::CaseInsensitive);
#else
    return false;
#endif
}

QColor ThemeManager::getColor(const QString& color_name) const {
    const ColorPalette& colors = current_theme_.colors;
    
    if (color_name == "primary") return colors.primary;
    if (color_name == "primary_light") return colors.primary_light;
    if (color_name == "primary_dark") return colors.primary_dark;
    if (color_name == "secondary") return colors.secondary;
    if (color_name == "secondary_light") return colors.secondary_light;
    if (color_name == "secondary_dark") return colors.secondary_dark;
    if (color_name == "background") return colors.background;
    if (color_name == "surface") return colors.surface;
    if (color_name == "card") return colors.card;
    if (color_name == "text_primary") return colors.text_primary;
    if (color_name == "text_secondary") return colors.text_secondary;
    if (color_name == "text_disabled") return colors.text_disabled;
    if (color_name == "success") return colors.success;
    if (color_name == "warning") return colors.warning;
    if (color_name == "error") return colors.error;
    if (color_name == "info") return colors.info;
    if (color_name == "border") return colors.border;
    if (color_name == "divider") return colors.divider;
    if (color_name == "hover") return colors.hover;
    if (color_name == "focus") return colors.focus;
    if (color_name == "active") return colors.active;
    if (color_name == "disabled") return colors.disabled;
    
    qWarning() << "🎨 Unknown color name:" << color_name;
    return QColor();
}

QFont ThemeManager::getFont(const QString& font_name) const {
    const Typography& typography = current_theme_.typography;

    if (font_name == "heading1") return typography.heading1;
    if (font_name == "heading2") return typography.heading2;
    if (font_name == "heading3") return typography.heading3;
    if (font_name == "heading4") return typography.heading4;
    if (font_name == "heading5") return typography.heading5;
    if (font_name == "heading6") return typography.heading6;
    if (font_name == "body1") return typography.body1;
    if (font_name == "body2") return typography.body2;
    if (font_name == "caption") return typography.caption;
    if (font_name == "button") return typography.button;
    if (font_name == "overline") return typography.overline;

    qWarning() << "🎨 Unknown font name:" << font_name;
    return QFont();
}

int ThemeManager::getSpacing(const QString& size) const {
    const Spacing& spacing = current_theme_.spacing;

    if (size == "xs") return spacing.xs;
    if (size == "sm") return spacing.sm;
    if (size == "md") return spacing.md;
    if (size == "lg") return spacing.lg;
    if (size == "xl") return spacing.xl;
    if (size == "xxl") return spacing.xxl;

    qWarning() << "🎨 Unknown spacing size:" << size;
    return 0;
}

int ThemeManager::getBorderRadius(const QString& size) const {
    const BorderRadius& radius = current_theme_.border_radius;

    if (size == "none") return radius.none;
    if (size == "sm") return radius.sm;
    if (size == "md") return radius.md;
    if (size == "lg") return radius.lg;
    if (size == "xl") return radius.xl;
    if (size == "full") return radius.full;

    qWarning() << "🎨 Unknown border radius size:" << size;
    return 0;
}

QString ThemeManager::getShadow(const QString& size) const {
    const Shadows& shadows = current_theme_.shadows;

    if (size == "none") return shadows.none;
    if (size == "sm") return shadows.sm;
    if (size == "md") return shadows.md;
    if (size == "lg") return shadows.lg;
    if (size == "xl") return shadows.xl;

    qWarning() << "🎨 Unknown shadow size:" << size;
    return QString();
}

QVariant ThemeManager::getCustomProperty(const QString& property) const {
    return current_theme_.custom_properties.value(property).toVariant();
}

ThemeManager::ThemeMode ThemeManager::getCurrentMode() const {
    return current_mode_;
}

ThemeManager::ColorScheme ThemeManager::getCurrentScheme() const {
    return current_scheme_;
}

QStringList ThemeManager::getAvailableThemes() const {
    QStringList themes;
    for (const auto& [name, theme] : registered_themes_) {
        themes.append(name);
    }
    return themes;
}

bool ThemeManager::isDarkMode() const {
    return current_theme_.mode == ThemeMode::Dark ||
           (current_theme_.mode == ThemeMode::Auto && isSystemDarkMode());
}

bool ThemeManager::isLightMode() const {
    return !isDarkMode();
}

QStringList ThemeManager::validateTheme(const Theme& theme) const {
    QStringList issues;

    // Check required fields
    if (theme.name.isEmpty()) {
        issues.append("Theme name is required");
    }

    // Validate colors
    if (!theme.colors.primary.isValid()) {
        issues.append("Primary color is invalid");
    }
    if (!theme.colors.background.isValid()) {
        issues.append("Background color is invalid");
    }
    if (!theme.colors.text_primary.isValid()) {
        issues.append("Primary text color is invalid");
    }

    // Validate typography
    if (theme.typography.body1.family().isEmpty()) {
        issues.append("Body font family is required");
    }

    // Validate spacing values
    if (theme.spacing.xs < 0 || theme.spacing.sm < 0 || theme.spacing.md < 0) {
        issues.append("Spacing values must be non-negative");
    }

    return issues;
}

void ThemeManager::debugTheme(const Theme& theme) const {
    qDebug() << "🎨 Theme Debug Info:";
    qDebug() << "  Name:" << theme.name;
    qDebug() << "  Version:" << theme.version;
    qDebug() << "  Author:" << theme.author;
    qDebug() << "  Mode:" << static_cast<int>(theme.mode);
    qDebug() << "  Scheme:" << static_cast<int>(theme.scheme);
    qDebug() << "  Primary Color:" << theme.colors.primary.name();
    qDebug() << "  Background Color:" << theme.colors.background.name();
    qDebug() << "  Text Color:" << theme.colors.text_primary.name();
    qDebug() << "  Body Font:" << theme.typography.body1.family() << theme.typography.body1.pointSize() << "pt";
}

QJsonObject ThemeManager::themeToJson(const Theme& theme) const {
    QJsonObject json;

    json["name"] = theme.name;
    json["version"] = theme.version;
    json["author"] = theme.author;
    json["description"] = theme.description;
    json["mode"] = static_cast<int>(theme.mode);
    json["scheme"] = static_cast<int>(theme.scheme);

    // Colors
    QJsonObject colors;
    colors["primary"] = theme.colors.primary.name();
    colors["primary_light"] = theme.colors.primary_light.name();
    colors["primary_dark"] = theme.colors.primary_dark.name();
    colors["secondary"] = theme.colors.secondary.name();
    colors["secondary_light"] = theme.colors.secondary_light.name();
    colors["secondary_dark"] = theme.colors.secondary_dark.name();
    colors["background"] = theme.colors.background.name();
    colors["surface"] = theme.colors.surface.name();
    colors["card"] = theme.colors.card.name();
    colors["text_primary"] = theme.colors.text_primary.name();
    colors["text_secondary"] = theme.colors.text_secondary.name();
    colors["text_disabled"] = theme.colors.text_disabled.name();
    colors["success"] = theme.colors.success.name();
    colors["warning"] = theme.colors.warning.name();
    colors["error"] = theme.colors.error.name();
    colors["info"] = theme.colors.info.name();
    colors["border"] = theme.colors.border.name();
    colors["divider"] = theme.colors.divider.name();
    colors["hover"] = theme.colors.hover.name();
    colors["focus"] = theme.colors.focus.name();
    colors["active"] = theme.colors.active.name();
    colors["disabled"] = theme.colors.disabled.name();
    json["colors"] = colors;

    // Typography
    QJsonObject typography;
    typography["heading1"] = QJsonObject{
        {"family", theme.typography.heading1.family()},
        {"size", theme.typography.heading1.pointSize()},
        {"weight", theme.typography.heading1.weight()}
    };
    typography["heading2"] = QJsonObject{
        {"family", theme.typography.heading2.family()},
        {"size", theme.typography.heading2.pointSize()},
        {"weight", theme.typography.heading2.weight()}
    };
    typography["heading3"] = QJsonObject{
        {"family", theme.typography.heading3.family()},
        {"size", theme.typography.heading3.pointSize()},
        {"weight", theme.typography.heading3.weight()}
    };
    typography["heading4"] = QJsonObject{
        {"family", theme.typography.heading4.family()},
        {"size", theme.typography.heading4.pointSize()},
        {"weight", theme.typography.heading4.weight()}
    };
    typography["heading5"] = QJsonObject{
        {"family", theme.typography.heading5.family()},
        {"size", theme.typography.heading5.pointSize()},
        {"weight", theme.typography.heading5.weight()}
    };
    typography["heading6"] = QJsonObject{
        {"family", theme.typography.heading6.family()},
        {"size", theme.typography.heading6.pointSize()},
        {"weight", theme.typography.heading6.weight()}
    };
    typography["body1"] = QJsonObject{
        {"family", theme.typography.body1.family()},
        {"size", theme.typography.body1.pointSize()},
        {"weight", theme.typography.body1.weight()}
    };
    typography["body2"] = QJsonObject{
        {"family", theme.typography.body2.family()},
        {"size", theme.typography.body2.pointSize()},
        {"weight", theme.typography.body2.weight()}
    };
    typography["caption"] = QJsonObject{
        {"family", theme.typography.caption.family()},
        {"size", theme.typography.caption.pointSize()},
        {"weight", theme.typography.caption.weight()}
    };
    typography["button"] = QJsonObject{
        {"family", theme.typography.button.family()},
        {"size", theme.typography.button.pointSize()},
        {"weight", theme.typography.button.weight()}
    };
    typography["overline"] = QJsonObject{
        {"family", theme.typography.overline.family()},
        {"size", theme.typography.overline.pointSize()},
        {"weight", theme.typography.overline.weight()}
    };
    json["typography"] = typography;

    // Spacing
    QJsonObject spacing;
    spacing["xs"] = theme.spacing.xs;
    spacing["sm"] = theme.spacing.sm;
    spacing["md"] = theme.spacing.md;
    spacing["lg"] = theme.spacing.lg;
    spacing["xl"] = theme.spacing.xl;
    spacing["xxl"] = theme.spacing.xxl;
    json["spacing"] = spacing;

    // Border radius
    QJsonObject border_radius;
    border_radius["none"] = theme.border_radius.none;
    border_radius["sm"] = theme.border_radius.sm;
    border_radius["md"] = theme.border_radius.md;
    border_radius["lg"] = theme.border_radius.lg;
    border_radius["xl"] = theme.border_radius.xl;
    border_radius["full"] = theme.border_radius.full;
    json["border_radius"] = border_radius;

    // Shadows
    QJsonObject shadows;
    shadows["none"] = theme.shadows.none;
    shadows["sm"] = theme.shadows.sm;
    shadows["md"] = theme.shadows.md;
    shadows["lg"] = theme.shadows.lg;
    shadows["xl"] = theme.shadows.xl;
    json["shadows"] = shadows;

    // Custom properties
    json["custom_properties"] = theme.custom_properties;
    json["css_overrides"] = theme.css_overrides;

    return json;
}

ThemeManager::Theme ThemeManager::themeFromJson(const QJsonObject& json) const {
    Theme theme;

    theme.name = json["name"].toString();
    theme.version = json["version"].toString("1.0");
    theme.author = json["author"].toString();
    theme.description = json["description"].toString();
    theme.mode = static_cast<ThemeMode>(json["mode"].toInt());
    theme.scheme = static_cast<ColorScheme>(json["scheme"].toInt());

    // Colors
    QJsonObject colors = json["colors"].toObject();
    theme.colors.primary = QColor(colors["primary"].toString());
    theme.colors.primary_light = QColor(colors["primary_light"].toString());
    theme.colors.primary_dark = QColor(colors["primary_dark"].toString());
    theme.colors.secondary = QColor(colors["secondary"].toString());
    theme.colors.secondary_light = QColor(colors["secondary_light"].toString());
    theme.colors.secondary_dark = QColor(colors["secondary_dark"].toString());
    theme.colors.background = QColor(colors["background"].toString());
    theme.colors.surface = QColor(colors["surface"].toString());
    theme.colors.card = QColor(colors["card"].toString());
    theme.colors.text_primary = QColor(colors["text_primary"].toString());
    theme.colors.text_secondary = QColor(colors["text_secondary"].toString());
    theme.colors.text_disabled = QColor(colors["text_disabled"].toString());
    theme.colors.success = QColor(colors["success"].toString());
    theme.colors.warning = QColor(colors["warning"].toString());
    theme.colors.error = QColor(colors["error"].toString());
    theme.colors.info = QColor(colors["info"].toString());
    theme.colors.border = QColor(colors["border"].toString());
    theme.colors.divider = QColor(colors["divider"].toString());
    theme.colors.hover = QColor(colors["hover"].toString());
    theme.colors.focus = QColor(colors["focus"].toString());
    theme.colors.active = QColor(colors["active"].toString());
    theme.colors.disabled = QColor(colors["disabled"].toString());

    // Typography (simplified - would need more complex parsing for full QFont)
    QJsonObject typography = json["typography"].toObject();
    QJsonObject body_font = typography["body1"].toObject();
    theme.typography.body1 = QFont(body_font["family"].toString(), body_font["size"].toInt());
    theme.typography.body1.setWeight(static_cast<QFont::Weight>(body_font["weight"].toInt()));

    // Spacing
    QJsonObject spacing = json["spacing"].toObject();
    theme.spacing.xs = spacing["xs"].toInt();
    theme.spacing.sm = spacing["sm"].toInt();
    theme.spacing.md = spacing["md"].toInt();
    theme.spacing.lg = spacing["lg"].toInt();
    theme.spacing.xl = spacing["xl"].toInt();
    theme.spacing.xxl = spacing["xxl"].toInt();

    // Border radius
    QJsonObject border_radius = json["border_radius"].toObject();
    theme.border_radius.none = border_radius["none"].toInt();
    theme.border_radius.sm = border_radius["sm"].toInt();
    theme.border_radius.md = border_radius["md"].toInt();
    theme.border_radius.lg = border_radius["lg"].toInt();
    theme.border_radius.xl = border_radius["xl"].toInt();
    theme.border_radius.full = border_radius["full"].toInt();

    // Shadows
    QJsonObject shadows = json["shadows"].toObject();
    theme.shadows.none = shadows["none"].toString();
    theme.shadows.sm = shadows["sm"].toString();
    theme.shadows.md = shadows["md"].toString();
    theme.shadows.lg = shadows["lg"].toString();
    theme.shadows.xl = shadows["xl"].toString();

    // Custom properties
    theme.custom_properties = json["custom_properties"].toObject();
    theme.css_overrides = json["css_overrides"].toString();

    return theme;
}

// Private slot implementations
void ThemeManager::onSystemThemeChanged() {
    detectSystemTheme();
}

void ThemeManager::onThemeTransitionFinished() {
    qDebug() << "🎨 Theme transition completed";
}

void ThemeManager::onApplicationPaletteChanged() {
    qDebug() << "🎨 Application palette changed";
}



// Private helper method implementations
void ThemeManager::setupBuiltinThemes() {
    // Register built-in light theme
    Theme light_theme;
    light_theme.name = "Light";
    light_theme.mode = ThemeMode::Light;
    light_theme.scheme = ColorScheme::Default;
    light_theme.colors = createLightPalette(ColorScheme::Default);
    light_theme.typography = createDefaultTypography();
    light_theme.spacing = Spacing{4, 8, 16, 24, 32, 48};
    light_theme.border_radius = BorderRadius{0, 2, 4, 8, 12, 9999};
    light_theme.shadows = Shadows{"none", "0 1px 2px rgba(0, 0, 0, 0.05)", "0 4px 6px rgba(0, 0, 0, 0.1)", "0 10px 15px rgba(0, 0, 0, 0.1)", "0 20px 25px rgba(0, 0, 0, 0.1)"};
    registerTheme(light_theme);

    // Register built-in dark theme
    Theme dark_theme;
    dark_theme.name = "Dark";
    dark_theme.mode = ThemeMode::Dark;
    dark_theme.scheme = ColorScheme::Default;
    dark_theme.colors = createDarkPalette(ColorScheme::Default);
    dark_theme.typography = createDefaultTypography();
    dark_theme.spacing = Spacing{4, 8, 16, 24, 32, 48};
    dark_theme.border_radius = BorderRadius{0, 2, 4, 8, 12, 9999};
    dark_theme.shadows = Shadows{"none", "0 1px 2px rgba(0, 0, 0, 0.24)", "0 4px 6px rgba(0, 0, 0, 0.32)", "0 10px 15px rgba(0, 0, 0, 0.48)", "0 20px 25px rgba(0, 0, 0, 0.64)"};
    registerTheme(dark_theme);

    qDebug() << "🎨 Built-in themes registered";
}

void ThemeManager::setupSystemThemeMonitoring() {
    // Setup timer for periodic system theme checking
    QTimer* system_timer = new QTimer(this);
    connect(system_timer, &QTimer::timeout, this, &ThemeManager::detectSystemTheme);
    system_timer->start(5000); // Check every 5 seconds

    qDebug() << "🎨 System theme monitoring enabled";
}

void ThemeManager::applyThemeInternal(const Theme& theme, bool animated) {
    Theme previous_theme = current_theme_;
    current_theme_ = theme;

    // Update application palette
    updateApplicationPalette(theme.colors);

    // Update application font
    updateApplicationFont(theme.typography);

    // Apply widget styles
    applyWidgetStyles();

    // Notify listeners
    notifyThemeListeners(theme);

    emit themeChanged(theme);

    qDebug() << "🎨 Theme applied internally:" << theme.name;
}

void ThemeManager::animateThemeTransition(const Theme& from_theme, const Theme& to_theme) {
    // Simplified animation implementation
    qDebug() << "🎨 Animating theme transition from" << from_theme.name << "to" << to_theme.name;
}

void ThemeManager::updateApplicationPalette(const ColorPalette& colors) {
    QPalette palette;

    // Window colors
    palette.setColor(QPalette::Window, colors.background);
    palette.setColor(QPalette::WindowText, colors.text_primary);

    // Base colors
    palette.setColor(QPalette::Base, colors.surface);
    palette.setColor(QPalette::Text, colors.text_primary);

    // Button colors
    palette.setColor(QPalette::Button, colors.primary);
    palette.setColor(QPalette::ButtonText, colors.text_primary);

    // Selection colors
    palette.setColor(QPalette::Highlight, colors.primary);
    palette.setColor(QPalette::HighlightedText, colors.background);

    QApplication::setPalette(palette);
}

void ThemeManager::updateApplicationFont(const Typography& typography) {
    QApplication::setFont(typography.body1);
}

void ThemeManager::applyWidgetStyles() {
    QString global_stylesheet = this->generateStyleSheet();
    if (auto app = qobject_cast<QApplication*>(QApplication::instance())) {
        app->setStyleSheet(global_stylesheet);
    }
}

QString ThemeManager::generateWidgetStyleSheet(QWidget* widget) const {
    QString stylesheet;

    // Add theme class styles if widget has a theme class
    auto class_it = widget_theme_classes_.find(widget);
    if (class_it != widget_theme_classes_.end()) {
        stylesheet += generateComponentStyleSheet(class_it->second);
    }

    return stylesheet;
}

ThemeManager::ColorPalette ThemeManager::createLightPalette(ColorScheme scheme) const {
    ColorPalette palette;

    switch (scheme) {
        case ColorScheme::Default:
            palette.primary = QColor("#3498db");
            palette.primary_light = QColor("#5dade2");
            palette.primary_dark = QColor("#2980b9");
            palette.secondary = QColor("#2ecc71");
            palette.secondary_light = QColor("#58d68d");
            palette.secondary_dark = QColor("#27ae60");
            break;
        case ColorScheme::Blue:
            palette.primary = QColor("#2196f3");
            palette.primary_light = QColor("#64b5f6");
            palette.primary_dark = QColor("#1976d2");
            palette.secondary = QColor("#03a9f4");
            palette.secondary_light = QColor("#4fc3f7");
            palette.secondary_dark = QColor("#0288d1");
            break;
        case ColorScheme::Green:
            palette.primary = QColor("#4caf50");
            palette.primary_light = QColor("#81c784");
            palette.primary_dark = QColor("#388e3c");
            palette.secondary = QColor("#8bc34a");
            palette.secondary_light = QColor("#aed581");
            palette.secondary_dark = QColor("#689f38");
            break;
        case ColorScheme::Purple:
            palette.primary = QColor("#9c27b0");
            palette.primary_light = QColor("#ba68c8");
            palette.primary_dark = QColor("#7b1fa2");
            palette.secondary = QColor("#673ab7");
            palette.secondary_light = QColor("#9575cd");
            palette.secondary_dark = QColor("#512da8");
            break;
        case ColorScheme::Orange:
            palette.primary = QColor("#ff9800");
            palette.primary_light = QColor("#ffb74d");
            palette.primary_dark = QColor("#f57c00");
            palette.secondary = QColor("#ff5722");
            palette.secondary_light = QColor("#ff8a65");
            palette.secondary_dark = QColor("#e64a19");
            break;
        case ColorScheme::Red:
            palette.primary = QColor("#f44336");
            palette.primary_light = QColor("#ef5350");
            palette.primary_dark = QColor("#d32f2f");
            palette.secondary = QColor("#e91e63");
            palette.secondary_light = QColor("#ec407a");
            palette.secondary_dark = QColor("#c2185b");
            break;
        default:
            palette.primary = QColor("#3498db");
            palette.primary_light = QColor("#5dade2");
            palette.primary_dark = QColor("#2980b9");
            palette.secondary = QColor("#2ecc71");
            palette.secondary_light = QColor("#58d68d");
            palette.secondary_dark = QColor("#27ae60");
            break;
    }

    // Common light theme colors
    palette.background = QColor("#ffffff");
    palette.surface = QColor("#f8f9fa");
    palette.card = QColor("#ffffff");
    palette.text_primary = QColor("#212529");
    palette.text_secondary = QColor("#6c757d");
    palette.text_disabled = QColor("#adb5bd");
    palette.success = QColor("#28a745");
    palette.warning = QColor("#ffc107");
    palette.error = QColor("#dc3545");
    palette.info = QColor("#17a2b8");
    palette.border = QColor("#dee2e6");
    palette.divider = QColor("#e9ecef");
    palette.hover = QColor("#f8f9fa");
    palette.focus = QColor("#0056b3");
    palette.active = QColor("#0056b3");
    palette.disabled = QColor("#e9ecef");

    return palette;
}

ThemeManager::ColorPalette ThemeManager::createDarkPalette(ColorScheme scheme) const {
    ColorPalette palette = createLightPalette(scheme);

    // Override with dark theme colors
    palette.background = QColor("#121212");
    palette.surface = QColor("#1e1e1e");
    palette.card = QColor("#2d2d2d");
    palette.text_primary = QColor("#ffffff");
    palette.text_secondary = QColor("#b3b3b3");
    palette.text_disabled = QColor("#666666");
    palette.border = QColor("#404040");
    palette.divider = QColor("#333333");
    palette.hover = QColor("#2d2d2d");
    palette.focus = QColor("#4dabf7");
    palette.active = QColor("#4dabf7");
    palette.disabled = QColor("#333333");

    return palette;
}

ThemeManager::Typography ThemeManager::createDefaultTypography() const {
    Typography typography;

    typography.heading1 = QFont("Arial", 32, QFont::Bold);
    typography.heading2 = QFont("Arial", 24, QFont::Bold);
    typography.heading3 = QFont("Arial", 20, QFont::Bold);
    typography.heading4 = QFont("Arial", 18, QFont::Bold);
    typography.heading5 = QFont("Arial", 16, QFont::Bold);
    typography.heading6 = QFont("Arial", 14, QFont::Bold);
    typography.body1 = QFont("Arial", 14, QFont::Normal);
    typography.body2 = QFont("Arial", 12, QFont::Normal);
    typography.caption = QFont("Arial", 10, QFont::Normal);
    typography.button = QFont("Arial", 14, QFont::Medium);
    typography.overline = QFont("Arial", 10, QFont::Normal);

    return typography;
}

QColor ThemeManager::adjustColorForMode(const QColor& color, ThemeMode mode) const {
    if (mode == ThemeMode::Dark) {
        // Lighten colors for dark mode
        return color.lighter(150);
    }
    return color;
}

void ThemeManager::notifyThemeListeners(const Theme& theme) {
    for (const auto& listener : theme_listeners_) {
        listener(theme);
    }
}

void ThemeManager::validateAndFixTheme(Theme& theme) {
    // Ensure required fields are set
    if (theme.name.isEmpty()) {
        theme.name = "Custom Theme";
    }

    if (theme.version.isEmpty()) {
        theme.version = "1.0";
    }

    // Validate and fix colors
    if (!theme.colors.primary.isValid()) {
        theme.colors.primary = QColor("#3498db");
    }

    if (!theme.colors.background.isValid()) {
        theme.colors.background = QColor("#ffffff");
    }

    if (!theme.colors.text_primary.isValid()) {
        theme.colors.text_primary = QColor("#212529");
    }
}

} // namespace DeclarativeUI::Theming
