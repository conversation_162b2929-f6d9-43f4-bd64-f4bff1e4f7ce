#include "AdvancedAnimationExample.hpp"
#include <QApplication>
#include <QSplitter>
#include <QFrame>
#include <QDateTime>
#include <QDebug>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

AdvancedAnimationExample::AdvancedAnimationExample(QWidget* parent)
    : QMainWindow(parent)
    , animation_engine_(std::make_unique<AnimationEngine>())
{
    setWindowTitle("Advanced Animation System Demo");
    setMinimumSize(1200, 800);
    
    setupUI();
    setupAnimationControls();
    setupPhysicsControls();
    setupTransitionControls();
    setupStatusDisplay();
    
    // Setup status update timer
    status_timer_ = new QTimer(this);
    connect(status_timer_, &QTimer::timeout, this, &AdvancedAnimationExample::updateAnimationStatus);
    status_timer_->start(100);  // Update every 100ms
    
    logAnimationEvent("🚀 Advanced Animation System initialized");
}

void AdvancedAnimationExample::setupUI() {
    central_widget_ = new QWidget(this);
    setCentralWidget(central_widget_);
    
    main_layout_ = new QVBoxLayout(central_widget_);
    
    // Create splitter for controls and animation area
    auto* splitter = new QSplitter(Qt::Horizontal, this);
    main_layout_->addWidget(splitter);
    
    // Left side: Controls
    auto* controls_widget = new QWidget();
    controls_widget->setMaximumWidth(400);
    controls_widget->setMinimumWidth(350);
    splitter->addWidget(controls_widget);
    
    controls_layout_ = new QHBoxLayout(controls_widget);
    
    // Right side: Animation area
    auto* animation_area = new QWidget();
    animation_area->setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;");
    splitter->addWidget(animation_area);
    
    animation_grid_ = new QGridLayout(animation_area);
    
    // Create animated widgets
    animated_widget1_ = new QWidget();
    animated_widget1_->setFixedSize(100, 100);
    animated_widget1_->setStyleSheet("background-color: #ff6b6b; border-radius: 10px;");
    animation_grid_->addWidget(animated_widget1_, 0, 0);
    
    animated_widget2_ = new QWidget();
    animated_widget2_->setFixedSize(100, 100);
    animated_widget2_->setStyleSheet("background-color: #4ecdc4; border-radius: 10px;");
    animation_grid_->addWidget(animated_widget2_, 0, 1);
    
    animated_widget3_ = new QWidget();
    animated_widget3_->setFixedSize(100, 100);
    animated_widget3_->setStyleSheet("background-color: #45b7d1; border-radius: 10px;");
    animation_grid_->addWidget(animated_widget3_, 1, 0);
    
    animated_widget4_ = new QWidget();
    animated_widget4_->setFixedSize(100, 100);
    animated_widget4_->setStyleSheet("background-color: #f9ca24; border-radius: 10px;");
    animation_grid_->addWidget(animated_widget4_, 1, 1);
    
    // Set splitter proportions
    splitter->setSizes({350, 850});
}

void AdvancedAnimationExample::setupAnimationControls() {
    basic_controls_ = new QGroupBox("Animation Controls");
    controls_layout_->addWidget(basic_controls_);
    
    auto* layout = new QVBoxLayout(basic_controls_);
    
    // Animation type buttons
    timeline_btn_ = new QPushButton("Timeline Animation");
    physics_btn_ = new QPushButton("Physics Animation");
    transition_btn_ = new QPushButton("Transition Effect");
    multi_property_btn_ = new QPushButton("Multi-Property");
    sequence_btn_ = new QPushButton("Animation Sequence");
    ripple_btn_ = new QPushButton("Ripple Effect");
    morph_btn_ = new QPushButton("Morph Animation");
    
    layout->addWidget(timeline_btn_);
    layout->addWidget(physics_btn_);
    layout->addWidget(transition_btn_);
    layout->addWidget(multi_property_btn_);
    layout->addWidget(sequence_btn_);
    layout->addWidget(ripple_btn_);
    layout->addWidget(morph_btn_);
    
    // Animation settings
    layout->addWidget(new QLabel("Duration (ms):"));
    duration_spin_ = new QSpinBox();
    duration_spin_->setRange(100, 5000);
    duration_spin_->setValue(1000);
    layout->addWidget(duration_spin_);
    
    layout->addWidget(new QLabel("Speed:"));
    speed_slider_ = new QSlider(Qt::Horizontal);
    speed_slider_->setRange(10, 200);
    speed_slider_->setValue(100);
    layout->addWidget(speed_slider_);
    
    layout->addWidget(new QLabel("Easing:"));
    easing_combo_ = new QComboBox();
    easing_combo_->addItems({"Linear", "QuadIn", "QuadOut", "QuadInOut", 
                            "CubicIn", "CubicOut", "CubicInOut",
                            "ElasticIn", "ElasticOut", "BounceOut"});
    easing_combo_->setCurrentText("QuadInOut");
    layout->addWidget(easing_combo_);
    
    // Connect signals
    connect(timeline_btn_, &QPushButton::clicked, this, &AdvancedAnimationExample::onTimelineAnimationClicked);
    connect(physics_btn_, &QPushButton::clicked, this, &AdvancedAnimationExample::onPhysicsAnimationClicked);
    connect(transition_btn_, &QPushButton::clicked, this, &AdvancedAnimationExample::onTransitionAnimationClicked);
    connect(multi_property_btn_, &QPushButton::clicked, this, &AdvancedAnimationExample::onMultiPropertyAnimationClicked);
    connect(sequence_btn_, &QPushButton::clicked, this, &AdvancedAnimationExample::onSequenceAnimationClicked);
    connect(ripple_btn_, &QPushButton::clicked, this, &AdvancedAnimationExample::onRippleEffectClicked);
    connect(morph_btn_, &QPushButton::clicked, this, &AdvancedAnimationExample::onMorphAnimationClicked);
    
    connect(speed_slider_, &QSlider::valueChanged, this, &AdvancedAnimationExample::onAnimationSpeedChanged);
    connect(easing_combo_, &QComboBox::currentTextChanged, this, &AdvancedAnimationExample::onEasingTypeChanged);
}

void AdvancedAnimationExample::setupPhysicsControls() {
    physics_controls_ = new QGroupBox("Physics Parameters");
    controls_layout_->addWidget(physics_controls_);
    
    auto* layout = new QVBoxLayout(physics_controls_);
    
    // Stiffness
    layout->addWidget(new QLabel("Stiffness:"));
    stiffness_slider_ = new QSlider(Qt::Horizontal);
    stiffness_slider_->setRange(10, 500);
    stiffness_slider_->setValue(100);
    stiffness_label_ = new QLabel("100.0");
    layout->addWidget(stiffness_slider_);
    layout->addWidget(stiffness_label_);
    
    // Damping
    layout->addWidget(new QLabel("Damping:"));
    damping_slider_ = new QSlider(Qt::Horizontal);
    damping_slider_->setRange(1, 50);
    damping_slider_->setValue(10);
    damping_label_ = new QLabel("10.0");
    layout->addWidget(damping_slider_);
    layout->addWidget(damping_label_);
    
    // Mass
    layout->addWidget(new QLabel("Mass:"));
    mass_slider_ = new QSlider(Qt::Horizontal);
    mass_slider_->setRange(1, 20);
    mass_slider_->setValue(10);
    mass_label_ = new QLabel("1.0");
    layout->addWidget(mass_slider_);
    layout->addWidget(mass_label_);
    
    // Gravity
    layout->addWidget(new QLabel("Gravity:"));
    gravity_slider_ = new QSlider(Qt::Horizontal);
    gravity_slider_->setRange(-100, 100);
    gravity_slider_->setValue(0);
    gravity_label_ = new QLabel("0.0");
    layout->addWidget(gravity_slider_);
    layout->addWidget(gravity_label_);
    
    // Friction
    layout->addWidget(new QLabel("Friction:"));
    friction_slider_ = new QSlider(Qt::Horizontal);
    friction_slider_->setRange(0, 50);
    friction_slider_->setValue(0);
    friction_label_ = new QLabel("0.0");
    layout->addWidget(friction_slider_);
    layout->addWidget(friction_label_);
    
    // Connect physics parameter signals
    connect(stiffness_slider_, &QSlider::valueChanged, this, &AdvancedAnimationExample::onPhysicsParameterChanged);
    connect(damping_slider_, &QSlider::valueChanged, this, &AdvancedAnimationExample::onPhysicsParameterChanged);
    connect(mass_slider_, &QSlider::valueChanged, this, &AdvancedAnimationExample::onPhysicsParameterChanged);
    connect(gravity_slider_, &QSlider::valueChanged, this, &AdvancedAnimationExample::onPhysicsParameterChanged);
    connect(friction_slider_, &QSlider::valueChanged, this, &AdvancedAnimationExample::onPhysicsParameterChanged);
}

void AdvancedAnimationExample::setupTransitionControls() {
    transition_controls_ = new QGroupBox("Transition Effects");
    controls_layout_->addWidget(transition_controls_);
    
    auto* layout = new QVBoxLayout(transition_controls_);
    
    auto* fade_btn = new QPushButton("Fade Transition");
    auto* slide_btn = new QPushButton("Slide Transition");
    auto* scale_btn = new QPushButton("Scale Transition");
    auto* bounce_btn = new QPushButton("Bounce Transition");
    auto* elastic_btn = new QPushButton("Elastic Transition");
    
    layout->addWidget(fade_btn);
    layout->addWidget(slide_btn);
    layout->addWidget(scale_btn);
    layout->addWidget(bounce_btn);
    layout->addWidget(elastic_btn);
    
    // Connect transition buttons (simplified for now)
    connect(fade_btn, &QPushButton::clicked, this, &AdvancedAnimationExample::onTransitionAnimationClicked);
    connect(slide_btn, &QPushButton::clicked, this, &AdvancedAnimationExample::onTransitionAnimationClicked);
    connect(scale_btn, &QPushButton::clicked, this, &AdvancedAnimationExample::onTransitionAnimationClicked);
    connect(bounce_btn, &QPushButton::clicked, this, &AdvancedAnimationExample::onTransitionAnimationClicked);
    connect(elastic_btn, &QPushButton::clicked, this, &AdvancedAnimationExample::onTransitionAnimationClicked);
}

void AdvancedAnimationExample::setupStatusDisplay() {
    status_group_ = new QGroupBox("Animation Status");
    main_layout_->addWidget(status_group_);
    status_group_->setMaximumHeight(200);
    
    auto* layout = new QVBoxLayout(status_group_);
    
    // Progress bar
    animation_progress_ = new QProgressBar();
    layout->addWidget(animation_progress_);
    
    // Active animations counter
    active_animations_label_ = new QLabel("Active Animations: 0");
    layout->addWidget(active_animations_label_);
    
    // Status text
    status_text_ = new QTextEdit();
    status_text_->setMaximumHeight(100);
    status_text_->setReadOnly(true);
    layout->addWidget(status_text_);
}

// **Animation Implementation Methods**

void AdvancedAnimationExample::onTimelineAnimationClicked() {
    createTimelineAnimation();
    logAnimationEvent("🎬 Timeline animation started");
}

void AdvancedAnimationExample::onPhysicsAnimationClicked() {
    createPhysicsAnimation();
    logAnimationEvent("⚡ Physics animation started");
}

void AdvancedAnimationExample::onTransitionAnimationClicked() {
    createTransitionAnimation();
    logAnimationEvent("🔄 Transition animation started");
}

void AdvancedAnimationExample::onMultiPropertyAnimationClicked() {
    createMultiPropertyAnimation();
    logAnimationEvent("🎯 Multi-property animation started");
}

void AdvancedAnimationExample::onSequenceAnimationClicked() {
    createSequenceAnimation();
    logAnimationEvent("📋 Animation sequence started");
}

void AdvancedAnimationExample::onRippleEffectClicked() {
    createRippleEffect();
    logAnimationEvent("💫 Ripple effect started");
}

void AdvancedAnimationExample::onMorphAnimationClicked() {
    createMorphAnimation();
    logAnimationEvent("🔮 Morph animation started");
}

void AdvancedAnimationExample::onPhysicsParameterChanged() {
    current_stiffness_ = stiffness_slider_->value();
    current_damping_ = damping_slider_->value() / 10.0;
    current_mass_ = mass_slider_->value() / 10.0;
    current_gravity_ = gravity_slider_->value() / 10.0;
    current_friction_ = friction_slider_->value() / 100.0;

    stiffness_label_->setText(QString::number(current_stiffness_, 'f', 1));
    damping_label_->setText(QString::number(current_damping_, 'f', 1));
    mass_label_->setText(QString::number(current_mass_, 'f', 1));
    gravity_label_->setText(QString::number(current_gravity_, 'f', 1));
    friction_label_->setText(QString::number(current_friction_, 'f', 2));
}

void AdvancedAnimationExample::onAnimationSpeedChanged(int speed) {
    double speed_factor = speed / 100.0;
    current_duration_ = static_cast<int>(duration_spin_->value() / speed_factor);
}

void AdvancedAnimationExample::onEasingTypeChanged(const QString& easing) {
    if (easing == "Linear") current_easing_ = EasingType::Linear;
    else if (easing == "QuadIn") current_easing_ = EasingType::QuadIn;
    else if (easing == "QuadOut") current_easing_ = EasingType::QuadOut;
    else if (easing == "QuadInOut") current_easing_ = EasingType::QuadInOut;
    else if (easing == "CubicIn") current_easing_ = EasingType::CubicIn;
    else if (easing == "CubicOut") current_easing_ = EasingType::CubicOut;
    else if (easing == "CubicInOut") current_easing_ = EasingType::CubicInOut;
    else if (easing == "ElasticIn") current_easing_ = EasingType::ElasticIn;
    else if (easing == "ElasticOut") current_easing_ = EasingType::ElasticOut;
    else if (easing == "BounceOut") current_easing_ = EasingType::BounceOut;
}

void AdvancedAnimationExample::updateAnimationStatus() {
    // Update active animations count
    int total_active = active_animations_.size() + active_physics_animations_.size() + active_transitions_.size();
    active_animations_label_->setText(QString("Active Animations: %1").arg(total_active));

    // Update progress bar (simplified - shows if any animation is running)
    bool any_running = false;
    for (const auto& anim : active_animations_) {
        if (anim && anim->getState() == AnimationState::Running) {
            any_running = true;
            animation_progress_->setValue(static_cast<int>(anim->getProgress() * 100));
            break;
        }
    }

    if (!any_running) {
        animation_progress_->setValue(0);
    }

    // Clean up finished animations
    active_animations_.erase(
        std::remove_if(active_animations_.begin(), active_animations_.end(),
                      [](const std::weak_ptr<Animation>& weak_anim) {
                          auto anim = weak_anim.lock();
                          return !anim || anim->getState() == AnimationState::Finished;
                      }),
        active_animations_.end()
    );
}

void AdvancedAnimationExample::createTimelineAnimation() {
    // Create a multi-keyframe timeline animation
    MultiPropertyTimeline timeline;

    timeline.at(0.0)
        .set("x", 50)
        .set("y", 50)
        .easing(EasingType::QuadOut)
        .commit();

    timeline.at(0.3)
        .set("x", 200)
        .set("y", 100)
        .easing(EasingType::ElasticOut)
        .commit();

    timeline.at(0.7)
        .set("x", 300)
        .set("y", 200)
        .easing(EasingType::BounceOut)
        .commit();

    timeline.at(1.0)
        .set("x", 400)
        .set("y", 50)
        .easing(EasingType::QuadIn)
        .commit();

    auto animation = animation_engine_->createTimelineAnimation(timeline);
    animation->setTarget(animated_widget1_, "pos");

    // Add progress callback
    animation->addProgressCallback([](double progress, const QVariant& value) {
        // Update widget position based on timeline
        Q_UNUSED(value)
        Q_UNUSED(progress)
    });

    active_animations_.push_back(animation);
    animation->start();
}

void AdvancedAnimationExample::createPhysicsAnimation() {
    // Create a spring-based physics animation
    QPoint current_pos = animated_widget2_->pos();
    QPoint target_pos = current_pos + QPoint(200, 150);

    auto physics_anim = animation_engine_->createSpringAnimation(
        animated_widget2_, "pos",
        QVariant::fromValue(target_pos),
        current_stiffness_, current_damping_
    );

    // Set additional physics properties
    PhysicsProperties physics;
    physics.mass = current_mass_;
    physics.gravity = current_gravity_;
    physics.friction = current_friction_;
    physics.use_physics = true;

    physics_anim->setPhysicsProperties(physics);

    // Connect signals
    connect(physics_anim.get(), &PhysicsAnimation::finished, [this]() {
        logAnimationEvent("⚡ Physics animation completed");
    });

    active_physics_animations_.push_back(physics_anim);
    physics_anim->start();
}

void AdvancedAnimationExample::createTransitionAnimation() {
    // Create a page transition effect
    auto transition = animation_engine_->createPageTransition(
        animated_widget3_, animated_widget4_,
        TransitionType::Fade, current_duration_
    );

    // Connect signals
    connect(transition.get(), &TransitionAnimator::finished, [this]() {
        logAnimationEvent("🔄 Transition animation completed");
    });

    active_transitions_.push_back(transition);
    transition->start();
}

void AdvancedAnimationExample::createMultiPropertyAnimation() {
    // Create an animation that animates multiple properties simultaneously
    auto animation = animation_engine_->createAnimation();
    animation->setTarget(animated_widget1_, "geometry");

    QRect current_geometry = animated_widget1_->geometry();
    QRect target_geometry = current_geometry.adjusted(100, 50, 150, 100);

    animation->setValues(QVariant::fromValue(current_geometry), QVariant::fromValue(target_geometry));

    AnimationProperties props;
    props.duration_ms = current_duration_;
    props.easing = current_easing_;
    props.auto_reverse = true;
    props.repeat_count = 2;

    animation->setProperties(props);

    // Add value transformer for custom effects
    animation->setValueTransformer([](const QVariant& value, double progress) {
        QRect rect = value.toRect();
        // Add a wobble effect
        int wobble = static_cast<int>(10 * std::sin(progress * 10 * M_PI));
        rect.adjust(wobble, wobble, wobble, wobble);
        return QVariant::fromValue(rect);
    });

    active_animations_.push_back(animation);
    animation->start();
}

void AdvancedAnimationExample::createSequenceAnimation() {
    // Create a sequence of animations
    auto sequence = animation_engine_->sequence()
        .then(animation_engine_->fadeOut(animated_widget2_, 500))
        .wait(200)
        .then(animation_engine_->slideIn(animated_widget2_, "right", 800))
        .then(animation_engine_->scaleAnimation(animated_widget2_, 1.0, 1.5, 600))
        .wait(300)
        .then(animation_engine_->scaleAnimation(animated_widget2_, 1.5, 1.0, 400))
        .build();

    // Start the sequence
    sequence->start();

    logAnimationEvent("📋 Animation sequence with 6 steps started");
}

void AdvancedAnimationExample::createRippleEffect() {
    // Create a ripple effect from the center of the widget
    QPoint center = animated_widget3_->rect().center();
    auto ripple = animation_engine_->createRippleEffect(animated_widget3_, center, 800);

    // Add custom transition effect
    TransitionConfig ripple_config;
    ripple_config.type = TransitionType::Ripple;
    ripple_config.origin = center;
    ripple_config.intensity = 2.0;

    ripple->setTransition(ripple_config);

    active_animations_.push_back(ripple);
    ripple->start();
}

void AdvancedAnimationExample::createMorphAnimation() {
    // Create a morph animation that changes the widget's shape
    QRect current_geometry = animated_widget4_->geometry();
    QRect target_geometry = QRect(
        current_geometry.x() - 50,
        current_geometry.y() - 30,
        current_geometry.width() + 100,
        current_geometry.height() + 60
    );

    auto morph = animation_engine_->createMorphAnimation(animated_widget4_, target_geometry, current_duration_);

    // Add elastic easing for smooth morphing
    AnimationProperties props;
    props.duration_ms = current_duration_;
    props.easing = EasingType::ElasticOut;
    props.auto_reverse = true;

    morph->setProperties(props);

    active_animations_.push_back(morph);
    morph->start();
}

void AdvancedAnimationExample::logAnimationEvent(const QString& message) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString log_message = QString("[%1] %2").arg(timestamp, message);

    status_text_->append(log_message);

    // Keep only the last 10 log entries
    QStringList lines = status_text_->toPlainText().split('\n');
    if (lines.size() > 10) {
        lines = lines.mid(lines.size() - 10);
        status_text_->setPlainText(lines.join('\n'));
    }

    // Scroll to bottom
    status_text_->moveCursor(QTextCursor::End);

    qDebug() << log_message;
}

// **Main function for the example**
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    AdvancedAnimationExample window;
    window.show();

    return app.exec();
}
