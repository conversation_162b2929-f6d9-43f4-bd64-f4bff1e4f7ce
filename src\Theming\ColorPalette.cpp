#include "ThemeManager.hpp"
#include <QDebug>
#include <QApplication>
#include <QPalette>
#include <cmath>

namespace DeclarativeUI::Theming {

/**
 * @brief Utility class for advanced color palette operations
 */
class ColorPaletteUtils {
public:
    /**
     * @brief Calculate the luminance of a color
     */
    static double calculateLuminance(const QColor& color) {
        // Convert to linear RGB
        auto toLinear = [](double c) {
            if (c <= 0.03928) {
                return c / 12.92;
            } else {
                return std::pow((c + 0.055) / 1.055, 2.4);
            }
        };
        
        double r = toLinear(color.redF());
        double g = toLinear(color.greenF());
        double b = toLinear(color.blueF());
        
        // Calculate relative luminance
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }
    
    /**
     * @brief Calculate contrast ratio between two colors
     */
    static double calculateContrastRatio(const QColor& color1, const QColor& color2) {
        double lum1 = calculateLuminance(color1);
        double lum2 = calculateLuminance(color2);
        
        double lighter = std::max(lum1, lum2);
        double darker = std::min(lum1, lum2);
        
        return (lighter + 0.05) / (darker + 0.05);
    }
    
    /**
     * @brief Check if color combination meets WCAG contrast requirements
     */
    static bool meetsWCAGContrast(const QColor& foreground, const QColor& background, 
                                  double minimumRatio = 4.5) {
        return calculateContrastRatio(foreground, background) >= minimumRatio;
    }
    
    /**
     * @brief Generate a harmonious color palette from a base color
     */
    static QList<QColor> generateHarmoniousPalette(const QColor& baseColor, int count = 5) {
        QList<QColor> palette;
        palette.append(baseColor);
        
        QColor hsv = baseColor.toHsv();
        int baseHue = hsv.hue();
        int baseSaturation = hsv.saturation();
        int baseValue = hsv.value();
        
        // Generate complementary and analogous colors
        for (int i = 1; i < count; ++i) {
            int hueOffset = (360 / count) * i;
            int newHue = (baseHue + hueOffset) % 360;
            
            // Vary saturation and value slightly for more natural look
            int saturationVariation = (i % 2 == 0) ? 20 : -20;
            int valueVariation = (i % 3 == 0) ? 30 : -15;
            
            int newSaturation = qBound(0, baseSaturation + saturationVariation, 255);
            int newValue = qBound(0, baseValue + valueVariation, 255);
            
            QColor newColor = QColor::fromHsv(newHue, newSaturation, newValue);
            palette.append(newColor);
        }
        
        return palette;
    }
    
    /**
     * @brief Generate a monochromatic palette from a base color
     */
    static QList<QColor> generateMonochromaticPalette(const QColor& baseColor, int count = 5) {
        QList<QColor> palette;
        
        QColor hsv = baseColor.toHsv();
        int baseHue = hsv.hue();
        int baseSaturation = hsv.saturation();
        int baseValue = hsv.value();
        
        // Generate lighter and darker variants
        for (int i = 0; i < count; ++i) {
            double factor = 1.0 - (2.0 * i / (count - 1) - 1.0) * 0.4; // Range from 0.6 to 1.4
            int newValue = qBound(0, static_cast<int>(baseValue * factor), 255);
            
            QColor newColor = QColor::fromHsv(baseHue, baseSaturation, newValue);
            palette.append(newColor);
        }
        
        return palette;
    }
    
    /**
     * @brief Adjust color for better accessibility
     */
    static QColor adjustForAccessibility(const QColor& color, const QColor& background, 
                                       double targetContrast = 4.5) {
        if (meetsWCAGContrast(color, background, targetContrast)) {
            return color;
        }
        
        QColor adjusted = color;
        QColor hsv = color.toHsv();
        
        // Try adjusting lightness first
        for (int value = 0; value <= 255; value += 5) {
            adjusted = QColor::fromHsv(hsv.hue(), hsv.saturation(), value);
            if (meetsWCAGContrast(adjusted, background, targetContrast)) {
                return adjusted;
            }
        }
        
        // If lightness adjustment doesn't work, try adjusting saturation
        for (int saturation = 255; saturation >= 0; saturation -= 5) {
            for (int value = 0; value <= 255; value += 10) {
                adjusted = QColor::fromHsv(hsv.hue(), saturation, value);
                if (meetsWCAGContrast(adjusted, background, targetContrast)) {
                    return adjusted;
                }
            }
        }
        
        // Fallback: return high contrast color
        double backgroundLuminance = calculateLuminance(background);
        return (backgroundLuminance > 0.5) ? QColor(0, 0, 0) : QColor(255, 255, 255);
    }
    
    /**
     * @brief Create a color palette optimized for dark mode
     */
    static ThemeManager::ColorPalette createDarkModePalette(const ThemeManager::ColorPalette& lightPalette) {
        ThemeManager::ColorPalette darkPalette = lightPalette;
        
        // Invert background colors
        darkPalette.background = QColor(18, 18, 18);        // Very dark gray
        darkPalette.surface = QColor(30, 30, 30);           // Dark gray
        darkPalette.card = QColor(45, 45, 45);              // Medium dark gray
        
        // Adjust text colors for dark background
        darkPalette.text_primary = QColor(255, 255, 255);   // White
        darkPalette.text_secondary = QColor(179, 179, 179); // Light gray
        darkPalette.text_disabled = QColor(102, 102, 102);  // Medium gray
        
        // Adjust border and divider colors
        darkPalette.border = QColor(64, 64, 64);            // Dark border
        darkPalette.divider = QColor(51, 51, 51);           // Darker divider
        
        // Adjust interactive colors for dark mode
        darkPalette.hover = QColor(45, 45, 45);             // Slightly lighter than surface
        darkPalette.disabled = QColor(51, 51, 51);          // Dark disabled state
        
        // Keep primary and secondary colors but ensure good contrast
        darkPalette.primary = adjustForAccessibility(lightPalette.primary, darkPalette.background);
        darkPalette.secondary = adjustForAccessibility(lightPalette.secondary, darkPalette.background);
        
        // Adjust state colors for dark mode
        darkPalette.success = adjustForAccessibility(lightPalette.success, darkPalette.background);
        darkPalette.warning = adjustForAccessibility(lightPalette.warning, darkPalette.background);
        darkPalette.error = adjustForAccessibility(lightPalette.error, darkPalette.background);
        darkPalette.info = adjustForAccessibility(lightPalette.info, darkPalette.background);
        
        return darkPalette;
    }
    
    /**
     * @brief Validate color palette for accessibility compliance
     */
    static QStringList validatePaletteAccessibility(const ThemeManager::ColorPalette& palette) {
        QStringList issues;
        
        // Check primary text contrast
        if (!meetsWCAGContrast(palette.text_primary, palette.background, 4.5)) {
            issues.append("Primary text does not meet WCAG AA contrast requirements");
        }
        
        // Check secondary text contrast
        if (!meetsWCAGContrast(palette.text_secondary, palette.background, 3.0)) {
            issues.append("Secondary text does not meet WCAG AA contrast requirements");
        }
        
        // Check primary button contrast
        if (!meetsWCAGContrast(palette.background, palette.primary, 3.0)) {
            issues.append("Primary button does not meet WCAG AA contrast requirements");
        }
        
        // Check error text contrast
        if (!meetsWCAGContrast(palette.error, palette.background, 4.5)) {
            issues.append("Error color does not meet WCAG AA contrast requirements");
        }
        
        // Check warning text contrast
        if (!meetsWCAGContrast(palette.warning, palette.background, 4.5)) {
            issues.append("Warning color does not meet WCAG AA contrast requirements");
        }
        
        // Check success text contrast
        if (!meetsWCAGContrast(palette.success, palette.background, 4.5)) {
            issues.append("Success color does not meet WCAG AA contrast requirements");
        }
        
        return issues;
    }
    
    /**
     * @brief Generate CSS custom properties from color palette
     */
    static QString generateCSSCustomProperties(const ThemeManager::ColorPalette& palette) {
        QString css = ":root {\n";
        
        css += QString("  --color-primary: %1;\n").arg(palette.primary.name());
        css += QString("  --color-primary-light: %1;\n").arg(palette.primary_light.name());
        css += QString("  --color-primary-dark: %1;\n").arg(palette.primary_dark.name());
        css += QString("  --color-secondary: %1;\n").arg(palette.secondary.name());
        css += QString("  --color-secondary-light: %1;\n").arg(palette.secondary_light.name());
        css += QString("  --color-secondary-dark: %1;\n").arg(palette.secondary_dark.name());
        css += QString("  --color-background: %1;\n").arg(palette.background.name());
        css += QString("  --color-surface: %1;\n").arg(palette.surface.name());
        css += QString("  --color-card: %1;\n").arg(palette.card.name());
        css += QString("  --color-text-primary: %1;\n").arg(palette.text_primary.name());
        css += QString("  --color-text-secondary: %1;\n").arg(palette.text_secondary.name());
        css += QString("  --color-text-disabled: %1;\n").arg(palette.text_disabled.name());
        css += QString("  --color-success: %1;\n").arg(palette.success.name());
        css += QString("  --color-warning: %1;\n").arg(palette.warning.name());
        css += QString("  --color-error: %1;\n").arg(palette.error.name());
        css += QString("  --color-info: %1;\n").arg(palette.info.name());
        css += QString("  --color-border: %1;\n").arg(palette.border.name());
        css += QString("  --color-divider: %1;\n").arg(palette.divider.name());
        css += QString("  --color-hover: %1;\n").arg(palette.hover.name());
        css += QString("  --color-focus: %1;\n").arg(palette.focus.name());
        css += QString("  --color-active: %1;\n").arg(palette.active.name());
        css += QString("  --color-disabled: %1;\n").arg(palette.disabled.name());
        
        css += "}\n";
        
        return css;
    }
};

} // namespace DeclarativeUI::Theming
