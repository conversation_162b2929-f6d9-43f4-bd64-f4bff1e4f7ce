#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <memory>

#include "../../src/HotReload/HotReloadConfig.hpp"

using namespace DeclarativeUI::HotReload;

class TestEnhancedConfigurationSystemSimple : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic tests for actual classes
    void testConfigProfileCreation();
    void testConfigProfileSerialization();
    void testFileFilterConfig();
    void testHotReloadConfigBasics();

private:
    QTemporaryDir* temp_dir_;
    std::unique_ptr<HotReloadConfig> config_;
    QString config_file_path_;
};

void TestEnhancedConfigurationSystemSimple::initTestCase() {
    temp_dir_ = new QTemporaryDir();
    QVERIFY(temp_dir_->isValid());
    config_file_path_ = temp_dir_->path() + "/test_config.json";
}

void TestEnhancedConfigurationSystemSimple::cleanupTestCase() {
    delete temp_dir_;
}

void TestEnhancedConfigurationSystemSimple::init() {
    config_ = std::make_unique<HotReloadConfig>();
}

void TestEnhancedConfigurationSystemSimple::cleanup() {
    config_.reset();
}

void TestEnhancedConfigurationSystemSimple::testConfigProfileCreation() {
    // Test ConfigProfile creation and basic properties
    ConfigProfile profile;
    profile.name = "test_profile";
    profile.description = "Test profile for unit testing";
    profile.is_active = true;
    
    QCOMPARE(profile.name, "test_profile");
    QCOMPARE(profile.description, "Test profile for unit testing");
    QVERIFY(profile.is_active);
}

void TestEnhancedConfigurationSystemSimple::testConfigProfileSerialization() {
    // Test ConfigProfile JSON serialization
    ConfigProfile profile;
    profile.name = "serialization_test";
    profile.description = "Test profile for serialization";
    profile.is_active = false;

    // Configure file filter - clear defaults first, then set our patterns
    profile.file_filter.include_patterns.clear();
    profile.file_filter.include_patterns = {"*.qml", "*.js"};
    profile.file_filter.exclude_patterns.clear();
    profile.file_filter.exclude_patterns = {"*.tmp"};
    profile.file_filter.max_file_size_bytes = 1024 * 1024; // 1MB

    // Serialize to JSON
    QJsonObject json = profile.toJson();
    QVERIFY(!json.isEmpty());
    QCOMPARE(json["name"].toString(), "serialization_test");
    QCOMPARE(json["description"].toString(), "Test profile for serialization");
    QCOMPARE(json["is_active"].toBool(), false);

    // Deserialize from JSON
    ConfigProfile restored = ConfigProfile::fromJson(json);
    QCOMPARE(restored.name, profile.name);
    QCOMPARE(restored.description, profile.description);
    QCOMPARE(restored.is_active, profile.is_active);

    // Note: fromJson appends to default patterns, so we check for our patterns being present
    QVERIFY(restored.file_filter.include_patterns.contains("*.qml"));
    QVERIFY(restored.file_filter.include_patterns.contains("*.js"));
    QVERIFY(restored.file_filter.exclude_patterns.contains("*.tmp"));
}

void TestEnhancedConfigurationSystemSimple::testFileFilterConfig() {
    // Test FileFilterConfig functionality
    FileFilterConfig filter;
    filter.include_patterns.clear(); // Clear defaults
    filter.include_patterns = {"*.qml", "*.css"};
    filter.exclude_patterns.clear(); // Clear defaults
    filter.exclude_patterns = {"*.tmp", "*.bak"};
    filter.max_file_size_bytes = 1024 * 1024; // 1MB

    // Test file matching
    QVERIFY(filter.matchesFile("test.qml"));
    QVERIFY(filter.matchesFile("style.css"));
    QVERIFY(!filter.matchesFile("temp.tmp"));
    QVERIFY(!filter.matchesFile("backup.bak"));
    QVERIFY(!filter.matchesFile("script.js")); // Not in include patterns

    // Test JSON serialization
    QJsonObject json = filter.toJson();
    QVERIFY(!json.isEmpty());

    FileFilterConfig restored = FileFilterConfig::fromJson(json);
    // Note: fromJson appends to default patterns, so we check for our patterns being present
    QVERIFY(restored.include_patterns.contains("*.qml"));
    QVERIFY(restored.include_patterns.contains("*.css"));
    QVERIFY(restored.exclude_patterns.contains("*.tmp"));
    QVERIFY(restored.exclude_patterns.contains("*.bak"));
    QCOMPARE(restored.max_file_size_bytes, filter.max_file_size_bytes);
}

void TestEnhancedConfigurationSystemSimple::testHotReloadConfigBasics() {
    // Test basic HotReloadConfig functionality
    QVERIFY(config_ != nullptr);
    
    // Test that we can create and use the config object
    // Note: We're testing basic instantiation since the actual methods
    // depend on the implementation details
    
    // Test signal connections work
    QSignalSpy spy(config_.get(), &HotReloadConfig::configurationChanged);
    QVERIFY(spy.isValid());
}

QTEST_MAIN(TestEnhancedConfigurationSystemSimple)
#include "test_enhanced_configuration_system_simple.moc"
