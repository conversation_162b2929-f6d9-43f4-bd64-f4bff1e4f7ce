# **Examples Build Configuration**
# This file contains all example applications
# Examples are built conditionally based on BUILD_EXAMPLES option

# **Enable Qt MOC processing for all examples**
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

# **Add subdirectories for example categories**
add_subdirectory(basic)
add_subdirectory(components)
add_subdirectory(command)
add_subdirectory(advanced)

# **Testing Framework Example (conditional)**
if(BUILD_TESTING_FRAMEWORK)
    add_executable(TestingFrameworkExample
        TestingFrameworkExample.hpp
        TestingFrameworkExample.cpp
        TestingFrameworkMain.cpp
    )

    target_link_libraries(TestingFrameworkExample
        DeclarativeUI
        Qt6::Core
        Qt6::Widgets
        Qt6::Test
        Qt6::Concurrent
    )

    set_target_properties(TestingFrameworkExample PROPERTIES
        AUTOMOC ON
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/examples
    )

    target_include_directories(TestingFrameworkExample PRIVATE
        ${CMAKE_SOURCE_DIR}/src
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    message(STATUS "✅ Testing Framework Example will be built")
endif()

# **Copy resources for examples**
add_custom_target(CopyExampleResources ALL
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_SOURCE_DIR}/Resources
    ${CMAKE_BINARY_DIR}/examples/Resources
)

# **Copy example-specific resources if they exist**
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/resources)
    add_custom_target(CopyExampleSpecificResources ALL
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        ${CMAKE_CURRENT_SOURCE_DIR}/resources
        ${CMAKE_BINARY_DIR}/examples/resources
    )
    message(STATUS "Example-specific resources will be copied")
endif()
