#include <QtTest/QtTest>
#include <QTemporaryFile>
#include <QJsonDocument>
#include <QJsonObject>
#include "../../src/HotReload/HotReloadConfig.hpp"

using namespace DeclarativeUI::HotReload;

class TestHotReloadConfig : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // **Configuration structure tests**
    void testFileFilterConfigSerialization();
    void testPerformanceConfigSerialization();
    void testErrorRecoveryConfigSerialization();
    void testFileWatchConfigSerialization();
    void testReloadConfigSerialization();
    void testConfigProfileSerialization();

    // **Main configuration class tests**
    void testHotReloadConfigDefaults();
    void testConfigurationSaveLoad();
    void testProfileManagement();
    void testDefaultProfiles();
    void testConfigurationValidation();
    void testRuntimeConfigurationUpdates();
    void testEnvironmentDetection();

    // **File filtering tests**
    void testFileFilterMatching();
    void testContentFilterMatching();

private:
    HotReloadConfig* config_;
    QTemporaryFile* temp_file_;
};

void TestHotReloadConfig::initTestCase() {
    // Setup for all tests
}

void TestHotReloadConfig::cleanupTestCase() {
    // Cleanup after all tests
}

void TestHotReloadConfig::init() {
    config_ = new HotReloadConfig(this);
    temp_file_ = new QTemporaryFile(this);
    temp_file_->open();
}

void TestHotReloadConfig::cleanup() {
    delete config_;
    config_ = nullptr;
    delete temp_file_;
    temp_file_ = nullptr;
}

void TestHotReloadConfig::testFileFilterConfigSerialization() {
    FileFilterConfig original;
    original.include_patterns = {"*.qml", "*.js"};
    original.exclude_patterns = {"*.tmp"};
    original.max_file_size_bytes = 1024 * 1024;
    original.enable_content_filtering = true;
    
    QJsonObject json = original.toJson();
    FileFilterConfig deserialized = FileFilterConfig::fromJson(json);
    
    QCOMPARE(deserialized.include_patterns, original.include_patterns);
    QCOMPARE(deserialized.exclude_patterns, original.exclude_patterns);
    QCOMPARE(deserialized.max_file_size_bytes, original.max_file_size_bytes);
    QCOMPARE(deserialized.enable_content_filtering, original.enable_content_filtering);
}

void TestHotReloadConfig::testPerformanceConfigSerialization() {
    PerformanceConfig original;
    original.enable_monitoring = false;
    original.warning_threshold_ms = 2000;
    original.enable_automatic_optimizations = true;
    
    QJsonObject json = original.toJson();
    PerformanceConfig deserialized = PerformanceConfig::fromJson(json);
    
    QCOMPARE(deserialized.enable_monitoring, original.enable_monitoring);
    QCOMPARE(deserialized.warning_threshold_ms, original.warning_threshold_ms);
    QCOMPARE(deserialized.enable_automatic_optimizations, original.enable_automatic_optimizations);
}

void TestHotReloadConfig::testErrorRecoveryConfigSerialization() {
    ErrorRecoveryConfig original;
    original.enable_automatic_rollback = false;
    original.max_rollback_points = 5;
    original.default_recovery_strategy = ErrorRecoveryConfig::RecoveryStrategy::Retry;
    
    QJsonObject json = original.toJson();
    ErrorRecoveryConfig deserialized = ErrorRecoveryConfig::fromJson(json);
    
    QCOMPARE(deserialized.enable_automatic_rollback, original.enable_automatic_rollback);
    QCOMPARE(deserialized.max_rollback_points, original.max_rollback_points);
    QCOMPARE(static_cast<int>(deserialized.default_recovery_strategy), 
             static_cast<int>(original.default_recovery_strategy));
}

void TestHotReloadConfig::testFileWatchConfigSerialization() {
    FileWatchConfig original;
    original.enable_file_watching = false;
    original.debounce_interval_ms = 200;
    original.max_watched_files = 5000;
    
    QJsonObject json = original.toJson();
    FileWatchConfig deserialized = FileWatchConfig::fromJson(json);
    
    QCOMPARE(deserialized.enable_file_watching, original.enable_file_watching);
    QCOMPARE(deserialized.debounce_interval_ms, original.debounce_interval_ms);
    QCOMPARE(deserialized.max_watched_files, original.max_watched_files);
}

void TestHotReloadConfig::testReloadConfigSerialization() {
    ReloadConfig original;
    original.strategy = ReloadConfig::Strategy::Batched;
    original.reload_delay_ms = 300;
    original.max_concurrent_reloads = 2;
    
    QJsonObject json = original.toJson();
    ReloadConfig deserialized = ReloadConfig::fromJson(json);
    
    QCOMPARE(static_cast<int>(deserialized.strategy), static_cast<int>(original.strategy));
    QCOMPARE(deserialized.reload_delay_ms, original.reload_delay_ms);
    QCOMPARE(deserialized.max_concurrent_reloads, original.max_concurrent_reloads);
}

void TestHotReloadConfig::testConfigProfileSerialization() {
    ConfigProfile original;
    original.name = "TestProfile";
    original.description = "Test profile description";
    original.is_active = true;
    
    QJsonObject json = original.toJson();
    ConfigProfile deserialized = ConfigProfile::fromJson(json);
    
    QCOMPARE(deserialized.name, original.name);
    QCOMPARE(deserialized.description, original.description);
    QCOMPARE(deserialized.is_active, original.is_active);
}

void TestHotReloadConfig::testHotReloadConfigDefaults() {
    // Test that default configuration is valid
    QVERIFY(config_->getFileFilterConfig().include_patterns.size() > 0);
    QVERIFY(config_->getPerformanceConfig().warning_threshold_ms > 0);
    QVERIFY(config_->getFileWatchConfig().debounce_interval_ms > 0);
    QVERIFY(config_->getReloadConfig().max_concurrent_reloads > 0);
}

void TestHotReloadConfig::testConfigurationSaveLoad() {
    // Modify configuration
    FileFilterConfig filter_config = config_->getFileFilterConfig();
    filter_config.include_patterns = {"*.test"};
    config_->setFileFilterConfig(filter_config);
    
    // Save to file
    QString file_path = temp_file_->fileName();
    QVERIFY(config_->saveToFile(file_path));
    
    // Create new config and load
    HotReloadConfig new_config;
    QVERIFY(new_config.loadFromFile(file_path));
    
    // Verify loaded configuration
    QCOMPARE(new_config.getFileFilterConfig().include_patterns, 
             QStringList({"*.test"}));
}

void TestHotReloadConfig::testProfileManagement() {
    // Test adding profile
    ConfigProfile test_profile;
    test_profile.name = "TestProfile";
    test_profile.description = "Test description";
    
    config_->addProfile(test_profile);
    QVERIFY(config_->getProfileNames().contains("TestProfile"));
    
    // Test setting active profile
    QVERIFY(config_->setActiveProfile("TestProfile"));
    QCOMPARE(config_->getActiveProfileName(), QString("TestProfile"));
    
    // Test removing profile
    config_->removeProfile("TestProfile");
    QVERIFY(!config_->getProfileNames().contains("TestProfile"));
}

void TestHotReloadConfig::testDefaultProfiles() {
    // Test that default profiles are created
    QStringList profile_names = config_->getProfileNames();
    QVERIFY(profile_names.contains("Development"));
    QVERIFY(profile_names.contains("Production"));
    QVERIFY(profile_names.contains("Testing"));
    QVERIFY(profile_names.contains("Minimal"));
    
    // Test profile characteristics
    ConfigProfile dev_profile = config_->getProfile("Development");
    QVERIFY(dev_profile.performance.enable_monitoring);
    QVERIFY(dev_profile.reload.reload_delay_ms < 100);
    
    ConfigProfile prod_profile = config_->getProfile("Production");
    QVERIFY(!prod_profile.performance.enable_real_time_analytics);
    QVERIFY(prod_profile.reload.reload_delay_ms >= 1000);
}

void TestHotReloadConfig::testConfigurationValidation() {
    // Test that default configuration is valid
    bool validation_passed = true;
    try {
        config_->validateConfiguration();
    } catch (...) {
        validation_passed = false;
    }
    QVERIFY(validation_passed);

    // Test invalid configuration
    FileFilterConfig invalid_filter;
    invalid_filter.include_patterns.clear(); // Invalid: no patterns
    config_->setFileFilterConfig(invalid_filter);

    bool exception_thrown = false;
    try {
        config_->validateConfiguration();
    } catch (const std::invalid_argument&) {
        exception_thrown = true;
    }
    QVERIFY(exception_thrown);
}

void TestHotReloadConfig::testRuntimeConfigurationUpdates() {
    QSignalSpy spy(config_, &HotReloadConfig::configurationChanged);
    
    // Test configuration update
    QJsonObject updates;
    updates["file_filter"] = QJsonObject{{"max_file_size_bytes", 2048}};
    
    config_->updateConfiguration(updates);
    
    QCOMPARE(spy.count(), 1);
    QCOMPARE(config_->getFileFilterConfig().max_file_size_bytes, qint64(2048));
}

void TestHotReloadConfig::testEnvironmentDetection() {
    QString env = HotReloadConfig::detectEnvironment();
    QVERIFY(!env.isEmpty());
    QVERIFY(env == "development" || env == "production");
}

void TestHotReloadConfig::testFileFilterMatching() {
    FileFilterConfig filter;
    filter.include_patterns = {"*.qml", "*.js"};
    filter.exclude_patterns = {"*.tmp"};
    
    QVERIFY(filter.matchesFile("test.qml"));
    QVERIFY(filter.matchesFile("script.js"));
    QVERIFY(!filter.matchesFile("temp.tmp"));
    QVERIFY(!filter.matchesFile("document.txt"));
}

void TestHotReloadConfig::testContentFilterMatching() {
    FileFilterConfig filter;
    filter.enable_content_filtering = true;
    filter.content_include_patterns = {"import.*Qt"};
    filter.content_exclude_patterns = {"// IGNORE"};
    
    QVERIFY(filter.matchesContent("import QtQuick 2.0"));
    QVERIFY(!filter.matchesContent("// IGNORE this file"));
    QVERIFY(!filter.matchesContent("plain text content"));
}

QTEST_MAIN(TestHotReloadConfig)
#include "test_hot_reload_config.moc"
