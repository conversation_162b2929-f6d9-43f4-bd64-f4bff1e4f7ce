#!/bin/bash
# Check for proper namespace usage in C++ files

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

errors=0
warnings=0

echo "🔍 Checking namespace usage patterns..."

for file in "$@"; do
    if [[ ! -f "$file" ]]; then
        continue
    fi
    
    echo "Checking: $file"
    
    # Check for global namespace pollution in headers
    if [[ "$file" == *.h || "$file" == *.hpp ]]; then
        if grep -n "using namespace" "$file" >/dev/null 2>&1; then
            echo -e "${RED}❌ Error: 'using namespace' found in header file $file${NC}"
            echo "   This can cause namespace pollution for users of the header"
            ((errors++))
        fi
    fi
    
    # Check for proper DeclarativeUI namespace usage
    if grep -n "namespace.*DeclarativeUI" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Using DeclarativeUI namespace in $file${NC}"
    fi
    
    # Check for nested namespace declarations (C++17 style)
    if grep -n "namespace.*::" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Using nested namespace syntax in $file${NC}"
    fi
    
    # Check for anonymous namespaces in implementation files
    if [[ "$file" == *.cpp ]]; then
        if grep -n "namespace\s*{" "$file" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Good: Using anonymous namespace for internal linkage in $file${NC}"
        fi
    fi
    
    # Check for std namespace usage
    if grep -n "using namespace std" "$file" >/dev/null 2>&1; then
        if [[ "$file" == *.h || "$file" == *.hpp ]]; then
            echo -e "${RED}❌ Error: 'using namespace std' in header file $file${NC}"
            ((errors++))
        else
            echo -e "${YELLOW}⚠️  Warning: 'using namespace std' found in $file${NC}"
            echo "   Consider using specific std:: prefixes for better code clarity"
            ((warnings++))
        fi
    fi
    
    # Check for Qt namespace usage
    if grep -n "using namespace Qt" "$file" >/dev/null 2>&1; then
        if [[ "$file" == *.h || "$file" == *.hpp ]]; then
            echo -e "${RED}❌ Error: 'using namespace Qt' in header file $file${NC}"
            ((errors++))
        else
            echo -e "${YELLOW}⚠️  Warning: 'using namespace Qt' found in $file${NC}"
            echo "   Consider using Qt:: prefix for better code clarity"
            ((warnings++))
        fi
    fi
    
    # Check for proper include guards or pragma once
    if [[ "$file" == *.h || "$file" == *.hpp ]]; then
        if ! grep -n "#pragma once\|#ifndef.*_H\|#ifndef.*_HPP" "$file" >/dev/null 2>&1; then
            echo -e "${RED}❌ Error: Missing include guard or #pragma once in $file${NC}"
            ((errors++))
        fi
    fi
    
done

echo ""
echo "📊 Summary:"
echo "   Errors: $errors"
echo "   Warnings: $warnings"

if [ $errors -gt 0 ]; then
    echo -e "${RED}❌ Namespace check failed with $errors errors${NC}"
    exit 1
elif [ $warnings -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Namespace check completed with $warnings warnings${NC}"
    exit 0
else
    echo -e "${GREEN}✅ Namespace check passed${NC}"
    exit 0
fi
