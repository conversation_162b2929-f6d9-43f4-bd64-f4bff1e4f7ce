#pragma once

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QSlider>
#include <QProgressBar>
#include <QSpinBox>
#include <QGroupBox>
#include <QTabWidget>
#include <QSplitter>
#include <QTreeWidget>
#include <QTableWidget>
#include <QListWidget>
#include <QScrollArea>
#include <QFrame>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QAction>
#include <QTimer>
#include <QElapsedTimer>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QFileDialog>
#include <QMessageBox>
#include <QApplication>
#include <QScreen>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>

#include "../src/Testing/TestingFramework.hpp"
#include "../src/Testing/TestRunner.hpp"

#include <memory>
#include <vector>

/**
 * @brief Comprehensive example application demonstrating the Testing Framework
 * 
 * This example showcases:
 * - UI automation and interaction simulation
 * - Visual regression testing with screenshot comparison
 * - Component testing with mock objects
 * - Performance benchmarking and profiling
 * - Test execution with parallel processing
 * - Accessibility testing utilities
 * - Memory leak detection
 * - Comprehensive test reporting
 */
namespace DeclarativeUI::Testing::Examples {

/**
 * @brief Main window for the testing framework example
 */
class TestingFrameworkExample : public QMainWindow {
    Q_OBJECT

public:
    explicit TestingFrameworkExample(QWidget* parent = nullptr);
    ~TestingFrameworkExample();

private slots:
    // **Test Execution**
    void runAllTests();
    void runSelectedTests();
    void runPerformanceTests();
    void runVisualTests();
    void runAccessibilityTests();
    void stopTestExecution();

    // **Test Management**
    void createSampleTests();
    void loadTestsFromFile();
    void saveTestConfiguration();
    void clearTestResults();

    // **UI Automation Demo**
    void demonstrateUIAutomation();
    void simulateUserInteractions();
    void testWidgetFinding();
    void testEventSimulation();

    // **Visual Testing Demo**
    void demonstrateVisualTesting();
    void captureBaselines();
    void compareWithBaselines();
    void showVisualDifferences();

    // **Component Testing Demo**
    void demonstrateComponentTesting();
    void testComponentCreation();
    void testComponentValidation();
    void testComponentInteractions();

    // **Performance Testing Demo**
    void demonstratePerformanceTesting();
    void benchmarkWidgetCreation();
    void benchmarkLayoutUpdates();
    void profileMemoryUsage();

    // **Test Results**
    void onTestStarted(const QString& test_name);
    void onTestFinished(const TestResult& result);
    void onExecutionFinished(int passed, int failed, qint64 total_time);
    void showTestDetails(const TestResult& result);

    // **Reporting**
    void generateHTMLReport();
    void generateJUnitXMLReport();
    void generateJSONReport();
    void exportTestResults();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupTestControlPanel();
    void setupTestResultsPanel();
    void setupDemoPanel();
    void setupConfigurationPanel();

    void updateTestProgress(int completed, int total);
    void updateTestStatistics();
    void populateTestList();
    void refreshTestResults();

    // **Sample Test Cases**
    class SampleUITest;
    class SamplePerformanceTest;
    class SampleVisualTest;
    class SampleComponentTest;
    class SampleAccessibilityTest;

    void createUITests();
    void createPerformanceTests();
    void createVisualTests();
    void createComponentTests();
    void createAccessibilityTests();

    // **UI Components**
    QWidget* central_widget_;
    QSplitter* main_splitter_;
    QTabWidget* main_tabs_;

    // **Test Control Panel**
    QWidget* test_control_panel_;
    QPushButton* run_all_button_;
    QPushButton* run_selected_button_;
    QPushButton* run_performance_button_;
    QPushButton* run_visual_button_;
    QPushButton* run_accessibility_button_;
    QPushButton* stop_button_;
    QPushButton* clear_results_button_;
    QProgressBar* test_progress_;
    QLabel* test_status_label_;

    // **Test Results Panel**
    QWidget* test_results_panel_;
    QTreeWidget* test_list_;
    QTextEdit* test_details_;
    QLabel* statistics_label_;

    // **Demo Panel**
    QWidget* demo_panel_;
    QTabWidget* demo_tabs_;
    QWidget* ui_automation_demo_;
    QWidget* visual_testing_demo_;
    QWidget* component_testing_demo_;
    QWidget* performance_testing_demo_;

    // **Configuration Panel**
    QWidget* config_panel_;
    QCheckBox* parallel_execution_checkbox_;
    QSpinBox* max_concurrent_spinbox_;
    QSpinBox* timeout_spinbox_;
    QCheckBox* fail_fast_checkbox_;
    QCheckBox* generate_html_checkbox_;
    QCheckBox* generate_xml_checkbox_;
    QLineEdit* output_directory_edit_;

    // **Demo Widgets for Testing**
    QWidget* demo_widget_container_;
    QPushButton* demo_button_;
    QLineEdit* demo_line_edit_;
    QLabel* demo_label_;
    QComboBox* demo_combo_box_;
    QCheckBox* demo_checkbox_;
    QSlider* demo_slider_;
    QProgressBar* demo_progress_bar_;

    // **Testing Framework Components**
    std::unique_ptr<TestRunner> test_runner_;
    std::unique_ptr<UIAutomation> ui_automation_;
    std::unique_ptr<VisualTesting> visual_testing_;
    std::unique_ptr<ComponentTester> component_tester_;
    std::unique_ptr<PerformanceBenchmark> performance_benchmark_;
    std::unique_ptr<AccessibilityTester> accessibility_tester_;
    std::unique_ptr<MemoryProfiler> memory_profiler_;

    // **Test Data**
    std::vector<std::unique_ptr<TestCase>> sample_tests_;
    std::vector<TestResult> test_results_;
    TestSuiteConfig test_config_;

    // **State**
    bool tests_running_ = false;
    QElapsedTimer execution_timer_;
    int total_tests_ = 0;
    int completed_tests_ = 0;
    int passed_tests_ = 0;
    int failed_tests_ = 0;
};

/**
 * @brief Sample UI test case demonstrating basic UI testing
 */
class TestingFrameworkExample::SampleUITest : public TestCase {
    Q_OBJECT

public:
    explicit SampleUITest(const QString& name, QWidget* target_widget);

    void setUp() override;
    void tearDown() override;
    void run() override;

private:
    QWidget* target_widget_;
    std::unique_ptr<UIAutomation> ui_automation_;
};

/**
 * @brief Sample performance test case demonstrating benchmarking
 */
class TestingFrameworkExample::SamplePerformanceTest : public TestCase {
    Q_OBJECT

public:
    explicit SamplePerformanceTest(const QString& name);

    void setUp() override;
    void tearDown() override;
    void run() override;

private:
    std::unique_ptr<PerformanceBenchmark> benchmark_;
};

/**
 * @brief Sample visual test case demonstrating visual regression testing
 */
class TestingFrameworkExample::SampleVisualTest : public TestCase {
    Q_OBJECT

public:
    explicit SampleVisualTest(const QString& name, QWidget* target_widget);

    void setUp() override;
    void tearDown() override;
    void run() override;

private:
    QWidget* target_widget_;
    std::unique_ptr<VisualTesting> visual_testing_;
};

/**
 * @brief Sample component test case demonstrating component validation
 */
class TestingFrameworkExample::SampleComponentTest : public TestCase {
    Q_OBJECT

public:
    explicit SampleComponentTest(const QString& name);

    void setUp() override;
    void tearDown() override;
    void run() override;

private:
    std::unique_ptr<ComponentTester> component_tester_;
    std::unique_ptr<QWidget> test_widget_;
};

/**
 * @brief Sample accessibility test case demonstrating accessibility validation
 */
class TestingFrameworkExample::SampleAccessibilityTest : public TestCase {
    Q_OBJECT

public:
    explicit SampleAccessibilityTest(const QString& name, QWidget* target_widget);

    void setUp() override;
    void tearDown() override;
    void run() override;

private:
    QWidget* target_widget_;
    std::unique_ptr<AccessibilityTester> accessibility_tester_;
};

/**
 * @brief Test configuration manager for saving/loading test settings
 */
class TestConfigurationManager {
public:
    static void saveConfiguration(const TestSuiteConfig& config, const QString& file_path);
    static TestSuiteConfig loadConfiguration(const QString& file_path);
    static TestSuiteConfig getDefaultConfiguration();

private:
    static QJsonObject configToJson(const TestSuiteConfig& config);
    static TestSuiteConfig configFromJson(const QJsonObject& json);
};

/**
 * @brief Test result exporter for various formats
 */
class TestResultExporter {
public:
    static void exportToCSV(const std::vector<TestResult>& results, const QString& file_path);
    static void exportToJSON(const std::vector<TestResult>& results, const QString& file_path);
    static void exportToXML(const std::vector<TestResult>& results, const QString& file_path);
    static void exportToHTML(const std::vector<TestResult>& results, const QString& file_path);

private:
    static QString resultToCSVRow(const TestResult& result);
    static QJsonObject resultToJson(const TestResult& result);
    static QString resultToXML(const TestResult& result);
    static QString resultToHTML(const TestResult& result);
};

} // namespace DeclarativeUI::Testing::Examples
