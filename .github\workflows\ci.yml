name: 🚀 Continuous Integration

on:
  push:
    branches: [ main, develop ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.github/ISSUE_TEMPLATE/**'
  pull_request:
    branches: [ main, develop ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.github/ISSUE_TEMPLATE/**'
  workflow_dispatch:

env:
  BUILD_TYPE: Release
  QT_VERSION: 6.7.2

jobs:
  # =============================================================================
  # Code Quality Checks
  # =============================================================================
  code-quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 🔧 Setup clang-format
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-format-15

    - name: 🎨 Check code formatting
      run: |
        find src tests examples -name "*.cpp" -o -name "*.hpp" -o -name "*.h" | \
        xargs clang-format-15 --dry-run --Werror

    - name: 📊 Check line endings
      run: |
        if git ls-files | xargs file | grep -q "CRLF"; then
          echo "❌ Found files with CRLF line endings"
          git ls-files | xargs file | grep "CRLF"
          exit 1
        else
          echo "✅ All files have correct line endings"
        fi

  # =============================================================================
  # Build Matrix
  # =============================================================================
  build:
    name: 🏗️ Build
    needs: code-quality
    strategy:
      fail-fast: false
      matrix:
        include:
          # Windows builds
          - os: windows-latest
            compiler: msvc
            qt-arch: win64_msvc2019_64
            preset: default
            name: "Windows MSVC"
          
          - os: windows-latest
            compiler: mingw
            qt-arch: win64_mingw
            preset: default
            name: "Windows MinGW"
          
          # Linux builds
          - os: ubuntu-latest
            compiler: gcc
            qt-arch: gcc_64
            preset: default
            name: "Linux GCC"
          
          - os: ubuntu-latest
            compiler: clang
            qt-arch: gcc_64
            preset: default
            name: "Linux Clang"
          
          # macOS builds
          - os: macos-latest
            compiler: clang
            qt-arch: clang_64
            preset: default
            name: "macOS Clang"

    runs-on: ${{ matrix.os }}
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 🔧 Setup Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: ${{ env.QT_VERSION }}
        arch: ${{ matrix.qt-arch }}
        cache: true
        modules: 'qtnetworkauth'

    - name: 🔧 Setup MSVC (Windows)
      if: matrix.os == 'windows-latest' && matrix.compiler == 'msvc'
      uses: ilammy/msvc-dev-cmd@v1

    - name: 🔧 Setup MinGW (Windows)
      if: matrix.os == 'windows-latest' && matrix.compiler == 'mingw'
      uses: egor-tensin/setup-mingw@v2
      with:
        platform: x64

    - name: 🔧 Setup GCC (Linux)
      if: matrix.os == 'ubuntu-latest' && matrix.compiler == 'gcc'
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-11 g++-11
        sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-11 100
        sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-11 100

    - name: 🔧 Setup Clang (Linux)
      if: matrix.os == 'ubuntu-latest' && matrix.compiler == 'clang'
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-15 libc++-15-dev libc++abi-15-dev
        sudo update-alternatives --install /usr/bin/clang clang /usr/bin/clang-15 100
        sudo update-alternatives --install /usr/bin/clang++ clang++ /usr/bin/clang++-15 100

    - name: 🏗️ Configure CMake
      run: |
        cmake --preset=${{ matrix.preset }} -B build \
          -DCMAKE_BUILD_TYPE=${{ env.BUILD_TYPE }} \
          -DBUILD_EXAMPLES=ON \
          -DBUILD_TESTS=ON \
          -DBUILD_COMMAND_SYSTEM=ON

    - name: 🔨 Build
      run: cmake --build build --config ${{ env.BUILD_TYPE }} --parallel

    - name: 🧪 Run Tests
      working-directory: build
      run: ctest --config ${{ env.BUILD_TYPE }} --output-on-failure --parallel

    - name: 📦 Package Artifacts (Release only)
      if: matrix.os == 'ubuntu-latest' && matrix.compiler == 'gcc' && github.event_name == 'push'
      run: |
        cmake --build build --target package
        
    - name: 📤 Upload Artifacts
      if: matrix.os == 'ubuntu-latest' && matrix.compiler == 'gcc'
      uses: actions/upload-artifact@v4
      with:
        name: DeclarativeUI-${{ matrix.name }}-${{ github.sha }}
        path: |
          build/examples/
          build/tests/
          build/*.tar.gz
        retention-days: 7

  # =============================================================================
  # Documentation Build
  # =============================================================================
  documentation:
    name: 📚 Documentation
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 🔧 Setup Doxygen
      run: |
        sudo apt-get update
        sudo apt-get install -y doxygen graphviz

    - name: 📖 Generate Documentation
      run: |
        # Create Doxygen configuration if it doesn't exist
        if [ ! -f Doxyfile ]; then
          doxygen -g Doxyfile
          # Configure for the project
          sed -i 's/PROJECT_NAME           = "My Project"/PROJECT_NAME           = "DeclarativeUI Framework"/' Doxyfile
          sed -i 's/INPUT                  =/INPUT                  = src/' Doxyfile
          sed -i 's/RECURSIVE              = NO/RECURSIVE              = YES/' Doxyfile
          sed -i 's/GENERATE_HTML          = YES/GENERATE_HTML          = YES/' Doxyfile
          sed -i 's/HTML_OUTPUT            = html/HTML_OUTPUT            = docs\/html/' Doxyfile
        fi
        doxygen Doxyfile

    - name: 📤 Upload Documentation
      uses: actions/upload-artifact@v4
      with:
        name: documentation
        path: docs/html/
        retention-days: 30

  # =============================================================================
  # Performance Testing
  # =============================================================================
  performance:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request'

    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 🔧 Setup Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: ${{ env.QT_VERSION }}
        arch: gcc_64
        cache: true

    - name: 🏗️ Build Performance Tests
      run: |
        cmake --preset=default -B build \
          -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_TESTS=ON
        cmake --build build --config Release --parallel

    - name: ⚡ Run Performance Benchmarks
      working-directory: build
      run: |
        # Run performance tests if they exist
        if [ -f tests/performance/benchmark ]; then
          ./tests/performance/benchmark --benchmark_format=json > benchmark_results.json
        fi

    - name: 📊 Performance Report
      if: always()
      run: |
        echo "## ⚡ Performance Test Results" >> $GITHUB_STEP_SUMMARY
        echo "Performance tests completed for commit ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

  # =============================================================================
  # Security Scan
  # =============================================================================
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    permissions:
      security-events: write

    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 🔍 Run CodeQL Analysis
      uses: github/codeql-action/init@v3
      with:
        languages: cpp

    - name: 🔧 Setup Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: ${{ env.QT_VERSION }}
        arch: gcc_64
        cache: true

    - name: 🏗️ Build for Analysis
      run: |
        cmake --preset=default -B build \
          -DCMAKE_BUILD_TYPE=Debug \
          -DBUILD_EXAMPLES=OFF \
          -DBUILD_TESTS=OFF
        cmake --build build --config Debug

    - name: 🔍 Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  # =============================================================================
  # Release Preparation
  # =============================================================================
  release-check:
    name: 🚀 Release Check
    runs-on: ubuntu-latest
    needs: [build, documentation]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: 🏷️ Check for Version Tags
      id: version
      run: |
        # Check if this commit should trigger a release
        if git describe --exact-match --tags HEAD 2>/dev/null; then
          echo "release=true" >> $GITHUB_OUTPUT
          echo "tag=$(git describe --exact-match --tags HEAD)" >> $GITHUB_OUTPUT
        else
          echo "release=false" >> $GITHUB_OUTPUT
        fi

    - name: 📋 Release Summary
      if: steps.version.outputs.release == 'true'
      run: |
        echo "## 🚀 Release Ready" >> $GITHUB_STEP_SUMMARY
        echo "Version: ${{ steps.version.outputs.tag }}" >> $GITHUB_STEP_SUMMARY
        echo "All checks passed - ready for release!" >> $GITHUB_STEP_SUMMARY
