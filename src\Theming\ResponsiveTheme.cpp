#include "ThemeManager.hpp"
#include <QDebug>
#include <QApplication>
#include <QScreen>
#include <QWindow>
#include <QWidget>
#include <QTimer>

namespace DeclarativeUI::Theming {

/**
 * @brief Responsive theming system that adapts to screen size and device characteristics
 */
class ResponsiveTheme : public QObject {
    Q_OBJECT
    
public:
    enum class BreakPoint {
        XSmall,     // < 576px
        Small,      // 576px - 768px
        Medium,     // 768px - 992px
        Large,      // 992px - 1200px
        XLarge      // >= 1200px
    };
    
    enum class DeviceType {
        Mobile,
        Tablet,
        Desktop,
        TV
    };
    
    struct ResponsiveConfig {
        BreakPoint breakpoint = BreakPoint::Medium;
        DeviceType deviceType = DeviceType::Desktop;
        double scaleFactor = 1.0;
        bool isHighDPI = false;
        bool isTouchDevice = false;
        int screenWidth = 1920;
        int screenHeight = 1080;
    };
    
    explicit ResponsiveTheme(QObject* parent = nullptr) : QObject(parent) {
        setupScreenMonitoring();
        updateCurrentConfig();
    }
    
    /**
     * @brief Get current responsive configuration
     */
    ResponsiveConfig getCurrentConfig() const {
        return current_config_;
    }
    
    /**
     * @brief Get breakpoint for given width
     */
    static BreakPoint getBreakPoint(int width) {
        if (width < 576) return BreakPoint::XSmall;
        if (width < 768) return BreakPoint::Small;
        if (width < 992) return BreakPoint::Medium;
        if (width < 1200) return BreakPoint::Large;
        return BreakPoint::XLarge;
    }
    
    /**
     * @brief Get device type based on screen characteristics
     */
    static DeviceType getDeviceType(int width, int height, double dpi) {
        double diagonal = std::sqrt(width * width + height * height) / dpi;
        
        if (diagonal < 7.0) return DeviceType::Mobile;
        if (diagonal < 13.0) return DeviceType::Tablet;
        if (diagonal < 32.0) return DeviceType::Desktop;
        return DeviceType::TV;
    }
    
    /**
     * @brief Create responsive theme variant for current configuration
     */
    ThemeManager::Theme createResponsiveTheme(const ThemeManager::Theme& baseTheme) const {
        ThemeManager::Theme responsiveTheme = baseTheme;
        
        // Adjust typography for device and screen size
        adjustTypographyForDevice(responsiveTheme);
        
        // Adjust spacing for device and screen size
        adjustSpacingForDevice(responsiveTheme);
        
        // Adjust colors for device characteristics
        adjustColorsForDevice(responsiveTheme);
        
        // Adjust borders and shadows for device
        adjustBordersForDevice(responsiveTheme);
        
        return responsiveTheme;
    }
    
    /**
     * @brief Get recommended font scale for current device
     */
    double getRecommendedFontScale() const {
        switch (current_config_.deviceType) {
            case DeviceType::Mobile:
                return current_config_.isHighDPI ? 1.1 : 1.0;
            case DeviceType::Tablet:
                return current_config_.isHighDPI ? 1.2 : 1.1;
            case DeviceType::Desktop:
                return 1.0;
            case DeviceType::TV:
                return 1.5;
        }
        return 1.0;
    }
    
    /**
     * @brief Get recommended spacing scale for current device
     */
    double getRecommendedSpacingScale() const {
        switch (current_config_.deviceType) {
            case DeviceType::Mobile:
                return current_config_.isTouchDevice ? 1.2 : 1.0;
            case DeviceType::Tablet:
                return current_config_.isTouchDevice ? 1.3 : 1.1;
            case DeviceType::Desktop:
                return 1.0;
            case DeviceType::TV:
                return 1.8;
        }
        return 1.0;
    }
    
    /**
     * @brief Get CSS media queries for responsive design
     */
    QString generateMediaQueries() const {
        QString css;
        
        // Mobile first approach
        css += "/* Mobile styles (default) */\n";
        css += "@media (max-width: 575px) {\n";
        css += "  .responsive-container { padding: 8px; }\n";
        css += "  .responsive-text { font-size: 14px; }\n";
        css += "  .responsive-button { min-height: 44px; }\n";
        css += "}\n\n";
        
        // Small devices (landscape phones, 576px and up)
        css += "@media (min-width: 576px) {\n";
        css += "  .responsive-container { padding: 12px; }\n";
        css += "  .responsive-text { font-size: 15px; }\n";
        css += "  .responsive-button { min-height: 40px; }\n";
        css += "}\n\n";
        
        // Medium devices (tablets, 768px and up)
        css += "@media (min-width: 768px) {\n";
        css += "  .responsive-container { padding: 16px; }\n";
        css += "  .responsive-text { font-size: 16px; }\n";
        css += "  .responsive-button { min-height: 36px; }\n";
        css += "}\n\n";
        
        // Large devices (desktops, 992px and up)
        css += "@media (min-width: 992px) {\n";
        css += "  .responsive-container { padding: 20px; }\n";
        css += "  .responsive-text { font-size: 16px; }\n";
        css += "  .responsive-button { min-height: 32px; }\n";
        css += "}\n\n";
        
        // Extra large devices (large desktops, 1200px and up)
        css += "@media (min-width: 1200px) {\n";
        css += "  .responsive-container { padding: 24px; }\n";
        css += "  .responsive-text { font-size: 17px; }\n";
        css += "  .responsive-button { min-height: 32px; }\n";
        css += "}\n\n";
        
        // High DPI displays
        css += "@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n";
        css += "  .responsive-icon { transform: scale(0.8); }\n";
        css += "  .responsive-border { border-width: 0.5px; }\n";
        css += "}\n\n";
        
        // Touch devices
        css += "@media (pointer: coarse) {\n";
        css += "  .responsive-button { min-height: 44px; padding: 12px 16px; }\n";
        css += "  .responsive-link { padding: 8px; }\n";
        css += "}\n\n";
        
        return css;
    }
    
signals:
    void configurationChanged(const ResponsiveConfig& config);
    void breakpointChanged(BreakPoint breakpoint);
    void deviceTypeChanged(DeviceType deviceType);
    
private slots:
    void onScreenChanged() {
        updateCurrentConfig();
    }
    
private:
    ResponsiveConfig current_config_;
    QTimer* update_timer_;
    
    void setupScreenMonitoring() {
        // Monitor screen changes
        connect(QApplication::primaryScreen(), &QScreen::geometryChanged,
                this, &ResponsiveTheme::onScreenChanged);
        connect(QApplication::primaryScreen(), &QScreen::logicalDotsPerInchChanged,
                this, &ResponsiveTheme::onScreenChanged);
        
        // Setup update timer to avoid too frequent updates
        update_timer_ = new QTimer(this);
        update_timer_->setSingleShot(true);
        update_timer_->setInterval(100); // 100ms debounce
        connect(update_timer_, &QTimer::timeout, this, [this]() {
            emit configurationChanged(current_config_);
        });
    }
    
    void updateCurrentConfig() {
        ResponsiveConfig new_config;
        
        QScreen* screen = QApplication::primaryScreen();
        if (!screen) return;
        
        QRect geometry = screen->geometry();
        new_config.screenWidth = geometry.width();
        new_config.screenHeight = geometry.height();
        new_config.breakpoint = getBreakPoint(geometry.width());
        
        double dpi = screen->logicalDotsPerInch();
        new_config.deviceType = getDeviceType(geometry.width(), geometry.height(), dpi);
        new_config.isHighDPI = (screen->devicePixelRatio() > 1.0);
        new_config.scaleFactor = screen->devicePixelRatio();
        
        // Detect touch capability (simplified)
        new_config.isTouchDevice = (new_config.deviceType == DeviceType::Mobile || 
                                   new_config.deviceType == DeviceType::Tablet);
        
        // Check if configuration changed
        bool changed = (new_config.breakpoint != current_config_.breakpoint ||
                       new_config.deviceType != current_config_.deviceType ||
                       new_config.isHighDPI != current_config_.isHighDPI);
        
        current_config_ = new_config;
        
        if (changed) {
            update_timer_->start();
            
            if (new_config.breakpoint != current_config_.breakpoint) {
                emit breakpointChanged(new_config.breakpoint);
            }
            
            if (new_config.deviceType != current_config_.deviceType) {
                emit deviceTypeChanged(new_config.deviceType);
            }
        }
    }
    
    void adjustTypographyForDevice(ThemeManager::Theme& theme) const {
        double fontScale = getRecommendedFontScale();
        
        // Scale all font sizes
        theme.typography.heading1.setPointSize(theme.typography.heading1.pointSize() * fontScale);
        theme.typography.heading2.setPointSize(theme.typography.heading2.pointSize() * fontScale);
        theme.typography.heading3.setPointSize(theme.typography.heading3.pointSize() * fontScale);
        theme.typography.heading4.setPointSize(theme.typography.heading4.pointSize() * fontScale);
        theme.typography.heading5.setPointSize(theme.typography.heading5.pointSize() * fontScale);
        theme.typography.heading6.setPointSize(theme.typography.heading6.pointSize() * fontScale);
        theme.typography.body1.setPointSize(theme.typography.body1.pointSize() * fontScale);
        theme.typography.body2.setPointSize(theme.typography.body2.pointSize() * fontScale);
        theme.typography.caption.setPointSize(theme.typography.caption.pointSize() * fontScale);
        theme.typography.button.setPointSize(theme.typography.button.pointSize() * fontScale);
        theme.typography.overline.setPointSize(theme.typography.overline.pointSize() * fontScale);
        
        // Adjust font weights for better readability on different devices
        if (current_config_.deviceType == DeviceType::Mobile && current_config_.isHighDPI) {
            // Slightly lighter fonts on high-DPI mobile screens
            if (theme.typography.body1.weight() > QFont::Normal) {
                theme.typography.body1.setWeight(QFont::Normal);
            }
        }
    }
    
    void adjustSpacingForDevice(ThemeManager::Theme& theme) const {
        double spacingScale = getRecommendedSpacingScale();
        
        theme.spacing.xs = static_cast<int>(theme.spacing.xs * spacingScale);
        theme.spacing.sm = static_cast<int>(theme.spacing.sm * spacingScale);
        theme.spacing.md = static_cast<int>(theme.spacing.md * spacingScale);
        theme.spacing.lg = static_cast<int>(theme.spacing.lg * spacingScale);
        theme.spacing.xl = static_cast<int>(theme.spacing.xl * spacingScale);
        theme.spacing.xxl = static_cast<int>(theme.spacing.xxl * spacingScale);
    }
    
    void adjustColorsForDevice(ThemeManager::Theme& theme) const {
        // Adjust colors for different device types
        if (current_config_.deviceType == DeviceType::TV) {
            // Increase contrast for TV viewing distance
            theme.colors.text_primary = theme.colors.text_primary.darker(110);
            theme.colors.border = theme.colors.border.darker(120);
        }
        
        if (current_config_.isHighDPI) {
            // Subtle adjustments for high-DPI displays
            // Colors often appear more saturated on high-DPI screens
            QColor primary = theme.colors.primary.toHsv();
            primary.setHsv(primary.hue(), 
                          qMax(0, primary.saturation() - 10), 
                          primary.value());
            theme.colors.primary = primary;
        }
    }
    
    void adjustBordersForDevice(ThemeManager::Theme& theme) const {
        if (current_config_.isHighDPI) {
            // Thinner borders on high-DPI displays
            theme.border_radius.sm = qMax(1, theme.border_radius.sm - 1);
            theme.border_radius.md = qMax(2, theme.border_radius.md - 1);
        }
        
        if (current_config_.isTouchDevice) {
            // Larger touch targets
            theme.border_radius.lg = theme.border_radius.lg + 2;
            theme.border_radius.xl = theme.border_radius.xl + 2;
        }
    }
};

} // namespace DeclarativeUI::Theming

#include "ResponsiveTheme.moc"
