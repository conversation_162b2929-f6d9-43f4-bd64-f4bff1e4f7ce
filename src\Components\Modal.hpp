#pragma once

#include "../Core/UIElement.hpp"
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QGraphicsBlurEffect>
#include <QEventLoop>
#include <functional>

namespace DeclarativeUI::Components {

/**
 * @brief Modern modal dialog component
 * 
 * Provides customizable modal dialogs with animations, backdrop blur,
 * and flexible content areas. Supports both blocking and non-blocking modes.
 */
class Modal : public Core::UIElement {
    Q_OBJECT

public:
    enum class Size {
        Small,      // 400x300
        Medium,     // 600x400
        Large,      // 800x600
        ExtraLarge, // 1000x700
        FullScreen,
        Custom
    };

    enum class Animation {
        Fade,
        Scale,
        SlideDown,
        SlideUp,
        None
    };

    explicit Modal(QObject* parent = nullptr);
    ~Modal() override = default;

    // **UIElement interface implementation**
    void initialize() override;

    // **Fluent interface for modal configuration**
    Modal& title(const QString& text);
    Modal& content(QWidget* widget);
    Modal& content(const QString& html);
    Modal& size(Size modal_size);
    Modal& customSize(int width, int height);
    Modal& animation(Animation anim_type);
    Modal& backdrop(bool show_backdrop = true);
    Modal& backdropBlur(bool blur_backdrop = true);
    Modal& closable(bool can_close = true);
    Modal& escapeToClose(bool escape_closes = true);
    Modal& clickOutsideToClose(bool click_closes = false);
    Modal& showCloseButton(bool show_button = true);
    Modal& buttons(const QStringList& button_texts);
    Modal& onButtonClicked(std::function<void(int)> callback);
    Modal& onClosed(std::function<void()> callback);

    // **Static convenience methods**
    static Modal* alert(const QString& title, const QString& message);
    static Modal* confirm(const QString& title, const QString& message, 
                         std::function<void(bool)> callback = nullptr);
    static Modal* prompt(const QString& title, const QString& message,
                        const QString& default_text = "",
                        std::function<void(const QString&)> callback = nullptr);
    static Modal* custom(const QString& title, QWidget* content);

    // **Show/hide methods**
    void show();
    void hide();
    void close();
    int exec(); // Blocking execution

    // **Content management**
    void setContentWidget(QWidget* widget);
    void setContentHtml(const QString& html);
    QWidget* getContentWidget() const;

    // **Getters**
    QString getTitle() const;
    Size getSize() const;
    Animation getAnimation() const;
    bool hasBackdrop() const;
    bool hasBackdropBlur() const;
    bool isClosable() const;

signals:
    void buttonClicked(int index);
    void closed();
    void accepted();
    void rejected();

private slots:
    void onCloseButtonClicked();
    void onBackdropClicked();
    void onButtonPressed();
    void onAnimationFinished();

protected:
    void keyPressEvent(QKeyEvent* event);
    void mousePressEvent(QMouseEvent* event);
    bool eventFilter(QObject* obj, QEvent* event) override;

private:
    // **UI components**
    QDialog* modal_dialog_;
    QWidget* backdrop_widget_;
    QFrame* modal_frame_;
    QVBoxLayout* main_layout_;
    QHBoxLayout* header_layout_;
    QVBoxLayout* content_layout_;
    QHBoxLayout* button_layout_;
    
    QLabel* title_label_;
    QPushButton* close_button_;
    QWidget* content_widget_;
    QLabel* content_label_;
    QList<QPushButton*> action_buttons_;

    // **Effects and animations**
    QPropertyAnimation* show_animation_;
    QPropertyAnimation* hide_animation_;
    QGraphicsOpacityEffect* opacity_effect_;
    QGraphicsBlurEffect* backdrop_blur_effect_;

    // **Configuration**
    QString title_text_;
    Size modal_size_;
    int custom_width_;
    int custom_height_;
    Animation animation_type_;
    bool show_backdrop_;
    bool backdrop_blur_;
    bool closable_;
    bool escape_to_close_;
    bool click_outside_to_close_;
    bool show_close_button_;
    QStringList button_texts_;
    
    // **Callbacks**
    std::function<void(int)> button_callback_;
    std::function<void()> closed_callback_;

    // **Event loop for blocking execution**
    QEventLoop* event_loop_;
    int result_code_;

    // **Helper methods**
    void setupUI();
    void setupAnimations();
    void setupBackdrop();
    void applySize();
    void applyAnimation();
    void createButtons();
    void centerOnScreen();
    void connectSignals();
    QSize getSizeForType(Size size_type) const;
    QString getModalStyleSheet() const;
};

} // namespace DeclarativeUI::Components
