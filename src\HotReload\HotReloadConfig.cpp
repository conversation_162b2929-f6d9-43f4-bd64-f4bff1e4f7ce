#include "HotReloadConfig.hpp"
#include <QFileSystemWatcher>
#include <QFile>
#include <QFileInfo>
#include <QRegularExpression>
#include <QDebug>
#include <QCoreApplication>
#include <QStandardPaths>

namespace DeclarativeUI::HotReload {

// **FileFilterConfig Implementation**
QJsonObject FileFilterConfig::toJson() const {
    QJsonObject json;
    json["include_patterns"] = QJsonArray::fromStringList(include_patterns);
    json["exclude_patterns"] = QJsonArray::fromStringList(exclude_patterns);
    json["exclude_directories"] = QJsonArray::fromStringList(exclude_directories);
    json["max_file_size_bytes"] = static_cast<qint64>(max_file_size_bytes);
    json["min_file_size_bytes"] = static_cast<qint64>(min_file_size_bytes);
    json["enable_content_filtering"] = enable_content_filtering;
    json["content_include_patterns"] = QJsonArray::fromStringList(content_include_patterns);
    json["content_exclude_patterns"] = QJsonArray::fromStringList(content_exclude_patterns);
    json["enable_fast_filtering"] = enable_fast_filtering;
    json["cache_filter_results"] = cache_filter_results;
    json["filter_cache_size"] = filter_cache_size;
    return json;
}

FileFilterConfig FileFilterConfig::fromJson(const QJsonObject& json) {
    FileFilterConfig config;
    
    if (json.contains("include_patterns")) {
        const QJsonArray array = json["include_patterns"].toArray();
        for (const auto& value : array) {
            config.include_patterns.append(value.toString());
        }
    }
    
    if (json.contains("exclude_patterns")) {
        const QJsonArray array = json["exclude_patterns"].toArray();
        for (const auto& value : array) {
            config.exclude_patterns.append(value.toString());
        }
    }
    
    if (json.contains("exclude_directories")) {
        const QJsonArray array = json["exclude_directories"].toArray();
        for (const auto& value : array) {
            config.exclude_directories.append(value.toString());
        }
    }
    
    config.max_file_size_bytes = json["max_file_size_bytes"].toVariant().toLongLong();
    config.min_file_size_bytes = json["min_file_size_bytes"].toVariant().toLongLong();
    config.enable_content_filtering = json["enable_content_filtering"].toBool();
    
    if (json.contains("content_include_patterns")) {
        const QJsonArray array = json["content_include_patterns"].toArray();
        for (const auto& value : array) {
            config.content_include_patterns.append(value.toString());
        }
    }
    
    if (json.contains("content_exclude_patterns")) {
        const QJsonArray array = json["content_exclude_patterns"].toArray();
        for (const auto& value : array) {
            config.content_exclude_patterns.append(value.toString());
        }
    }
    
    config.enable_fast_filtering = json["enable_fast_filtering"].toBool(true);
    config.cache_filter_results = json["cache_filter_results"].toBool(true);
    config.filter_cache_size = json["filter_cache_size"].toInt(1000);
    
    return config;
}

bool FileFilterConfig::matchesFile(const QString& file_path) const {
    QFileInfo file_info(file_path);
    
    // Check directory exclusions
    QString dir_path = file_info.absolutePath();
    for (const QString& exclude_dir : exclude_directories) {
        if (dir_path.contains(exclude_dir)) {
            return false;
        }
    }
    
    // Check file size limits
    qint64 file_size = file_info.size();
    if (file_size < min_file_size_bytes || file_size > max_file_size_bytes) {
        return false;
    }
    
    // Check exclude patterns first
    QString file_name = file_info.fileName();
    for (const QString& pattern : exclude_patterns) {
        QRegularExpression regex(QRegularExpression::wildcardToRegularExpression(pattern));
        if (regex.match(file_name).hasMatch()) {
            return false;
        }
    }
    
    // Check include patterns
    for (const QString& pattern : include_patterns) {
        QRegularExpression regex(QRegularExpression::wildcardToRegularExpression(pattern));
        if (regex.match(file_name).hasMatch()) {
            return true;
        }
    }
    
    return false;
}

bool FileFilterConfig::matchesContent(const QString& content) const {
    if (!enable_content_filtering) {
        return true;
    }
    
    // Check exclude patterns first
    for (const QString& pattern : content_exclude_patterns) {
        QRegularExpression regex(pattern);
        if (regex.match(content).hasMatch()) {
            return false;
        }
    }
    
    // Check include patterns
    if (content_include_patterns.isEmpty()) {
        return true; // No include patterns means include all
    }
    
    for (const QString& pattern : content_include_patterns) {
        QRegularExpression regex(pattern);
        if (regex.match(content).hasMatch()) {
            return true;
        }
    }
    
    return false;
}

// **PerformanceConfig Implementation**
QJsonObject PerformanceConfig::toJson() const {
    QJsonObject json;
    json["enable_monitoring"] = enable_monitoring;
    json["enable_real_time_analytics"] = enable_real_time_analytics;
    json["enable_bottleneck_detection"] = enable_bottleneck_detection;
    json["enable_memory_profiling"] = enable_memory_profiling;
    json["enable_predictive_modeling"] = enable_predictive_modeling;
    json["warning_threshold_ms"] = warning_threshold_ms;
    json["error_threshold_ms"] = error_threshold_ms;
    json["memory_warning_threshold_mb"] = static_cast<qint64>(memory_warning_threshold_mb);
    json["memory_error_threshold_mb"] = static_cast<qint64>(memory_error_threshold_mb);
    json["cpu_warning_threshold_percent"] = cpu_warning_threshold_percent;
    json["cpu_error_threshold_percent"] = cpu_error_threshold_percent;
    json["max_history_size"] = max_history_size;
    json["report_interval_ms"] = report_interval_ms;
    json["auto_save_reports"] = auto_save_reports;
    json["reports_directory"] = reports_directory;
    json["enable_automatic_optimizations"] = enable_automatic_optimizations;
    json["enable_adaptive_thresholds"] = enable_adaptive_thresholds;
    return json;
}

PerformanceConfig PerformanceConfig::fromJson(const QJsonObject& json) {
    PerformanceConfig config;
    config.enable_monitoring = json["enable_monitoring"].toBool(true);
    config.enable_real_time_analytics = json["enable_real_time_analytics"].toBool(true);
    config.enable_bottleneck_detection = json["enable_bottleneck_detection"].toBool(true);
    config.enable_memory_profiling = json["enable_memory_profiling"].toBool(false);
    config.enable_predictive_modeling = json["enable_predictive_modeling"].toBool(false);
    config.warning_threshold_ms = json["warning_threshold_ms"].toInt(1000);
    config.error_threshold_ms = json["error_threshold_ms"].toInt(5000);
    config.memory_warning_threshold_mb = json["memory_warning_threshold_mb"].toVariant().toLongLong();
    config.memory_error_threshold_mb = json["memory_error_threshold_mb"].toVariant().toLongLong();
    config.cpu_warning_threshold_percent = json["cpu_warning_threshold_percent"].toDouble(80.0);
    config.cpu_error_threshold_percent = json["cpu_error_threshold_percent"].toDouble(95.0);
    config.max_history_size = json["max_history_size"].toInt(1000);
    config.report_interval_ms = json["report_interval_ms"].toInt(5000);
    config.auto_save_reports = json["auto_save_reports"].toBool(false);
    config.reports_directory = json["reports_directory"].toString("hot_reload_reports");
    config.enable_automatic_optimizations = json["enable_automatic_optimizations"].toBool(true);
    config.enable_adaptive_thresholds = json["enable_adaptive_thresholds"].toBool(true);
    return config;
}

// **ErrorRecoveryConfig Implementation**
QJsonObject ErrorRecoveryConfig::toJson() const {
    QJsonObject json;
    json["enable_automatic_rollback"] = enable_automatic_rollback;
    json["enable_graceful_degradation"] = enable_graceful_degradation;
    json["enable_user_notifications"] = enable_user_notifications;
    json["enable_detailed_error_reporting"] = enable_detailed_error_reporting;
    json["max_rollback_points"] = max_rollback_points;
    json["auto_create_rollback_points"] = auto_create_rollback_points;
    json["rollback_timeout_ms"] = rollback_timeout_ms;
    json["default_recovery_strategy"] = static_cast<int>(default_recovery_strategy);
    json["show_error_dialogs"] = show_error_dialogs;
    json["show_warning_dialogs"] = show_warning_dialogs;
    json["log_to_console"] = log_to_console;
    json["log_to_file"] = log_to_file;
    json["log_file_path"] = log_file_path;
    return json;
}

ErrorRecoveryConfig ErrorRecoveryConfig::fromJson(const QJsonObject& json) {
    ErrorRecoveryConfig config;
    config.enable_automatic_rollback = json["enable_automatic_rollback"].toBool(true);
    config.enable_graceful_degradation = json["enable_graceful_degradation"].toBool(true);
    config.enable_user_notifications = json["enable_user_notifications"].toBool(true);
    config.enable_detailed_error_reporting = json["enable_detailed_error_reporting"].toBool(true);
    config.max_rollback_points = json["max_rollback_points"].toInt(10);
    config.auto_create_rollback_points = json["auto_create_rollback_points"].toBool(true);
    config.rollback_timeout_ms = json["rollback_timeout_ms"].toInt(5000);
    config.default_recovery_strategy = static_cast<RecoveryStrategy>(
        json["default_recovery_strategy"].toInt(static_cast<int>(RecoveryStrategy::Rollback)));
    config.show_error_dialogs = json["show_error_dialogs"].toBool(true);
    config.show_warning_dialogs = json["show_warning_dialogs"].toBool(false);
    config.log_to_console = json["log_to_console"].toBool(true);
    config.log_to_file = json["log_to_file"].toBool(false);
    config.log_file_path = json["log_file_path"].toString("hot_reload_errors.log");
    return config;
}

// **FileWatchConfig Implementation**
QJsonObject FileWatchConfig::toJson() const {
    QJsonObject json;
    json["enable_file_watching"] = enable_file_watching;
    json["enable_directory_watching"] = enable_directory_watching;
    json["recursive_directory_watching"] = recursive_directory_watching;
    json["debounce_interval_ms"] = debounce_interval_ms;
    json["max_debounce_interval_ms"] = max_debounce_interval_ms;
    json["enable_adaptive_debouncing"] = enable_adaptive_debouncing;
    json["max_watched_files"] = max_watched_files;
    json["max_watched_directories"] = max_watched_directories;
    json["enable_batch_processing"] = enable_batch_processing;
    json["batch_size"] = batch_size;
    json["thread_pool_size"] = thread_pool_size;
    json["enable_change_frequency_tracking"] = enable_change_frequency_tracking;
    json["enable_smart_filtering"] = enable_smart_filtering;
    json["enable_file_content_hashing"] = enable_file_content_hashing;
    return json;
}

FileWatchConfig FileWatchConfig::fromJson(const QJsonObject& json) {
    FileWatchConfig config;
    config.enable_file_watching = json["enable_file_watching"].toBool(true);
    config.enable_directory_watching = json["enable_directory_watching"].toBool(true);
    config.recursive_directory_watching = json["recursive_directory_watching"].toBool(true);
    config.debounce_interval_ms = json["debounce_interval_ms"].toInt(100);
    config.max_debounce_interval_ms = json["max_debounce_interval_ms"].toInt(1000);
    config.enable_adaptive_debouncing = json["enable_adaptive_debouncing"].toBool(true);
    config.max_watched_files = json["max_watched_files"].toInt(10000);
    config.max_watched_directories = json["max_watched_directories"].toInt(1000);
    config.enable_batch_processing = json["enable_batch_processing"].toBool(true);
    config.batch_size = json["batch_size"].toInt(50);
    config.thread_pool_size = json["thread_pool_size"].toInt(4);
    config.enable_change_frequency_tracking = json["enable_change_frequency_tracking"].toBool(true);
    config.enable_smart_filtering = json["enable_smart_filtering"].toBool(true);
    config.enable_file_content_hashing = json["enable_file_content_hashing"].toBool(false);
    return config;
}

// **ReloadConfig Implementation**
QJsonObject ReloadConfig::toJson() const {
    QJsonObject json;
    json["strategy"] = static_cast<int>(strategy);
    json["reload_delay_ms"] = reload_delay_ms;
    json["batch_timeout_ms"] = batch_timeout_ms;
    json["max_reload_time_ms"] = max_reload_time_ms;
    json["max_concurrent_reloads"] = max_concurrent_reloads;
    json["enable_parallel_processing"] = enable_parallel_processing;
    json["enable_incremental_reloading"] = enable_incremental_reloading;
    json["enable_smart_caching"] = enable_smart_caching;
    json["enable_preload_dependencies"] = enable_preload_dependencies;
    json["cache_size_limit_mb"] = static_cast<qint64>(cache_size_limit_mb);
    json["memory_limit_mb"] = static_cast<qint64>(memory_limit_mb);
    json["enable_memory_optimization"] = enable_memory_optimization;
    json["enable_garbage_collection"] = enable_garbage_collection;
    return json;
}

ReloadConfig ReloadConfig::fromJson(const QJsonObject& json) {
    ReloadConfig config;
    config.strategy = static_cast<Strategy>(json["strategy"].toInt(static_cast<int>(Strategy::Smart)));
    config.reload_delay_ms = json["reload_delay_ms"].toInt(100);
    config.batch_timeout_ms = json["batch_timeout_ms"].toInt(500);
    config.max_reload_time_ms = json["max_reload_time_ms"].toInt(10000);
    config.max_concurrent_reloads = json["max_concurrent_reloads"].toInt(4);
    config.enable_parallel_processing = json["enable_parallel_processing"].toBool(true);
    config.enable_incremental_reloading = json["enable_incremental_reloading"].toBool(true);
    config.enable_smart_caching = json["enable_smart_caching"].toBool(true);
    config.enable_preload_dependencies = json["enable_preload_dependencies"].toBool(false);
    config.cache_size_limit_mb = json["cache_size_limit_mb"].toVariant().toLongLong();
    config.memory_limit_mb = json["memory_limit_mb"].toVariant().toLongLong();
    config.enable_memory_optimization = json["enable_memory_optimization"].toBool(true);
    config.enable_garbage_collection = json["enable_garbage_collection"].toBool(true);
    return config;
}

// **ConfigProfile Implementation**
QJsonObject ConfigProfile::toJson() const {
    QJsonObject json;
    json["name"] = name;
    json["description"] = description;
    json["is_active"] = is_active;
    json["file_filter"] = file_filter.toJson();
    json["performance"] = performance.toJson();
    json["error_recovery"] = error_recovery.toJson();
    json["file_watch"] = file_watch.toJson();
    json["reload"] = reload.toJson();
    json["custom_settings"] = custom_settings;
    return json;
}

ConfigProfile ConfigProfile::fromJson(const QJsonObject& json) {
    ConfigProfile profile;
    profile.name = json["name"].toString();
    profile.description = json["description"].toString();
    profile.is_active = json["is_active"].toBool(false);
    profile.file_filter = FileFilterConfig::fromJson(json["file_filter"].toObject());
    profile.performance = PerformanceConfig::fromJson(json["performance"].toObject());
    profile.error_recovery = ErrorRecoveryConfig::fromJson(json["error_recovery"].toObject());
    profile.file_watch = FileWatchConfig::fromJson(json["file_watch"].toObject());
    profile.reload = ReloadConfig::fromJson(json["reload"].toObject());
    profile.custom_settings = json["custom_settings"].toObject();
    return profile;
}

void ConfigProfile::applyTo(HotReloadConfig* config) const {
    if (!config) return;

    config->setFileFilterConfig(file_filter);
    config->setPerformanceConfig(performance);
    config->setErrorRecoveryConfig(error_recovery);
    config->setFileWatchConfig(file_watch);
    config->setReloadConfig(reload);
}

// **HotReloadConfig Implementation**
HotReloadConfig::HotReloadConfig(QObject* parent)
    : QObject(parent)
    , config_watcher_(std::make_unique<QFileSystemWatcher>(this)) {

    initializeDefaults();
    createDefaultProfiles();

    connect(config_watcher_.get(), &QFileSystemWatcher::fileChanged,
            this, &HotReloadConfig::onConfigurationFileChanged);
}

bool HotReloadConfig::loadFromFile(const QString& file_path) {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        emit configurationError(QString("Cannot open configuration file: %1").arg(file_path));
        return false;
    }

    QByteArray data = file.readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (doc.isNull()) {
        emit configurationError(QString("JSON parse error: %1").arg(error.errorString()));
        return false;
    }

    config_file_path_ = file_path;
    return loadFromJson(doc.object());
}

bool HotReloadConfig::saveToFile(const QString& file_path) const {
    QFile file(file_path);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    QJsonDocument doc(toJson());
    file.write(doc.toJson());
    return true;
}

bool HotReloadConfig::loadFromJson(const QJsonObject& json) {
    try {
        // Load core configurations
        if (json.contains("file_filter")) {
            file_filter_config_ = FileFilterConfig::fromJson(json["file_filter"].toObject());
        }

        if (json.contains("performance")) {
            performance_config_ = PerformanceConfig::fromJson(json["performance"].toObject());
        }

        if (json.contains("error_recovery")) {
            error_recovery_config_ = ErrorRecoveryConfig::fromJson(json["error_recovery"].toObject());
        }

        if (json.contains("file_watch")) {
            file_watch_config_ = FileWatchConfig::fromJson(json["file_watch"].toObject());
        }

        if (json.contains("reload")) {
            reload_config_ = ReloadConfig::fromJson(json["reload"].toObject());
        }

        // Load profiles
        if (json.contains("profiles")) {
            profiles_.clear();
            const QJsonObject profiles_obj = json["profiles"].toObject();
            for (auto it = profiles_obj.begin(); it != profiles_obj.end(); ++it) {
                ConfigProfile profile = ConfigProfile::fromJson(it.value().toObject());
                profiles_[it.key()] = profile;
            }
        }

        // Set active profile
        if (json.contains("active_profile")) {
            active_profile_name_ = json["active_profile"].toString();
        }

        validateConfiguration();
        emit configurationChanged();
        return true;

    } catch (const std::exception& e) {
        emit configurationError(QString("Configuration loading error: %1").arg(e.what()));
        return false;
    }
}

QJsonObject HotReloadConfig::toJson() const {
    QJsonObject json;
    json["file_filter"] = file_filter_config_.toJson();
    json["performance"] = performance_config_.toJson();
    json["error_recovery"] = error_recovery_config_.toJson();
    json["file_watch"] = file_watch_config_.toJson();
    json["reload"] = reload_config_.toJson();
    json["active_profile"] = active_profile_name_;

    // Save profiles
    QJsonObject profiles_obj;
    for (auto it = profiles_.begin(); it != profiles_.end(); ++it) {
        profiles_obj[it.key()] = it.value().toJson();
    }
    json["profiles"] = profiles_obj;

    return json;
}

void HotReloadConfig::addProfile(const ConfigProfile& profile) {
    profiles_[profile.name] = profile;
    emit configurationChanged();
}

void HotReloadConfig::removeProfile(const QString& profile_name) {
    if (profiles_.remove(profile_name) > 0) {
        if (active_profile_name_ == profile_name) {
            active_profile_name_.clear();
        }
        emit configurationChanged();
    }
}

bool HotReloadConfig::setActiveProfile(const QString& profile_name) {
    if (!profiles_.contains(profile_name)) {
        return false;
    }

    active_profile_name_ = profile_name;
    profiles_[profile_name].applyTo(this);
    emit profileChanged(profile_name);
    return true;
}

QString HotReloadConfig::getActiveProfileName() const {
    return active_profile_name_;
}

QStringList HotReloadConfig::getProfileNames() const {
    return profiles_.keys();
}

ConfigProfile HotReloadConfig::getProfile(const QString& profile_name) const {
    return profiles_.value(profile_name);
}

// **Default profile creation methods**
ConfigProfile HotReloadConfig::createDevelopmentProfile() {
    ConfigProfile profile;
    profile.name = "Development";
    profile.description = "Optimized for development with fast reloads and detailed monitoring";

    // Development-friendly file filtering
    profile.file_filter.include_patterns = {"*.json", "*.qml", "*.ui", "*.css", "*.scss", "*.js", "*.ts"};
    profile.file_filter.enable_content_filtering = false;
    profile.file_filter.enable_fast_filtering = true;

    // Enhanced performance monitoring for development
    profile.performance.enable_monitoring = true;
    profile.performance.enable_real_time_analytics = true;
    profile.performance.enable_bottleneck_detection = true;
    profile.performance.warning_threshold_ms = 500;
    profile.performance.enable_automatic_optimizations = true;

    // Aggressive error recovery for development
    profile.error_recovery.enable_automatic_rollback = true;
    profile.error_recovery.enable_user_notifications = true;
    profile.error_recovery.show_error_dialogs = true;
    profile.error_recovery.log_to_console = true;

    // Fast file watching
    profile.file_watch.debounce_interval_ms = 50;
    profile.file_watch.enable_adaptive_debouncing = true;
    profile.file_watch.enable_batch_processing = true;

    // Immediate reloads for development
    profile.reload.strategy = ReloadConfig::Strategy::Smart;
    profile.reload.reload_delay_ms = 50;
    profile.reload.enable_parallel_processing = true;
    profile.reload.enable_incremental_reloading = true;

    return profile;
}

ConfigProfile HotReloadConfig::createProductionProfile() {
    ConfigProfile profile;
    profile.name = "Production";
    profile.description = "Conservative settings for production environments";

    // Conservative file filtering
    profile.file_filter.include_patterns = {"*.json", "*.qml", "*.ui"};
    profile.file_filter.enable_content_filtering = true;
    profile.file_filter.max_file_size_bytes = 5 * 1024 * 1024; // 5MB limit

    // Minimal performance monitoring
    profile.performance.enable_monitoring = true;
    profile.performance.enable_real_time_analytics = false;
    profile.performance.enable_bottleneck_detection = false;
    profile.performance.warning_threshold_ms = 2000;
    profile.performance.enable_automatic_optimizations = false;

    // Conservative error recovery
    profile.error_recovery.enable_automatic_rollback = false;
    profile.error_recovery.enable_user_notifications = false;
    profile.error_recovery.show_error_dialogs = false;
    profile.error_recovery.log_to_file = true;

    // Conservative file watching
    profile.file_watch.debounce_interval_ms = 500;
    profile.file_watch.enable_adaptive_debouncing = false;
    profile.file_watch.max_watched_files = 1000;

    // Batched reloads for production
    profile.reload.strategy = ReloadConfig::Strategy::Batched;
    profile.reload.reload_delay_ms = 1000;
    profile.reload.enable_parallel_processing = false;
    profile.reload.max_concurrent_reloads = 1;

    return profile;
}

ConfigProfile HotReloadConfig::createTestingProfile() {
    ConfigProfile profile;
    profile.name = "Testing";
    profile.description = "Optimized for automated testing environments";

    // Comprehensive file filtering for testing
    profile.file_filter.include_patterns = {"*.json", "*.qml", "*.ui", "*.test.*"};
    profile.file_filter.exclude_patterns = {"*.tmp", "*.bak"};

    // Detailed monitoring for testing
    profile.performance.enable_monitoring = true;
    profile.performance.enable_real_time_analytics = true;
    profile.performance.auto_save_reports = true;
    profile.performance.reports_directory = "test_reports";

    // Strict error handling for testing
    profile.error_recovery.enable_automatic_rollback = false;
    profile.error_recovery.enable_user_notifications = false;
    profile.error_recovery.log_to_console = true;
    profile.error_recovery.log_to_file = true;

    // Predictable file watching for testing
    profile.file_watch.debounce_interval_ms = 100;
    profile.file_watch.enable_adaptive_debouncing = false;
    profile.file_watch.enable_batch_processing = false;

    // Deterministic reloads for testing
    profile.reload.strategy = ReloadConfig::Strategy::Immediate;
    profile.reload.reload_delay_ms = 0;
    profile.reload.enable_parallel_processing = false;
    profile.reload.max_concurrent_reloads = 1;

    return profile;
}

ConfigProfile HotReloadConfig::createMinimalProfile() {
    ConfigProfile profile;
    profile.name = "Minimal";
    profile.description = "Minimal resource usage for constrained environments";

    // Basic file filtering
    profile.file_filter.include_patterns = {"*.json"};
    profile.file_filter.enable_fast_filtering = true;
    profile.file_filter.cache_filter_results = false;

    // Minimal monitoring
    profile.performance.enable_monitoring = false;
    profile.performance.enable_real_time_analytics = false;
    profile.performance.enable_bottleneck_detection = false;

    // Basic error recovery
    profile.error_recovery.enable_automatic_rollback = false;
    profile.error_recovery.enable_user_notifications = false;
    profile.error_recovery.log_to_console = false;

    // Minimal file watching
    profile.file_watch.debounce_interval_ms = 1000;
    profile.file_watch.max_watched_files = 100;
    profile.file_watch.enable_batch_processing = false;
    profile.file_watch.thread_pool_size = 1;

    // Simple reloads
    profile.reload.strategy = ReloadConfig::Strategy::Manual;
    profile.reload.enable_parallel_processing = false;
    profile.reload.enable_incremental_reloading = false;
    profile.reload.enable_smart_caching = false;

    return profile;
}

// **Configuration update methods**
void HotReloadConfig::setFileFilterConfig(const FileFilterConfig& config) {
    file_filter_config_ = config;
    emit configurationChanged();
}

void HotReloadConfig::setPerformanceConfig(const PerformanceConfig& config) {
    performance_config_ = config;
    emit configurationChanged();
}

void HotReloadConfig::setErrorRecoveryConfig(const ErrorRecoveryConfig& config) {
    error_recovery_config_ = config;
    emit configurationChanged();
}

void HotReloadConfig::setFileWatchConfig(const FileWatchConfig& config) {
    file_watch_config_ = config;
    emit configurationChanged();
}

void HotReloadConfig::setReloadConfig(const ReloadConfig& config) {
    reload_config_ = config;
    emit configurationChanged();
}

// **Runtime configuration and utility methods**
void HotReloadConfig::updateConfiguration(const QJsonObject& updates) {
    QJsonObject current = toJson();
    mergeConfiguration(current, updates);
    loadFromJson(current);
}

void HotReloadConfig::resetToDefaults() {
    initializeDefaults();
    createDefaultProfiles();
    emit configurationChanged();
}

void HotReloadConfig::validateConfiguration() const {
    validateFileFilterConfig();
    validatePerformanceConfig();
    validateErrorRecoveryConfig();
    validateFileWatchConfig();
    validateReloadConfig();
}

QString HotReloadConfig::detectEnvironment() {
    // Check environment variables
    QString env = qgetenv("QT_HOTRELOAD_ENV");
    if (!env.isEmpty()) {
        return env.toLower();
    }

    // Check build type
    #ifdef QT_DEBUG
        return "development";
    #else
        return "production";
    #endif
}

void HotReloadConfig::loadEnvironmentSpecificConfig() {
    QString env = detectEnvironment();
    QString config_file = QString("hotreload_%1.json").arg(env);

    // Try to load environment-specific config
    QStringList search_paths = {
        QCoreApplication::applicationDirPath(),
        QStandardPaths::writableLocation(QStandardPaths::ConfigLocation),
        "."
    };

    for (const QString& path : search_paths) {
        QString full_path = QDir(path).filePath(config_file);
        if (QFileInfo::exists(full_path)) {
            loadFromFile(full_path);
            break;
        }
    }
}

void HotReloadConfig::enableConfigurationWatching(bool enabled) {
    config_watching_enabled_.store(enabled);

    if (enabled && !config_file_path_.isEmpty()) {
        config_watcher_->addPath(config_file_path_);
    } else {
        config_watcher_->removePaths(config_watcher_->files());
    }
}

bool HotReloadConfig::isConfigurationWatchingEnabled() const {
    return config_watching_enabled_.load();
}

void HotReloadConfig::onConfigurationFileChanged() {
    if (config_watching_enabled_.load() && !config_file_path_.isEmpty()) {
        qDebug() << "Configuration file changed, reloading:" << config_file_path_;
        loadFromFile(config_file_path_);
    }
}

// **Private helper methods**
void HotReloadConfig::initializeDefaults() {
    file_filter_config_ = FileFilterConfig();
    performance_config_ = PerformanceConfig();
    error_recovery_config_ = ErrorRecoveryConfig();
    file_watch_config_ = FileWatchConfig();
    reload_config_ = ReloadConfig();
}

void HotReloadConfig::createDefaultProfiles() {
    profiles_.clear();

    addProfile(createDevelopmentProfile());
    addProfile(createProductionProfile());
    addProfile(createTestingProfile());
    addProfile(createMinimalProfile());

    // Set default active profile based on environment
    QString env = detectEnvironment();
    if (env == "development" || env == "debug") {
        setActiveProfile("Development");
    } else if (env == "production" || env == "release") {
        setActiveProfile("Production");
    } else if (env == "testing" || env == "test") {
        setActiveProfile("Testing");
    } else {
        setActiveProfile("Development"); // Default fallback
    }
}

void HotReloadConfig::validateFileFilterConfig() const {
    if (file_filter_config_.include_patterns.isEmpty()) {
        throw std::invalid_argument("File filter must have at least one include pattern");
    }

    if (file_filter_config_.max_file_size_bytes <= 0) {
        throw std::invalid_argument("Maximum file size must be positive");
    }

    if (file_filter_config_.filter_cache_size <= 0) {
        throw std::invalid_argument("Filter cache size must be positive");
    }
}

void HotReloadConfig::validatePerformanceConfig() const {
    if (performance_config_.warning_threshold_ms <= 0) {
        throw std::invalid_argument("Warning threshold must be positive");
    }

    if (performance_config_.error_threshold_ms <= performance_config_.warning_threshold_ms) {
        throw std::invalid_argument("Error threshold must be greater than warning threshold");
    }

    if (performance_config_.max_history_size <= 0) {
        throw std::invalid_argument("Max history size must be positive");
    }
}

void HotReloadConfig::validateErrorRecoveryConfig() const {
    if (error_recovery_config_.max_rollback_points <= 0) {
        throw std::invalid_argument("Max rollback points must be positive");
    }

    if (error_recovery_config_.rollback_timeout_ms <= 0) {
        throw std::invalid_argument("Rollback timeout must be positive");
    }
}

void HotReloadConfig::validateFileWatchConfig() const {
    if (file_watch_config_.debounce_interval_ms <= 0) {
        throw std::invalid_argument("Debounce interval must be positive");
    }

    if (file_watch_config_.max_watched_files <= 0) {
        throw std::invalid_argument("Max watched files must be positive");
    }

    if (file_watch_config_.thread_pool_size <= 0) {
        throw std::invalid_argument("Thread pool size must be positive");
    }
}

void HotReloadConfig::validateReloadConfig() const {
    if (reload_config_.max_concurrent_reloads <= 0) {
        throw std::invalid_argument("Max concurrent reloads must be positive");
    }

    if (reload_config_.memory_limit_mb <= 0) {
        throw std::invalid_argument("Memory limit must be positive");
    }
}

QString HotReloadConfig::getDefaultConfigPath() const {
    return QStandardPaths::writableLocation(QStandardPaths::ConfigLocation) +
           "/DeclarativeUI/hotreload_config.json";
}

void HotReloadConfig::mergeConfiguration(const QJsonObject& base, const QJsonObject& overlay) {
    // Simple merge implementation - overlay values override base values
    for (auto it = overlay.begin(); it != overlay.end(); ++it) {
        const_cast<QJsonObject&>(base)[it.key()] = it.value();
    }
}

} // namespace DeclarativeUI::HotReload
