{"type": "QWidget", "properties": {"windowTitle": "05 - Event Handling | DeclarativeUI", "minimumSize": [600, 550]}, "layout": {"type": "QVBoxLayout", "properties": {"spacing": 15, "contentsMargins": [20, 20, 20, 20]}, "children": [{"type": "QLabel", "properties": {"text": "🎯 Event Handling Example", "alignment": "AlignCenter", "styleSheet": "font-size: 20px; font-weight: bold; color: #2c3e50;"}}, {"type": "QLabel", "properties": {"text": "Interact with the controls below to see event handling in action!", "alignment": "AlignCenter", "styleSheet": "color: #7f8c8d; font-style: italic;"}}, {"type": "QGroupBox", "properties": {"title": "🎮 Interactive Controls"}, "layout": {"type": "QGridLayout", "children": [{"type": "QPushButton", "properties": {"text": "🔘 Click Me!", "styleSheet": "QPushButton { background-color: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold; } QPushButton:hover { background-color: #2980b9; }"}, "events": {"clicked": "buttonClick"}, "gridPosition": [0, 0]}, {"type": "QLabel", "properties": {"objectName": "clickCounter", "text": "Clicks: 0", "styleSheet": "color: #2c3e50; font-weight: bold;"}, "gridPosition": [0, 1]}, {"type": "QLineEdit", "properties": {"objectName": "textInput", "placeholderText": "Type something to see text events..."}, "gridPosition": [1, 0]}, {"type": "QLabel", "properties": {"objectName": "charCount", "text": "Characters: 0", "styleSheet": "color: #2c3e50;"}, "gridPosition": [1, 1]}, {"type": "QSlider", "properties": {"objectName": "valueSlider", "orientation": "Horizontal", "minimum": 0, "maximum": 100, "value": 50}, "gridPosition": [2, 0]}, {"type": "QLabel", "properties": {"objectName": "slider<PERSON><PERSON><PERSON>", "text": "Slider: 50%", "styleSheet": "color: #2c3e50;"}, "gridPosition": [2, 1]}, {"type": "QCheckBox", "properties": {"objectName": "to<PERSON><PERSON><PERSON><PERSON>", "text": "Toggle me!", "checked": false}, "gridPosition": [3, 0]}, {"type": "QLabel", "properties": {"objectName": "checkboxStatus", "text": "❌ Disabled", "styleSheet": "color: #2c3e50;"}, "gridPosition": [3, 1]}, {"type": "QComboBox", "properties": {"objectName": "optionCombo", "items": ["Option 1", "Option 2", "Option 3", "Option 4"]}, "gridPosition": [4, 0]}, {"type": "QLabel", "properties": {"objectName": "comboSelection", "text": "Selected: Option 1", "styleSheet": "color: #2c3e50;"}, "gridPosition": [4, 1]}, {"type": "QSpinBox", "properties": {"objectName": "numberSpin", "minimum": 0, "maximum": 100, "value": 10}, "gridPosition": [5, 0]}, {"type": "QLabel", "properties": {"objectName": "spinboxValue", "text": "Number: 10", "styleSheet": "color: #2c3e50;"}, "gridPosition": [5, 1]}]}}, {"type": "QGroupBox", "properties": {"title": "📋 Event Log"}, "layout": {"type": "QVBoxLayout", "children": [{"type": "QTextEdit", "properties": {"objectName": "eventLog", "readOnly": true, "maximumHeight": 150, "styleSheet": "QTextEdit { background-color: #2c3e50; color: #ecf0f1; font-family: 'Courier New', monospace; font-size: 11px; }"}}, {"type": "layout", "layoutType": "QHBoxLayout", "children": [{"type": "QPushButton", "properties": {"text": "🗑️ Clear Log", "styleSheet": "QPushButton { background-color: #e74c3c; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #c0392b; }"}, "events": {"clicked": "clearLog"}}, {"type": "QPushButton", "properties": {"text": "🧪 Test All Events", "styleSheet": "QPushButton { background-color: #f39c12; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #e67e22; }"}, "events": {"clicked": "testAllEvents"}}, {"type": "spacer", "properties": {"orientation": "horizontal"}}]}]}}, {"type": "QLabel", "properties": {"text": "💡 All interactions are logged in real-time above", "alignment": "AlignCenter", "styleSheet": "font-size: 12px; color: #7f8c8d; font-style: italic;"}}]}}