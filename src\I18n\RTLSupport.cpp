#include "RTLSupport.hpp"
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QSpinBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QGroupBox>
#include <QTabWidget>
#include <QSplitter>
#include <QScrollArea>
#include <QRegularExpression>
#include <QDebug>

namespace DeclarativeUI {
namespace I18n {

RTLSupport* RTLSupport::instance_ = nullptr;
QMutex RTLSupport::instance_mutex_;

RTLSupport& RTLSupport::instance() {
    QMutexLocker locker(&instance_mutex_);
    if (!instance_) {
        instance_ = new RTLSupport();
    }
    return *instance_;
}

RTLSupport::RTLSupport(QObject* parent)
    : QObject(parent)
    , current_direction_(LayoutDirection::LeftToRight)
    , global_rtl_enabled_(false)
{
    // Initialize RTL languages list
    rtl_languages_ << "ar" << "he" << "fa" << "ur" << "yi" << "ji" << "iw" << "ku" << "ps" << "sd";
    
    qDebug() << "🌍 RTLSupport initialized";
}

void RTLSupport::setLayoutDirection(LayoutDirection direction) {
    if (direction == current_direction_) {
        return;
    }
    
    LayoutDirection old_direction = current_direction_;
    current_direction_ = direction;
    
    // Apply to application if global RTL is enabled
    if (global_rtl_enabled_) {
        Qt::LayoutDirection qt_direction = (direction == LayoutDirection::RightToLeft) 
            ? Qt::RightToLeft : Qt::LeftToRight;
        QApplication::setLayoutDirection(qt_direction);
    }
    
    qDebug() << "🌍 Layout direction changed to" << (direction == LayoutDirection::RightToLeft ? "RTL" : "LTR");
    emit layoutDirectionChanged(current_direction_, old_direction);
}

void RTLSupport::setLayoutDirection(const QLocale& locale) {
    if (isRTLLocale(locale)) {
        setLayoutDirection(LayoutDirection::RightToLeft);
    } else {
        setLayoutDirection(LayoutDirection::LeftToRight);
    }
}

void RTLSupport::applyRTLSupport(QWidget* widget, const RTLConfiguration& config) {
    if (!widget) {
        return;
    }
    
    applyRTLToWidget(widget, config);
}

void RTLSupport::applyRTLSupportRecursive(QWidget* widget, const RTLConfiguration& config) {
    if (!widget) {
        return;
    }
    
    applyRTLToWidget(widget, config);
    processChildWidgets(widget, config);
}

void RTLSupport::setWidgetLayoutDirection(QWidget* widget, LayoutDirection direction) {
    if (!widget) {
        return;
    }
    
    Qt::LayoutDirection qt_direction;
    switch (direction) {
        case LayoutDirection::RightToLeft:
            qt_direction = Qt::RightToLeft;
            break;
        case LayoutDirection::LeftToRight:
            qt_direction = Qt::LeftToRight;
            break;
        case LayoutDirection::Auto:
            qt_direction = isRightToLeft() ? Qt::RightToLeft : Qt::LeftToRight;
            break;
    }
    
    widget->setLayoutDirection(qt_direction);
}

void RTLSupport::mirrorLayout(QLayout* layout) {
    if (!layout) {
        return;
    }
    
    if (auto* box_layout = qobject_cast<QBoxLayout*>(layout)) {
        mirrorBoxLayout(box_layout);
    } else if (auto* grid_layout = qobject_cast<QGridLayout*>(layout)) {
        mirrorGridLayout(grid_layout);
    } else if (auto* form_layout = qobject_cast<QFormLayout*>(layout)) {
        mirrorFormLayout(form_layout);
    }
    
    adjustContentsMargins(layout);
}

void RTLSupport::mirrorBoxLayout(QBoxLayout* layout) {
    if (!layout || !isRightToLeft()) {
        return;
    }
    
    // Reverse the direction for horizontal layouts
    if (layout->direction() == QBoxLayout::LeftToRight) {
        layout->setDirection(QBoxLayout::RightToLeft);
    } else if (layout->direction() == QBoxLayout::RightToLeft) {
        layout->setDirection(QBoxLayout::LeftToRight);
    }
}

void RTLSupport::mirrorGridLayout(QGridLayout* layout) {
    if (!layout || !isRightToLeft()) {
        return;
    }
    
    // For grid layouts, we need to mirror column positions
    // This is more complex and might require rebuilding the layout
    // For now, we'll just adjust margins
    adjustContentsMargins(layout);
}

void RTLSupport::mirrorFormLayout(QFormLayout* layout) {
    if (!layout || !isRightToLeft()) {
        return;
    }
    
    // Mirror field growth policy
    if (layout->fieldGrowthPolicy() == QFormLayout::FieldsStayAtSizeHint) {
        // Keep as is
    } else {
        // Adjust label alignment
        layout->setLabelAlignment(Qt::AlignRight | Qt::AlignVCenter);
    }
}

Qt::Alignment RTLSupport::convertAlignment(Qt::Alignment alignment) const {
    if (!isRightToLeft()) {
        return alignment;
    }
    
    Qt::Alignment result = alignment;
    
    // Convert horizontal alignments
    if (alignment & Qt::AlignLeft) {
        result &= ~Qt::AlignLeft;
        result |= Qt::AlignRight;
    } else if (alignment & Qt::AlignRight) {
        result &= ~Qt::AlignRight;
        result |= Qt::AlignLeft;
    }
    
    return result;
}

Qt::Alignment RTLSupport::getLeadingAlignment() const {
    return isRightToLeft() ? Qt::AlignRight : Qt::AlignLeft;
}

Qt::Alignment RTLSupport::getTrailingAlignment() const {
    return isRightToLeft() ? Qt::AlignLeft : Qt::AlignRight;
}

RTLSupport::TextAlignment RTLSupport::convertTextAlignment(TextAlignment alignment) const {
    if (!isRightToLeft()) {
        return alignment;
    }
    
    switch (alignment) {
        case TextAlignment::Leading:
            return TextAlignment::Right;
        case TextAlignment::Trailing:
            return TextAlignment::Left;
        case TextAlignment::Left:
            return TextAlignment::Right;
        case TextAlignment::Right:
            return TextAlignment::Left;
        default:
            return alignment;
    }
}

void RTLSupport::adjustMargins(QWidget* widget) {
    if (!widget || !isRightToLeft()) {
        return;
    }
    
    QMargins margins = widget->contentsMargins();
    widget->setContentsMargins(mirrorMargins(margins));
}

void RTLSupport::adjustContentsMargins(QLayout* layout) {
    if (!layout || !isRightToLeft()) {
        return;
    }
    
    QMargins margins = layout->contentsMargins();
    layout->setContentsMargins(mirrorMargins(margins));
}

QMargins RTLSupport::mirrorMargins(const QMargins& margins) const {
    if (!isRightToLeft()) {
        return margins;
    }
    
    return QMargins(margins.right(), margins.top(), margins.left(), margins.bottom());
}

QPixmap RTLSupport::mirrorPixmap(const QPixmap& pixmap) const {
    if (!isRightToLeft() || pixmap.isNull()) {
        return pixmap;
    }
    
    // Check cache first
    QMutexLocker locker(&cache_mutex_);
    auto cache_key = pixmap.cacheKey();
    auto cache_it = pixmap_cache_.find(cache_key);
    if (cache_it != pixmap_cache_.end()) {
        return cache_it.value();
    }
    
    // Create mirrored pixmap
    QPixmap mirrored = pixmap.transformed(QTransform().scale(-1, 1));
    pixmap_cache_[cache_key] = mirrored;
    
    return mirrored;
}

QIcon RTLSupport::mirrorIcon(const QIcon& icon) const {
    if (!isRightToLeft() || icon.isNull()) {
        return icon;
    }
    
    // Create a new icon with mirrored pixmaps
    QIcon mirrored_icon;
    QList<QSize> sizes = icon.availableSizes();
    
    for (const QSize& size : sizes) {
        QPixmap pixmap = icon.pixmap(size);
        QPixmap mirrored = mirrorPixmap(pixmap);
        mirrored_icon.addPixmap(mirrored);
    }
    
    return mirrored_icon;
}

void RTLSupport::applyIconMirroring(QWidget* widget) {
    if (!widget || !isRightToLeft()) {
        return;
    }
    
    // Apply to common widgets with icons
    if (auto* button = qobject_cast<QPushButton*>(widget)) {
        if (!button->icon().isNull()) {
            button->setIcon(mirrorIcon(button->icon()));
        }
    } else if (auto* label = qobject_cast<QLabel*>(widget)) {
        if (label->pixmap() && !label->pixmap()->isNull()) {
            label->setPixmap(mirrorPixmap(*label->pixmap()));
        }
    }
}

QString RTLSupport::convertStyleSheet(const QString& styleSheet) const {
    if (!isRightToLeft() || styleSheet.isEmpty()) {
        return styleSheet;
    }
    
    // Check cache first
    QMutexLocker locker(&cache_mutex_);
    auto cache_it = style_cache_.find(styleSheet);
    if (cache_it != style_cache_.end()) {
        return cache_it.value();
    }
    
    QString converted = styleSheet;
    
    // Convert common CSS properties
    converted.replace("text-align: left", "text-align: right");
    converted.replace("text-align: right", "text-align: left");
    converted.replace("float: left", "float: right");
    converted.replace("float: right", "float: left");
    converted.replace("margin-left:", "margin-right:");
    converted.replace("margin-right:", "margin-left:");
    converted.replace("padding-left:", "padding-right:");
    converted.replace("padding-right:", "padding-left:");
    converted.replace("border-left:", "border-right:");
    converted.replace("border-right:", "border-left:");
    
    style_cache_[styleSheet] = converted;
    return converted;
}

QString RTLSupport::mirrorStyleProperty(const QString& property, const QString& value) const {
    if (!isRightToLeft()) {
        return value;
    }

    return convertCSSValue(property, value);
}

QString RTLSupport::processBidirectionalText(const QString& text) const {
    // Basic bidirectional text processing
    // In a full implementation, this would use ICU or similar
    return text;
}

bool RTLSupport::containsRTLCharacters(const QString& text) const {
    // Check for RTL Unicode characters
    for (const QChar& ch : text) {
        if (ch.direction() == QChar::DirR || ch.direction() == QChar::DirAL) {
            return true;
        }
    }
    return false;
}

bool RTLSupport::containsLTRCharacters(const QString& text) const {
    // Check for LTR Unicode characters
    for (const QChar& ch : text) {
        if (ch.direction() == QChar::DirL) {
            return true;
        }
    }
    return false;
}

void RTLSupport::adjustTabOrder(QWidget* parent) {
    if (!parent || !isRightToLeft()) {
        return;
    }

    QList<QWidget*> all_widgets = parent->findChildren<QWidget*>();
    QList<QWidget*> widgets;
    for (QWidget* w : all_widgets) {
        if (w->focusPolicy() != Qt::NoFocus) {
            widgets.append(w);
        }
    }

    reverseTabOrder(widgets);
}

void RTLSupport::reverseTabOrder(const QList<QWidget*>& widgets) {
    if (widgets.size() < 2) {
        return;
    }

    // Reverse the tab order
    for (int i = widgets.size() - 1; i > 0; --i) {
        QWidget::setTabOrder(widgets[i], widgets[i-1]);
    }
}

bool RTLSupport::isRTLLocale(const QLocale& locale) const {
    QString language = locale.name().left(2);
    return rtl_languages_.contains(language);
}

QStringList RTLSupport::getRTLLanguages() const {
    return rtl_languages_;
}

void RTLSupport::enableGlobalRTLSupport(bool enable) {
    global_rtl_enabled_ = enable;

    if (enable && isRightToLeft()) {
        QApplication::setLayoutDirection(Qt::RightToLeft);
    } else {
        QApplication::setLayoutDirection(Qt::LeftToRight);
    }

    qDebug() << "🌍 Global RTL support" << (enable ? "enabled" : "disabled");
}

// Private helper methods

void RTLSupport::applyRTLToWidget(QWidget* widget, const RTLConfiguration& config) {
    if (!widget) {
        return;
    }

    // Set layout direction
    if (config.auto_mirror_layout) {
        setWidgetLayoutDirection(widget, LayoutDirection::Auto);
    }

    // Adjust margins
    if (config.auto_adjust_margins) {
        adjustMargins(widget);
    }

    // Mirror icons
    if (config.auto_mirror_icons) {
        applyIconMirroring(widget);
    }

    // Apply custom styles
    if (!config.custom_styles.isEmpty()) {
        QString current_style = widget->styleSheet();
        for (auto it = config.custom_styles.begin(); it != config.custom_styles.end(); ++it) {
            current_style += convertStyleSheet(it.value());
        }
        widget->setStyleSheet(current_style);
    }

    // Mirror layout if present
    if (widget->layout()) {
        mirrorLayout(widget->layout());
    }
}

void RTLSupport::processChildWidgets(QWidget* parent, const RTLConfiguration& config) {
    if (!parent) {
        return;
    }

    QList<QWidget*> children = parent->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
    for (QWidget* child : children) {
        applyRTLSupportRecursive(child, config);
    }

    // Adjust tab order if requested
    if (config.preserve_tab_order) {
        adjustTabOrder(parent);
    }
}

QString RTLSupport::convertCSSProperty(const QString& property) const {
    if (!isRightToLeft()) {
        return property;
    }

    if (property == "margin-left") return "margin-right";
    if (property == "margin-right") return "margin-left";
    if (property == "padding-left") return "padding-right";
    if (property == "padding-right") return "padding-left";
    if (property == "border-left") return "border-right";
    if (property == "border-right") return "border-left";
    if (property == "left") return "right";
    if (property == "right") return "left";

    return property;
}

QString RTLSupport::convertCSSValue(const QString& property, const QString& value) const {
    if (!isRightToLeft()) {
        return value;
    }

    if (property.contains("text-align")) {
        if (value == "left") return "right";
        if (value == "right") return "left";
    }

    if (property.contains("float")) {
        if (value == "left") return "right";
        if (value == "right") return "left";
    }

    return value;
}

// RTLContext implementation

RTLContext::RTLContext(RTLSupport::LayoutDirection direction) {
    previous_direction_ = RTLSupport::instance().getLayoutDirection();
    RTLSupport::instance().setLayoutDirection(direction);
}

RTLContext::~RTLContext() {
    RTLSupport::instance().setLayoutDirection(previous_direction_);
}

} // namespace I18n
} // namespace DeclarativeUI
