#pragma once

#include "IFormatProcessor.hpp"
#include <QRegularExpression>
#include <QStringList>
#include <QHash>
#include <QFileSystemWatcher>
#include <QProcess>
#include <memory>

namespace DeclarativeUI::HotReload::FormatSupport {

/**
 * @brief SCSS/CSS processor for hot reload support
 * 
 * Handles SCSS and CSS file processing with features:
 * - SCSS compilation to CSS
 * - CSS validation and minification
 * - Import dependency tracking
 * - Variable extraction and substitution
 * - Live CSS injection
 * - Source map generation
 */
class SCSSProcessor : public IFormatProcessor {
    Q_OBJECT

public:
    explicit SCSSProcessor(QObject* parent = nullptr);
    ~SCSSProcessor() override = default;

    // Core interface implementation
    QString getFormatName() const override { return "SCSS/CSS"; }
    QStringList getSupportedExtensions() const override { return {"scss", "sass", "css"}; }
    bool canProcess(const QString& file_path) const override;

    // Processing methods
    ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;

    // Validation methods
    bool validateFile(const QString& file_path) const override;
    bool validateContent(const QString& content) const override;

    // Configuration and capabilities
    ProcessingConfig getDefaultConfig() const override;
    QStringList getRequiredDependencies() const override;
    bool isAvailable() const override;

    // Hot reload specific methods
    bool supportsLiveInjection() const override { return true; }
    ProcessingResult prepareLiveInjection(const QString& content, const QString& file_path) override;

    // SCSS/CSS specific methods
    QString compileSCSS(const QString& scss_content, const QString& file_path) const;
    QString minifyCSS(const QString& css_content) const;
    QStringList extractImports(const QString& content) const;
    QStringList extractVariables(const QString& scss_content) const;
    QString resolveImportPath(const QString& import_path, const QString& base_path) const;
    bool hasValidCSSSyntax(const QString& css_content, QString& error_message) const;
    QString generateSourceMap(const QString& scss_content, const QString& css_content, const QString& file_path) const;

    // Dependency management
    QStringList getDependencies(const QString& file_path) const;
    void watchDependencies(const QString& file_path);
    void unwatchDependencies(const QString& file_path);

    // Compilation settings
    void setSassExecutable(const QString& path);
    QString getSassExecutable() const;
    void setIncludePaths(const QStringList& paths);
    QStringList getIncludePaths() const;

signals:
    void scssCompiled(const QString& file_path, const QString& css_content);
    void cssMinified(const QString& file_path, const QString& minified_css);
    void dependencyChanged(const QString& file_path, const QString& dependency);
    void variablesExtracted(const QString& file_path, const QStringList& variables);
    void importResolved(const QString& import_path, const QString& resolved_path);

private:
    QString sass_executable_;
    QStringList include_paths_;
    QHash<QString, QStringList> file_dependencies_;
    std::unique_ptr<QFileSystemWatcher> dependency_watcher_;
    
    // Regex patterns
    QRegularExpression import_regex_;
    QRegularExpression variable_regex_;
    QRegularExpression comment_regex_;
    QRegularExpression selector_regex_;

    // Helper methods
    void initializeRegexPatterns();
    void findSassExecutable();
    QString runSassCompiler(const QString& input_content, const QString& file_path, const QStringList& options) const;
    QJsonObject createSCSSMetadata(const QString& file_path, const QString& content) const;
    QString removeComments(const QString& content) const;
    QString normalizeCSS(const QString& css_content) const;
    bool isValidSCSSFile(const QString& file_path) const;
    bool isValidCSSFile(const QString& file_path) const;
    QString extractFileExtension(const QString& file_path) const;
    QStringList parseImportStatement(const QString& import_line) const;
    QString resolveRelativeImport(const QString& import_path, const QString& base_path) const;
    QString resolveNodeModuleImport(const QString& import_path) const;
    void updateDependencyTracking(const QString& file_path, const QStringList& dependencies);

private slots:
    void onDependencyFileChanged(const QString& path);
};

} // namespace DeclarativeUI::HotReload::FormatSupport
