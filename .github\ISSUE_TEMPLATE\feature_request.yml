name: ✨ Feature Request
description: Suggest a new feature or enhancement for DeclarativeUI Framework
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for suggesting a new feature! Please provide detailed information about your request.

  - type: textarea
    id: summary
    attributes:
      label: Feature Summary
      description: A clear and concise description of the feature you'd like to see added.
      placeholder: Briefly describe the feature...
    validations:
      required: true

  - type: textarea
    id: motivation
    attributes:
      label: Motivation and Use Case
      description: Explain why this feature would be useful and what problem it solves.
      placeholder: |
        - What problem does this solve?
        - How would this feature be used?
        - Who would benefit from this feature?
    validations:
      required: true

  - type: dropdown
    id: component
    attributes:
      label: Component Area
      description: Which area of the framework would this feature affect?
      options:
        - Core Framework
        - Components Library
        - Command System
        - State Management
        - Hot Reload
        - JSON Support
        - Build System
        - Documentation
        - Examples
        - Developer Tools
        - Performance
        - API Design
        - Other
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would improve workflow
        - High - Blocking current work
        - Critical - Essential for project success
    validations:
      required: true

  - type: textarea
    id: detailed-description
    attributes:
      label: Detailed Description
      description: Provide a detailed description of how the feature should work.
      placeholder: |
        Describe the feature in detail:
        - How should it behave?
        - What should the API look like?
        - How should it integrate with existing features?
    validations:
      required: true

  - type: textarea
    id: api-proposal
    attributes:
      label: Proposed API (if applicable)
      description: If you have ideas for the API design, please share them here.
      render: cpp
      placeholder: |
        // Example API usage
        auto component = ComponentBuilder()
            .newFeature(parameters)
            .build();

  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered.
      placeholder: |
        - Alternative approach 1: ...
        - Alternative approach 2: ...
        - Why the proposed solution is better: ...

  - type: textarea
    id: examples
    attributes:
      label: Usage Examples
      description: Provide examples of how this feature would be used in practice.
      placeholder: |
        Example 1: Basic usage
        Example 2: Advanced usage
        Example 3: Integration with existing code

  - type: dropdown
    id: breaking-changes
    attributes:
      label: Breaking Changes
      description: Would this feature require breaking changes to the existing API?
      options:
        - No breaking changes required
        - Minor breaking changes (with migration path)
        - Major breaking changes required
        - Unsure
    validations:
      required: true

  - type: textarea
    id: implementation-notes
    attributes:
      label: Implementation Notes
      description: If you have ideas about how this could be implemented, please share them.
      placeholder: |
        - Technical considerations
        - Potential challenges
        - Dependencies on other features
        - Performance implications

  - type: textarea
    id: testing-considerations
    attributes:
      label: Testing Considerations
      description: How should this feature be tested?
      placeholder: |
        - Unit tests needed
        - Integration tests needed
        - Performance tests needed
        - Example applications to demonstrate usage

  - type: textarea
    id: documentation-needs
    attributes:
      label: Documentation Requirements
      description: What documentation would be needed for this feature?
      placeholder: |
        - API documentation
        - User guide updates
        - Tutorial/example updates
        - Migration guide (if breaking changes)

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided a clear description of the feature and its benefits
          required: true
        - label: I have considered the impact on existing users and API compatibility
          required: true
        - label: I am willing to help implement this feature
          required: false
        - label: I am willing to help with testing and documentation
          required: false
