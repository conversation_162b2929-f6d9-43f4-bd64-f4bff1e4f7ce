#!/bin/bash
# Validate CMake structure and best practices

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

errors=0
warnings=0

echo "🔍 Checking CMake structure and best practices..."

# Check main CMakeLists.txt
if [[ -f "CMakeLists.txt" ]]; then
    echo "Checking: CMakeLists.txt"
    
    # Check for minimum CMake version
    if ! grep -n "cmake_minimum_required" CMakeLists.txt >/dev/null 2>&1; then
        echo -e "${RED}❌ Error: Missing cmake_minimum_required in CMakeLists.txt${NC}"
        ((errors++))
    fi
    
    # Check for project declaration
    if ! grep -n "project(" CMakeLists.txt >/dev/null 2>&1; then
        echo -e "${RED}❌ Error: Missing project() declaration in CMakeLists.txt${NC}"
        ((errors++))
    fi
    
    # Check for C++ standard
    if ! grep -n "CMAKE_CXX_STANDARD" CMakeLists.txt >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Warning: Consider setting CMAKE_CXX_STANDARD${NC}"
        ((warnings++))
    fi
    
    # Check for Qt6 usage
    if grep -n "find_package.*Qt6" CMakeLists.txt >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Using Qt6${NC}"
    elif grep -n "find_package.*Qt5" CMakeLists.txt >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Warning: Consider upgrading to Qt6${NC}"
        ((warnings++))
    fi
    
    # Check for proper target linking
    if grep -n "target_link_libraries" CMakeLists.txt >/dev/null 2>&1; then
        if ! grep -n "Qt6::" CMakeLists.txt >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Warning: Consider using Qt6:: targets for linking${NC}"
            ((warnings++))
        fi
    fi
fi

# Check for CMake files in subdirectories
find . -name "CMakeLists.txt" -not -path "./build/*" -not -path "./.cache/*" | while read -r cmake_file; do
    if [[ "$cmake_file" != "./CMakeLists.txt" ]]; then
        echo "Checking: $cmake_file"
        
        # Check for proper target definitions
        if ! grep -n "add_executable\|add_library" "$cmake_file" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Warning: No targets defined in $cmake_file${NC}"
            ((warnings++))
        fi
    fi
done

# Check for CMake presets
if [[ -f "CMakePresets.json" ]]; then
    echo -e "${GREEN}✅ Good: CMakePresets.json found${NC}"
    
    # Validate JSON syntax
    if command -v python3 >/dev/null 2>&1; then
        if ! python3 -m json.tool CMakePresets.json >/dev/null 2>&1; then
            echo -e "${RED}❌ Error: Invalid JSON in CMakePresets.json${NC}"
            ((errors++))
        fi
    fi
else
    echo -e "${YELLOW}⚠️  Warning: Consider adding CMakePresets.json for better developer experience${NC}"
    ((warnings++))
fi

echo ""
echo "📊 Summary:"
echo "   Errors: $errors"
echo "   Warnings: $warnings"

if [ $errors -gt 0 ]; then
    echo -e "${RED}❌ CMake structure check failed with $errors errors${NC}"
    exit 1
elif [ $warnings -gt 0 ]; then
    echo -e "${YELLOW}⚠️  CMake structure check completed with $warnings warnings${NC}"
    exit 0
else
    echo -e "${GREEN}✅ CMake structure check passed${NC}"
    exit 0
fi
