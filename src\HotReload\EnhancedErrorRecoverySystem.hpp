#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QTimer>
#include <QMutex>
#include <QReadWriteLock>
#include <QProgressBar>
#include <QStatusBar>
#include <QSystemTrayIcon>
#include <QFuture>
#include <QtConcurrent>
#include <memory>
#include <deque>
#include <unordered_map>
#include <functional>
#include <atomic>
#include <vector>

#include "DiagnosticsEngine.hpp"
#include "HotReloadConfig.hpp"
#include "../Components/MessageBox.hpp"
#include "../Components/StatusBar.hpp"
#include "../Components/ProgressBar.hpp"

namespace DeclarativeUI::HotReload {

// Using ErrorRecoveryConfig from HotReloadConfig.hpp

/**
 * @brief Recovery operation result
 */
struct RecoveryResult {
    enum Status {
        Success = 0,
        PartialSuccess = 1,
        Failed = 2,
        Timeout = 3,
        Cancelled = 4
    };
    
    Status status = Failed;
    QString operation_id;
    QString target_id;
    QString strategy_name;
    QString error_message;
    QStringList actions_taken;
    QStringList remaining_issues;
    qint64 duration_ms = 0;
    QDateTime timestamp;
    QJsonObject metadata;
    
    bool isSuccess() const { return status == Success || status == PartialSuccess; }
    
    static RecoveryResult createSuccess(const QString& operation_id, const QString& strategy_name) {
        RecoveryResult result;
        result.status = Success;
        result.operation_id = operation_id;
        result.strategy_name = strategy_name;
        result.timestamp = QDateTime::currentDateTime();
        return result;
    }
    
    static RecoveryResult createFailure(const QString& operation_id, const QString& error_message) {
        RecoveryResult result;
        result.status = Failed;
        result.operation_id = operation_id;
        result.error_message = error_message;
        result.timestamp = QDateTime::currentDateTime();
        return result;
    }
    
    QJsonObject toJson() const;
    static RecoveryResult fromJson(const QJsonObject& json);
};

/**
 * @brief Advanced recovery strategy with enhanced capabilities
 */
struct AdvancedRecoveryStrategy {
    QString name;
    QString description;
    QString category; // "rollback", "restoration", "isolation", "degradation"
    int priority = 50; // Higher priority strategies are tried first
    
    // Conditions and checks
    std::function<bool(const DiagnosticInfo&)> condition_check;
    std::function<bool(const DiagnosticInfo&)> pre_recovery_check;
    std::function<bool(const DiagnosticInfo&)> post_recovery_validation;
    
    // Recovery actions
    std::function<RecoveryResult(const DiagnosticInfo&)> recovery_action;
    std::function<void(const DiagnosticInfo&)> cleanup_action;
    
    // Configuration
    int max_attempts = 3;
    int delay_ms = 1000;
    int timeout_ms = 30000;
    bool enabled = true;
    bool requires_user_confirmation = false;
    bool supports_partial_recovery = true;
    
    // Progress tracking
    std::function<void(int)> progress_callback;
    std::function<void(const QString&)> status_callback;
    
    AdvancedRecoveryStrategy() = default;
    AdvancedRecoveryStrategy(const QString& name, const QString& description, const QString& category) 
        : name(name), description(description), category(category) {}
};

/**
 * @brief User notification manager for error recovery
 */
class ErrorNotificationManager : public QObject {
    Q_OBJECT

public:
    enum NotificationType {
        Info = 0,
        Warning = 1,
        Error = 2,
        Critical = 3,
        Recovery = 4,
        Success = 5
    };

    explicit ErrorNotificationManager(QObject* parent = nullptr);
    ~ErrorNotificationManager() override = default;

    // Notification methods
    void showNotification(NotificationType type, const QString& title, const QString& message, int timeout_ms = 5000);
    void showSystemNotification(const QString& title, const QString& message, QSystemTrayIcon::MessageIcon icon = QSystemTrayIcon::Information);
    void showStatusMessage(const QString& message, int timeout_ms = 3000);
    void showProgressDialog(const QString& title, const QString& message, int maximum = 100);
    void updateProgress(int value, const QString& status = QString());
    void hideProgress();
    
    // Error-specific notifications
    void notifyErrorDetected(const DiagnosticInfo& diagnostic);
    void notifyRecoveryStarted(const QString& strategy_name, const QString& target_id);
    void notifyRecoveryProgress(const QString& operation_id, int progress, const QString& status);
    void notifyRecoveryCompleted(const RecoveryResult& result);
    void notifyGracefulDegradation(const QString& feature_name, const QString& reason);
    
    // Configuration
    void setStatusBar(QStatusBar* status_bar);
    void setSystemTrayIcon(QSystemTrayIcon* tray_icon);
    void setNotificationsEnabled(bool enabled);
    void setProgressIndicatorsEnabled(bool enabled);

signals:
    void notificationShown(NotificationType type, const QString& title, const QString& message);
    void userActionRequested(const QString& action_id, const QJsonObject& parameters);

private:
    QStatusBar* status_bar_ = nullptr;
    QSystemTrayIcon* system_tray_ = nullptr;
    std::unique_ptr<QProgressBar> progress_bar_;
    std::unique_ptr<Components::MessageBox> message_box_;
    
    bool notifications_enabled_ = true;
    bool progress_indicators_enabled_ = true;
    
    void setupSystemTray();
    void setupProgressBar();
};

/**
 * @brief Graceful degradation manager
 */
class GracefulDegradationManager : public QObject {
    Q_OBJECT

public:
    enum DegradationLevel {
        None = 0,
        Minimal = 1,
        Moderate = 2,
        Severe = 3,
        SafeMode = 4
    };

    explicit GracefulDegradationManager(QObject* parent = nullptr);
    ~GracefulDegradationManager() override = default;

    // Degradation control
    void enableDegradation(const QString& feature_name, DegradationLevel level, const QString& reason);
    void disableDegradation(const QString& feature_name);
    void setGlobalDegradationLevel(DegradationLevel level);
    DegradationLevel getCurrentDegradationLevel() const;
    
    // Feature management
    bool isFeatureEnabled(const QString& feature_name) const;
    QStringList getDisabledFeatures() const;
    QStringList getDegradedFeatures() const;
    
    // Safe mode
    void enterSafeMode(const QString& reason);
    void exitSafeMode();
    bool isInSafeMode() const;

signals:
    void degradationEnabled(const QString& feature_name, DegradationLevel level, const QString& reason);
    void degradationDisabled(const QString& feature_name);
    void safeModeEntered(const QString& reason);
    void safeModeExited();

private:
    struct FeatureDegradation {
        DegradationLevel level = None;
        QString reason;
        QDateTime timestamp;
        bool user_initiated = false;
    };
    
    mutable QReadWriteLock degradation_lock_;
    std::unordered_map<QString, FeatureDegradation> degraded_features_;
    DegradationLevel global_level_ = None;
    bool safe_mode_active_ = false;
    QString safe_mode_reason_;
};

/**
 * @brief Enhanced Error Recovery System
 *
 * Provides comprehensive error recovery with features:
 * - Automatic rollback and restoration
 * - Graceful degradation with partial functionality
 * - User notifications and progress indicators
 * - Detailed error reporting with suggested fixes
 * - Smart recovery strategies with priority ordering
 * - Performance impact assessment
 * - Recovery operation tracking and analytics
 */
class EnhancedErrorRecoverySystem : public QObject {
    Q_OBJECT

public:
    explicit EnhancedErrorRecoverySystem(QObject* parent = nullptr);
    ~EnhancedErrorRecoverySystem() override = default;

    // Configuration
    void setConfiguration(const ErrorRecoveryConfig& config);
    ErrorRecoveryConfig getConfiguration() const;
    void setDiagnosticsEngine(DiagnosticsEngine* engine);
    void setNotificationManager(ErrorNotificationManager* manager);
    void setDegradationManager(GracefulDegradationManager* manager);

    // Recovery strategy management
    void registerRecoveryStrategy(const AdvancedRecoveryStrategy& strategy);
    void unregisterRecoveryStrategy(const QString& strategy_name);
    void enableRecoveryStrategy(const QString& strategy_name, bool enabled);
    QStringList getAvailableStrategies() const;
    QStringList getEnabledStrategies() const;

    // Recovery operations
    QFuture<RecoveryResult> attemptRecovery(const QString& diagnostic_id);
    QFuture<RecoveryResult> attemptRecoveryForFile(const QString& file_path);
    QFuture<QList<RecoveryResult>> attemptBatchRecovery(const QStringList& diagnostic_ids);
    void cancelRecovery(const QString& operation_id);
    void cancelAllRecoveries();

    // Error handling integration
    void handleError(const DiagnosticInfo& diagnostic);
    void handleCriticalError(const DiagnosticInfo& diagnostic);
    void handleRecoverableError(const DiagnosticInfo& diagnostic);

    // Recovery monitoring and analytics
    QJsonObject getRecoveryStatistics() const;
    QList<RecoveryResult> getRecoveryHistory(int days = 7) const;
    QStringList getMostEffectiveStrategies() const;
    QStringList getMostProblematicFiles() const;
    double getOverallRecoverySuccessRate() const;

    // Manual recovery controls
    void triggerManualRecovery(const QString& target_id, const QString& strategy_name);
    void createRecoveryCheckpoint(const QString& checkpoint_name);
    void restoreFromCheckpoint(const QString& checkpoint_name);
    QStringList getAvailableCheckpoints() const;

signals:
    void recoveryStarted(const QString& operation_id, const QString& target_id, const QString& strategy_name);
    void recoveryProgress(const QString& operation_id, int progress, const QString& status);
    void recoveryCompleted(const RecoveryResult& result);
    void recoveryFailed(const QString& operation_id, const QString& error_message);
    void gracefulDegradationTriggered(const QString& feature_name, const QString& reason);
    void criticalErrorRequiresAttention(const DiagnosticInfo& diagnostic);
    void recoveryStatisticsUpdated();

private slots:
    void onDiagnosticReported(const DiagnosticInfo& diagnostic);
    void onCriticalErrorDetected(const DiagnosticInfo& diagnostic);
    void onRecoveryTimerTimeout();
    void onProgressUpdate();

private:
    // Core components
    DiagnosticsEngine* diagnostics_engine_ = nullptr;
    ErrorNotificationManager* notification_manager_ = nullptr;
    GracefulDegradationManager* degradation_manager_ = nullptr;

    // Configuration
    ErrorRecoveryConfig config_;
    mutable QReadWriteLock config_lock_;

    // Recovery strategies
    std::vector<AdvancedRecoveryStrategy> recovery_strategies_;
    mutable QReadWriteLock strategies_lock_;

    // Active recovery operations
    struct ActiveRecovery {
        QString operation_id;
        QString diagnostic_id;
        QString strategy_name;
        QDateTime start_time;
        std::atomic<int> progress{0};
        std::atomic<bool> cancelled{false};
        QFuture<RecoveryResult> future;
    };

    std::unordered_map<QString, ActiveRecovery> active_recoveries_;
    mutable QMutex active_recoveries_lock_;

    // Recovery history and analytics
    std::deque<RecoveryResult> recovery_history_;
    mutable QReadWriteLock history_lock_;

    // Checkpoints
    struct RecoveryCheckpoint {
        QString name;
        QDateTime timestamp;
        QJsonObject state_data;
        QStringList affected_files;
    };

    std::unordered_map<QString, RecoveryCheckpoint> checkpoints_;
    mutable QReadWriteLock checkpoints_lock_;

    // Timers and monitoring
    std::unique_ptr<QTimer> recovery_timer_;
    std::unique_ptr<QTimer> progress_timer_;
    std::unique_ptr<QTimer> cleanup_timer_;

    // Helper methods
    void initializeBuiltinStrategies();
    void setupTimers();
    QString generateOperationId() const;
    RecoveryResult executeRecoveryStrategy(const AdvancedRecoveryStrategy& strategy, const DiagnosticInfo& diagnostic);
    void updateRecoveryProgress(const QString& operation_id, int progress, const QString& status);
    void finalizeRecovery(const QString& operation_id, const RecoveryResult& result);
    void cleanupCompletedRecoveries();
    void recordRecoveryResult(const RecoveryResult& result);
    bool shouldAttemptRecovery(const DiagnosticInfo& diagnostic) const;
    QList<AdvancedRecoveryStrategy> selectRecoveryStrategies(const DiagnosticInfo& diagnostic) const;
    void handleRecoveryTimeout(const QString& operation_id);
    void triggerGracefulDegradation(const DiagnosticInfo& diagnostic);
    void assessPerformanceImpact(const RecoveryResult& result);
};

} // namespace DeclarativeUI::HotReload
