#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QScreen>
#include <QFontDatabase>

#include "AdvancedLayoutExample.hpp"

/**
 * @brief Main entry point for the Advanced Layout Example
 * 
 * This application demonstrates the comprehensive advanced layout capabilities
 * of the DeclarativeUI framework, including:
 * - CSS Grid-like layouts with named areas and flexible tracks
 * - Flexbox-style layouts with flexible item arrangement
 * - Responsive design with breakpoints and adaptive layouts
 * - Constraint-based positioning with relationships between elements
 * - Interactive controls for real-time layout parameter adjustment
 * - Performance monitoring and debugging tools
 */
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // **Application Metadata**
    app.setApplicationName("Advanced Layout Example");
    app.setApplicationVersion("1.0.0");
    app.setApplicationDisplayName("DeclarativeUI Advanced Layout Demo");
    app.setOrganizationName("DeclarativeUI Framework");
    app.setOrganizationDomain("declarativeui.org");
    
    qDebug() << "🚀 Starting Advanced Layout Example";
    qDebug() << "📱 Application:" << app.applicationDisplayName();
    qDebug() << "🔢 Version:" << app.applicationVersion();
    qDebug() << "🖥️ Qt Version:" << qVersion();
    
    // **Setup Application Style**
    qDebug() << "🎨 Available styles:" << QStyleFactory::keys();
    
    // Use a modern style if available
    QStringList preferred_styles = {"Fusion", "Windows", "macOS"};
    for (const QString& style : preferred_styles) {
        if (QStyleFactory::keys().contains(style, Qt::CaseInsensitive)) {
            app.setStyle(QStyleFactory::create(style));
            qDebug() << "🎨 Using style:" << style;
            break;
        }
    }
    
    // **Setup High DPI Support**
    if (app.primaryScreen()) {
        qreal dpr = app.primaryScreen()->devicePixelRatio();
        qDebug() << "🖥️ Screen DPI ratio:" << dpr;
        qDebug() << "🖥️ Screen resolution:" << app.primaryScreen()->geometry().size();
        qDebug() << "🖥️ Available geometry:" << app.primaryScreen()->availableGeometry().size();
        
        if (dpr > 1.0) {
            qDebug() << "✨ High DPI display detected";
        }
    }
    
    // **Load Custom Fonts (if available)**
    QStringList font_paths = {
        ":/fonts/",
        QDir::currentPath() + "/fonts/",
        QStandardPaths::writableLocation(QStandardPaths::FontsLocation)
    };
    
    for (const QString& path : font_paths) {
        QDir font_dir(path);
        if (font_dir.exists()) {
            QStringList font_files = font_dir.entryList({"*.ttf", "*.otf"}, QDir::Files);
            for (const QString& font_file : font_files) {
                int font_id = QFontDatabase::addApplicationFont(font_dir.absoluteFilePath(font_file));
                if (font_id != -1) {
                    QStringList families = QFontDatabase::applicationFontFamilies(font_id);
                    qDebug() << "🔤 Loaded font:" << families;
                }
            }
        }
    }
    
    // **Setup Application Stylesheet**
    QString app_style = R"(
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }
        
        QTabBar::tab {
            background-color: #e0e0e0;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 1px solid white;
        }
        
        QTabBar::tab:hover {
            background-color: #f0f0f0;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            font-size: 14px;
            border-radius: 4px;
        }
        
        QPushButton:hover {
            background-color: #45a049;
        }
        
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        
        QPushButton:checked {
            background-color: #2196F3;
        }
        
        QSlider::groove:horizontal {
            border: 1px solid #bbb;
            background: white;
            height: 10px;
            border-radius: 4px;
        }
        
        QSlider::sub-page:horizontal {
            background: #4CAF50;
            border: 1px solid #777;
            height: 10px;
            border-radius: 4px;
        }
        
        QSlider::add-page:horizontal {
            background: #fff;
            border: 1px solid #777;
            height: 10px;
            border-radius: 4px;
        }
        
        QSlider::handle:horizontal {
            background: #4CAF50;
            border: 1px solid #777;
            width: 18px;
            margin-top: -2px;
            margin-bottom: -2px;
            border-radius: 3px;
        }
        
        QSlider::handle:horizontal:hover {
            background: #45a049;
        }
        
        QComboBox {
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 4px 8px;
            background-color: white;
        }
        
        QComboBox:hover {
            border-color: #4CAF50;
        }
        
        QComboBox::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 15px;
            border-left-width: 1px;
            border-left-color: #ccc;
            border-left-style: solid;
        }
        
        QStatusBar {
            background-color: #e0e0e0;
            border-top: 1px solid #ccc;
        }
    )";
    
    app.setStyleSheet(app_style);
    
    // **Create and Show Main Window**
    AdvancedLayoutExample window;
    window.show();
    
    qDebug() << "🏗️ Advanced Layout Example window created and shown";
    qDebug() << "🎯 Window geometry:" << window.geometry();
    
    // **Run Application Event Loop**
    int result = app.exec();
    
    qDebug() << "👋 Advanced Layout Example finished with code:" << result;
    return result;
}
