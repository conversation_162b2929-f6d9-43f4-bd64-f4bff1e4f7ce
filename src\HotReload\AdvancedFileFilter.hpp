#pragma once

#include <QRegularExpression>
#include <QFileInfo>
#include <QDir>
#include <QDateTime>
#include <QMimeDatabase>
#include <QMimeType>
#include <QJsonObject>
#include <QJsonArray>
#include <QStringList>
#include <QHash>
#include <QCache>
#include <QReadWriteLock>
#include <QMutex>

#include <memory>
#include <atomic>
#include <chrono>
#include <unordered_map>
#include <unordered_set>

namespace DeclarativeUI::HotReload {

/**
 * @brief Advanced file filtering rule
 */
struct FilterRule {
    enum class Type {
        Include,
        Exclude
    };
    
    enum class MatchType {
        Extension,      // Match by file extension
        Glob,          // Glob pattern matching
        Regex,         // Regular expression matching
        Size,          // File size constraints
        Content,       // Content-based matching
        MimeType,      // MIME type matching
        Directory,     // Directory path matching
        Age,           // File age constraints
        Custom         // Custom filter function
    };
    
    Type type = Type::Include;
    MatchType match_type = MatchType::Glob;
    QString pattern;
    QString description;
    
    // Size constraints (for Size match type)
    qint64 min_size_bytes = 0;
    qint64 max_size_bytes = -1; // -1 means no limit
    
    // Age constraints (for Age match type)
    std::chrono::seconds max_age{0}; // 0 means no limit
    std::chrono::seconds min_age{0};
    
    // Priority for rule ordering
    int priority = 0;
    
    // Performance optimization
    mutable QRegularExpression compiled_regex;
    mutable bool regex_compiled = false;
    
    // Custom filter function
    std::function<bool(const QString&, const QFileInfo&)> custom_filter;
    
    QJsonObject toJson() const;
    static FilterRule fromJson(const QJsonObject& json);
    bool matches(const QString& file_path, const QFileInfo& file_info) const;
    bool matchesContent(const QString& content) const;
    
private:
    void compileRegex() const;
};

/**
 * @brief File filtering statistics and analytics
 */
struct FilterStats {
    qint64 total_files_processed{0};
    qint64 files_included{0};
    qint64 files_excluded{0};
    qint64 cache_hits{0};
    qint64 cache_misses{0};

    // Performance metrics
    qint64 total_filter_time_ms{0};
    qint64 average_filter_time_ns{0};

    // Rule usage statistics
    std::unordered_map<QString, qint64> rule_usage_count;
    
    void reset();
    QJsonObject toJson() const;
    double getInclusionRate() const;
    double getCacheHitRate() const;
};

/**
 * @brief Advanced file filter with intelligent caching and performance optimization
 */
class AdvancedFileFilter : public QObject {
    Q_OBJECT

public:
    explicit AdvancedFileFilter(QObject* parent = nullptr);
    ~AdvancedFileFilter() = default;

    // **Rule management**
    void addRule(const FilterRule& rule);
    void removeRule(const QString& pattern);
    void clearRules();
    void setRules(const std::vector<FilterRule>& rules);
    std::vector<FilterRule> getRules() const;
    
    // **Predefined rule sets**
    void addCommonWebRules();
    void addCommonQtRules();
    void addCommonBuildExclusions();
    void addCommonIDEExclusions();
    void addSecurityExclusions();
    
    // **Filtering operations**
    bool shouldIncludeFile(const QString& file_path) const;
    bool shouldIncludeFile(const QString& file_path, const QFileInfo& file_info) const;
    bool shouldIncludeContent(const QString& file_path, const QString& content) const;
    
    // **Batch operations for performance**
    QStringList filterFiles(const QStringList& file_paths) const;
    QStringList filterDirectory(const QString& directory_path, bool recursive = false) const;
    
    // **Configuration**
    void enableCaching(bool enabled);
    void setCacheSize(int max_entries);
    void enableContentFiltering(bool enabled);
    void enableMimeTypeDetection(bool enabled);
    void setMaxFileSize(qint64 max_size_bytes);
    void setMinFileSize(qint64 min_size_bytes);
    
    // **Performance tuning**
    void enableFastMode(bool enabled); // Skip expensive operations
    void setMaxContentScanSize(qint64 max_size_bytes);
    void enableParallelProcessing(bool enabled);
    void setThreadPoolSize(int thread_count);
    
    // **Statistics and monitoring**
    FilterStats getStatistics() const;
    void resetStatistics();
    QStringList getRecentlyExcludedFiles(int max_count = 100) const;
    QStringList getMostExcludedPatterns(int max_count = 10) const;
    
    // **Serialization**
    QJsonObject toJson() const;
    bool loadFromJson(const QJsonObject& json);
    bool saveToFile(const QString& file_path) const;
    bool loadFromFile(const QString& file_path);
    
    // **Validation and testing**
    bool validateRules() const;
    QStringList testFilter(const QString& test_directory) const;
    QString explainDecision(const QString& file_path) const;

signals:
    void ruleAdded(const QString& pattern);
    void ruleRemoved(const QString& pattern);
    void statisticsUpdated();
    void filteringError(const QString& error);

private:
    // **Core filtering logic**
    bool evaluateRules(const QString& file_path, const QFileInfo& file_info) const;
    bool evaluateRule(const FilterRule& rule, const QString& file_path, const QFileInfo& file_info) const;
    
    // **Content analysis**
    bool shouldScanContent(const QFileInfo& file_info) const;
    QString readFileContent(const QString& file_path, qint64 max_size) const;
    
    // **Caching system**
    struct CacheEntry {
        bool should_include;
        QDateTime timestamp;
        qint64 file_size;
        QString file_hash; // For content-based caching
    };
    
    mutable QCache<QString, CacheEntry> filter_cache_;
    mutable QReadWriteLock cache_lock_;
    
    // **Rule storage and management**
    std::vector<FilterRule> rules_;
    mutable QReadWriteLock rules_lock_;
    
    // **Configuration**
    std::atomic<bool> caching_enabled_{true};
    std::atomic<bool> content_filtering_enabled_{false};
    std::atomic<bool> mime_type_detection_enabled_{false};
    std::atomic<bool> fast_mode_enabled_{false};
    std::atomic<bool> parallel_processing_enabled_{false};
    
    std::atomic<qint64> max_file_size_bytes_{10 * 1024 * 1024}; // 10MB
    std::atomic<qint64> min_file_size_bytes_{0};
    std::atomic<qint64> max_content_scan_size_{1024 * 1024}; // 1MB
    std::atomic<int> thread_pool_size_{4};
    
    // **Performance tracking**
    mutable std::atomic<qint64> total_files_processed_{0};
    mutable std::atomic<qint64> files_included_{0};
    mutable std::atomic<qint64> files_excluded_{0};
    mutable std::atomic<qint64> cache_hits_{0};
    mutable std::atomic<qint64> cache_misses_{0};
    mutable std::atomic<qint64> total_filter_time_ms_{0};
    mutable std::atomic<qint64> average_filter_time_ns_{0};
    mutable std::unordered_map<QString, qint64> rule_usage_count_;
    mutable QMutex rule_stats_mutex_;

    mutable QStringList recently_excluded_files_;
    mutable QMutex excluded_files_mutex_;
    
    // **MIME type detection**
    mutable QMimeDatabase mime_database_;
    
    // **Helper methods**
    void sortRulesByPriority();
    void updateStatistics(bool included, qint64 processing_time_ns) const;
    void addToRecentlyExcluded(const QString& file_path) const;
    QString calculateFileHash(const QString& file_path) const;
    bool isCacheEntryValid(const CacheEntry& entry, const QFileInfo& file_info) const;
    
    // **Predefined patterns**
    static std::vector<FilterRule> createWebDevelopmentRules();
    static std::vector<FilterRule> createQtDevelopmentRules();
    static std::vector<FilterRule> createBuildExclusionRules();
    static std::vector<FilterRule> createIDEExclusionRules();
    static std::vector<FilterRule> createSecurityExclusionRules();
};

/**
 * @brief Factory for creating common filter configurations
 */
class FilterPresets {
public:
    static std::unique_ptr<AdvancedFileFilter> createWebDevelopmentFilter();
    static std::unique_ptr<AdvancedFileFilter> createQtDevelopmentFilter();
    static std::unique_ptr<AdvancedFileFilter> createMinimalFilter();
    static std::unique_ptr<AdvancedFileFilter> createComprehensiveFilter();
    static std::unique_ptr<AdvancedFileFilter> createPerformanceOptimizedFilter();
    
    // Configuration-based factory
    static std::unique_ptr<AdvancedFileFilter> createFromConfig(const QJsonObject& config);
};

} // namespace DeclarativeUI::HotReload
