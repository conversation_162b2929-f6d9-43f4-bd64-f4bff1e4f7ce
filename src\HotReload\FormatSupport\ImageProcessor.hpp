#pragma once

#include "IFormatProcessor.hpp"
#include <QPixmap>
#include <QImageReader>
#include <QImageWriter>
#include <QBuffer>
#include <QCryptographicHash>
#include <QCache>
#include <QMutex>
#include <memory>

namespace DeclarativeUI::HotReload::FormatSupport {

/**
 * @brief Image asset processor for hot reload support
 * 
 * Handles image file processing with features:
 * - Image format validation and conversion
 * - Automatic cache invalidation
 * - Image optimization and compression
 * - Metadata extraction (dimensions, format, size)
 * - Live image injection with cache busting
 */
class ImageProcessor : public IFormatProcessor {
    Q_OBJECT

public:
    explicit ImageProcessor(QObject* parent = nullptr);
    ~ImageProcessor() override = default;

    // Core interface implementation
    QString getFormatName() const override { return "Image"; }
    QStringList getSupportedExtensions() const override;
    bool canProcess(const QString& file_path) const override;

    // Processing methods
    ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;

    // Validation methods
    bool validateFile(const QString& file_path) const override;
    bool validateContent(const QString& content) const override;

    // Configuration and capabilities
    ProcessingConfig getDefaultConfig() const override;
    QStringList getRequiredDependencies() const override { return {"Qt6::Gui"}; }
    bool isAvailable() const override;

    // Hot reload specific methods
    bool supportsLiveInjection() const override { return true; }
    ProcessingResult prepareLiveInjection(const QString& content, const QString& file_path) override;

    // Image-specific methods
    QSize getImageDimensions(const QString& file_path) const;
    QString getImageFormat(const QString& file_path) const;
    qint64 getImageFileSize(const QString& file_path) const;
    QString calculateImageHash(const QString& file_path) const;
    bool optimizeImage(const QString& file_path, const QString& output_path, int quality = 85) const;
    QPixmap loadImageWithCache(const QString& file_path);
    void invalidateImageCache(const QString& file_path);
    void clearImageCache();

    // Cache management
    void setCacheSize(int max_cache_size_mb);
    int getCacheSize() const;
    QStringList getCachedImages() const;

signals:
    void imageLoaded(const QString& file_path, const QPixmap& pixmap);
    void imageCacheUpdated(const QString& file_path);
    void imageCacheCleared();
    void imageOptimized(const QString& file_path, qint64 original_size, qint64 optimized_size);

private:
    mutable QMutex cache_mutex_;
    QCache<QString, QPixmap> image_cache_;
    QStringList supported_formats_;
    int max_cache_size_mb_;
    
    // Performance tracking
    mutable QHash<QString, QString> file_hashes_;
    mutable QHash<QString, QDateTime> last_modified_times_;

    // Helper methods
    void initializeSupportedFormats();
    QJsonObject createImageMetadata(const QString& file_path) const;
    QString generateCacheKey(const QString& file_path) const;
    bool isImageModified(const QString& file_path) const;
    QByteArray imageToBase64(const QString& file_path) const;
    bool base64ToImage(const QByteArray& base64_data, const QString& output_path) const;
    QString detectImageFormat(const QString& file_path) const;
    bool isValidImageFormat(const QString& format) const;
    QPixmap loadImageFromFile(const QString& file_path) const;
    void updateFileTracking(const QString& file_path) const;
};

} // namespace DeclarativeUI::HotReload::FormatSupport
