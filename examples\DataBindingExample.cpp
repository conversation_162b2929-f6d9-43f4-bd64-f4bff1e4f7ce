#include "DataBindingExample.hpp"
#include <QApplication>
#include <QMessageBox>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>
#include <QTimer>

// DataBindingExample implementation

DataBindingExample::DataBindingExample(QWidget* parent) 
    : QWidget(parent), binding_engine_(&DeclarativeUI::DataBinding::DataBindingEngine::instance()) {
    
    setWindowTitle("Data Binding and Validation Example");
    setMinimumSize(800, 600);
    
    setupUI();
    setupDataBinding();
    setupValidation();
    setupFormBinding();
    
    // Connect to binding engine signals
    connect(binding_engine_, &DeclarativeUI::DataBinding::DataBindingEngine::bindingError,
            this, &DataBindingExample::onBindingError);
    
    qDebug() << "📋 DataBindingExample created";
}

void DataBindingExample::setupUI() {
    main_layout_ = new QVBoxLayout(this);
    main_splitter_ = new QSplitter(Qt::Horizontal, this);
    main_layout_->addWidget(main_splitter_);
    
    // Form section
    form_group_ = new QGroupBox("Employee Form", this);
    form_layout_ = new QFormLayout(form_group_);
    
    name_edit_ = new QLineEdit(this);
    name_edit_->setObjectName("name");
    name_edit_->setPlaceholderText("Enter full name");
    form_layout_->addRow("Name:", name_edit_);
    
    email_edit_ = new QLineEdit(this);
    email_edit_->setObjectName("email");
    email_edit_->setPlaceholderText("<EMAIL>");
    form_layout_->addRow("Email:", email_edit_);
    
    phone_edit_ = new QLineEdit(this);
    phone_edit_->setObjectName("phone");
    phone_edit_->setPlaceholderText("+1234567890");
    form_layout_->addRow("Phone:", phone_edit_);
    
    age_spin_ = new QSpinBox(this);
    age_spin_->setObjectName("age");
    age_spin_->setRange(18, 100);
    age_spin_->setValue(25);
    form_layout_->addRow("Age:", age_spin_);
    
    salary_spin_ = new QDoubleSpinBox(this);
    salary_spin_->setObjectName("salary");
    salary_spin_->setRange(0, 1000000);
    salary_spin_->setDecimals(2);
    salary_spin_->setSuffix(" USD");
    salary_spin_->setValue(50000.00);
    form_layout_->addRow("Salary:", salary_spin_);
    
    department_combo_ = new QComboBox(this);
    department_combo_->setObjectName("department");
    department_combo_->addItems({"Engineering", "Marketing", "Sales", "HR", "Finance"});
    form_layout_->addRow("Department:", department_combo_);
    
    active_check_ = new QCheckBox("Active Employee", this);
    active_check_->setObjectName("active");
    active_check_->setChecked(true);
    form_layout_->addRow(active_check_);
    
    start_date_edit_ = new QDateEdit(QDate::currentDate(), this);
    start_date_edit_->setObjectName("startDate");
    start_date_edit_->setCalendarPopup(true);
    form_layout_->addRow("Start Date:", start_date_edit_);
    
    notes_edit_ = new QTextEdit(this);
    notes_edit_->setObjectName("notes");
    notes_edit_->setPlaceholderText("Additional notes...");
    notes_edit_->setMaximumHeight(100);
    form_layout_->addRow("Notes:", notes_edit_);
    
    main_splitter_->addWidget(form_group_);
    
    // Right panel with validation and binding info
    auto* right_panel = new QWidget(this);
    auto* right_layout = new QVBoxLayout(right_panel);
    
    // Validation section
    validation_group_ = new QGroupBox("Validation", this);
    validation_layout_ = new QVBoxLayout(validation_group_);
    
    auto* button_layout = new QHBoxLayout();
    validate_button_ = new QPushButton("Validate Form", this);
    clear_button_ = new QPushButton("Clear Form", this);
    load_sample_button_ = new QPushButton("Load Sample", this);
    save_button_ = new QPushButton("Save Data", this);
    
    button_layout->addWidget(validate_button_);
    button_layout->addWidget(clear_button_);
    button_layout->addWidget(load_sample_button_);
    button_layout->addWidget(save_button_);
    validation_layout_->addLayout(button_layout);
    
    validation_status_label_ = new QLabel("Form Status: Ready", this);
    validation_layout_->addWidget(validation_status_label_);
    
    errors_list_ = new QListWidget(this);
    errors_list_->setMaximumHeight(150);
    validation_layout_->addWidget(errors_list_);
    
    right_layout->addWidget(validation_group_);
    
    // Binding info section
    binding_group_ = new QGroupBox("Data Binding", this);
    binding_layout_ = new QVBoxLayout(binding_group_);
    
    bound_widgets_label_ = new QLabel("Bound Widgets:", this);
    binding_layout_->addWidget(bound_widgets_label_);
    
    bound_widgets_list_ = new QListWidget(this);
    bound_widgets_list_->setMaximumHeight(100);
    binding_layout_->addWidget(bound_widgets_list_);
    
    form_completeness_bar_ = new QProgressBar(this);
    form_completeness_bar_->setRange(0, 100);
    form_completeness_bar_->setValue(0);
    binding_layout_->addWidget(new QLabel("Form Completeness:", this));
    binding_layout_->addWidget(form_completeness_bar_);
    
    // Salary slider for demonstration
    salary_slider_ = new QSlider(Qt::Horizontal, this);
    salary_slider_->setRange(0, 200000);
    salary_slider_->setValue(50000);
    salary_display_label_ = new QLabel("Salary: $50,000", this);
    binding_layout_->addWidget(new QLabel("Salary Control:", this));
    binding_layout_->addWidget(salary_slider_);
    binding_layout_->addWidget(salary_display_label_);
    
    right_layout->addWidget(binding_group_);
    right_layout->addStretch();
    
    main_splitter_->addWidget(right_panel);
    main_splitter_->setSizes({400, 400});
    
    // Connect button signals
    connect(validate_button_, &QPushButton::clicked, this, &DataBindingExample::onValidateForm);
    connect(clear_button_, &QPushButton::clicked, this, &DataBindingExample::onClearForm);
    connect(load_sample_button_, &QPushButton::clicked, this, &DataBindingExample::onLoadSampleData);
    connect(save_button_, &QPushButton::clicked, this, &DataBindingExample::onSaveData);
}

void DataBindingExample::setupDataBinding() {
    // Create form data
    form_data_ = DeclarativeUI::DataBinding::BindingUtils::createFormData();
    
    // Bind form to the data binding engine
    binding_engine_->bindForm(form_group_, form_data_);
    
    // Bind individual widgets with custom configurations
    DeclarativeUI::DataBinding::BindingConfig name_config("employee.name");
    name_config.immediateValidation();
    binding_engine_->bindWidget(name_edit_, name_config);
    
    DeclarativeUI::DataBinding::BindingConfig email_config("employee.email");
    email_config.immediateValidation();
    binding_engine_->bindWidget(email_edit_, email_config);
    
    DeclarativeUI::DataBinding::BindingConfig salary_config("employee.salary");
    salary_config.transformToWidget(DeclarativeUI::DataBinding::BindingUtils::createDoubleToStringTransformer())
                 .transformToState(DeclarativeUI::DataBinding::BindingUtils::createStringToDoubleTransformer());
    binding_engine_->bindWidget(salary_spin_, salary_config);
    
    // Bind salary slider with transformation
    DeclarativeUI::DataBinding::BindingConfig slider_config("employee.salary");
    slider_config.transformToWidget([](const QVariant& value) -> QVariant {
        return QVariant(static_cast<int>(value.toDouble()));
    }).transformToState([](const QVariant& value) -> QVariant {
        return QVariant(static_cast<double>(value.toInt()));
    });
    binding_engine_->bindWidget(salary_slider_, slider_config);
    
    // Connect salary changes to display label
    connect(salary_slider_, &QSlider::valueChanged, this, [this](int value) {
        salary_display_label_->setText(QString("Salary: $%1").arg(QLocale().toString(value)));
    });
    
    // Connect form data signals
    connect(form_data_.get(), &DeclarativeUI::DataBinding::FormData::fieldChanged,
            this, [this](const QString& field, const QVariant& value) {
        qDebug() << "Field changed:" << field << "=" << value;
        updateValidationStatus();
    });
    
    connect(form_data_.get(), &DeclarativeUI::DataBinding::FormData::validationChanged,
            this, &DataBindingExample::onFieldValidationChanged);
    
    connect(form_data_.get(), &DeclarativeUI::DataBinding::FormData::formValidationChanged,
            this, &DataBindingExample::onFormValidationChanged);
    
    // Update bound widgets list
    QTimer::singleShot(100, this, [this]() {
        QStringList bound_widgets = binding_engine_->getBoundWidgets();
        bound_widgets_list_->clear();
        bound_widgets_list_->addItems(bound_widgets);
    });
}

void DataBindingExample::setupValidation() {
    // Create validation engine
    validation_engine_ = std::make_shared<DeclarativeUI::DataBinding::ValidationEngine>();
    
    // Set up validation rules
    validation_engine_->addRule("name")
        .required()
        .minLength(2)
        .maxLength(50)
        .regex(R"(^[a-zA-Z\s]+$)", "Name must contain only letters and spaces");
    
    validation_engine_->addRule("email")
        .required()
        .email();
    
    validation_engine_->addRule("phone")
        .required()
        .regex(R"(^\+?[1-9]\d{1,14}$)", "Phone must be in international format");
    
    validation_engine_->addRule("age")
        .required()
        .range(18, 100);
    
    validation_engine_->addRule("salary")
        .required()
        .range(0, 1000000);
    
    validation_engine_->addRule("department")
        .required();
    
    validation_engine_->addRule("startDate")
        .required()
        .date(QDate(1900, 1, 1), QDate::currentDate().addYears(1));
    
    // Set validation engine for form data
    form_data_->setValidationEngine(validation_engine_);
    
    // Set validation engine for binding engine
    binding_engine_->setValidationEngine(validation_engine_);
    
    // Connect validation signals
    connect(validation_engine_.get(), &DeclarativeUI::DataBinding::ValidationEngine::errorAdded,
            this, [this](const QString& field, const QString& error) {
        qDebug() << "Validation error:" << field << "-" << error;
        showValidationErrors();
    });
    
    connect(validation_engine_.get(), &DeclarativeUI::DataBinding::ValidationEngine::errorCleared,
            this, [this](const QString& field) {
        qDebug() << "Validation error cleared:" << field;
        showValidationErrors();
    });
}

void DataBindingExample::setupFormBinding() {
    // Initialize field validation status
    field_validation_status_["name"] = false;
    field_validation_status_["email"] = false;
    field_validation_status_["phone"] = false;
    field_validation_status_["age"] = true;  // Has default value
    field_validation_status_["salary"] = true;  // Has default value
    field_validation_status_["department"] = true;  // Has default value
    field_validation_status_["startDate"] = true;  // Has default value
    
    updateValidationStatus();
}

// Slot implementations

void DataBindingExample::onValidateForm() {
    bool is_valid = form_data_->validateAll();

    if (is_valid) {
        QMessageBox::information(this, "Validation", "Form is valid!");
        validation_status_label_->setText("Form Status: ✅ Valid");
        validation_status_label_->setStyleSheet("color: green;");
    } else {
        QMessageBox::warning(this, "Validation", "Form has validation errors. Please check the fields.");
        validation_status_label_->setText("Form Status: ❌ Invalid");
        validation_status_label_->setStyleSheet("color: red;");
    }

    showValidationErrors();
}

void DataBindingExample::onClearForm() {
    form_data_->clear();

    // Reset form widgets to default values
    name_edit_->clear();
    email_edit_->clear();
    phone_edit_->clear();
    age_spin_->setValue(25);
    salary_spin_->setValue(50000.00);
    department_combo_->setCurrentIndex(0);
    active_check_->setChecked(true);
    start_date_edit_->setDate(QDate::currentDate());
    notes_edit_->clear();

    validation_status_label_->setText("Form Status: Cleared");
    validation_status_label_->setStyleSheet("");
    errors_list_->clear();
    form_completeness_bar_->setValue(0);
}

void DataBindingExample::onLoadSampleData() {
    // Load sample employee data
    std::unordered_map<QString, QVariant> sample_data = {
        {"name", "John Doe"},
        {"email", "<EMAIL>"},
        {"phone", "+1234567890"},
        {"age", 30},
        {"salary", 75000.00},
        {"department", 1},  // Engineering index
        {"active", true},
        {"startDate", QDate(2020, 1, 15)},
        {"notes", "Experienced software engineer with expertise in Qt development."}
    };

    form_data_->setAllData(sample_data);

    // Update UI widgets
    name_edit_->setText(sample_data["name"].toString());
    email_edit_->setText(sample_data["email"].toString());
    phone_edit_->setText(sample_data["phone"].toString());
    age_spin_->setValue(sample_data["age"].toInt());
    salary_spin_->setValue(sample_data["salary"].toDouble());
    department_combo_->setCurrentIndex(sample_data["department"].toInt());
    active_check_->setChecked(sample_data["active"].toBool());
    start_date_edit_->setDate(sample_data["startDate"].toDate());
    notes_edit_->setPlainText(sample_data["notes"].toString());

    validation_status_label_->setText("Form Status: Sample Data Loaded");
    validation_status_label_->setStyleSheet("color: blue;");
}

void DataBindingExample::onSaveData() {
    if (!form_data_->validateAll()) {
        QMessageBox::warning(this, "Save Error", "Cannot save invalid data. Please fix validation errors first.");
        return;
    }

    QJsonObject json = form_data_->toJson();
    QJsonDocument doc(json);

    QString json_string = doc.toJson(QJsonDocument::Indented);

    QMessageBox::information(this, "Save Data",
        QString("Data would be saved:\n\n%1").arg(json_string));

    qDebug() << "Form data JSON:" << json_string;
}

void DataBindingExample::onFieldValidationChanged(const QString& field, bool is_valid) {
    field_validation_status_[field] = is_valid;
    updateValidationStatus();

    qDebug() << "Field validation changed:" << field << "valid:" << is_valid;
}

void DataBindingExample::onFormValidationChanged(bool is_valid) {
    if (is_valid) {
        validation_status_label_->setText("Form Status: ✅ All Valid");
        validation_status_label_->setStyleSheet("color: green;");
    } else {
        validation_status_label_->setText("Form Status: ❌ Has Errors");
        validation_status_label_->setStyleSheet("color: red;");
    }

    qDebug() << "Form validation changed:" << is_valid;
}

void DataBindingExample::onBindingError(QWidget* widget, const QString& error) {
    QMessageBox::critical(this, "Binding Error",
        QString("Error with widget %1:\n%2").arg(widget->metaObject()->className()).arg(error));

    qWarning() << "Binding error for" << widget << ":" << error;
}

// Helper methods

void DataBindingExample::updateValidationStatus() {
    // Calculate form completeness
    int valid_fields = 0;
    int total_fields = static_cast<int>(field_validation_status_.size());

    for (const auto& [field, is_valid] : field_validation_status_) {
        if (is_valid) {
            valid_fields++;
        }
    }

    int completeness = (total_fields > 0) ? (valid_fields * 100 / total_fields) : 0;
    form_completeness_bar_->setValue(completeness);

    // Update status text
    QString status = QString("Valid Fields: %1/%2 (%3%)")
                        .arg(valid_fields)
                        .arg(total_fields)
                        .arg(completeness);

    bound_widgets_label_->setText(QString("Bound Widgets: %1").arg(status));
}

void DataBindingExample::showValidationErrors() {
    errors_list_->clear();

    if (validation_engine_) {
        auto errors = validation_engine_->getErrors();
        for (const auto& [field, error] : errors) {
            errors_list_->addItem(QString("%1: %2").arg(field).arg(error));
        }
    }

    if (errors_list_->count() == 0) {
        errors_list_->addItem("No validation errors");
    }
}

#include "DataBindingExample.moc"
