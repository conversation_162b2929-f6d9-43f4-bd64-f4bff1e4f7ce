#pragma once

#include <QObject>
#include <QWidget>
#include <QLayout>
#include <QBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QApplication>
#include <QStyleOption>
#include <QLocale>
#include <QString>
#include <QHash>
#include <QMutex>

namespace DeclarativeUI {
namespace I18n {

/**
 * @brief Comprehensive Right-to-Left (RTL) language support
 * 
 * Features:
 * - Automatic layout direction adjustment
 * - Text alignment correction
 * - Icon and image mirroring
 * - Margin and padding adjustments
 * - Custom RTL-aware styling
 * - Bidirectional text handling
 */
class RTLSupport : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Layout direction modes
     */
    enum class LayoutDirection {
        LeftToRight,
        RightToLeft,
        Auto  // Based on locale
    };

    /**
     * @brief Text alignment options for RTL
     */
    enum class TextAlignment {
        Leading,   // Left in LTR, Right in RTL
        Trailing,  // Right in LTR, Left in RTL
        Center,
        Left,      // Always left
        Right,     // Always right
        Justify
    };

    /**
     * @brief RTL configuration for widgets
     */
    struct RTLConfiguration {
        bool auto_mirror_layout = true;
        bool auto_mirror_text = true;
        bool auto_mirror_icons = true;
        bool auto_adjust_margins = true;
        bool preserve_tab_order = true;
        QHash<QString, QString> custom_styles;
        
        RTLConfiguration& mirrorLayout(bool enable) {
            auto_mirror_layout = enable;
            return *this;
        }
        
        RTLConfiguration& mirrorText(bool enable) {
            auto_mirror_text = enable;
            return *this;
        }
        
        RTLConfiguration& mirrorIcons(bool enable) {
            auto_mirror_icons = enable;
            return *this;
        }
        
        RTLConfiguration& adjustMargins(bool enable) {
            auto_adjust_margins = enable;
            return *this;
        }
    };

    /**
     * @brief Get singleton instance
     */
    static RTLSupport& instance();

    /**
     * @brief Set global layout direction
     */
    void setLayoutDirection(LayoutDirection direction);
    void setLayoutDirection(const QLocale& locale);

    /**
     * @brief Get current layout direction
     */
    LayoutDirection getLayoutDirection() const { return current_direction_; }
    bool isRightToLeft() const { return current_direction_ == LayoutDirection::RightToLeft; }

    /**
     * @brief Apply RTL support to widgets
     */
    void applyRTLSupport(QWidget* widget, const RTLConfiguration& config = RTLConfiguration());
    void applyRTLSupportRecursive(QWidget* widget, const RTLConfiguration& config = RTLConfiguration());

    /**
     * @brief Layout direction management
     */
    void setWidgetLayoutDirection(QWidget* widget, LayoutDirection direction);
    void mirrorLayout(QLayout* layout);
    void mirrorBoxLayout(QBoxLayout* layout);
    void mirrorGridLayout(QGridLayout* layout);
    void mirrorFormLayout(QFormLayout* layout);

    /**
     * @brief Text and alignment utilities
     */
    Qt::Alignment convertAlignment(Qt::Alignment alignment) const;
    Qt::Alignment getLeadingAlignment() const;
    Qt::Alignment getTrailingAlignment() const;
    TextAlignment convertTextAlignment(TextAlignment alignment) const;

    /**
     * @brief Margin and padding adjustments
     */
    void adjustMargins(QWidget* widget);
    void adjustContentsMargins(QLayout* layout);
    QMargins mirrorMargins(const QMargins& margins) const;

    /**
     * @brief Icon and image mirroring
     */
    QPixmap mirrorPixmap(const QPixmap& pixmap) const;
    QIcon mirrorIcon(const QIcon& icon) const;
    void applyIconMirroring(QWidget* widget);

    /**
     * @brief Style sheet utilities
     */
    QString convertStyleSheet(const QString& styleSheet) const;
    QString mirrorStyleProperty(const QString& property, const QString& value) const;

    /**
     * @brief Bidirectional text support
     */
    QString processBidirectionalText(const QString& text) const;
    bool containsRTLCharacters(const QString& text) const;
    bool containsLTRCharacters(const QString& text) const;

    /**
     * @brief Tab order management
     */
    void adjustTabOrder(QWidget* parent);
    void reverseTabOrder(const QList<QWidget*>& widgets);

    /**
     * @brief Utility methods
     */
    bool isRTLLocale(const QLocale& locale) const;
    QStringList getRTLLanguages() const;
    void enableGlobalRTLSupport(bool enable = true);

signals:
    /**
     * @brief Emitted when layout direction changes
     */
    void layoutDirectionChanged(LayoutDirection new_direction, LayoutDirection old_direction);

private:
    explicit RTLSupport(QObject* parent = nullptr);
    ~RTLSupport() override = default;

    // Helper methods
    void applyRTLToWidget(QWidget* widget, const RTLConfiguration& config);
    void processChildWidgets(QWidget* parent, const RTLConfiguration& config);
    QString convertCSSProperty(const QString& property) const;
    QString convertCSSValue(const QString& property, const QString& value) const;

    // Current state
    LayoutDirection current_direction_;
    bool global_rtl_enabled_;

    // Cached data
    mutable QHash<QString, QString> style_cache_;
    mutable QHash<QPixmap::cacheKey(), QPixmap> pixmap_cache_;
    mutable QMutex cache_mutex_;

    // RTL language detection
    QStringList rtl_languages_;

    static RTLSupport* instance_;
    static QMutex instance_mutex_;
};

/**
 * @brief RAII class for temporary RTL context
 */
class RTLContext {
public:
    explicit RTLContext(RTLSupport::LayoutDirection direction);
    ~RTLContext();

private:
    RTLSupport::LayoutDirection previous_direction_;
};

/**
 * @brief Convenience macros for RTL support
 */
#define DUI_RTL_WIDGET(widget) DeclarativeUI::I18n::RTLSupport::instance().applyRTLSupport(widget)
#define DUI_RTL_RECURSIVE(widget) DeclarativeUI::I18n::RTLSupport::instance().applyRTLSupportRecursive(widget)
#define DUI_RTL_ALIGNMENT(align) DeclarativeUI::I18n::RTLSupport::instance().convertAlignment(align)
#define DUI_RTL_MARGINS(margins) DeclarativeUI::I18n::RTLSupport::instance().mirrorMargins(margins)

/**
 * @brief RTL-aware layout direction property
 */
#define DUI_LAYOUT_DIRECTION(widget, direction) \
    DeclarativeUI::I18n::RTLSupport::instance().setWidgetLayoutDirection(widget, direction)

} // namespace I18n
} // namespace DeclarativeUI
