# Performance Profiling Tools

The DeclarativeUI Performance Profiling Tools provide comprehensive performance monitoring, analysis, and optimization capabilities for Qt applications. This advanced profiling system enables developers to identify bottlenecks, optimize performance, and ensure smooth user experiences.

## Table of Contents

1. [Overview](#overview)
2. [Core Components](#core-components)
3. [Getting Started](#getting-started)
4. [Performance Profiler](#performance-profiler)
5. [Render Profiler](#render-profiler)
6. [Memory Leak Detector](#memory-leak-detector)
7. [Performance Dashboard](#performance-dashboard)
8. [Bottleneck Detection](#bottleneck-detection)
9. [Optimization Recommendations](#optimization-recommendations)
10. [Report Generation](#report-generation)
11. [Best Practices](#best-practices)
12. [API Reference](#api-reference)

## Overview

The Performance Profiling Tools offer:

- **Real-time Performance Monitoring**: Track CPU, memory, render, and I/O performance
- **Bottleneck Detection**: Automatically identify performance issues
- **Optimization Recommendations**: Get actionable suggestions for improvements
- **Memory Leak Detection**: Find and analyze memory leaks
- **Render Performance Analysis**: Optimize UI rendering and frame rates
- **Interactive Dashboard**: Visualize performance data in real-time
- **Comprehensive Reporting**: Generate detailed performance reports

## Core Components

### PerformanceProfiler
The main profiling engine that collects and analyzes performance metrics across multiple dimensions.

### RenderProfiler
Specialized profiler for analyzing UI rendering performance, frame rates, and paint operations.

### MemoryLeakDetector
Advanced memory tracking system for detecting and analyzing memory leaks.

### PerformanceDashboard
Interactive dashboard for real-time performance monitoring and visualization.

## Getting Started

### Basic Setup

```cpp
#include "Profiling/PerformanceProfiler.hpp"
#include "Profiling/PerformanceDashboard.hpp"

using namespace DeclarativeUI::Profiling;

// Create profiler instance
auto profiler = std::make_shared<PerformanceProfiler>();

// Configure profiling
PerformanceProfiler::ProfilerConfig config;
config.enable_cpu_profiling = true;
config.enable_memory_profiling = true;
config.enable_render_profiling = true;
config.sampling_interval_ms = 100;
profiler->setConfiguration(config);

// Start profiling
profiler->startProfiling();
```

### Dashboard Integration

```cpp
// Create and show performance dashboard
auto dashboard = std::make_unique<PerformanceDashboard>();
dashboard->setProfiler(profiler);
dashboard->startMonitoring();
dashboard->show();
```

## Performance Profiler

### Configuration Options

```cpp
PerformanceProfiler::ProfilerConfig config;

// Enable/disable profiling categories
config.enable_cpu_profiling = true;
config.enable_memory_profiling = true;
config.enable_render_profiling = true;
config.enable_io_profiling = true;
config.enable_thread_profiling = true;
config.enable_gpu_profiling = false;

// Sampling configuration
config.sampling_interval_ms = 100;  // Sample every 100ms
config.max_history_size = 10000;    // Keep 10,000 samples

// Performance thresholds
config.cpu_warning_threshold = 80.0;           // 80% CPU usage
config.memory_warning_threshold_mb = 512;      // 512 MB memory usage
config.frame_rate_warning_threshold = 30.0;    // 30 FPS

// Auto-reporting
config.auto_save_reports = true;
config.reports_directory = "profiling_reports";
```

### Collecting Metrics

```cpp
// Take a performance snapshot
auto snapshot = profiler->takeSnapshot();

// Get specific metrics
auto cpu_metrics = profiler->getCurrentCPUMetrics();
auto memory_metrics = profiler->getCurrentMemoryMetrics();
auto render_metrics = profiler->getCurrentRenderMetrics();

// Access historical data
auto history = profiler->getHistory();
auto filtered_history = profiler->getHistory(start_time, end_time);
```

### Event Handling

```cpp
// Connect to profiler signals
connect(profiler.get(), &PerformanceProfiler::snapshotTaken,
        [](const PerformanceSnapshot& snapshot) {
    qDebug() << "CPU Usage:" << snapshot.cpu.usage_percentage << "%";
    qDebug() << "Memory Usage:" << snapshot.memory.used_memory_kb / 1024 << "MB";
});

connect(profiler.get(), &PerformanceProfiler::bottleneckDetected,
        [](const BottleneckInfo& bottleneck) {
    qWarning() << "Bottleneck detected:" << bottleneck.description;
    qDebug() << "Severity:" << bottleneck.severity;
    qDebug() << "Recommendations:" << bottleneck.recommendations;
});

connect(profiler.get(), &PerformanceProfiler::performanceWarning,
        [](const QString& message, const QJsonObject& details) {
    qWarning() << "Performance warning:" << message;
    qDebug() << "Details:" << details;
});
```

## Render Profiler

### Widget Profiling

```cpp
auto render_profiler = std::make_shared<RenderProfiler>();

// Start profiling a specific widget
render_profiler->startProfiling(my_widget);

// Profile a single frame
render_profiler->profileSingleFrame(my_widget);

// Get render statistics
auto stats = render_profiler->getStatistics(my_widget);
qDebug() << "Average FPS:" << stats.average_fps;
qDebug() << "Frame time variance:" << stats.frame_time_variance;

// Generate render report
auto report = render_profiler->generateRenderReport(my_widget);
```

### Render Analysis

```cpp
// Detect render issues
auto issues = render_profiler->detectRenderIssues(my_widget);
for (const QString& issue : issues) {
    qWarning() << "Render issue:" << issue;
}

// Analyze frame drops
auto frame_analysis = render_profiler->analyzeFrameDrops(my_widget);
qDebug() << "Drop rate:" << frame_analysis["drop_rate_percentage"].toDouble() << "%";

// Get optimization suggestions
auto suggestions = render_profiler->generateOptimizationSuggestions(my_widget);
```

## Memory Leak Detector

### Basic Usage

```cpp
auto& leak_detector = MemoryLeakDetector::instance();

// Enable memory tracking
leak_detector.enableTracking(true);

// Track allocations (usually done automatically)
void* ptr = malloc(1024);
leak_detector.trackAllocation(ptr, 1024, __FILE__, __LINE__);

// Track deallocations
free(ptr);
leak_detector.trackDeallocation(ptr);

// Detect leaks
auto leak_report = leak_detector.detectLeaks();
qDebug() << "Total leaked bytes:" << leak_report.total_leaked_bytes;
qDebug() << "Leak count:" << leak_report.leak_count;
```

### Leak Analysis

```cpp
// Get active allocations
auto allocations = leak_detector.getActiveAllocations();
for (const auto& allocation : allocations) {
    qDebug() << "Address:" << allocation.address;
    qDebug() << "Size:" << allocation.size;
    qDebug() << "Source:" << allocation.source_file << ":" << allocation.source_line;
    qDebug() << "Allocated at:" << allocation.allocation_time;
}

// Generate leak report
auto report = leak_detector.generateLeakReport();

// Export leak report
leak_detector.exportLeakReport("leak_report.json");
```

## Performance Dashboard

### Dashboard Features

The Performance Dashboard provides:

- **Real-time Charts**: CPU, memory, render, and I/O performance graphs
- **Metrics Overview**: Current performance statistics
- **Bottleneck Timeline**: Visual representation of detected bottlenecks
- **Optimization Panel**: Actionable recommendations
- **Configuration**: Profiling settings and thresholds
- **Report Generation**: Export performance data

### Dashboard Usage

```cpp
auto dashboard = std::make_unique<PerformanceDashboard>();

// Configure dashboard
dashboard->setUpdateInterval(1000);  // Update every second
dashboard->setHistorySize(1000);     // Keep 1000 data points
dashboard->setThresholds(80.0, 512, 30.0);  // CPU, Memory, FPS thresholds

// Set profiler
dashboard->setProfiler(profiler);

// Start monitoring
dashboard->startMonitoring();
dashboard->show();
```

## Bottleneck Detection

### Automatic Detection

The profiler automatically detects various types of bottlenecks:

- **CPU Bottlenecks**: High CPU usage, excessive processing
- **Memory Bottlenecks**: Memory pressure, allocation patterns
- **Render Bottlenecks**: Low frame rates, expensive paint operations
- **I/O Bottlenecks**: Slow file operations, network delays
- **Thread Bottlenecks**: Thread contention, synchronization issues

### Custom Bottleneck Detection

```cpp
// Detect bottlenecks manually
auto bottlenecks = profiler->detectBottlenecks();
for (const auto& bottleneck : bottlenecks) {
    qDebug() << "Type:" << static_cast<int>(bottleneck.type);
    qDebug() << "Description:" << bottleneck.description;
    qDebug() << "Component:" << bottleneck.component;
    qDebug() << "Severity:" << bottleneck.severity;
    qDebug() << "Duration:" << bottleneck.duration_ms << "ms";
    
    for (const QString& recommendation : bottleneck.recommendations) {
        qDebug() << "Recommendation:" << recommendation;
    }
}
```

## Optimization Recommendations

### Automatic Recommendations

```cpp
// Generate optimization recommendations
auto recommendations = profiler->generateOptimizationRecommendations();
for (const auto& rec : recommendations) {
    qDebug() << "Priority:" << static_cast<int>(rec.priority);
    qDebug() << "Category:" << rec.category;
    qDebug() << "Title:" << rec.title;
    qDebug() << "Description:" << rec.description;
    qDebug() << "Estimated improvement:" << rec.estimated_improvement;
    
    for (const QString& action : rec.action_items) {
        qDebug() << "Action:" << action;
    }
}
```

### Recommendation Categories

- **CPU Optimization**: Algorithm improvements, caching strategies
- **Memory Optimization**: Memory pool usage, object reuse
- **Render Optimization**: Widget caching, paint optimization
- **I/O Optimization**: Asynchronous operations, batching
- **Thread Optimization**: Work distribution, synchronization

## Report Generation

### Export Formats

```cpp
// Export as JSON
profiler->exportReport("performance_report.json", "json");

// Export as CSV
profiler->exportCSV("performance_data.csv");

// Export as HTML
profiler->exportHTML("performance_report.html");

// Export flame graph
profiler->exportFlameGraph("performance_flame.svg");
```

### Custom Reports

```cpp
// Generate custom performance report
auto report = profiler->generatePerformanceReport();

// Generate trend analysis
auto trends = profiler->generateTrendAnalysis();

// Generate comparison report
auto comparison = profiler->generateComparisonReport(baseline_start, baseline_end);
```

## Best Practices

### 1. Profiling Strategy

- **Profile Early**: Start profiling during development
- **Profile Continuously**: Monitor performance in production
- **Profile Realistically**: Use realistic data and scenarios
- **Profile Incrementally**: Focus on one area at a time

### 2. Performance Optimization

- **Measure First**: Always measure before optimizing
- **Focus on Bottlenecks**: Address the most significant issues first
- **Validate Changes**: Measure the impact of optimizations
- **Monitor Regressions**: Watch for performance degradation

### 3. Memory Management

- **Track Allocations**: Monitor memory usage patterns
- **Detect Leaks Early**: Run leak detection regularly
- **Optimize Allocation**: Use object pools and caching
- **Profile Memory Access**: Optimize data locality

### 4. Render Performance

- **Minimize Repaints**: Reduce unnecessary widget updates
- **Optimize Paint Events**: Keep paint operations lightweight
- **Use Caching**: Cache expensive rendering operations
- **Profile Frame Rates**: Maintain consistent frame rates

## API Reference

### PerformanceProfiler Class

```cpp
class PerformanceProfiler : public QObject {
public:
    // Configuration
    void setConfiguration(const ProfilerConfig& config);
    ProfilerConfig getConfiguration() const;
    
    // Profiling control
    void startProfiling();
    void stopProfiling();
    void pauseProfiling();
    void resumeProfiling();
    
    // Data collection
    PerformanceSnapshot takeSnapshot();
    std::vector<PerformanceSnapshot> getHistory() const;
    
    // Analysis
    std::vector<BottleneckInfo> detectBottlenecks();
    std::vector<OptimizationRecommendation> generateOptimizationRecommendations();
    
    // Custom metrics
    void recordCustomMetric(const QString& name, double value);
    void startCustomTimer(const QString& name);
    void endCustomTimer(const QString& name);
    
    // Widget profiling
    void profileWidget(QWidget* widget);
    void profileRenderPerformance(QWidget* widget, int frame_count = 60);
    
    // Memory tracking
    void enableMemoryTracking(bool enabled);
    void trackAllocation(void* ptr, size_t size);
    void trackDeallocation(void* ptr);
    
    // Export and reporting
    void exportReport(const QString& file_path, const QString& format = "json");
    void exportCSV(const QString& file_path);
    void exportHTML(const QString& file_path);
    
signals:
    void snapshotTaken(const PerformanceSnapshot& snapshot);
    void bottleneckDetected(const BottleneckInfo& bottleneck);
    void performanceWarning(const QString& message, const QJsonObject& details);
    void optimizationRecommendation(const OptimizationRecommendation& recommendation);
};
```

For complete API documentation, see the header files in `src/Profiling/`.

## Conclusion

The Performance Profiling Tools provide a comprehensive solution for monitoring, analyzing, and optimizing Qt application performance. By leveraging these tools, developers can ensure their applications deliver smooth, responsive user experiences while efficiently utilizing system resources.

For examples and demonstrations, see the `examples/PerformanceProfilingExample` application.
