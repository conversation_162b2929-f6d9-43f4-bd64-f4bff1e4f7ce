#pragma once

#include <QObject>
#include <QWidget>
#include <QApplication>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QTimer>
#include <QMutex>
#include <QReadWriteLock>
#include <QFileSystemWatcher>
#include <QElapsedTimer>

#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <functional>
#include <atomic>

#include "HotReloadManager.hpp"
#include "AdvancedFileFilter.hpp"
#include "FormatSupport/CSSProcessor.hpp"
#include "FormatSupport/IFormatProcessor.hpp"
#include "../JSON/JSONUILoader.hpp"

namespace DeclarativeUI::HotReload {

/**
 * @brief Types of selective reload operations
 */
enum class SelectiveReloadType {
    Component,      // Reload specific UI component by ID
    Stylesheet,     // Reload CSS/stylesheet changes
    Resource,       // Reload images, icons, or other resources
    Property,       // Reload specific property changes
    Layout,         // Reload layout configuration
    Event,          // Reload event handlers
    State,          // Reload state bindings
    Theme,          // Reload theme configuration
    Translation     // Reload translation/localization
};

/**
 * @brief Selective reload target specification
 */
struct SelectiveReloadTarget {
    SelectiveReloadType type;
    QString target_id;              // Component ID, CSS selector, resource name, etc.
    QString file_path;              // Source file path
    QString scope;                  // Scope: "global", "widget", "component"
    QJsonObject metadata;           // Additional target-specific metadata
    QStringList dependencies;       // List of dependent targets
    int priority = 0;               // Reload priority (higher = first)
    bool enabled = true;            // Whether this target is enabled
    
    // Comparison operators for sorting and deduplication
    bool operator==(const SelectiveReloadTarget& other) const {
        return type == other.type && target_id == other.target_id && 
               file_path == other.file_path && scope == other.scope;
    }
    
    bool operator<(const SelectiveReloadTarget& other) const {
        if (priority != other.priority) return priority > other.priority;
        if (type != other.type) return static_cast<int>(type) < static_cast<int>(other.type);
        if (target_id != other.target_id) return target_id < other.target_id;
        return file_path < other.file_path;
    }
};

/**
 * @brief Component tracking information for selective reloading
 */
struct ComponentTrackingInfo {
    QString component_id;           // Unique component identifier
    QString component_type;         // Component type (QLabel, QPushButton, etc.)
    QWidget* widget_instance;       // Pointer to actual widget instance
    QWidget* parent_widget;         // Parent widget for hierarchy tracking
    QString source_file;            // Source file that defines this component
    QJsonObject original_config;    // Original JSON configuration
    QJsonObject current_config;     // Current configuration (may differ from original)
    QDateTime last_updated;         // Last update timestamp
    QStringList bound_properties;   // List of bound properties
    QStringList event_handlers;     // List of registered event handlers
    QString stylesheet_id;          // Associated stylesheet identifier
    QStringList resource_dependencies; // Dependent resources (images, etc.)
    bool is_dynamic = false;        // Whether component was created dynamically
    int reload_count = 0;           // Number of times this component has been reloaded
};

/**
 * @brief Stylesheet tracking information
 */
struct StylesheetTrackingInfo {
    QString stylesheet_id;          // Unique stylesheet identifier
    QString file_path;              // Source CSS file path
    QString css_content;            // Current CSS content
    QString compiled_content;       // Compiled/processed CSS content
    QStringList target_selectors;   // CSS selectors this stylesheet affects
    QStringList affected_components; // Component IDs affected by this stylesheet
    QWidget* target_widget;         // Target widget (if scoped to specific widget)
    QString injection_scope;        // "global", "application", "widget", "component"
    QDateTime last_modified;        // Last modification time
    bool is_injected = false;       // Whether stylesheet is currently injected
    QJsonObject processing_config;  // CSS processing configuration
};

/**
 * @brief Resource tracking information
 */
struct ResourceTrackingInfo {
    QString resource_id;            // Unique resource identifier
    QString file_path;              // Resource file path
    QString resource_type;          // Type: "image", "icon", "font", "data"
    QStringList dependent_components; // Components that use this resource
    QDateTime last_modified;        // Last modification time
    qint64 file_size;              // File size for change detection
    QString checksum;              // File checksum for integrity verification
    QJsonObject metadata;          // Resource-specific metadata
    bool is_cached = false;        // Whether resource is cached
};

/**
 * @brief Selective reload operation result
 */
struct SelectiveReloadResult {
    bool success = false;
    QString target_id;
    SelectiveReloadType type;
    QString error_message;
    qint64 duration_ms = 0;
    QStringList affected_components;
    QJsonObject metadata;
    
    static SelectiveReloadResult createSuccess(const QString& id, SelectiveReloadType type, qint64 duration = 0) {
        SelectiveReloadResult result;
        result.success = true;
        result.target_id = id;
        result.type = type;
        result.duration_ms = duration;
        return result;
    }
    
    static SelectiveReloadResult createError(const QString& id, SelectiveReloadType type, const QString& error) {
        SelectiveReloadResult result;
        result.success = false;
        result.target_id = id;
        result.type = type;
        result.error_message = error;
        return result;
    }
};

/**
 * @brief Configuration for selective reload behavior
 */
struct SelectiveReloadConfig {
    bool enabled = true;
    bool auto_detect_changes = true;
    bool cascade_dependencies = true;
    bool validate_before_reload = true;
    bool create_rollback_points = true;
    bool enable_performance_monitoring = true;
    
    int max_concurrent_reloads = 5;
    int reload_debounce_ms = 100;
    int dependency_resolution_timeout_ms = 5000;
    
    QStringList enabled_reload_types = {
        "Component", "Stylesheet", "Resource", "Property", "Layout", "Event", "State", "Theme"
    };
    
    QJsonObject type_specific_config;
};

/**
 * @brief Comprehensive Selective Reload Manager
 * 
 * Provides fine-grained hot-reloading capabilities for specific UI components,
 * stylesheets, resources, and other application elements without requiring
 * full application or file reloads.
 * 
 * Key Features:
 * - Component-level reloading by ID or type
 * - Live CSS injection and stylesheet updates
 * - Resource hot-swapping (images, icons, fonts)
 * - Property-level updates
 * - Layout reconfiguration
 * - Event handler updates
 * - State binding updates
 * - Theme switching
 * - Dependency tracking and cascading updates
 * - Performance monitoring and optimization
 * - Rollback and error recovery
 */
class SelectiveReloadManager : public QObject {
    Q_OBJECT

public:
    explicit SelectiveReloadManager(QObject* parent = nullptr);
    ~SelectiveReloadManager() override;

    // **Configuration and setup**
    void setConfiguration(const SelectiveReloadConfig& config);
    SelectiveReloadConfig getConfiguration() const;
    void setHotReloadManager(HotReloadManager* manager);
    void setAdvancedFilter(AdvancedFileFilter* filter);
    void setUILoader(JSON::JSONUILoader* loader);

    // **Component tracking and registration**
    void registerComponent(const QString& component_id, QWidget* widget, const QJsonObject& config, const QString& source_file);
    void unregisterComponent(const QString& component_id);
    void updateComponentConfig(const QString& component_id, const QJsonObject& new_config);
    ComponentTrackingInfo getComponentInfo(const QString& component_id) const;
    QStringList getRegisteredComponents() const;

    // **Stylesheet management**
    void registerStylesheet(const QString& stylesheet_id, const QString& file_path, const QString& scope = "application");
    void unregisterStylesheet(const QString& stylesheet_id);
    void updateStylesheet(const QString& stylesheet_id, const QString& css_content);
    StylesheetTrackingInfo getStylesheetInfo(const QString& stylesheet_id) const;
    QStringList getRegisteredStylesheets() const;

    // **Resource management**
    void registerResource(const QString& resource_id, const QString& file_path, const QString& type);
    void unregisterResource(const QString& resource_id);
    void updateResource(const QString& resource_id);
    ResourceTrackingInfo getResourceInfo(const QString& resource_id) const;
    QStringList getRegisteredResources() const;

    // **Selective reload operations**
    SelectiveReloadResult reloadComponent(const QString& component_id);
    SelectiveReloadResult reloadComponentProperty(const QString& component_id, const QString& property_name, const QVariant& new_value);
    SelectiveReloadResult reloadStylesheet(const QString& stylesheet_id);
    SelectiveReloadResult reloadResource(const QString& resource_id);
    SelectiveReloadResult reloadComponentsByType(const QString& component_type);
    SelectiveReloadResult reloadComponentsBySelector(const QString& css_selector);

    // **Batch operations**
    QList<SelectiveReloadResult> reloadTargets(const QList<SelectiveReloadTarget>& targets);
    QList<SelectiveReloadResult> reloadDependencies(const QString& target_id);
    QList<SelectiveReloadResult> reloadAffectedComponents(const QString& file_path);

    // **Auto-detection and monitoring**
    void enableAutoDetection(bool enabled);
    void startMonitoring();
    void stopMonitoring();
    void pauseMonitoring();

    // **Dependency management**
    void addDependency(const QString& source_id, const QString& dependent_id);
    void removeDependency(const QString& source_id, const QString& dependent_id);
    QStringList getDependencies(const QString& target_id) const;
    QStringList getDependents(const QString& target_id) const;

    // **Rollback and recovery**
    void createRollbackPoint(const QString& target_id);
    bool rollbackTarget(const QString& target_id);
    void clearRollbackPoints();

    // **Performance and statistics**
    QJsonObject getPerformanceMetrics() const;
    QJsonObject getReloadStatistics() const;
    void resetStatistics();

    // **Validation and diagnostics**
    bool validateTarget(const SelectiveReloadTarget& target) const;
    QStringList validateAllTargets() const;
    QJsonObject generateDiagnosticsReport() const;

signals:
    void componentReloaded(const QString& component_id, bool success);
    void stylesheetReloaded(const QString& stylesheet_id, bool success);
    void resourceReloaded(const QString& resource_id, bool success);
    void reloadStarted(const QString& target_id, SelectiveReloadType type);
    void reloadCompleted(const QString& target_id, SelectiveReloadType type, qint64 duration_ms);
    void reloadFailed(const QString& target_id, SelectiveReloadType type, const QString& error);
    void dependencyReloadTriggered(const QString& source_id, const QStringList& dependent_ids);
    void rollbackPerformed(const QString& target_id, bool success);
    void monitoringStateChanged(bool enabled);
    void configurationChanged();

public slots:
    void onFileChanged(const QString& file_path);
    void onComponentPropertyChanged(const QString& component_id, const QString& property_name);
    void onStylesheetChanged(const QString& stylesheet_id);
    void onResourceChanged(const QString& resource_id);

private slots:
    void processReloadQueue();
    void performScheduledReloads();
    void updatePerformanceMetrics();

private:
    // **Core data structures**
    std::unordered_map<QString, ComponentTrackingInfo> tracked_components_;
    std::unordered_map<QString, StylesheetTrackingInfo> tracked_stylesheets_;
    std::unordered_map<QString, ResourceTrackingInfo> tracked_resources_;
    std::unordered_map<QString, QStringList> dependency_graph_;
    std::unordered_map<QString, QJsonObject> rollback_points_;

    // **Configuration and state**
    SelectiveReloadConfig config_;
    std::atomic<bool> monitoring_enabled_{false};
    std::atomic<bool> auto_detection_enabled_{true};

    // **External dependencies**
    HotReloadManager* hot_reload_manager_ = nullptr;
    AdvancedFileFilter* advanced_filter_ = nullptr;
    JSON::JSONUILoader* ui_loader_ = nullptr;
    std::unique_ptr<FormatSupport::CSSProcessor> css_processor_;

    // **Processing and scheduling**
    std::unique_ptr<QTimer> reload_timer_;
    std::unique_ptr<QTimer> metrics_timer_;
    std::vector<SelectiveReloadTarget> reload_queue_;
    QMutex queue_mutex_;

    // **Thread safety**
    mutable QReadWriteLock components_lock_;
    mutable QReadWriteLock stylesheets_lock_;
    mutable QReadWriteLock resources_lock_;
    mutable QMutex dependency_lock_;

    // **Performance tracking**
    QElapsedTimer session_timer_;
    std::unordered_map<QString, qint64> reload_times_;
    std::unordered_map<SelectiveReloadType, int> reload_counts_;
    std::unordered_map<QString, int> error_counts_;

    // **Implementation methods**
    void setupCSSProcessor();
    void setupTimers();
    void connectSignals();

    // **Reload implementation**
    SelectiveReloadResult performComponentReload(const QString& component_id);
    SelectiveReloadResult performStylesheetReload(const QString& stylesheet_id);
    SelectiveReloadResult performResourceReload(const QString& resource_id);
    SelectiveReloadResult performPropertyReload(const QString& component_id, const QString& property_name, const QVariant& value);

    // **Dependency resolution**
    QStringList resolveDependencies(const QString& target_id) const;
    QList<SelectiveReloadTarget> buildReloadPlan(const QList<SelectiveReloadTarget>& targets) const;
    void cascadeReload(const QString& source_id);

    // **Validation and safety**
    bool isValidComponent(const QString& component_id) const;
    bool isValidStylesheet(const QString& stylesheet_id) const;
    bool isValidResource(const QString& resource_id) const;
    bool canReloadSafely(const SelectiveReloadTarget& target) const;

    // **File monitoring and change detection**
    void detectChangesInFile(const QString& file_path);
    QList<SelectiveReloadTarget> analyzeFileChanges(const QString& file_path, const QJsonObject& old_content, const QJsonObject& new_content) const;
    bool hasComponentChanged(const QJsonObject& old_config, const QJsonObject& new_config) const;

    // **Utility methods**
    QString generateUniqueId(const QString& prefix = "target") const;
    QWidget* findWidgetById(const QString& component_id, QWidget* root = nullptr) const;
    QStringList extractComponentIds(const QJsonObject& ui_definition) const;
    QJsonObject loadJsonFromFile(const QString& file_path) const;
    void updateComponentWidget(QWidget* widget, const QJsonObject& config);
    void applyStylesheetToWidget(QWidget* widget, const QString& css_content, const QString& stylesheet_id);
    void updateResourceInWidget(QWidget* widget, const QString& resource_id, const QString& new_path);

    // **Error handling and recovery**
    void handleReloadError(const QString& target_id, SelectiveReloadType type, const QString& error);
    void attemptErrorRecovery(const QString& target_id);
    void logReloadOperation(const QString& target_id, SelectiveReloadType type, bool success, qint64 duration_ms);
};

} // namespace DeclarativeUI::HotReload

// Hash function for SelectiveReloadTarget to enable use in std::unordered_set
namespace std {
template<>
struct hash<DeclarativeUI::HotReload::SelectiveReloadTarget> {
    size_t operator()(const DeclarativeUI::HotReload::SelectiveReloadTarget& target) const {
        return hash<QString>()(target.target_id) ^
               hash<QString>()(target.file_path) ^
               hash<int>()(static_cast<int>(target.type));
    }
};
}
