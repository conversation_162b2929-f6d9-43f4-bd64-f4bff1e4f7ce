name: 🚀 Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

env:
  QT_VERSION: 6.7.2

jobs:
  # =============================================================================
  # Create Release
  # =============================================================================
  create-release:
    name: 📦 Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      version: ${{ steps.version.outputs.version }}
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: 🏷️ Get Version
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
        else
          echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
        fi

    - name: 📋 Generate Release Notes
      id: release_notes
      run: |
        # Generate release notes from commits since last tag
        LAST_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
        if [ -n "$LAST_TAG" ]; then
          echo "## 🎉 What's New" > release_notes.md
          echo "" >> release_notes.md
          git log --pretty=format:"- %s (%h)" $LAST_TAG..HEAD >> release_notes.md
        else
          echo "## 🎉 Initial Release" > release_notes.md
          echo "" >> release_notes.md
          echo "First release of DeclarativeUI Framework!" >> release_notes.md
        fi
        
        echo "" >> release_notes.md
        echo "## 📋 Features" >> release_notes.md
        echo "- Modern C++20 declarative UI framework for Qt6" >> release_notes.md
        echo "- 30+ pre-built UI components" >> release_notes.md
        echo "- Advanced Command-based UI system" >> release_notes.md
        echo "- Hot reload functionality" >> release_notes.md
        echo "- Comprehensive state management" >> release_notes.md
        echo "- JSON UI loading support" >> release_notes.md
        echo "" >> release_notes.md
        echo "## 📚 Documentation" >> release_notes.md
        echo "- [Getting Started Guide](https://github.com/${{ github.repository }}#-quick-start)" >> release_notes.md
        echo "- [API Documentation](https://github.com/${{ github.repository }}/tree/main/docs/api)" >> release_notes.md
        echo "- [Examples](https://github.com/${{ github.repository }}/tree/main/examples)" >> release_notes.md

    - name: 🚀 Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ steps.version.outputs.version }}
        release_name: DeclarativeUI Framework ${{ steps.version.outputs.version }}
        body_path: release_notes.md
        draft: false
        prerelease: ${{ contains(steps.version.outputs.version, 'alpha') || contains(steps.version.outputs.version, 'beta') || contains(steps.version.outputs.version, 'rc') }}

  # =============================================================================
  # Build Release Artifacts
  # =============================================================================
  build-artifacts:
    name: 🏗️ Build ${{ matrix.name }}
    needs: create-release
    strategy:
      matrix:
        include:
          - os: windows-latest
            name: Windows
            artifact: DeclarativeUI-Windows-x64
            qt-arch: win64_msvc2019_64
            
          - os: ubuntu-latest
            name: Linux
            artifact: DeclarativeUI-Linux-x64
            qt-arch: gcc_64
            
          - os: macos-latest
            name: macOS
            artifact: DeclarativeUI-macOS-x64
            qt-arch: clang_64

    runs-on: ${{ matrix.os }}
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 🔧 Setup Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: ${{ env.QT_VERSION }}
        arch: ${{ matrix.qt-arch }}
        cache: true

    - name: 🔧 Setup MSVC (Windows)
      if: matrix.os == 'windows-latest'
      uses: ilammy/msvc-dev-cmd@v1

    - name: 🏗️ Configure and Build
      run: |
        cmake --preset=release -B build \
          -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_EXAMPLES=ON \
          -DBUILD_TESTS=ON \
          -DBUILD_COMMAND_SYSTEM=ON
        cmake --build build --config Release --parallel

    - name: 🧪 Run Tests
      working-directory: build
      run: ctest --config Release --output-on-failure

    - name: 📦 Package
      run: |
        cmake --build build --target package
        
    - name: 📁 Prepare Artifacts
      shell: bash
      run: |
        mkdir -p artifacts
        
        # Copy built libraries and executables
        if [ "${{ matrix.os }}" = "windows-latest" ]; then
          cp build/Release/*.lib artifacts/ 2>/dev/null || true
          cp build/Release/*.dll artifacts/ 2>/dev/null || true
          cp -r build/examples/Release artifacts/examples 2>/dev/null || true
        else
          cp build/*.a artifacts/ 2>/dev/null || true
          cp build/*.so artifacts/ 2>/dev/null || true
          cp -r build/examples artifacts/ 2>/dev/null || true
        fi
        
        # Copy headers
        cp -r src artifacts/include
        
        # Copy documentation
        cp -r docs artifacts/
        cp README.md LICENSE CONTRIBUTING.md artifacts/
        
        # Create archive
        if [ "${{ matrix.os }}" = "windows-latest" ]; then
          7z a ${{ matrix.artifact }}.zip artifacts/*
        else
          tar -czf ${{ matrix.artifact }}.tar.gz -C artifacts .
        fi

    - name: 📤 Upload Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ${{ matrix.artifact }}.${{ matrix.os == 'windows-latest' && 'zip' || 'tar.gz' }}
        asset_name: ${{ matrix.artifact }}.${{ matrix.os == 'windows-latest' && 'zip' || 'tar.gz' }}
        asset_content_type: application/octet-stream

  # =============================================================================
  # Source Distribution
  # =============================================================================
  source-distribution:
    name: 📦 Source Distribution
    needs: create-release
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 📦 Create Source Archive
      run: |
        # Create clean source distribution
        git archive --format=tar.gz --prefix=DeclarativeUI-${{ needs.create-release.outputs.version }}/ HEAD > DeclarativeUI-${{ needs.create-release.outputs.version }}-src.tar.gz
        
        # Create zip version for Windows users
        git archive --format=zip --prefix=DeclarativeUI-${{ needs.create-release.outputs.version }}/ HEAD > DeclarativeUI-${{ needs.create-release.outputs.version }}-src.zip

    - name: 📤 Upload Source Tar.gz
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: DeclarativeUI-${{ needs.create-release.outputs.version }}-src.tar.gz
        asset_name: DeclarativeUI-${{ needs.create-release.outputs.version }}-src.tar.gz
        asset_content_type: application/gzip

    - name: 📤 Upload Source Zip
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: DeclarativeUI-${{ needs.create-release.outputs.version }}-src.zip
        asset_name: DeclarativeUI-${{ needs.create-release.outputs.version }}-src.zip
        asset_content_type: application/zip

  # =============================================================================
  # Post-Release Tasks
  # =============================================================================
  post-release:
    name: 📢 Post-Release
    needs: [create-release, build-artifacts, source-distribution]
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4

    - name: 📢 Announce Release
      run: |
        echo "## 🎉 Release ${{ needs.create-release.outputs.version }} Published!" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📦 Available Downloads:" >> $GITHUB_STEP_SUMMARY
        echo "- Windows x64 (MSVC)" >> $GITHUB_STEP_SUMMARY
        echo "- Linux x64 (GCC)" >> $GITHUB_STEP_SUMMARY
        echo "- macOS x64 (Clang)" >> $GITHUB_STEP_SUMMARY
        echo "- Source Code (tar.gz & zip)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🔗 Links:" >> $GITHUB_STEP_SUMMARY
        echo "- [Release Page](https://github.com/${{ github.repository }}/releases/tag/${{ needs.create-release.outputs.version }})" >> $GITHUB_STEP_SUMMARY
        echo "- [Documentation](https://github.com/${{ github.repository }}/tree/main/docs)" >> $GITHUB_STEP_SUMMARY
        echo "- [Examples](https://github.com/${{ github.repository }}/tree/main/examples)" >> $GITHUB_STEP_SUMMARY
