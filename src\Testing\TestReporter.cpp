#include "TestRunner.hpp"
#include <QFile>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QDebug>

namespace DeclarativeUI::Testing {

TestReporter::TestReporter(QObject* parent)
    : QObject(parent)
    , include_passed_tests_(true)
    , include_metadata_(true)
    , include_stack_traces_(true)
    , color_output_(true)
{
}

void TestReporter::generateHTMLReport(const std::vector<TestResult>& results, const QString& output_file) {
    QFile file(output_file);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for writing:" << output_file;
        return;
    }
    
    QTextStream stream(&file);
    stream << generateHTMLHeader(results);
    stream << generateHTMLSummary(results);
    stream << generateHTMLTestList(results);
    stream << generateHTMLFooter();
    
    emit reportGenerated(output_file, "html");
}

void TestReporter::generateJUnitXMLReport(const std::vector<TestResult>& results, const QString& output_file) {
    QFile file(output_file);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for writing:" << output_file;
        return;
    }
    
    QTextStream stream(&file);
    stream << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    stream << generateJUnitTestSuite(results);
    
    emit reportGenerated(output_file, "xml");
}

void TestReporter::generateJSONReport(const std::vector<TestResult>& results, const QString& output_file) {
    QJsonObject report;
    report["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    report["total_tests"] = static_cast<int>(results.size());
    
    int passed = 0, failed = 0, skipped = 0, errors = 0;
    for (const auto& result : results) {
        switch (result.status) {
            case TestResult::Status::Passed: passed++; break;
            case TestResult::Status::Failed: failed++; break;
            case TestResult::Status::Skipped: skipped++; break;
            case TestResult::Status::Error: errors++; break;
        }
    }
    
    report["passed"] = passed;
    report["failed"] = failed;
    report["skipped"] = skipped;
    report["errors"] = errors;
    
    QJsonArray tests;
    for (const auto& result : results) {
        QJsonObject test;
        test["name"] = result.test_name;
        test["status"] = static_cast<int>(result.status);
        test["execution_time_ms"] = result.execution_time_ms;
        test["error_message"] = result.error_message;
        if (include_metadata_) {
            test["metadata"] = result.metadata;
        }
        tests.append(test);
    }
    report["tests"] = tests;
    
    QFile file(output_file);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QJsonDocument doc(report);
        file.write(doc.toJson());
    }
    
    emit reportGenerated(output_file, "json");
}

void TestReporter::generateMarkdownReport(const std::vector<TestResult>& results, const QString& output_file) {
    QFile file(output_file);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for writing:" << output_file;
        return;
    }
    
    QTextStream stream(&file);
    stream << "# Test Report\n\n";
    stream << "Generated: " << QDateTime::currentDateTime().toString() << "\n\n";
    
    int passed = 0, failed = 0;
    for (const auto& result : results) {
        if (result.isPassed()) passed++;
        else if (result.isFailed()) failed++;
    }
    
    stream << "## Summary\n\n";
    stream << "- Total Tests: " << results.size() << "\n";
    stream << "- Passed: " << passed << "\n";
    stream << "- Failed: " << failed << "\n\n";
    
    stream << "## Test Results\n\n";
    for (const auto& result : results) {
        QString status = result.isPassed() ? "✅ PASS" : "❌ FAIL";
        stream << "- " << status << " " << result.test_name << "\n";
        if (!result.error_message.isEmpty()) {
            stream << "  - Error: " << result.error_message << "\n";
        }
    }
    
    emit reportGenerated(output_file, "markdown");
}

void TestReporter::generateCSVReport(const std::vector<TestResult>& results, const QString& output_file) {
    QFile file(output_file);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for writing:" << output_file;
        return;
    }
    
    QTextStream stream(&file);
    stream << "Test Name,Status,Execution Time (ms),Error Message\n";
    
    for (const auto& result : results) {
        QString status;
        switch (result.status) {
            case TestResult::Status::Passed: status = "PASSED"; break;
            case TestResult::Status::Failed: status = "FAILED"; break;
            case TestResult::Status::Skipped: status = "SKIPPED"; break;
            case TestResult::Status::Error: status = "ERROR"; break;
        }
        
        stream << "\"" << result.test_name << "\","
               << status << ","
               << result.execution_time_ms << ","
               << "\"" << result.error_message << "\"\n";
    }
    
    emit reportGenerated(output_file, "csv");
}

void TestReporter::printSummary(const std::vector<TestResult>& results) {
    int passed = 0, failed = 0, skipped = 0, errors = 0;
    qint64 total_time = 0;
    
    for (const auto& result : results) {
        total_time += result.execution_time_ms;
        switch (result.status) {
            case TestResult::Status::Passed: passed++; break;
            case TestResult::Status::Failed: failed++; break;
            case TestResult::Status::Skipped: skipped++; break;
            case TestResult::Status::Error: errors++; break;
        }
    }
    
    qDebug() << "=== Test Summary ===";
    qDebug() << "Total Tests:" << results.size();
    qDebug() << "Passed:" << passed;
    qDebug() << "Failed:" << failed;
    qDebug() << "Skipped:" << skipped;
    qDebug() << "Errors:" << errors;
    qDebug() << "Total Time:" << formatDuration(total_time);
    
    double pass_rate = results.empty() ? 0.0 : (double(passed) / results.size()) * 100.0;
    qDebug() << "Pass Rate:" << QString::number(pass_rate, 'f', 1) << "%";
}

void TestReporter::printDetailedResults(const std::vector<TestResult>& results) {
    for (const auto& result : results) {
        QString status;
        switch (result.status) {
            case TestResult::Status::Passed: status = "PASS"; break;
            case TestResult::Status::Failed: status = "FAIL"; break;
            case TestResult::Status::Skipped: status = "SKIP"; break;
            case TestResult::Status::Error: status = "ERROR"; break;
        }
        
        qDebug() << "[" << status << "]" << result.test_name 
                 << "(" << formatDuration(result.execution_time_ms) << ")";
        
        if (!result.error_message.isEmpty()) {
            qDebug() << "  Error:" << result.error_message;
        }
    }
}

void TestReporter::printProgressBar(int completed, int total) {
    if (total <= 0) return;
    
    int width = 50;
    int progress = (completed * width) / total;
    
    QString bar = "[";
    for (int i = 0; i < width; ++i) {
        bar += (i < progress) ? "=" : " ";
    }
    bar += "]";
    
    double percentage = (double(completed) / total) * 100.0;
    qDebug() << bar << QString::number(percentage, 'f', 1) << "%" 
             << "(" << completed << "/" << total << ")";
}

void TestReporter::setHTMLTemplate(const QString& template_file) {
    // Stub implementation
    Q_UNUSED(template_file)
}

void TestReporter::setCustomCSS(const QString& css_content) {
    // Stub implementation
    Q_UNUSED(css_content)
}

// Private helper methods
QString TestReporter::generateHTMLHeader(const std::vector<TestResult>& results) {
    Q_UNUSED(results)
    return "<!DOCTYPE html><html><head><title>Test Report</title></head><body>";
}

QString TestReporter::generateHTMLSummary(const std::vector<TestResult>& results) {
    int passed = 0, failed = 0;
    for (const auto& result : results) {
        if (result.isPassed()) passed++;
        else if (result.isFailed()) failed++;
    }
    
    return QString("<h1>Test Summary</h1><p>Passed: %1, Failed: %2</p>")
           .arg(passed).arg(failed);
}

QString TestReporter::generateHTMLTestList(const std::vector<TestResult>& results) {
    QString html = "<h2>Test Results</h2><ul>";
    for (const auto& result : results) {
        QString status = result.isPassed() ? "PASS" : "FAIL";
        html += QString("<li>%1: %2</li>").arg(status, result.test_name);
    }
    html += "</ul>";
    return html;
}

QString TestReporter::generateHTMLFooter() {
    return "</body></html>";
}

QString TestReporter::generateJUnitTestSuite(const std::vector<TestResult>& results) {
    QString xml = "<testsuite>\n";
    for (const auto& result : results) {
        xml += generateJUnitTestCase(result);
    }
    xml += "</testsuite>\n";
    return xml;
}

QString TestReporter::generateJUnitTestCase(const TestResult& result) {
    QString status = result.isPassed() ? "passed" : "failed";
    return QString("<testcase name=\"%1\" status=\"%2\"/>\n")
           .arg(escapeXML(result.test_name), status);
}

QString TestReporter::formatDuration(qint64 milliseconds) {
    if (milliseconds < 1000) {
        return QString("%1ms").arg(milliseconds);
    } else {
        return QString("%1s").arg(milliseconds / 1000.0, 0, 'f', 2);
    }
}

QString TestReporter::formatTimestamp(const QDateTime& timestamp) {
    return timestamp.toString(Qt::ISODate);
}

QString TestReporter::escapeHTML(const QString& text) {
    return text.toHtmlEscaped();
}

QString TestReporter::escapeXML(const QString& text) {
    return text.toHtmlEscaped(); // Same as HTML escaping for basic cases
}

QString TestReporter::getStatusColor(TestResult::Status status) {
    switch (status) {
        case TestResult::Status::Passed: return "green";
        case TestResult::Status::Failed: return "red";
        case TestResult::Status::Skipped: return "yellow";
        case TestResult::Status::Error: return "orange";
    }
    return "black";
}

QString TestReporter::getStatusIcon(TestResult::Status status) {
    switch (status) {
        case TestResult::Status::Passed: return "✅";
        case TestResult::Status::Failed: return "❌";
        case TestResult::Status::Skipped: return "⏭️";
        case TestResult::Status::Error: return "⚠️";
    }
    return "❓";
}

} // namespace DeclarativeUI::Testing
