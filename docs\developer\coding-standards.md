# Coding Standards

This document outlines the coding standards and conventions for the DeclarativeUI Framework project.

## Table of Contents

- [General Principles](#general-principles)
- [C++ Standards](#c-standards)
- [Naming Conventions](#naming-conventions)
- [Code Organization](#code-organization)
- [Documentation](#documentation)
- [Error Handling](#error-handling)
- [Performance Guidelines](#performance-guidelines)
- [Qt-Specific Guidelines](#qt-specific-guidelines)

## General Principles

### Modern C++20

- Use modern C++20 features when appropriate
- Prefer standard library solutions over custom implementations
- Use RAII (Resource Acquisition Is Initialization) for resource management
- Favor composition over inheritance
- Write self-documenting code with clear variable and function names

### Code Quality

- Write code that is easy to read, understand, and maintain
- Follow the principle of least surprise
- Keep functions small and focused on a single responsibility
- Minimize dependencies between modules
- Write tests for all public APIs

## C++ Standards

### Language Features

```cpp
// ✅ Good: Use auto for type deduction
auto widget = std::make_unique<QWidget>();
auto result = calculateValue();

// ❌ Bad: Explicit types when auto is clearer
std::unique_ptr<QWidget> widget = std::make_unique<QWidget>();
int result = calculateValue();

// ✅ Good: Use range-based for loops
for (const auto& item : container) {
    processItem(item);
}

// ✅ Good: Use structured bindings (C++17)
auto [success, value] = parseInput(input);

// ✅ Good: Use constexpr for compile-time constants
constexpr int MaxRetries = 3;
constexpr double Pi = 3.14159265359;
```

### Memory Management

```cpp
// ✅ Good: Use smart pointers
auto widget = std::make_unique<QWidget>();
auto shared = std::make_shared<Component>();

// ✅ Good: Use Qt parent-child relationships
auto button = new QPushButton("Click me", parent);

// ❌ Bad: Raw pointers without clear ownership
QWidget* widget = new QWidget(); // Who deletes this?

// ✅ Good: RAII for resource management
class FileHandler {
public:
    FileHandler(const QString& filename) : file_(filename) {
        file_.open(QIODevice::ReadOnly);
    }
    // Destructor automatically closes file
private:
    QFile file_;
};
```

## Naming Conventions

### Classes and Types

```cpp
// ✅ Good: PascalCase for classes
class ComponentBuilder;
class StateManager;
class UIElement;

// ✅ Good: Descriptive names
class HotReloadManager;
class PropertyBindingTemplate;

// ❌ Bad: Abbreviations or unclear names
class CompBldr;
class Mgr;
```

### Functions and Variables

```cpp
// ✅ Good: camelCase for functions and variables
void processComponent();
bool isEnabled();
int componentCount;

// ✅ Good: Descriptive names
void updatePropertyBinding();
bool hasValidConfiguration();
QString currentThemeName;

// ❌ Bad: Unclear abbreviations
void procComp();
bool isEn();
int cnt;
```

### Constants and Enums

```cpp
// ✅ Good: PascalCase for enum classes
enum class ComponentType {
    Button,
    Label,
    Container
};

// ✅ Good: SCREAMING_SNAKE_CASE for macros
#define DECLARATIVE_UI_VERSION_MAJOR 1

// ✅ Good: camelCase for constexpr
constexpr int defaultSpacing = 10;
constexpr double animationDuration = 0.3;
```

### Files and Directories

```cpp
// ✅ Good: PascalCase for header files
ComponentBuilder.hpp
StateManager.hpp
UIElement.hpp

// ✅ Good: Matching implementation files
ComponentBuilder.cpp
StateManager.cpp
UIElement.cpp
```

## Code Organization

### Header Files

```cpp
#pragma once

// System includes first
#include <memory>
#include <string>
#include <vector>

// Qt includes
#include <QObject>
#include <QWidget>
#include <QString>

// Project includes
#include "Core/UIElement.hpp"
#include "Binding/PropertyBinding.hpp"

namespace DeclarativeUI {
namespace Components {

class Button : public Core::UIElement {
    Q_OBJECT

public:
    explicit Button(QObject* parent = nullptr);
    ~Button() override = default;

    // Fluent interface
    Button& text(const QString& text);
    Button& enabled(bool enabled);

    // Getters
    QString text() const;
    bool isEnabled() const;

protected:
    void initialize() override;

private:
    QString text_;
    bool enabled_ = true;
};

} // namespace Components
} // namespace DeclarativeUI
```

### Implementation Files

```cpp
#include "Components/Button.hpp"

#include <QPushButton>
#include <QDebug>

namespace DeclarativeUI {
namespace Components {

Button::Button(QObject* parent)
    : UIElement(parent)
{
    // Constructor implementation
}

Button& Button::text(const QString& text) {
    text_ = text;
    if (auto* widget = qobject_cast<QPushButton*>(getWidget())) {
        widget->setText(text);
    }
    return *this;
}

void Button::initialize() {
    auto* button = new QPushButton();
    setWidget(button);
    
    // Apply current state
    button->setText(text_);
    button->setEnabled(enabled_);
}

} // namespace Components
} // namespace DeclarativeUI
```

## Documentation

### Doxygen Comments

```cpp
/**
 * @brief Manages the state of UI components with reactive updates.
 * 
 * The StateManager provides a centralized store for application state
 * with automatic UI updates when state changes occur.
 * 
 * @example
 * @code
 * auto& manager = StateManager::instance();
 * manager.setState("user.name", "John Doe");
 * auto name = manager.getState<QString>("user.name");
 * @endcode
 */
class StateManager {
public:
    /**
     * @brief Sets a state value and triggers reactive updates.
     * @param key The state key in dot notation (e.g., "user.name")
     * @param value The value to set
     * @throws StateException if the key is invalid
     */
    template<typename T>
    void setState(const QString& key, const T& value);
    
    /**
     * @brief Gets a state value with type safety.
     * @tparam T The expected type of the value
     * @param key The state key
     * @return The state value
     * @throws StateException if key not found or type mismatch
     */
    template<typename T>
    T getState(const QString& key) const;
};
```

### Inline Comments

```cpp
// Use comments to explain WHY, not WHAT
void processComponents() {
    // Process in reverse order to maintain parent-child relationships
    for (auto it = components_.rbegin(); it != components_.rend(); ++it) {
        (*it)->process();
    }
    
    // Clear the processed flag after all components are handled
    // This ensures the next update cycle starts fresh
    clearProcessedFlags();
}
```

## Error Handling

### Exceptions

```cpp
// ✅ Good: Use specific exception types
class ComponentException : public std::runtime_error {
public:
    explicit ComponentException(const QString& message)
        : std::runtime_error(message.toStdString()) {}
};

// ✅ Good: Provide context in error messages
void loadComponent(const QString& filename) {
    if (!QFile::exists(filename)) {
        throw ComponentException(
            QString("Component file not found: %1").arg(filename)
        );
    }
}
```

### Qt Error Handling

```cpp
// ✅ Good: Check Qt operation results
bool saveConfiguration(const QString& filename) {
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open file for writing:" << filename;
        return false;
    }
    
    // Save data...
    return true;
}
```

## Performance Guidelines

### Memory Efficiency

```cpp
// ✅ Good: Use move semantics
class Component {
public:
    void setData(std::vector<int> data) {
        data_ = std::move(data); // Avoid copy
    }
    
private:
    std::vector<int> data_;
};

// ✅ Good: Reserve container capacity when known
std::vector<Component> components;
components.reserve(expectedCount);
```

### Qt Performance

```cpp
// ✅ Good: Use const references for Qt types
void processWidgets(const QList<QWidget*>& widgets) {
    for (const auto* widget : widgets) {
        // Process widget
    }
}

// ✅ Good: Minimize signal-slot connections in loops
// Connect once, not in every iteration
connect(button, &QPushButton::clicked, this, &Handler::onClicked);
```

## Qt-Specific Guidelines

### Signals and Slots

```cpp
class ComponentManager : public QObject {
    Q_OBJECT

public:
    // ✅ Good: Use new signal-slot syntax
    connect(component, &Component::valueChanged,
            this, &ComponentManager::onValueChanged);
    
    // ✅ Good: Use lambda for simple connections
    connect(button, &QPushButton::clicked, [this]() {
        updateUI();
    });

signals:
    // ✅ Good: Descriptive signal names
    void componentAdded(const Component* component);
    void stateChanged(const QString& key, const QVariant& value);

private slots:
    void onValueChanged(const QVariant& value);
};
```

### Object Ownership

```cpp
// ✅ Good: Use Qt parent-child relationships
auto* layout = new QVBoxLayout(parentWidget);
auto* button = new QPushButton("Click me", parentWidget);

// ✅ Good: Explicit ownership with smart pointers
auto component = std::make_unique<Component>();
manager.addComponent(std::move(component));
```

## Code Review Checklist

- [ ] Code follows naming conventions
- [ ] Functions are small and focused
- [ ] Memory management is correct (RAII, smart pointers)
- [ ] Error handling is appropriate
- [ ] Documentation is complete and accurate
- [ ] Tests cover the new functionality
- [ ] Performance implications are considered
- [ ] Qt best practices are followed
