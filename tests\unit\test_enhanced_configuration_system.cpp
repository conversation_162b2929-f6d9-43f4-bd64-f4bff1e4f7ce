#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <memory>

#include "../../src/HotReload/HotReloadConfig.hpp"

using namespace DeclarativeUI::HotReload;

class TestEnhancedConfigurationSystem : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Configuration profile tests
    void testConfigurationProfileCreation();
    void testConfigurationProfileSerialization();
    void testConfigurationProfileValidation();
    void testConfigurationProfileMerging();

    // Configuration manager tests
    void testConfigurationManagerBasics();
    void testProfileManagement();
    void testActiveProfileSwitching();
    void testConfigurationPersistence();
    void testConfigurationWatching();
    void testEnvironmentVariableSubstitution();

    // Runtime configuration tests
    void testRuntimeConfigurationUpdates();
    void testConfigurationChangeNotifications();
    void testConfigurationValidation();
    void testConfigurationRollback();

    // Advanced features tests
    void testConfigurationTemplates();
    void testConfigurationInheritance();
    void testConfigurationEncryption();
    void testConfigurationBackup();

    // Integration tests
    void testFileSystemIntegration();
    void testHotReloadIntegration();
    void testPerformanceOptimization();

private:
    std::unique_ptr<HotReloadConfig> config_;
    QTemporaryDir temp_dir_;
    QString config_file_path_;
};

void TestEnhancedConfigurationSystem::initTestCase() {
    QVERIFY(temp_dir_.isValid());
    config_file_path_ = temp_dir_.filePath("test_config.json");
}

void TestEnhancedConfigurationSystem::cleanupTestCase() {
    // Cleanup handled by destructors
}

void TestEnhancedConfigurationSystem::init() {
    config_ = std::make_unique<HotReloadConfig>();
}

void TestEnhancedConfigurationSystem::cleanup() {
    config_.reset();
}

void TestEnhancedConfigurationSystem::testConfigurationProfileCreation() {
    // Test default profile creation
    ConfigurationProfile default_profile;
    QCOMPARE(default_profile.name, "default");
    QVERIFY(default_profile.hot_reload_enabled);
    QCOMPARE(default_profile.watch_delay_ms, 500);
    QVERIFY(default_profile.file_patterns.contains("*.qml"));
    QVERIFY(default_profile.file_patterns.contains("*.css"));
    
    // Test custom profile creation
    ConfigurationProfile custom_profile("development");
    custom_profile.hot_reload_enabled = true;
    custom_profile.watch_delay_ms = 100;
    custom_profile.file_patterns = {"*.qml", "*.js", "*.css", "*.scss"};
    custom_profile.exclude_patterns = {"**/node_modules/**", "**/build/**"};
    custom_profile.max_file_size_mb = 10;
    custom_profile.enable_performance_monitoring = true;
    
    QCOMPARE(custom_profile.name, "development");
    QVERIFY(custom_profile.hot_reload_enabled);
    QCOMPARE(custom_profile.watch_delay_ms, 100);
    QCOMPARE(custom_profile.file_patterns.size(), 4);
    QCOMPARE(custom_profile.exclude_patterns.size(), 2);
    QCOMPARE(custom_profile.max_file_size_mb, 10);
    QVERIFY(custom_profile.enable_performance_monitoring);
}

void TestEnhancedConfigurationSystem::testConfigurationProfileSerialization() {
    // Create a test profile
    ConfigurationProfile profile("test_profile");
    profile.description = "Test profile for serialization";
    profile.hot_reload_enabled = false;
    profile.watch_delay_ms = 1000;
    profile.file_patterns = {"*.qml", "*.cpp"};
    profile.exclude_patterns = {"*.tmp"};
    profile.watch_directories = {"/src", "/ui"};
    profile.max_file_size_mb = 5;
    profile.enable_performance_monitoring = false;
    profile.enable_detailed_logging = true;
    profile.auto_save_enabled = false;
    profile.backup_count = 3;
    
    // Test serialization
    QJsonObject json = profile.toJson();
    QCOMPARE(json["name"].toString(), "test_profile");
    QCOMPARE(json["description"].toString(), "Test profile for serialization");
    QCOMPARE(json["hot_reload_enabled"].toBool(), false);
    QCOMPARE(json["watch_delay_ms"].toInt(), 1000);
    QCOMPARE(json["max_file_size_mb"].toInt(), 5);
    QCOMPARE(json["enable_performance_monitoring"].toBool(), false);
    QCOMPARE(json["enable_detailed_logging"].toBool(), true);
    QCOMPARE(json["auto_save_enabled"].toBool(), false);
    QCOMPARE(json["backup_count"].toInt(), 3);
    
    QJsonArray file_patterns = json["file_patterns"].toArray();
    QCOMPARE(file_patterns.size(), 2);
    QVERIFY(file_patterns.contains("*.qml"));
    QVERIFY(file_patterns.contains("*.cpp"));
    
    QJsonArray exclude_patterns = json["exclude_patterns"].toArray();
    QCOMPARE(exclude_patterns.size(), 1);
    QVERIFY(exclude_patterns.contains("*.tmp"));
    
    QJsonArray watch_directories = json["watch_directories"].toArray();
    QCOMPARE(watch_directories.size(), 2);
    QVERIFY(watch_directories.contains("/src"));
    QVERIFY(watch_directories.contains("/ui"));
    
    // Test deserialization
    ConfigurationProfile restored_profile = ConfigurationProfile::fromJson(json);
    QCOMPARE(restored_profile.name, "test_profile");
    QCOMPARE(restored_profile.description, "Test profile for serialization");
    QCOMPARE(restored_profile.hot_reload_enabled, false);
    QCOMPARE(restored_profile.watch_delay_ms, 1000);
    QCOMPARE(restored_profile.max_file_size_mb, 5);
    QCOMPARE(restored_profile.enable_performance_monitoring, false);
    QCOMPARE(restored_profile.enable_detailed_logging, true);
    QCOMPARE(restored_profile.auto_save_enabled, false);
    QCOMPARE(restored_profile.backup_count, 3);
    QCOMPARE(restored_profile.file_patterns.size(), 2);
    QCOMPARE(restored_profile.exclude_patterns.size(), 1);
    QCOMPARE(restored_profile.watch_directories.size(), 2);
}

void TestEnhancedConfigurationSystem::testConfigurationProfileValidation() {
    ConfigurationProfile profile("validation_test");
    
    // Test valid profile
    QVERIFY(profile.isValid());
    
    // Test invalid watch delay
    profile.watch_delay_ms = -1;
    QVERIFY(!profile.isValid());
    profile.watch_delay_ms = 500; // Reset to valid
    
    // Test invalid file size limit
    profile.max_file_size_mb = -1;
    QVERIFY(!profile.isValid());
    profile.max_file_size_mb = 10; // Reset to valid
    
    // Test invalid backup count
    profile.backup_count = -1;
    QVERIFY(!profile.isValid());
    profile.backup_count = 5; // Reset to valid
    
    // Test empty name
    profile.name = "";
    QVERIFY(!profile.isValid());
    profile.name = "validation_test"; // Reset to valid
    
    QVERIFY(profile.isValid());
}

void TestEnhancedConfigurationSystem::testConfigurationProfileMerging() {
    // Create base profile
    ConfigurationProfile base_profile("base");
    base_profile.hot_reload_enabled = true;
    base_profile.watch_delay_ms = 500;
    base_profile.file_patterns = {"*.qml", "*.css"};
    base_profile.exclude_patterns = {"*.tmp"};
    base_profile.max_file_size_mb = 5;
    
    // Create override profile
    ConfigurationProfile override_profile("override");
    override_profile.watch_delay_ms = 100; // Override
    override_profile.file_patterns = {"*.js"}; // Override
    override_profile.exclude_patterns = {"*.log"}; // Override
    override_profile.enable_performance_monitoring = true; // New setting
    
    // Test merging
    ConfigurationProfile merged = base_profile.merge(override_profile);
    
    // Verify merged values
    QCOMPARE(merged.name, "base"); // Base name preserved
    QVERIFY(merged.hot_reload_enabled); // Base value preserved
    QCOMPARE(merged.watch_delay_ms, 100); // Override value used
    QCOMPARE(merged.max_file_size_mb, 5); // Base value preserved
    QVERIFY(merged.enable_performance_monitoring); // Override value used
    
    // Verify arrays were overridden (not merged)
    QCOMPARE(merged.file_patterns.size(), 1);
    QVERIFY(merged.file_patterns.contains("*.js"));
    QVERIFY(!merged.file_patterns.contains("*.qml"));
    
    QCOMPARE(merged.exclude_patterns.size(), 1);
    QVERIFY(merged.exclude_patterns.contains("*.log"));
    QVERIFY(!merged.exclude_patterns.contains("*.tmp"));
}

void TestEnhancedConfigurationSystem::testConfigurationManagerBasics() {
    // Test initial state
    QVERIFY(config_manager_->getAvailableProfiles().isEmpty());
    QVERIFY(config_manager_->getActiveProfile().name.isEmpty());
    
    // Test adding profiles
    ConfigurationProfile dev_profile("development");
    dev_profile.description = "Development configuration";
    dev_profile.watch_delay_ms = 100;
    
    ConfigurationProfile prod_profile("production");
    prod_profile.description = "Production configuration";
    prod_profile.hot_reload_enabled = false;
    
    config_manager_->addProfile(dev_profile);
    config_manager_->addProfile(prod_profile);
    
    // Verify profiles were added
    QStringList available_profiles = config_manager_->getAvailableProfiles();
    QCOMPARE(available_profiles.size(), 2);
    QVERIFY(available_profiles.contains("development"));
    QVERIFY(available_profiles.contains("production"));
    
    // Test getting specific profile
    ConfigurationProfile retrieved_dev = config_manager_->getProfile("development");
    QCOMPARE(retrieved_dev.name, "development");
    QCOMPARE(retrieved_dev.description, "Development configuration");
    QCOMPARE(retrieved_dev.watch_delay_ms, 100);
    
    ConfigurationProfile retrieved_prod = config_manager_->getProfile("production");
    QCOMPARE(retrieved_prod.name, "production");
    QCOMPARE(retrieved_prod.description, "Production configuration");
    QVERIFY(!retrieved_prod.hot_reload_enabled);
}

void TestEnhancedConfigurationSystem::testProfileManagement() {
    // Add test profiles
    ConfigurationProfile profile1("profile1");
    profile1.description = "First test profile";
    
    ConfigurationProfile profile2("profile2");
    profile2.description = "Second test profile";
    
    config_manager_->addProfile(profile1);
    config_manager_->addProfile(profile2);
    
    // Test profile existence
    QVERIFY(config_manager_->hasProfile("profile1"));
    QVERIFY(config_manager_->hasProfile("profile2"));
    QVERIFY(!config_manager_->hasProfile("nonexistent"));
    
    // Test profile removal
    config_manager_->removeProfile("profile1");
    QVERIFY(!config_manager_->hasProfile("profile1"));
    QVERIFY(config_manager_->hasProfile("profile2"));
    
    QStringList remaining_profiles = config_manager_->getAvailableProfiles();
    QCOMPARE(remaining_profiles.size(), 1);
    QVERIFY(remaining_profiles.contains("profile2"));
    
    // Test clearing all profiles
    config_manager_->clearProfiles();
    QVERIFY(config_manager_->getAvailableProfiles().isEmpty());
    QVERIFY(!config_manager_->hasProfile("profile2"));
}

void TestEnhancedConfigurationSystem::testActiveProfileSwitching() {
    QSignalSpy profile_changed_spy(config_manager_.get(), &EnhancedConfigurationManager::activeProfileChanged);
    QSignalSpy config_changed_spy(config_manager_.get(), &EnhancedConfigurationManager::configurationChanged);
    
    // Add test profiles
    ConfigurationProfile dev_profile("development");
    dev_profile.watch_delay_ms = 100;
    
    ConfigurationProfile test_profile("testing");
    test_profile.watch_delay_ms = 200;
    
    config_manager_->addProfile(dev_profile);
    config_manager_->addProfile(test_profile);
    
    // Test setting active profile
    QVERIFY(config_manager_->setActiveProfile("development"));
    
    QCOMPARE(profile_changed_spy.count(), 1);
    QCOMPARE(config_changed_spy.count(), 1);
    
    QList<QVariant> arguments = profile_changed_spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), "development");
    
    // Verify active profile
    ConfigurationProfile active_profile = config_manager_->getActiveProfile();
    QCOMPARE(active_profile.name, "development");
    QCOMPARE(active_profile.watch_delay_ms, 100);
    
    // Test switching to another profile
    QVERIFY(config_manager_->setActiveProfile("testing"));
    
    QCOMPARE(profile_changed_spy.count(), 1);
    QCOMPARE(config_changed_spy.count(), 1);
    
    arguments = profile_changed_spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), "testing");
    
    active_profile = config_manager_->getActiveProfile();
    QCOMPARE(active_profile.name, "testing");
    QCOMPARE(active_profile.watch_delay_ms, 200);
    
    // Test setting non-existent profile
    QVERIFY(!config_manager_->setActiveProfile("nonexistent"));
    
    // Active profile should remain unchanged
    active_profile = config_manager_->getActiveProfile();
    QCOMPARE(active_profile.name, "testing");
}

QTEST_MAIN(TestEnhancedConfigurationSystem)
#include "test_enhanced_configuration_system.moc"
