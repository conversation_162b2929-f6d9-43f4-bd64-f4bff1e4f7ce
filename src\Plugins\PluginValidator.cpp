#include "PluginValidator.hpp"
#include <QFileInfo>
#include <QDir>
#include <QJsonDocument>
#include <QJsonArray>
#include <QCryptographicHash>
#include <QStandardPaths>
#include <QDebug>

namespace DeclarativeUI::Plugins {

PluginValidator::PluginValidator(QObject* parent)
    : QObject(parent)
    , framework_version_(1, 0, 0)
{
    // Set default allowed extensions
#ifdef Q_OS_WIN
    allowed_extensions_ << "dll";
#elif defined(Q_OS_MAC)
    allowed_extensions_ << "dylib" << "so";
#else
    allowed_extensions_ << "so";
#endif
}

ValidationResult PluginValidator::validatePlugin(const QString& pluginPath) {
    emit validationStarted(pluginPath);
    validation_count_++;
    
    ValidationResult result;
    
    // Basic file system validation
    ValidationResult fsResult = validateFileSystem(pluginPath);
    if (!fsResult.isValid) {
        result.error = fsResult.error;
        result.warnings.append(fsResult.warnings);
        failed_validations_++;
        emit validationCompleted(pluginPath, false);
        return result;
    }
    
    // Find and validate manifest
    QFileInfo info(pluginPath);
    QString manifestPath;
    
    if (info.isDir()) {
        manifestPath = QDir(pluginPath).filePath("plugin.json");
    } else {
        manifestPath = info.dir().filePath("plugin.json");
    }
    
    if (!QFile::exists(manifestPath)) {
        result.error = "Plugin manifest not found";
        failed_validations_++;
        emit validationCompleted(pluginPath, false);
        return result;
    }
    
    ValidationResult manifestResult = validatePluginManifest(manifestPath);
    if (!manifestResult.isValid) {
        result.error = manifestResult.error;
        result.warnings.append(manifestResult.warnings);
        failed_validations_++;
        emit validationCompleted(pluginPath, false);
        return result;
    }
    
    // Security validation
    if (security_level_ != SecurityLevel::None) {
        ValidationResult securityResult = validateSecurityPolicy(pluginPath);
        if (!securityResult.isValid) {
            result.error = securityResult.error;
            result.warnings.append(securityResult.warnings);
            failed_validations_++;
            emit validationCompleted(pluginPath, false);
            return result;
        }
        result.warnings.append(securityResult.warnings);
    }
    
    // Custom validation rules
    for (auto it = custom_rules_.begin(); it != custom_rules_.end(); ++it) {
        ValidationResult customResult = it.value()(pluginPath);
        if (!customResult.isValid) {
            result.error = QString("Custom rule '%1' failed: %2").arg(it.key(), customResult.error);
            failed_validations_++;
            emit validationCompleted(pluginPath, false);
            return result;
        }
        result.warnings.append(customResult.warnings);
    }
    
    result.isValid = true;
    result.warnings.append(manifestResult.warnings);
    successful_validations_++;
    emit validationCompleted(pluginPath, true);
    
    return result;
}

ValidationResult PluginValidator::validatePluginManifest(const QString& manifestPath) const {
    ValidationResult result;
    
    QFile file(manifestPath);
    if (!file.open(QIODevice::ReadOnly)) {
        result.error = "Cannot read manifest file: " + file.errorString();
        return result;
    }
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &parseError);
    if (parseError.error != QJsonParseError::NoError) {
        result.error = "Invalid manifest JSON: " + parseError.errorString();
        return result;
    }
    
    return validateManifestContent(doc.object());
}

ValidationResult PluginValidator::validatePluginLibrary(const QString& libraryPath) const {
    ValidationResult result;
    
    if (!QFile::exists(libraryPath)) {
        result.error = "Plugin library not found: " + libraryPath;
        return result;
    }
    
    return validateLibraryContent(libraryPath);
}

ValidationResult PluginValidator::validatePluginMetadata(const PluginMetadata& metadata) const {
    ValidationResult result;
    
    if (!metadata.isValid()) {
        result.error = "Invalid plugin metadata";
        return result;
    }
    
    // Check framework compatibility
    if (!checkFrameworkCompatibility(metadata)) {
        result.error = "Plugin is not compatible with current framework version";
        return result;
    }
    
    // Check author trust
    if (!metadata.author.isEmpty() && !checkAuthorTrust(metadata.author)) {
        result.warnings.append("Plugin author is not in trusted list: " + metadata.author);
    }
    
    // Check dependencies
    if (!checkDependencyCompatibility(metadata.dependencies)) {
        result.warnings.append("Some plugin dependencies may not be available");
    }
    
    result.isValid = true;
    return result;
}

bool PluginValidator::validateSecurity(const QString& pluginPath) {
    if (security_level_ == SecurityLevel::None) {
        return true;
    }
    
    // Check if plugin is blacklisted
    if (isPluginBlacklisted(pluginPath)) {
        emit securityViolation(pluginPath, "Plugin is blacklisted");
        return false;
    }
    
    // Check file integrity
    if (!checkFileIntegrity(pluginPath)) {
        emit securityViolation(pluginPath, "File integrity check failed");
        return false;
    }
    
    // Check signature if required
    if (require_signature_ && !verifySignature(pluginPath)) {
        emit securityViolation(pluginPath, "Signature verification failed");
        return false;
    }
    
    // Check permissions
    if (!checkPermissions(pluginPath)) {
        emit securityViolation(pluginPath, "File permissions check failed");
        return false;
    }
    
    return true;
}

bool PluginValidator::checkFileIntegrity(const QString& filePath) const {
    QFileInfo info(filePath);
    
    // Check file size
    if (!checkFileSize(filePath)) {
        return false;
    }
    
    // Check file extension
    if (!checkFileExtension(filePath)) {
        return false;
    }
    
    // Check file location
    if (!checkFileLocation(filePath)) {
        return false;
    }
    
    return true;
}

bool PluginValidator::verifySignature(const QString& filePath) const {
    // TODO: Implement digital signature verification
    // This would typically involve checking a .sig file or embedded signature
    Q_UNUSED(filePath)
    return true; // Placeholder implementation
}

bool PluginValidator::checkPermissions(const QString& filePath) const {
    QFileInfo info(filePath);
    
    // Check if file is readable
    if (!info.isReadable()) {
        return false;
    }
    
    // Check if file is in a secure location
    QString canonicalPath = info.canonicalFilePath();
    QStringList securePaths = {
        QStandardPaths::writableLocation(QStandardPaths::AppDataLocation),
        QStandardPaths::writableLocation(QStandardPaths::GenericDataLocation) + "/DeclarativeUI"
    };
    
    bool inSecureLocation = false;
    for (const QString& securePath : securePaths) {
        if (canonicalPath.startsWith(securePath)) {
            inSecureLocation = true;
            break;
        }
    }
    
    if (security_level_ == SecurityLevel::Strict && !inSecureLocation) {
        return false;
    }
    
    return true;
}

bool PluginValidator::checkFrameworkCompatibility(const PluginMetadata& metadata) const {
    // Check minimum version
    if (!metadata.minFrameworkVersion.isNull() &&
        framework_version_ < metadata.minFrameworkVersion) {
        return false;
    }
    
    // Check maximum version
    if (!metadata.maxFrameworkVersion.isNull() &&
        framework_version_ > metadata.maxFrameworkVersion) {
        return false;
    }
    
    return true;
}

bool PluginValidator::checkDependencyCompatibility(const QStringList& dependencies) const {
    // TODO: Implement dependency checking against available plugins
    Q_UNUSED(dependencies)
    return true; // Placeholder implementation
}

bool PluginValidator::checkVersionCompatibility(const QVersionNumber& pluginVersion,
                                              const QVersionNumber& minVersion,
                                              const QVersionNumber& maxVersion) const {
    if (!minVersion.isNull() && pluginVersion < minVersion) {
        return false;
    }
    
    if (!maxVersion.isNull() && pluginVersion > maxVersion) {
        return false;
    }
    
    return true;
}

ValidationResult PluginValidator::validateFileSystem(const QString& pluginPath) const {
    ValidationResult result;
    
    QFileInfo info(pluginPath);
    if (!info.exists()) {
        result.error = "Plugin path does not exist: " + pluginPath;
        return result;
    }
    
    if (!info.isReadable()) {
        result.error = "Plugin path is not readable: " + pluginPath;
        return result;
    }
    
    result.isValid = true;
    return result;
}

ValidationResult PluginValidator::validateManifestContent(const QJsonObject& manifest) const {
    ValidationResult result;
    
    // Check required fields
    QStringList requiredFields = {"name", "version", "description", "author"};
    for (const QString& field : requiredFields) {
        if (!manifest.contains(field) || manifest[field].toString().isEmpty()) {
            result.error = "Missing required field in manifest: " + field;
            return result;
        }
    }
    
    // Validate metadata
    PluginMetadata metadata = PluginMetadata::fromJson(manifest);
    ValidationResult metadataResult = validatePluginMetadata(metadata);
    if (!metadataResult.isValid) {
        result.error = metadataResult.error;
        result.warnings.append(metadataResult.warnings);
        return result;
    }
    
    result.isValid = true;
    result.warnings.append(metadataResult.warnings);
    return result;
}

ValidationResult PluginValidator::validateLibraryContent(const QString& libraryPath) const {
    ValidationResult result;
    
    // Check file extension
    if (!checkFileExtension(libraryPath)) {
        result.error = "Invalid library file extension";
        return result;
    }
    
    // Check file size
    if (!checkFileSize(libraryPath)) {
        result.error = "Library file size exceeds maximum allowed size";
        return result;
    }
    
    result.isValid = true;
    return result;
}

ValidationResult PluginValidator::validateSecurityPolicy(const QString& pluginPath) {
    ValidationResult result;
    
    if (!validateSecurity(pluginPath)) {
        result.error = "Security validation failed";
        return result;
    }
    
    result.isValid = true;
    return result;
}

bool PluginValidator::checkFileSize(const QString& filePath) const {
    QFileInfo info(filePath);
    return info.size() <= max_plugin_size_;
}

bool PluginValidator::checkFileExtension(const QString& filePath) const {
    QFileInfo info(filePath);
    QString extension = info.suffix().toLower();
    return allowed_extensions_.contains(extension, Qt::CaseInsensitive);
}

bool PluginValidator::checkFileLocation(const QString& filePath) const {
    // TODO: Implement location-based security checks
    Q_UNUSED(filePath)
    return true; // Placeholder implementation
}

bool PluginValidator::checkAuthorTrust(const QString& author) const {
    if (trusted_authors_.isEmpty()) {
        return true; // No trust list means all authors are trusted
    }
    
    return trusted_authors_.contains(author, Qt::CaseInsensitive);
}

bool PluginValidator::isPluginBlacklisted(const QString& pluginPath) const {
    QFileInfo info(pluginPath);
    QString fileName = info.fileName();
    QString baseName = info.baseName();
    
    return blacklisted_plugins_.contains(fileName, Qt::CaseInsensitive) ||
           blacklisted_plugins_.contains(baseName, Qt::CaseInsensitive) ||
           blacklisted_plugins_.contains(pluginPath, Qt::CaseInsensitive);
}

QString PluginValidator::calculateFileHash(const QString& filePath) const {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }
    
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(&file);
    return hash.result().toHex();
}

void PluginValidator::setSecurityLevel(SecurityLevel level) {
    security_level_ = level;
}

void PluginValidator::setFrameworkVersion(const QVersionNumber& version) {
    framework_version_ = version;
}

void PluginValidator::setMaxPluginSize(qint64 maxSize) {
    max_plugin_size_ = maxSize;
}

void PluginValidator::setAllowedExtensions(const QStringList& extensions) {
    allowed_extensions_ = extensions;
}

void PluginValidator::setRequireSignature(bool require) {
    require_signature_ = require;
}

void PluginValidator::setTrustedAuthors(const QStringList& authors) {
    trusted_authors_ = authors;
}

void PluginValidator::setBlacklistedPlugins(const QStringList& plugins) {
    blacklisted_plugins_ = plugins;
}

void PluginValidator::addCustomValidationRule(const QString& name, 
                                            std::function<ValidationResult(const QString&)> rule) {
    custom_rules_[name] = rule;
}

void PluginValidator::removeCustomValidationRule(const QString& name) {
    custom_rules_.remove(name);
}

QStringList PluginValidator::getCustomValidationRules() const {
    return custom_rules_.keys();
}

void PluginValidator::clearWarnings() {
    validation_warnings_.clear();
}

void PluginValidator::resetStatistics() {
    validation_count_ = 0;
    successful_validations_ = 0;
    failed_validations_ = 0;
}

void PluginValidator::setError(const QString& error) const {
    last_error_ = error;
    qWarning() << "PluginValidator error:" << error;
}

void PluginValidator::addWarning(const QString& warning) const {
    validation_warnings_.append(warning);
    qDebug() << "PluginValidator warning:" << warning;
}

} // namespace DeclarativeUI::Plugins
