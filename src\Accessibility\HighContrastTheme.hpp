#pragma once

#include <QObject>
#include <QWidget>
#include <QColor>
#include <QFont>
#include <QHash>
#include <QJsonObject>
#include <QPalette>
#include <QApplication>

namespace DeclarativeUI::Accessibility {

/**
 * @brief High contrast theme presets
 */
enum class ContrastTheme {
    Default,        // Standard theme
    HighContrast,   // High contrast black on white
    InvertedHigh,   // High contrast white on black
    YellowBlack,    // Yellow text on black background
    WhiteBlue,      // White text on blue background
    Custom          // User-defined custom theme
};

/**
 * @brief Color scheme for accessibility
 */
struct AccessibilityColorScheme {
    // Background colors
    QColor windowBackground = QColor(255, 255, 255);
    QColor widgetBackground = QColor(240, 240, 240);
    QColor alternateBackground = QColor(245, 245, 245);
    QColor selectedBackground = QColor(0, 120, 215);
    QColor disabledBackground = QColor(240, 240, 240);
    
    // Text colors
    QColor primaryText = QColor(0, 0, 0);
    QColor secondaryText = QColor(100, 100, 100);
    QColor selectedText = QColor(255, 255, 255);
    QColor disabledText = QColor(150, 150, 150);
    QColor linkText = QColor(0, 0, 255);
    QColor visitedLinkText = QColor(128, 0, 128);
    
    // Border and accent colors
    QColor border = QColor(200, 200, 200);
    QColor focusBorder = QColor(0, 120, 215);
    QColor errorBorder = QColor(255, 0, 0);
    QColor warningBorder = QColor(255, 165, 0);
    QColor successBorder = QColor(0, 128, 0);
    
    // Button colors
    QColor buttonBackground = QColor(225, 225, 225);
    QColor buttonHover = QColor(210, 210, 210);
    QColor buttonPressed = QColor(190, 190, 190);
    QColor buttonText = QColor(0, 0, 0);
    
    // Input field colors
    QColor inputBackground = QColor(255, 255, 255);
    QColor inputBorder = QColor(200, 200, 200);
    QColor inputFocusBorder = QColor(0, 120, 215);
    QColor inputText = QColor(0, 0, 0);
    QColor inputPlaceholder = QColor(150, 150, 150);
    
    // Status colors
    QColor errorBackground = QColor(255, 240, 240);
    QColor warningBackground = QColor(255, 250, 240);
    QColor successBackground = QColor(240, 255, 240);
    QColor infoBackground = QColor(240, 248, 255);
    
    QJsonObject toJson() const;
    static AccessibilityColorScheme fromJson(const QJsonObject& json);
    
    // Validation
    bool hasGoodContrast() const;
    double getContrastRatio(const QColor& foreground, const QColor& background) const;
};

/**
 * @brief Font settings for accessibility
 */
struct AccessibilityFontSettings {
    QString family = "Arial";
    int baseSize = 12;
    int scaleFactor = 100;  // Percentage
    bool bold = false;
    bool antialiasing = true;
    int letterSpacing = 0;  // Additional letter spacing in pixels
    int lineSpacing = 0;    // Additional line spacing in pixels
    
    QFont createFont(int sizeAdjustment = 0) const;
    QJsonObject toJson() const;
    static AccessibilityFontSettings fromJson(const QJsonObject& json);
};

/**
 * @brief Complete accessibility theme configuration
 */
struct AccessibilityThemeConfig {
    QString name;
    QString description;
    ContrastTheme themeType = ContrastTheme::Default;
    AccessibilityColorScheme colors;
    AccessibilityFontSettings fonts;
    
    // Additional settings
    bool reducedMotion = false;
    bool reducedTransparency = false;
    bool increasedClickTargets = false;
    int minimumClickTargetSize = 44;  // pixels
    bool showFocusIndicators = true;
    bool highContrastCursor = false;
    
    QJsonObject toJson() const;
    static AccessibilityThemeConfig fromJson(const QJsonObject& json);
};

/**
 * @brief High contrast theme manager
 */
class HighContrastTheme : public QObject {
    Q_OBJECT
    
public:
    explicit HighContrastTheme(QObject* parent = nullptr);
    ~HighContrastTheme() override = default;
    
    // Theme management
    void setTheme(ContrastTheme theme);
    ContrastTheme getCurrentTheme() const { return current_theme_; }
    
    void setCustomTheme(const AccessibilityThemeConfig& config);
    AccessibilityThemeConfig getCurrentThemeConfig() const { return current_config_; }
    
    // Predefined themes
    static AccessibilityThemeConfig getDefaultTheme();
    static AccessibilityThemeConfig getHighContrastTheme();
    static AccessibilityThemeConfig getInvertedHighContrastTheme();
    static AccessibilityThemeConfig getYellowBlackTheme();
    static AccessibilityThemeConfig getWhiteBlueTheme();
    
    // Theme application
    void applyTheme(QWidget* widget = nullptr);
    void applyThemeToApplication();
    void restoreOriginalTheme();
    
    // Color scheme management
    void setColorScheme(const AccessibilityColorScheme& scheme);
    AccessibilityColorScheme getColorScheme() const { return current_config_.colors; }
    
    // Font management
    void setFontSettings(const AccessibilityFontSettings& settings);
    AccessibilityFontSettings getFontSettings() const { return current_config_.fonts; }
    
    void increaseFontSize(int increment = 2);
    void decreaseFontSize(int decrement = 2);
    void resetFontSize();
    
    // Widget-specific styling
    void styleButton(QWidget* button);
    void styleLineEdit(QWidget* lineEdit);
    void styleLabel(QWidget* label);
    void styleListWidget(QWidget* listWidget);
    void styleTreeWidget(QWidget* treeWidget);
    void styleTabWidget(QWidget* tabWidget);
    void styleGroupBox(QWidget* groupBox);
    void styleScrollBar(QWidget* scrollBar);
    
    // Focus indicators
    void enableFocusIndicators(bool enabled = true);
    void setFocusIndicatorStyle(const QString& style);
    void updateFocusIndicator(QWidget* widget);
    
    // Accessibility enhancements
    void enableReducedMotion(bool enabled = true);
    void enableReducedTransparency(bool enabled = true);
    void enableIncreasedClickTargets(bool enabled = true);
    void setMinimumClickTargetSize(int size);
    
    // Color utilities
    static QColor adjustContrast(const QColor& color, double factor);
    static QColor ensureContrast(const QColor& foreground, const QColor& background, double minRatio = 4.5);
    static double calculateContrastRatio(const QColor& color1, const QColor& color2);
    static double getLuminance(const QColor& color);
    
    // System integration
    void detectSystemHighContrast();
    bool isSystemHighContrastEnabled() const;
    void followSystemTheme(bool follow = true);
    
    // Theme persistence
    void saveTheme(const QString& name, const AccessibilityThemeConfig& config);
    AccessibilityThemeConfig loadTheme(const QString& name);
    QStringList getAvailableThemes() const;
    void deleteTheme(const QString& name);
    
    // Validation
    QStringList validateTheme(const AccessibilityThemeConfig& config) const;
    bool isThemeAccessible(const AccessibilityThemeConfig& config) const;
    
signals:
    void themeChanged(ContrastTheme theme);
    void colorSchemeChanged(const AccessibilityColorScheme& scheme);
    void fontSettingsChanged(const AccessibilityFontSettings& settings);
    void accessibilitySettingsChanged();
    
private slots:
    void onSystemThemeChanged();
    void onApplicationPaletteChanged();
    
private:
    // Theme application helpers
    QString generateStyleSheet(const AccessibilityThemeConfig& config) const;
    QString generateButtonStyle(const AccessibilityColorScheme& colors) const;
    QString generateInputStyle(const AccessibilityColorScheme& colors) const;
    QString generateListStyle(const AccessibilityColorScheme& colors) const;
    QString generateScrollBarStyle(const AccessibilityColorScheme& colors) const;
    QString generateFocusStyle(const AccessibilityColorScheme& colors) const;
    
    // Font helpers
    void applyFontSettings(QWidget* widget, const AccessibilityFontSettings& settings);
    void updateApplicationFont(const AccessibilityFontSettings& settings);
    
    // Color helpers
    QPalette createPalette(const AccessibilityColorScheme& scheme) const;
    void applyColorScheme(QWidget* widget, const AccessibilityColorScheme& scheme);
    
    // Widget enhancement helpers
    void enhanceClickTargets(QWidget* widget);
    void addFocusIndicator(QWidget* widget);
    void reduceMotionEffects(QWidget* widget);
    void reduceTransparency(QWidget* widget);
    
    // System integration helpers
    void connectToSystemThemeChanges();
    void updateFromSystemSettings();
    
    // Persistence helpers
    QString getThemeFilePath(const QString& name) const;
    void ensureThemeDirectory() const;
    
private:
    ContrastTheme current_theme_ = ContrastTheme::Default;
    AccessibilityThemeConfig current_config_;
    AccessibilityThemeConfig original_config_;
    
    // State tracking
    bool theme_applied_ = false;
    bool follow_system_theme_ = false;
    bool system_high_contrast_ = false;
    
    // Original application state
    QPalette original_palette_;
    QFont original_font_;
    QString original_stylesheet_;
    
    // Widget tracking
    QHash<QWidget*, QString> widget_original_styles_;
    QHash<QWidget*, QPalette> widget_original_palettes_;
    QHash<QWidget*, QFont> widget_original_fonts_;
    
    // Focus indicators
    bool focus_indicators_enabled_ = true;
    QString focus_indicator_style_;
    QHash<QWidget*, QWidget*> focus_indicators_;
};

} // namespace DeclarativeUI::Accessibility
