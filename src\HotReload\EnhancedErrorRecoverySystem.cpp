#include "EnhancedErrorRecoverySystem.hpp"
#include <QDebug>
#include <QApplication>
#include <QFileInfo>
#include <QDir>
#include <QJsonDocument>
#include <QUuid>
#include <QtConcurrent>
#include <QThread>
#include <QElapsedTimer>

namespace DeclarativeUI::HotReload {

// ErrorRecoveryConfig implementation
QJsonObject ErrorRecoveryConfig::toJson() const {
    QJsonObject json;
    json["auto_recovery_enabled"] = auto_recovery_enabled;
    json["graceful_degradation_enabled"] = graceful_degradation_enabled;
    json["user_notifications_enabled"] = user_notifications_enabled;
    json["detailed_error_reporting"] = detailed_error_reporting;
    json["suggested_fixes_enabled"] = suggested_fixes_enabled;
    
    json["recovery_delay_ms"] = recovery_delay_ms;
    json["max_recovery_attempts"] = max_recovery_attempts;
    json["recovery_timeout_ms"] = recovery_timeout_ms;
    json["graceful_degradation_timeout_ms"] = graceful_degradation_timeout_ms;
    
    json["show_system_notifications"] = show_system_notifications;
    json["show_status_bar_messages"] = show_status_bar_messages;
    json["show_progress_indicators"] = show_progress_indicators;
    json["show_error_dialogs"] = show_error_dialogs;
    
    json["enable_rollback_recovery"] = enable_rollback_recovery;
    json["enable_file_restoration"] = enable_file_restoration;
    json["enable_component_isolation"] = enable_component_isolation;
    json["enable_dependency_resolution"] = enable_dependency_resolution;
    json["enable_cache_clearing"] = enable_cache_clearing;
    
    json["allow_partial_functionality"] = allow_partial_functionality;
    json["fallback_to_safe_mode"] = fallback_to_safe_mode;
    json["disable_problematic_features"] = disable_problematic_features;
    
    return json;
}

ErrorRecoveryConfig ErrorRecoveryConfig::fromJson(const QJsonObject& json) {
    ErrorRecoveryConfig config;
    config.auto_recovery_enabled = json["auto_recovery_enabled"].toBool(true);
    config.graceful_degradation_enabled = json["graceful_degradation_enabled"].toBool(true);
    config.user_notifications_enabled = json["user_notifications_enabled"].toBool(true);
    config.detailed_error_reporting = json["detailed_error_reporting"].toBool(true);
    config.suggested_fixes_enabled = json["suggested_fixes_enabled"].toBool(true);
    
    config.recovery_delay_ms = json["recovery_delay_ms"].toInt(1000);
    config.max_recovery_attempts = json["max_recovery_attempts"].toInt(3);
    config.recovery_timeout_ms = json["recovery_timeout_ms"].toInt(30000);
    config.graceful_degradation_timeout_ms = json["graceful_degradation_timeout_ms"].toInt(5000);
    
    config.show_system_notifications = json["show_system_notifications"].toBool(true);
    config.show_status_bar_messages = json["show_status_bar_messages"].toBool(true);
    config.show_progress_indicators = json["show_progress_indicators"].toBool(true);
    config.show_error_dialogs = json["show_error_dialogs"].toBool(false);
    
    config.enable_rollback_recovery = json["enable_rollback_recovery"].toBool(true);
    config.enable_file_restoration = json["enable_file_restoration"].toBool(true);
    config.enable_component_isolation = json["enable_component_isolation"].toBool(true);
    config.enable_dependency_resolution = json["enable_dependency_resolution"].toBool(true);
    config.enable_cache_clearing = json["enable_cache_clearing"].toBool(true);
    
    config.allow_partial_functionality = json["allow_partial_functionality"].toBool(true);
    config.fallback_to_safe_mode = json["fallback_to_safe_mode"].toBool(true);
    config.disable_problematic_features = json["disable_problematic_features"].toBool(true);
    
    return config;
}

// RecoveryResult implementation
QJsonObject RecoveryResult::toJson() const {
    QJsonObject json;
    json["status"] = static_cast<int>(status);
    json["operation_id"] = operation_id;
    json["target_id"] = target_id;
    json["strategy_name"] = strategy_name;
    json["error_message"] = error_message;
    json["duration_ms"] = duration_ms;
    json["timestamp"] = timestamp.toString(Qt::ISODate);
    
    QJsonArray actions_array;
    for (const QString& action : actions_taken) {
        actions_array.append(action);
    }
    json["actions_taken"] = actions_array;
    
    QJsonArray issues_array;
    for (const QString& issue : remaining_issues) {
        issues_array.append(issue);
    }
    json["remaining_issues"] = issues_array;
    
    json["metadata"] = metadata;
    
    return json;
}

RecoveryResult RecoveryResult::fromJson(const QJsonObject& json) {
    RecoveryResult result;
    result.status = static_cast<Status>(json["status"].toInt());
    result.operation_id = json["operation_id"].toString();
    result.target_id = json["target_id"].toString();
    result.strategy_name = json["strategy_name"].toString();
    result.error_message = json["error_message"].toString();
    result.duration_ms = json["duration_ms"].toInt();
    result.timestamp = QDateTime::fromString(json["timestamp"].toString(), Qt::ISODate);
    
    QJsonArray actions_array = json["actions_taken"].toArray();
    for (const QJsonValue& value : actions_array) {
        result.actions_taken.append(value.toString());
    }
    
    QJsonArray issues_array = json["remaining_issues"].toArray();
    for (const QJsonValue& value : issues_array) {
        result.remaining_issues.append(value.toString());
    }
    
    result.metadata = json["metadata"].toObject();
    
    return result;
}

// ErrorNotificationManager implementation
ErrorNotificationManager::ErrorNotificationManager(QObject* parent)
    : QObject(parent) {
    setupSystemTray();
    setupProgressBar();
}

void ErrorNotificationManager::showNotification(NotificationType type, const QString& title, const QString& message, int timeout_ms) {
    if (!notifications_enabled_) {
        return;
    }
    
    // Show system notification if available
    if (system_tray_ && system_tray_->isVisible()) {
        QSystemTrayIcon::MessageIcon icon = QSystemTrayIcon::Information;
        switch (type) {
            case Warning: icon = QSystemTrayIcon::Warning; break;
            case Error:
            case Critical: icon = QSystemTrayIcon::Critical; break;
            default: icon = QSystemTrayIcon::Information; break;
        }
        showSystemNotification(title, message, icon);
    }
    
    // Show status bar message if available
    if (status_bar_) {
        showStatusMessage(QString("%1: %2").arg(title, message), timeout_ms);
    }
    
    // Log the notification
    QString type_str;
    switch (type) {
        case Info: type_str = "INFO"; break;
        case Warning: type_str = "WARNING"; break;
        case Error: type_str = "ERROR"; break;
        case Critical: type_str = "CRITICAL"; break;
        case Recovery: type_str = "RECOVERY"; break;
        case Success: type_str = "SUCCESS"; break;
    }
    
    qDebug() << "🔔" << type_str << title << "-" << message;
    
    emit notificationShown(type, title, message);
}

void ErrorNotificationManager::showSystemNotification(const QString& title, const QString& message, QSystemTrayIcon::MessageIcon icon) {
    if (system_tray_ && system_tray_->isVisible()) {
        system_tray_->showMessage(title, message, icon, 5000);
    }
}

void ErrorNotificationManager::showStatusMessage(const QString& message, int timeout_ms) {
    if (status_bar_) {
        status_bar_->showMessage(message, timeout_ms);
    }
}

void ErrorNotificationManager::showProgressDialog(const QString& title, const QString& message, int maximum) {
    if (!progress_indicators_enabled_) {
        return;
    }
    
    if (progress_bar_) {
        progress_bar_->setMaximum(maximum);
        progress_bar_->setValue(0);
        progress_bar_->setFormat(QString("%1: %2 (%p%)").arg(title, message));
        progress_bar_->setVisible(true);
    }
}

void ErrorNotificationManager::updateProgress(int value, const QString& status) {
    if (progress_bar_) {
        progress_bar_->setValue(value);
        if (!status.isEmpty()) {
            QString current_format = progress_bar_->format();
            QString base_format = current_format.split(':').first();
            progress_bar_->setFormat(QString("%1: %2 (%p%)").arg(base_format, status));
        }
    }
}

void ErrorNotificationManager::hideProgress() {
    if (progress_bar_) {
        progress_bar_->setVisible(false);
    }
}

void ErrorNotificationManager::notifyErrorDetected(const DiagnosticInfo& diagnostic) {
    NotificationType type = static_cast<NotificationType>(diagnostic.severity);
    QString title = QString("Error Detected: %1").arg(diagnostic.title);
    QString message = diagnostic.description;
    
    if (!diagnostic.file_path.isEmpty()) {
        message += QString(" (File: %1)").arg(QFileInfo(diagnostic.file_path).fileName());
    }
    
    showNotification(type, title, message);
}

void ErrorNotificationManager::notifyRecoveryStarted(const QString& strategy_name, const QString& target_id) {
    QString title = "Recovery Started";
    QString message = QString("Attempting recovery using %1 for %2").arg(strategy_name, target_id);
    showNotification(Recovery, title, message, 3000);
}

void ErrorNotificationManager::notifyRecoveryProgress(const QString& operation_id, int progress, const QString& status) {
    Q_UNUSED(operation_id)
    updateProgress(progress, status);
}

void ErrorNotificationManager::notifyRecoveryCompleted(const RecoveryResult& result) {
    NotificationType type = result.isSuccess() ? Success : Error;
    QString title = result.isSuccess() ? "Recovery Successful" : "Recovery Failed";
    QString message = result.isSuccess() ? 
        QString("Successfully recovered %1 using %2").arg(result.target_id, result.strategy_name) :
        QString("Failed to recover %1: %2").arg(result.target_id, result.error_message);
    
    showNotification(type, title, message);
    hideProgress();
}

void ErrorNotificationManager::notifyGracefulDegradation(const QString& feature_name, const QString& reason) {
    QString title = "Feature Temporarily Disabled";
    QString message = QString("Feature '%1' has been disabled: %2").arg(feature_name, reason);
    showNotification(Warning, title, message);
}

void ErrorNotificationManager::setStatusBar(QStatusBar* status_bar) {
    status_bar_ = status_bar;
}

void ErrorNotificationManager::setSystemTrayIcon(QSystemTrayIcon* tray_icon) {
    system_tray_ = tray_icon;
}

void ErrorNotificationManager::setNotificationsEnabled(bool enabled) {
    notifications_enabled_ = enabled;
}

void ErrorNotificationManager::setProgressIndicatorsEnabled(bool enabled) {
    progress_indicators_enabled_ = enabled;
}

void ErrorNotificationManager::setupSystemTray() {
    // System tray setup would be handled by the main application
    // This is a placeholder for future implementation
}

void ErrorNotificationManager::setupProgressBar() {
    progress_bar_ = std::make_unique<QProgressBar>();
    progress_bar_->setVisible(false);
    progress_bar_->setTextVisible(true);
}

// GracefulDegradationManager implementation
GracefulDegradationManager::GracefulDegradationManager(QObject* parent)
    : QObject(parent) {
}

void GracefulDegradationManager::enableDegradation(const QString& feature_name, DegradationLevel level, const QString& reason) {
    QWriteLocker locker(&degradation_lock_);

    FeatureDegradation degradation;
    degradation.level = level;
    degradation.reason = reason;
    degradation.timestamp = QDateTime::currentDateTime();

    degraded_features_[feature_name] = degradation;

    // Update global degradation level if necessary
    if (level > global_level_) {
        global_level_ = level;
    }

    qDebug() << "🔧 Feature degradation enabled:" << feature_name << "level:" << static_cast<int>(level) << "reason:" << reason;

    emit degradationEnabled(feature_name, level, reason);
}

void GracefulDegradationManager::disableDegradation(const QString& feature_name) {
    QWriteLocker locker(&degradation_lock_);

    auto it = degraded_features_.find(feature_name);
    if (it != degraded_features_.end()) {
        degraded_features_.erase(it);

        // Recalculate global degradation level
        global_level_ = None;
        for (const auto& [name, degradation] : degraded_features_) {
            if (degradation.level > global_level_) {
                global_level_ = degradation.level;
            }
        }

        qDebug() << "🔧 Feature degradation disabled:" << feature_name;

        emit degradationDisabled(feature_name);
    }
}

void GracefulDegradationManager::setGlobalDegradationLevel(DegradationLevel level) {
    QWriteLocker locker(&degradation_lock_);
    global_level_ = level;

    qDebug() << "🔧 Global degradation level set to:" << static_cast<int>(level);
}

GracefulDegradationManager::DegradationLevel GracefulDegradationManager::getCurrentDegradationLevel() const {
    QReadLocker locker(&degradation_lock_);
    return global_level_;
}

bool GracefulDegradationManager::isFeatureEnabled(const QString& feature_name) const {
    QReadLocker locker(&degradation_lock_);

    auto it = degraded_features_.find(feature_name);
    if (it != degraded_features_.end()) {
        // Feature is degraded, check if it's completely disabled
        return it->second.level < Severe;
    }

    // Feature is not degraded, check global level
    return global_level_ < SafeMode;
}

QStringList GracefulDegradationManager::getDisabledFeatures() const {
    QReadLocker locker(&degradation_lock_);

    QStringList disabled_features;
    for (const auto& [name, degradation] : degraded_features_) {
        if (degradation.level >= Severe) {
            disabled_features.append(name);
        }
    }

    return disabled_features;
}

QStringList GracefulDegradationManager::getDegradedFeatures() const {
    QReadLocker locker(&degradation_lock_);

    QStringList degraded_features;
    for (const auto& [name, degradation] : degraded_features_) {
        if (degradation.level > None && degradation.level < Severe) {
            degraded_features.append(name);
        }
    }

    return degraded_features;
}

void GracefulDegradationManager::enterSafeMode(const QString& reason) {
    QWriteLocker locker(&degradation_lock_);

    if (!safe_mode_active_) {
        safe_mode_active_ = true;
        safe_mode_reason_ = reason;
        global_level_ = SafeMode;

        qWarning() << "🚨 Entering safe mode:" << reason;

        emit safeModeEntered(reason);
    }
}

void GracefulDegradationManager::exitSafeMode() {
    QWriteLocker locker(&degradation_lock_);

    if (safe_mode_active_) {
        safe_mode_active_ = false;
        safe_mode_reason_.clear();

        // Recalculate global degradation level
        global_level_ = None;
        for (const auto& [name, degradation] : degraded_features_) {
            if (degradation.level > global_level_) {
                global_level_ = degradation.level;
            }
        }

        qDebug() << "🚨 Exiting safe mode";

        emit safeModeExited();
    }
}

bool GracefulDegradationManager::isInSafeMode() const {
    QReadLocker locker(&degradation_lock_);
    return safe_mode_active_;
}

// EnhancedErrorRecoverySystem implementation
EnhancedErrorRecoverySystem::EnhancedErrorRecoverySystem(QObject* parent)
    : QObject(parent) {

    // Initialize default configuration
    config_ = ErrorRecoveryConfig{};

    // Setup timers
    setupTimers();

    // Initialize built-in recovery strategies
    initializeBuiltinStrategies();

    qDebug() << "🔧 Enhanced Error Recovery System initialized";
}

void EnhancedErrorRecoverySystem::setConfiguration(const ErrorRecoveryConfig& config) {
    QWriteLocker locker(&config_lock_);
    config_ = config;

    qDebug() << "🔧 Error recovery configuration updated";
}

ErrorRecoveryConfig EnhancedErrorRecoverySystem::getConfiguration() const {
    QReadLocker locker(&config_lock_);
    return config_;
}

void EnhancedErrorRecoverySystem::setDiagnosticsEngine(DiagnosticsEngine* engine) {
    diagnostics_engine_ = engine;

    if (diagnostics_engine_) {
        // Connect to diagnostics engine signals
        connect(diagnostics_engine_, &DiagnosticsEngine::diagnosticReported,
                this, &EnhancedErrorRecoverySystem::onDiagnosticReported);
        connect(diagnostics_engine_, &DiagnosticsEngine::criticalErrorDetected,
                this, &EnhancedErrorRecoverySystem::onCriticalErrorDetected);

        qDebug() << "🔧 Diagnostics engine connected to error recovery system";
    }
}

void EnhancedErrorRecoverySystem::setNotificationManager(ErrorNotificationManager* manager) {
    notification_manager_ = manager;
    qDebug() << "🔧 Notification manager connected to error recovery system";
}

void EnhancedErrorRecoverySystem::setDegradationManager(GracefulDegradationManager* manager) {
    degradation_manager_ = manager;
    qDebug() << "🔧 Degradation manager connected to error recovery system";
}

void EnhancedErrorRecoverySystem::registerRecoveryStrategy(const AdvancedRecoveryStrategy& strategy) {
    QWriteLocker locker(&strategies_lock_);

    // Remove existing strategy with same name
    auto it = std::find_if(recovery_strategies_.begin(), recovery_strategies_.end(),
                          [&strategy](const AdvancedRecoveryStrategy& s) {
                              return s.name == strategy.name;
                          });

    if (it != recovery_strategies_.end()) {
        recovery_strategies_.erase(it);
    }

    recovery_strategies_.push_back(strategy);

    // Sort strategies by priority (higher priority first)
    std::sort(recovery_strategies_.begin(), recovery_strategies_.end(),
              [](const AdvancedRecoveryStrategy& a, const AdvancedRecoveryStrategy& b) {
                  return a.priority > b.priority;
              });

    qDebug() << "🔧 Recovery strategy registered:" << strategy.name << "priority:" << strategy.priority;
}

void EnhancedErrorRecoverySystem::unregisterRecoveryStrategy(const QString& strategy_name) {
    QWriteLocker locker(&strategies_lock_);

    auto it = std::find_if(recovery_strategies_.begin(), recovery_strategies_.end(),
                          [&strategy_name](const AdvancedRecoveryStrategy& s) {
                              return s.name == strategy_name;
                          });

    if (it != recovery_strategies_.end()) {
        recovery_strategies_.erase(it);
        qDebug() << "🔧 Recovery strategy unregistered:" << strategy_name;
    }
}

void EnhancedErrorRecoverySystem::enableRecoveryStrategy(const QString& strategy_name, bool enabled) {
    QWriteLocker locker(&strategies_lock_);

    auto it = std::find_if(recovery_strategies_.begin(), recovery_strategies_.end(),
                          [&strategy_name](AdvancedRecoveryStrategy& s) {
                              return s.name == strategy_name;
                          });

    if (it != recovery_strategies_.end()) {
        it->enabled = enabled;
        qDebug() << "🔧 Recovery strategy" << strategy_name << (enabled ? "enabled" : "disabled");
    }
}

QStringList EnhancedErrorRecoverySystem::getAvailableStrategies() const {
    QReadLocker locker(&strategies_lock_);

    QStringList strategies;
    for (const auto& strategy : recovery_strategies_) {
        strategies.append(strategy.name);
    }

    return strategies;
}

QStringList EnhancedErrorRecoverySystem::getEnabledStrategies() const {
    QReadLocker locker(&strategies_lock_);

    QStringList strategies;
    for (const auto& strategy : recovery_strategies_) {
        if (strategy.enabled) {
            strategies.append(strategy.name);
        }
    }

    return strategies;
}

QFuture<RecoveryResult> EnhancedErrorRecoverySystem::attemptRecovery(const QString& diagnostic_id) {
    return QtConcurrent::run([this, diagnostic_id]() -> RecoveryResult {
        if (!diagnostics_engine_) {
            return RecoveryResult::createFailure(generateOperationId(), "Diagnostics engine not available");
        }

        // Find the diagnostic
        auto diagnostics = diagnostics_engine_->getAllDiagnostics();
        auto it = std::find_if(diagnostics.begin(), diagnostics.end(),
                              [&diagnostic_id](const DiagnosticInfo& d) {
                                  return d.id == diagnostic_id;
                              });

        if (it == diagnostics.end()) {
            return RecoveryResult::createFailure(generateOperationId(), "Diagnostic not found");
        }

        const DiagnosticInfo& diagnostic = *it;

        // Check if recovery should be attempted
        if (!shouldAttemptRecovery(diagnostic)) {
            return RecoveryResult::createFailure(generateOperationId(), "Recovery not recommended for this diagnostic");
        }

        // Select recovery strategies
        auto strategies = selectRecoveryStrategies(diagnostic);
        if (strategies.isEmpty()) {
            return RecoveryResult::createFailure(generateOperationId(), "No suitable recovery strategies found");
        }

        QString operation_id = generateOperationId();

        // Track active recovery
        {
            QMutexLocker locker(&active_recoveries_lock_);
            ActiveRecovery recovery;
            recovery.operation_id = operation_id;
            recovery.diagnostic_id = diagnostic_id;
            recovery.strategy_name = strategies.first().name;
            recovery.start_time = QDateTime::currentDateTime();
            active_recoveries_.emplace(operation_id, std::move(recovery));
        }

        emit recoveryStarted(operation_id, diagnostic.id, strategies.first().name);

        if (notification_manager_) {
            notification_manager_->notifyRecoveryStarted(strategies.first().name, diagnostic.id);
            notification_manager_->showProgressDialog("Recovery in Progress",
                                                     QString("Attempting to recover from: %1").arg(diagnostic.title),
                                                     100);
        }

        // Attempt recovery with each strategy
        RecoveryResult final_result;
        for (const auto& strategy : strategies) {
            if (!strategy.enabled) {
                continue;
            }

            updateRecoveryProgress(operation_id, 25, QString("Trying strategy: %1").arg(strategy.name));

            RecoveryResult result = executeRecoveryStrategy(strategy, diagnostic);

            if (result.isSuccess()) {
                final_result = result;
                final_result.operation_id = operation_id;
                break;
            } else {
                // Log failed attempt
                qWarning() << "🔧 Recovery strategy failed:" << strategy.name << result.error_message;
            }
        }

        // Finalize recovery
        finalizeRecovery(operation_id, final_result);

        if (notification_manager_) {
            notification_manager_->notifyRecoveryCompleted(final_result);
        }

        return final_result;
    });
}

QFuture<RecoveryResult> EnhancedErrorRecoverySystem::attemptRecoveryForFile(const QString& file_path) {
    return QtConcurrent::run([this, file_path]() -> RecoveryResult {
        if (!diagnostics_engine_) {
            return RecoveryResult::createFailure(generateOperationId(), "Diagnostics engine not available");
        }

        // Find diagnostics for the file
        auto diagnostics = diagnostics_engine_->getDiagnosticsByFile(file_path);
        if (diagnostics.isEmpty()) {
            return RecoveryResult::createFailure(generateOperationId(), "No diagnostics found for file");
        }

        // Attempt recovery for the most severe diagnostic
        std::sort(diagnostics.begin(), diagnostics.end(),
                  [](const DiagnosticInfo& a, const DiagnosticInfo& b) {
                      return a.severity > b.severity;
                  });

        return attemptRecovery(diagnostics.first().id).result();
    });
}

QString EnhancedErrorRecoverySystem::generateOperationId() const {
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

void EnhancedErrorRecoverySystem::setupTimers() {
    recovery_timer_ = std::make_unique<QTimer>(this);
    recovery_timer_->setSingleShot(false);
    recovery_timer_->setInterval(1000); // Check every second
    connect(recovery_timer_.get(), &QTimer::timeout, this, &EnhancedErrorRecoverySystem::onRecoveryTimerTimeout);

    progress_timer_ = std::make_unique<QTimer>(this);
    progress_timer_->setSingleShot(false);
    progress_timer_->setInterval(500); // Update progress every 500ms
    connect(progress_timer_.get(), &QTimer::timeout, this, &EnhancedErrorRecoverySystem::onProgressUpdate);

    cleanup_timer_ = std::make_unique<QTimer>(this);
    cleanup_timer_->setSingleShot(false);
    cleanup_timer_->setInterval(60000); // Cleanup every minute
    connect(cleanup_timer_.get(), &QTimer::timeout, this, &EnhancedErrorRecoverySystem::cleanupCompletedRecoveries);

    recovery_timer_->start();
    progress_timer_->start();
    cleanup_timer_->start();
}

} // namespace DeclarativeUI::HotReload
