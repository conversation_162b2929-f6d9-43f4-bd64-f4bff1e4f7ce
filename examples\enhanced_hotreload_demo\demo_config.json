{"version": "1.0", "active_profile": "development", "profiles": [{"name": "development", "watch_interval": 100, "enable_debug_logging": true, "auto_reload": true, "debounce_delay": 500, "max_reload_attempts": 3, "backup_enabled": true, "watched_directories": ["src/", "examples/", "docs/"], "file_extensions": [".cpp", ".hpp", ".h", ".c", ".qml", ".js", ".css", ".scss", ".json"]}, {"name": "testing", "watch_interval": 500, "enable_debug_logging": false, "auto_reload": true, "debounce_delay": 1000, "max_reload_attempts": 2, "backup_enabled": false, "watched_directories": ["src/", "tests/"], "file_extensions": [".cpp", ".hpp", ".h"]}, {"name": "production", "watch_interval": 2000, "enable_debug_logging": false, "auto_reload": false, "debounce_delay": 2000, "max_reload_attempts": 1, "backup_enabled": true, "watched_directories": ["src/"], "file_extensions": [".cpp", ".hpp"]}], "filter_rules": [{"pattern": "build/**/*", "match_type": "glob", "type": "exclude", "priority": 100, "description": "Exclude all build artifacts"}, {"pattern": "*.{tmp,bak,cache,log}", "match_type": "glob", "type": "exclude", "priority": 90, "description": "Exclude temporary and backup files"}, {"pattern": ".git/**/*", "match_type": "glob", "type": "exclude", "priority": 95, "description": "Exclude git repository files"}, {"match_type": "size", "type": "exclude", "min_size_bytes": 10485760, "max_size_bytes": 9223372036854775807, "priority": 80, "description": "Exclude files larger than 10MB"}, {"pattern": "*.{cpp,hpp,h,c,qml,js,css,scss,json,md}", "match_type": "glob", "type": "include", "priority": 50, "description": "Include source and documentation files"}], "dashboard_settings": {"update_interval": 1000, "max_history_size": 1000, "enable_real_time_updates": true, "theme": "light", "performance_thresholds": {"cpu_threshold": 0.8, "memory_threshold": 104857600, "reload_time_threshold": 5000}}, "error_recovery": {"enable_auto_recovery": true, "max_recovery_attempts": 3, "recovery_delay": 1000, "notification_level": "warning", "enable_popup_notifications": true, "enable_status_bar_notifications": true}, "selective_reload": {"enable_css_live_injection": true, "enable_resource_hot_swapping": true, "enable_dependency_tracking": true, "rollback_on_failure": true, "batch_reload_delay": 200}}