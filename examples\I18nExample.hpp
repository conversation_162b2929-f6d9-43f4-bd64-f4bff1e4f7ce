#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QDateEdit>
#include <QSpinBox>
#include <QGroupBox>
#include <QFormLayout>

#ifdef DECLARATIVE_UI_I18N_ENABLED
#include "../src/I18n/I18nManager.hpp"
#endif

/**
 * @brief Example widget demonstrating internationalization features
 * 
 * This example showcases:
 * - Dynamic language switching
 * - Text translation
 * - Number and currency formatting
 * - Date/time formatting
 * - RTL language support
 * - Pluralization
 */
class I18nExample : public QWidget {
    Q_OBJECT

public:
    explicit I18nExample(QWidget* parent = nullptr);

private slots:
    void onLanguageChanged();
    void onUpdateValues();
    void onToggleRTL();

private:
    void setupUI();
    void updateTranslations();
    void updateFormatting();
    void connectSignals();

    // UI Components
    QVBoxLayout* main_layout_;
    QHBoxLayout* controls_layout_;
    
    // Language controls
    QGroupBox* language_group_;
    QComboBox* language_combo_;
    QPushButton* rtl_toggle_button_;
    
    // Translation examples
    QGroupBox* translation_group_;
    QLabel* app_title_label_;
    QLabel* description_label_;
    QLabel* status_label_;
    QPushButton* action_button_;
    
    // Formatting examples
    QGroupBox* formatting_group_;
    QFormLayout* formatting_layout_;
    QLabel* number_label_;
    QLabel* currency_label_;
    QLabel* date_label_;
    QLabel* time_label_;
    QLabel* plural_label_;
    
    // Input controls
    QGroupBox* input_group_;
    QSpinBox* count_spinbox_;
    QLineEdit* amount_input_;
    QDateEdit* date_input_;
    
    // Text area for testing
    QTextEdit* text_area_;
    
    // Current values
    double current_amount_;
    int current_count_;
    
#ifdef DECLARATIVE_UI_I18N_ENABLED
    DeclarativeUI::I18n::I18nManager& i18n_manager_;
#endif
};

/**
 * @brief Helper class for demonstrating i18n integration in custom components
 */
class I18nAwareButton : public QPushButton {
    Q_OBJECT

public:
    explicit I18nAwareButton(const QString& translation_key, QWidget* parent = nullptr);
    
    void setTranslationKey(const QString& key);
    QString getTranslationKey() const { return translation_key_; }

private slots:
    void updateText();

private:
    QString translation_key_;
    
#ifdef DECLARATIVE_UI_I18N_ENABLED
    DeclarativeUI::I18n::I18nManager& i18n_manager_;
#endif
};

/**
 * @brief Helper class for demonstrating locale-aware formatting
 */
class LocaleAwareLabel : public QLabel {
    Q_OBJECT

public:
    enum class FormatType {
        Number,
        Currency,
        Date,
        Time,
        DateTime
    };

    explicit LocaleAwareLabel(FormatType type, QWidget* parent = nullptr);
    
    void setValue(const QVariant& value);
    void setCurrencyCode(const QString& code) { currency_code_ = code; updateDisplay(); }

private slots:
    void updateDisplay();

private:
    FormatType format_type_;
    QVariant current_value_;
    QString currency_code_;
    
#ifdef DECLARATIVE_UI_I18N_ENABLED
    DeclarativeUI::I18n::I18nManager& i18n_manager_;
#endif
};

/**
 * @brief Utility functions for i18n integration
 */
namespace I18nUtils {
    
    /**
     * @brief Apply internationalization to a widget and all its children
     */
    void internationalizeWidget(QWidget* widget);
    
    /**
     * @brief Create a language selector combo box
     */
    QComboBox* createLanguageSelector(QWidget* parent = nullptr);
    
    /**
     * @brief Create an RTL toggle button
     */
    QPushButton* createRTLToggle(QWidget* parent = nullptr);
    
    /**
     * @brief Get localized language name
     */
    QString getLocalizedLanguageName(const QString& language_code);
    
    /**
     * @brief Format a number according to current locale
     */
    QString formatNumber(double number);
    
    /**
     * @brief Format currency according to current locale
     */
    QString formatCurrency(double amount, const QString& currency_code = QString());
    
    /**
     * @brief Format date according to current locale
     */
    QString formatDate(const QDate& date);
    
    /**
     * @brief Get translated text with fallback
     */
    QString translate(const QString& key, const QString& fallback = QString());
    
    /**
     * @brief Get pluralized text
     */
    QString translatePlural(const QString& key, int count);
}

#include "I18nExample.moc"
