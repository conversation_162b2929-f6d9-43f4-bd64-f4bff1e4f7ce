// Additional implementation methods for EnhancedErrorRecoverySystem
#include "EnhancedErrorRecoverySystem.hpp"
#include <QDebug>
#include <QFileInfo>
#include <QDir>
#include <QElapsedTimer>

namespace DeclarativeUI::HotReload {

void EnhancedErrorRecoverySystem::initializeBuiltinStrategies() {
    // Rollback Recovery Strategy
    AdvancedRecoveryStrategy rollback_strategy("Rollback Recovery", 
                                              "Restore previous working state from rollback point", 
                                              "rollback");
    rollback_strategy.priority = 90;
    rollback_strategy.condition_check = [](const DiagnosticInfo& diagnostic) {
        return diagnostic.auto_recoverable && !diagnostic.recovery_action.isEmpty();
    };
    rollback_strategy.recovery_action = [this](const DiagnosticInfo& diagnostic) -> RecoveryResult {
        QString operation_id = generateOperationId();
        RecoveryResult result = RecoveryResult::createSuccess(operation_id, "Rollback Recovery");
        result.target_id = diagnostic.id;
        result.actions_taken.append("Restored from rollback point");
        
        // Simulate rollback operation
        QThread::msleep(500);
        
        return result;
    };
    registerRecoveryStrategy(rollback_strategy);
    
    // File Restoration Strategy
    AdvancedRecoveryStrategy file_restore_strategy("File Restoration", 
                                                  "Restore file from backup or version control", 
                                                  "restoration");
    file_restore_strategy.priority = 80;
    file_restore_strategy.condition_check = [](const DiagnosticInfo& diagnostic) {
        return !diagnostic.file_path.isEmpty() && QFileInfo::exists(diagnostic.file_path);
    };
    file_restore_strategy.recovery_action = [this](const DiagnosticInfo& diagnostic) -> RecoveryResult {
        QString operation_id = generateOperationId();
        RecoveryResult result = RecoveryResult::createSuccess(operation_id, "File Restoration");
        result.target_id = diagnostic.file_path;
        result.actions_taken.append(QString("Restored file: %1").arg(diagnostic.file_path));
        
        // Simulate file restoration
        QThread::msleep(1000);
        
        return result;
    };
    registerRecoveryStrategy(file_restore_strategy);
    
    // Component Isolation Strategy
    AdvancedRecoveryStrategy isolation_strategy("Component Isolation", 
                                               "Isolate problematic component to prevent cascading failures", 
                                               "isolation");
    isolation_strategy.priority = 70;
    isolation_strategy.condition_check = [](const DiagnosticInfo& diagnostic) {
        return diagnostic.category == DiagnosticInfo::Compilation || 
               diagnostic.category == DiagnosticInfo::Validation;
    };
    isolation_strategy.recovery_action = [this](const DiagnosticInfo& diagnostic) -> RecoveryResult {
        QString operation_id = generateOperationId();
        RecoveryResult result = RecoveryResult::createSuccess(operation_id, "Component Isolation");
        result.target_id = diagnostic.id;
        result.actions_taken.append("Isolated problematic component");
        
        // Trigger graceful degradation
        if (degradation_manager_) {
            QString feature_name = QString("Component_%1").arg(diagnostic.id);
            degradation_manager_->enableDegradation(feature_name, 
                                                   GracefulDegradationManager::Moderate, 
                                                   diagnostic.description);
        }
        
        return result;
    };
    registerRecoveryStrategy(isolation_strategy);
    
    // Cache Clearing Strategy
    AdvancedRecoveryStrategy cache_clear_strategy("Cache Clearing", 
                                                 "Clear caches and temporary data", 
                                                 "cache");
    cache_clear_strategy.priority = 60;
    cache_clear_strategy.condition_check = [](const DiagnosticInfo& diagnostic) {
        return diagnostic.category == DiagnosticInfo::Memory || 
               diagnostic.category == DiagnosticInfo::Performance;
    };
    cache_clear_strategy.recovery_action = [this](const DiagnosticInfo& diagnostic) -> RecoveryResult {
        QString operation_id = generateOperationId();
        RecoveryResult result = RecoveryResult::createSuccess(operation_id, "Cache Clearing");
        result.target_id = diagnostic.id;
        result.actions_taken.append("Cleared application caches");
        result.actions_taken.append("Freed memory resources");
        
        // Simulate cache clearing
        QThread::msleep(300);
        
        return result;
    };
    registerRecoveryStrategy(cache_clear_strategy);
    
    // Safe Mode Fallback Strategy
    AdvancedRecoveryStrategy safe_mode_strategy("Safe Mode Fallback", 
                                               "Enter safe mode with minimal functionality", 
                                               "degradation");
    safe_mode_strategy.priority = 10; // Lowest priority - last resort
    safe_mode_strategy.condition_check = [](const DiagnosticInfo& diagnostic) {
        return diagnostic.severity >= DiagnosticInfo::Critical;
    };
    safe_mode_strategy.recovery_action = [this](const DiagnosticInfo& diagnostic) -> RecoveryResult {
        QString operation_id = generateOperationId();
        RecoveryResult result = RecoveryResult::createSuccess(operation_id, "Safe Mode Fallback");
        result.target_id = diagnostic.id;
        result.actions_taken.append("Entered safe mode");
        
        // Enter safe mode
        if (degradation_manager_) {
            degradation_manager_->enterSafeMode(diagnostic.description);
        }
        
        return result;
    };
    registerRecoveryStrategy(safe_mode_strategy);
    
    qDebug() << "🔧 Built-in recovery strategies initialized:" << recovery_strategies_.size() << "strategies";
}

bool EnhancedErrorRecoverySystem::shouldAttemptRecovery(const DiagnosticInfo& diagnostic) const {
    QReadLocker locker(&config_lock_);
    
    if (!config_.auto_recovery_enabled) {
        return false;
    }
    
    // Don't attempt recovery if already attempted too many times
    if (diagnostic.recovery_attempts >= config_.max_recovery_attempts) {
        return false;
    }
    
    // Don't attempt recovery for info-level diagnostics
    if (diagnostic.severity == DiagnosticInfo::Info) {
        return false;
    }
    
    return true;
}

QList<AdvancedRecoveryStrategy> EnhancedErrorRecoverySystem::selectRecoveryStrategies(const DiagnosticInfo& diagnostic) const {
    QReadLocker locker(&strategies_lock_);
    
    QList<AdvancedRecoveryStrategy> selected_strategies;
    
    for (const auto& strategy : recovery_strategies_) {
        if (!strategy.enabled) {
            continue;
        }
        
        if (strategy.condition_check && strategy.condition_check(diagnostic)) {
            selected_strategies.append(strategy);
        }
    }
    
    // Sort by priority (higher priority first)
    std::sort(selected_strategies.begin(), selected_strategies.end(),
              [](const AdvancedRecoveryStrategy& a, const AdvancedRecoveryStrategy& b) {
                  return a.priority > b.priority;
              });
    
    return selected_strategies;
}

RecoveryResult EnhancedErrorRecoverySystem::executeRecoveryStrategy(const AdvancedRecoveryStrategy& strategy, const DiagnosticInfo& diagnostic) {
    QElapsedTimer timer;
    timer.start();
    
    try {
        // Pre-recovery check
        if (strategy.pre_recovery_check && !strategy.pre_recovery_check(diagnostic)) {
            return RecoveryResult::createFailure(generateOperationId(), "Pre-recovery check failed");
        }
        
        // Execute recovery action
        RecoveryResult result;
        if (strategy.recovery_action) {
            result = strategy.recovery_action(diagnostic);
        } else {
            result = RecoveryResult::createFailure(generateOperationId(), "No recovery action defined");
        }
        
        result.duration_ms = timer.elapsed();
        
        // Post-recovery validation
        if (result.isSuccess() && strategy.post_recovery_validation && !strategy.post_recovery_validation(diagnostic)) {
            result.status = RecoveryResult::Failed;
            result.error_message = "Post-recovery validation failed";
        }
        
        return result;
        
    } catch (const std::exception& e) {
        RecoveryResult result = RecoveryResult::createFailure(generateOperationId(), QString("Exception: %1").arg(e.what()));
        result.duration_ms = timer.elapsed();
        return result;
    } catch (...) {
        RecoveryResult result = RecoveryResult::createFailure(generateOperationId(), "Unknown exception occurred");
        result.duration_ms = timer.elapsed();
        return result;
    }
}

void EnhancedErrorRecoverySystem::updateRecoveryProgress(const QString& operation_id, int progress, const QString& status) {
    QMutexLocker locker(&active_recoveries_lock_);
    
    auto it = active_recoveries_.find(operation_id);
    if (it != active_recoveries_.end()) {
        it->second.progress = progress;
        
        emit recoveryProgress(operation_id, progress, status);
        
        if (notification_manager_) {
            notification_manager_->notifyRecoveryProgress(operation_id, progress, status);
        }
    }
}

void EnhancedErrorRecoverySystem::finalizeRecovery(const QString& operation_id, const RecoveryResult& result) {
    // Remove from active recoveries
    {
        QMutexLocker locker(&active_recoveries_lock_);
        active_recoveries_.erase(operation_id);
    }
    
    // Record result in history
    recordRecoveryResult(result);
    
    // Emit completion signal
    if (result.isSuccess()) {
        emit recoveryCompleted(result);
    } else {
        emit recoveryFailed(operation_id, result.error_message);
    }
    
    emit recoveryStatisticsUpdated();
}

void EnhancedErrorRecoverySystem::recordRecoveryResult(const RecoveryResult& result) {
    QWriteLocker locker(&history_lock_);
    
    recovery_history_.push_back(result);
    
    // Keep only recent history (last 1000 entries)
    while (recovery_history_.size() > 1000) {
        recovery_history_.pop_front();
    }
}

void EnhancedErrorRecoverySystem::cleanupCompletedRecoveries() {
    QMutexLocker locker(&active_recoveries_lock_);
    
    auto it = active_recoveries_.begin();
    while (it != active_recoveries_.end()) {
        // Remove recoveries that have been running for too long
        if (it->second.start_time.secsTo(QDateTime::currentDateTime()) > 300) { // 5 minutes
            qWarning() << "🔧 Cleaning up stale recovery operation:" << it->first;
            it = active_recoveries_.erase(it);
        } else {
            ++it;
        }
    }
}

void EnhancedErrorRecoverySystem::onDiagnosticReported(const DiagnosticInfo& diagnostic) {
    if (shouldAttemptRecovery(diagnostic)) {
        // Attempt automatic recovery
        auto future = attemptRecovery(diagnostic.id);
        Q_UNUSED(future) // Recovery runs asynchronously
    }
}

void EnhancedErrorRecoverySystem::onCriticalErrorDetected(const DiagnosticInfo& diagnostic) {
    emit criticalErrorRequiresAttention(diagnostic);
    
    if (notification_manager_) {
        notification_manager_->notifyErrorDetected(diagnostic);
    }
    
    // Trigger graceful degradation for critical errors
    triggerGracefulDegradation(diagnostic);
}

void EnhancedErrorRecoverySystem::triggerGracefulDegradation(const DiagnosticInfo& diagnostic) {
    if (!degradation_manager_) {
        return;
    }
    
    QReadLocker locker(&config_lock_);
    if (!config_.graceful_degradation_enabled) {
        return;
    }
    
    QString feature_name = QString("Feature_%1").arg(diagnostic.id);
    GracefulDegradationManager::DegradationLevel level = GracefulDegradationManager::Minimal;
    
    switch (diagnostic.severity) {
        case DiagnosticInfo::Warning:
            level = GracefulDegradationManager::Minimal;
            break;
        case DiagnosticInfo::Error:
            level = GracefulDegradationManager::Moderate;
            break;
        case DiagnosticInfo::Critical:
            level = GracefulDegradationManager::Severe;
            break;
        default:
            return; // No degradation for info level
    }
    
    degradation_manager_->enableDegradation(feature_name, level, diagnostic.description);
    
    emit gracefulDegradationTriggered(feature_name, diagnostic.description);
    
    if (notification_manager_) {
        notification_manager_->notifyGracefulDegradation(feature_name, diagnostic.description);
    }
}

void EnhancedErrorRecoverySystem::onRecoveryTimerTimeout() {
    // Check for recovery timeouts
    QMutexLocker locker(&active_recoveries_lock_);
    
    QReadLocker config_locker(&config_lock_);
    int timeout_ms = config_.recovery_timeout_ms;
    config_locker.unlock();
    
    auto it = active_recoveries_.begin();
    while (it != active_recoveries_.end()) {
        if (it->second.start_time.msecsTo(QDateTime::currentDateTime()) > timeout_ms) {
            handleRecoveryTimeout(it->first);
            it = active_recoveries_.erase(it);
        } else {
            ++it;
        }
    }
}

void EnhancedErrorRecoverySystem::handleRecoveryTimeout(const QString& operation_id) {
    qWarning() << "🔧 Recovery operation timed out:" << operation_id;
    
    RecoveryResult result = RecoveryResult::createFailure(operation_id, "Recovery operation timed out");
    result.status = RecoveryResult::Timeout;
    
    recordRecoveryResult(result);
    emit recoveryFailed(operation_id, "Recovery operation timed out");
    
    if (notification_manager_) {
        notification_manager_->notifyRecoveryCompleted(result);
    }
}

void EnhancedErrorRecoverySystem::onProgressUpdate() {
    // Update progress for all active recoveries
    QMutexLocker locker(&active_recoveries_lock_);
    
    for (const auto& [operation_id, recovery] : active_recoveries_) {
        int progress = recovery.progress.load();
        if (progress > 0) {
            emit recoveryProgress(operation_id, progress, "In progress...");
        }
    }
}

} // namespace DeclarativeUI::HotReload
