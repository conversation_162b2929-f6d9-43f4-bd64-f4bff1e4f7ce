#include "ValidationEngine.hpp"
#include <QDebug>
#include <QRegularExpression>
#include <QUrl>

namespace DeclarativeUI {
namespace DataBinding {

// BuiltinValidators implementations

ValidationResult BuiltinValidators::RequiredValidator::validate(const QVariant& value, const QString& field_name) const {
    if (!value.isValid() || value.isNull()) {
        return ValidationResult::error(QString("%1 is required").arg(field_name.isEmpty() ? "Field" : field_name), field_name);
    }
    
    QString str_value = value.toString().trimmed();
    if (str_value.isEmpty()) {
        return ValidationResult::error(QString("%1 cannot be empty").arg(field_name.isEmpty() ? "Field" : field_name), field_name);
    }
    
    return ValidationResult::success(value);
}

ValidationResult BuiltinValidators::LengthValidator::validate(const QVariant& value, const QString& field_name) const {
    QString str_value = value.toString();
    int length = str_value.length();
    
    if (min_length_ > 0 && length < min_length_) {
        return ValidationResult::error(
            QString("%1 must be at least %2 characters long").arg(field_name.isEmpty() ? "Field" : field_name).arg(min_length_),
            field_name
        );
    }
    
    if (max_length_ >= 0 && length > max_length_) {
        return ValidationResult::error(
            QString("%1 must be no more than %2 characters long").arg(field_name.isEmpty() ? "Field" : field_name).arg(max_length_),
            field_name
        );
    }
    
    return ValidationResult::success(value);
}

QString BuiltinValidators::LengthValidator::getDescription() const {
    if (min_length_ > 0 && max_length_ >= 0) {
        return QString("Length must be between %1 and %2 characters").arg(min_length_).arg(max_length_);
    } else if (min_length_ > 0) {
        return QString("Minimum length: %1 characters").arg(min_length_);
    } else if (max_length_ >= 0) {
        return QString("Maximum length: %1 characters").arg(max_length_);
    }
    return "Length validation";
}

ValidationResult BuiltinValidators::RegexValidator::validate(const QVariant& value, const QString& field_name) const {
    QString str_value = value.toString();
    
    if (!regex_.match(str_value).hasMatch()) {
        return ValidationResult::error(
            QString("%1 format is invalid").arg(field_name.isEmpty() ? "Field" : field_name),
            field_name
        );
    }
    
    return ValidationResult::success(value);
}

ValidationResult BuiltinValidators::EmailValidator::validate(const QVariant& value, const QString& field_name) const {
    QString email = value.toString().trimmed();
    
    // Basic email regex pattern
    static const QRegularExpression email_regex(
        R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)"
    );
    
    if (!email_regex.match(email).hasMatch()) {
        return ValidationResult::error(
            QString("%1 must be a valid email address").arg(field_name.isEmpty() ? "Email" : field_name),
            field_name
        );
    }
    
    return ValidationResult::success(value);
}

ValidationResult BuiltinValidators::UrlValidator::validate(const QVariant& value, const QString& field_name) const {
    QString url_string = value.toString().trimmed();
    QUrl url(url_string);
    
    if (!url.isValid() || url.scheme().isEmpty()) {
        return ValidationResult::error(
            QString("%1 must be a valid URL").arg(field_name.isEmpty() ? "URL" : field_name),
            field_name
        );
    }
    
    return ValidationResult::success(value);
}

ValidationResult BuiltinValidators::RangeValidator::validate(const QVariant& value, const QString& field_name) const {
    bool ok;
    double num_value = value.toDouble(&ok);
    
    if (!ok) {
        return ValidationResult::error(
            QString("%1 must be a valid number").arg(field_name.isEmpty() ? "Field" : field_name),
            field_name
        );
    }
    
    if (num_value < min_value_ || num_value > max_value_) {
        return ValidationResult::error(
            QString("%1 must be between %2 and %3").arg(field_name.isEmpty() ? "Value" : field_name).arg(min_value_).arg(max_value_),
            field_name
        );
    }
    
    return ValidationResult::success(value);
}

QString BuiltinValidators::RangeValidator::getDescription() const {
    return QString("Value must be between %1 and %2").arg(min_value_).arg(max_value_);
}

ValidationResult BuiltinValidators::DateValidator::validate(const QVariant& value, const QString& field_name) const {
    QDate date = value.toDate();
    
    if (!date.isValid()) {
        return ValidationResult::error(
            QString("%1 must be a valid date").arg(field_name.isEmpty() ? "Date" : field_name),
            field_name
        );
    }
    
    if (min_date_.isValid() && date < min_date_) {
        return ValidationResult::error(
            QString("%1 must be on or after %2").arg(field_name.isEmpty() ? "Date" : field_name).arg(min_date_.toString()),
            field_name
        );
    }
    
    if (max_date_.isValid() && date > max_date_) {
        return ValidationResult::error(
            QString("%1 must be on or before %2").arg(field_name.isEmpty() ? "Date" : field_name).arg(max_date_.toString()),
            field_name
        );
    }
    
    return ValidationResult::success(value);
}

QString BuiltinValidators::DateValidator::getDescription() const {
    if (min_date_.isValid() && max_date_.isValid()) {
        return QString("Date must be between %1 and %2").arg(min_date_.toString()).arg(max_date_.toString());
    } else if (min_date_.isValid()) {
        return QString("Date must be on or after %1").arg(min_date_.toString());
    } else if (max_date_.isValid()) {
        return QString("Date must be on or before %1").arg(max_date_.toString());
    }
    return "Valid date required";
}

ValidationResult BuiltinValidators::ComparisonValidator::validate(const QVariant& value, const QString& field_name) const {
    bool result = false;
    QString operation = getComparisonTypeString();
    
    switch (comparison_type_) {
        case ComparisonType::Equal:
            result = (value == compare_value_);
            break;
        case ComparisonType::NotEqual:
            result = (value != compare_value_);
            break;
        case ComparisonType::GreaterThan:
            result = (value.toDouble() > compare_value_.toDouble());
            break;
        case ComparisonType::GreaterThanOrEqual:
            result = (value.toDouble() >= compare_value_.toDouble());
            break;
        case ComparisonType::LessThan:
            result = (value.toDouble() < compare_value_.toDouble());
            break;
        case ComparisonType::LessThanOrEqual:
            result = (value.toDouble() <= compare_value_.toDouble());
            break;
    }
    
    if (!result) {
        return ValidationResult::error(
            QString("%1 must be %2 %3").arg(field_name.isEmpty() ? "Value" : field_name).arg(operation).arg(compare_value_.toString()),
            field_name
        );
    }
    
    return ValidationResult::success(value);
}

QString BuiltinValidators::ComparisonValidator::getDescription() const {
    return QString("Value must be %1 %2").arg(getComparisonTypeString()).arg(compare_value_.toString());
}

QString BuiltinValidators::ComparisonValidator::getComparisonTypeString() const {
    switch (comparison_type_) {
        case ComparisonType::Equal: return "equal to";
        case ComparisonType::NotEqual: return "not equal to";
        case ComparisonType::GreaterThan: return "greater than";
        case ComparisonType::GreaterThanOrEqual: return "greater than or equal to";
        case ComparisonType::LessThan: return "less than";
        case ComparisonType::LessThanOrEqual: return "less than or equal to";
    }
    return "compared to";
}

// ValidationRule implementations

ValidationRule& ValidationRule::required() {
    validators_.push_back(std::make_unique<BuiltinValidators::RequiredValidator>());
    return *this;
}

ValidationRule& ValidationRule::minLength(int length) {
    validators_.push_back(std::make_unique<BuiltinValidators::LengthValidator>(length, -1));
    return *this;
}

ValidationRule& ValidationRule::maxLength(int length) {
    validators_.push_back(std::make_unique<BuiltinValidators::LengthValidator>(0, length));
    return *this;
}

ValidationRule& ValidationRule::length(int min_length, int max_length) {
    validators_.push_back(std::make_unique<BuiltinValidators::LengthValidator>(min_length, max_length));
    return *this;
}

ValidationRule& ValidationRule::regex(const QString& pattern, const QString& description) {
    validators_.push_back(std::make_unique<BuiltinValidators::RegexValidator>(pattern, description));
    return *this;
}

ValidationRule& ValidationRule::email() {
    validators_.push_back(std::make_unique<BuiltinValidators::EmailValidator>());
    return *this;
}

ValidationRule& ValidationRule::url() {
    validators_.push_back(std::make_unique<BuiltinValidators::UrlValidator>());
    return *this;
}

ValidationRule& ValidationRule::range(double min_value, double max_value) {
    validators_.push_back(std::make_unique<BuiltinValidators::RangeValidator>(min_value, max_value));
    return *this;
}

ValidationRule& ValidationRule::date(const QDate& min_date, const QDate& max_date) {
    validators_.push_back(std::make_unique<BuiltinValidators::DateValidator>(min_date, max_date));
    return *this;
}

ValidationRule& ValidationRule::custom(ValidatorFunction func, const QString& description, const QString& rule_name) {
    validators_.push_back(std::make_unique<FunctionValidator>(std::move(func), description, rule_name));
    return *this;
}

ValidationResult ValidationRule::validate(const QVariant& value) const {
    for (const auto& validator : validators_) {
        ValidationResult result = validator->validate(value, field_name_);
        if (!result.is_valid) {
            result.validation_rule = validator->getRuleName();
            return result;
        }
    }
    return ValidationResult::success(value);
}

std::vector<ValidationResult> ValidationRule::validateAll(const QVariant& value) const {
    std::vector<ValidationResult> results;
    results.reserve(validators_.size());
    
    for (const auto& validator : validators_) {
        ValidationResult result = validator->validate(value, field_name_);
        result.validation_rule = validator->getRuleName();
        results.push_back(result);
    }
    
    return results;
}

std::vector<QString> ValidationRule::getValidatorDescriptions() const {
    std::vector<QString> descriptions;
    descriptions.reserve(validators_.size());
    
    for (const auto& validator : validators_) {
        descriptions.push_back(validator->getDescription());
    }
    
    return descriptions;
}

// ValidationEngine implementations

ValidationEngine::ValidationEngine(QObject* parent) : QObject(parent) {
    qDebug() << "🔍 ValidationEngine created";
}

ValidationRule& ValidationEngine::addRule(const QString& field_name) {
    auto result = rules_.emplace(field_name, ValidationRule(field_name));
    return result.first->second;
}

ValidationRule& ValidationEngine::getRule(const QString& field_name) {
    auto it = rules_.find(field_name);
    if (it == rules_.end()) {
        throw std::runtime_error(QString("Validation rule for field '%1' not found").arg(field_name).toStdString());
    }
    return it->second;
}

bool ValidationEngine::hasRule(const QString& field_name) const {
    return rules_.find(field_name) != rules_.end();
}

void ValidationEngine::removeRule(const QString& field_name) {
    rules_.erase(field_name);
    current_errors_.erase(field_name);
    emit errorCleared(field_name);
}

void ValidationEngine::clearRules() {
    rules_.clear();
    clearErrors();
}

ValidationResult ValidationEngine::validateField(const QString& field_name, const QVariant& value) {
    auto it = rules_.find(field_name);
    if (it == rules_.end()) {
        // No rule found, consider valid
        return ValidationResult::success(value);
    }

    ValidationResult result = it->second.validate(value);

    if (result.is_valid) {
        // Clear any existing error
        auto error_it = current_errors_.find(field_name);
        if (error_it != current_errors_.end()) {
            current_errors_.erase(error_it);
            emit errorCleared(field_name);
        }
    } else {
        // Store error
        current_errors_[field_name] = result.error_message;
        emit errorAdded(field_name, result.error_message);
    }

    emit validationCompleted(field_name, result);
    return result;
}

std::unordered_map<QString, ValidationResult> ValidationEngine::validateAll(const std::unordered_map<QString, QVariant>& data) {
    std::unordered_map<QString, ValidationResult> results;
    bool all_valid = true;

    // Clear previous errors
    current_errors_.clear();

    // Validate each field that has a rule
    for (const auto& [field_name, rule] : rules_) {
        auto data_it = data.find(field_name);
        QVariant value = (data_it != data.end()) ? data_it->second : QVariant();

        ValidationResult result = rule.validate(value);
        results[field_name] = result;

        if (!result.is_valid) {
            all_valid = false;
            current_errors_[field_name] = result.error_message;
            emit errorAdded(field_name, result.error_message);

            if (stop_on_first_error_) {
                break;
            }
        }

        emit validationCompleted(field_name, result);
    }

    emit allValidationCompleted(all_valid);
    return results;
}

bool ValidationEngine::isValid(const std::unordered_map<QString, QVariant>& data) {
    auto results = validateAll(data);
    for (const auto& [field_name, result] : results) {
        if (!result.is_valid) {
            return false;
        }
    }
    return true;
}

std::unordered_map<QString, QString> ValidationEngine::getErrors() const {
    return current_errors_;
}

QString ValidationEngine::getError(const QString& field_name) const {
    auto it = current_errors_.find(field_name);
    return (it != current_errors_.end()) ? it->second : QString();
}

bool ValidationEngine::hasErrors() const {
    return !current_errors_.empty();
}

void ValidationEngine::clearErrors() {
    for (const auto& [field_name, error] : current_errors_) {
        emit errorCleared(field_name);
    }
    current_errors_.clear();
}

QStringList ValidationEngine::getFieldNames() const {
    QStringList names;
    names.reserve(static_cast<int>(rules_.size()));

    for (const auto& [field_name, rule] : rules_) {
        names.append(field_name);
    }

    return names;
}

} // namespace DataBinding
} // namespace DeclarativeUI
