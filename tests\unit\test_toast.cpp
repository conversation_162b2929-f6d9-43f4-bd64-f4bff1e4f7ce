#include <QtTest/QtTest>
#include <QApplication>
#include <QSignalSpy>
#include <QTimer>
#include <QWidget>
#include <memory>

#include "../../src/Components/Toast.hpp"

using namespace DeclarativeUI::Components;

class TestToast : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // **Basic functionality tests**
    void testToastCreation();
    void testFluentInterface();
    void testStaticConvenienceMethods();
    void testMessageProperty();
    void testTypeProperty();
    void testDurationProperty();
    void testPositionProperty();

    // **UI behavior tests**
    void testShowHide();
    void testAutoHide();
    void testPersistentToast();
    void testClosableToast();
    void testActionButton();
    void testCloseButton();

    // **Animation tests**
    void testFadeInAnimation();
    void testFadeOutAnimation();
    void testAnimationDuration();

    // **Signal tests**
    void testClosedSignal();
    void testActionClickedSignal();

    // **Edge cases and error handling**
    void testNullPointerHandling();
    void testEmptyMessage();
    void testVeryLongMessage();
    void testInvalidDuration();
    void testMultipleToasts();
    void testToastPositioning();

    // **Performance tests**
    void testToastCreationPerformance();
    void testMultipleToastPerformance();
    void testMemoryLeaks();

    // **Integration tests**
    void testToastWithParentWidget();
    void testToastScreenPositioning();
    void testToastZOrder();

private:
    QApplication* app_;
    std::unique_ptr<Toast> toast_;
    QWidget* parent_widget_;

    // **Helper methods**
    void waitForAnimation(int timeout_ms = 1000);
    void verifyToastVisible(Toast* toast);
    void verifyToastHidden(Toast* toast);
    QPoint getExpectedPosition(Toast::Position pos, const QSize& toast_size);
};

void TestToast::initTestCase() {
    // QApplication is required for GUI tests
    if (!QApplication::instance()) {
        int argc = 0;
        char** argv = nullptr;
        app_ = new QApplication(argc, argv);
    }
    
    parent_widget_ = new QWidget();
    parent_widget_->resize(800, 600);
    parent_widget_->show();
}

void TestToast::cleanupTestCase() {
    delete parent_widget_;
    parent_widget_ = nullptr;
}

void TestToast::init() {
    toast_ = std::make_unique<Toast>();
}

void TestToast::cleanup() {
    if (toast_) {
        toast_->close();
        toast_.reset();
    }
    
    // Clean up any remaining toasts
    QTest::qWait(100); // Allow cleanup to complete
}

void TestToast::testToastCreation() {
    QVERIFY(toast_ != nullptr);
    QCOMPARE(toast_->getType(), Toast::Type::Info);
    QCOMPARE(toast_->getDuration(), 3000);
    QCOMPARE(toast_->getPosition(), Toast::Position::TopRight);
    QVERIFY(toast_->isClosable());
    QVERIFY(!toast_->isPersistent());
}

void TestToast::testFluentInterface() {
    auto& result = toast_->message("Test message")
                         .type(Toast::Type::Success)
                         .duration(5000)
                         .position(Toast::Position::BottomCenter)
                         .closable(false)
                         .persistent(true);
    
    // Verify fluent interface returns reference to same object
    QCOMPARE(&result, toast_.get());
    
    // Verify properties were set
    QCOMPARE(toast_->getMessage(), QString("Test message"));
    QCOMPARE(toast_->getType(), Toast::Type::Success);
    QCOMPARE(toast_->getDuration(), 5000);
    QCOMPARE(toast_->getPosition(), Toast::Position::BottomCenter);
    QVERIFY(!toast_->isClosable());
    QVERIFY(toast_->isPersistent());
}

void TestToast::testStaticConvenienceMethods() {
    // Test info toast
    auto* info_toast = Toast::info("Info message", 2000);
    QVERIFY(info_toast != nullptr);
    QCOMPARE(info_toast->getType(), Toast::Type::Info);
    QCOMPARE(info_toast->getMessage(), QString("Info message"));
    QCOMPARE(info_toast->getDuration(), 2000);
    info_toast->close();
    
    // Test success toast
    auto* success_toast = Toast::success("Success message", 3000);
    QVERIFY(success_toast != nullptr);
    QCOMPARE(success_toast->getType(), Toast::Type::Success);
    QCOMPARE(success_toast->getMessage(), QString("Success message"));
    QCOMPARE(success_toast->getDuration(), 3000);
    success_toast->close();
    
    // Test warning toast
    auto* warning_toast = Toast::warning("Warning message", 4000);
    QVERIFY(warning_toast != nullptr);
    QCOMPARE(warning_toast->getType(), Toast::Type::Warning);
    QCOMPARE(warning_toast->getMessage(), QString("Warning message"));
    QCOMPARE(warning_toast->getDuration(), 4000);
    warning_toast->close();
    
    // Test error toast
    auto* error_toast = Toast::error("Error message", 5000);
    QVERIFY(error_toast != nullptr);
    QCOMPARE(error_toast->getType(), Toast::Type::Error);
    QCOMPARE(error_toast->getMessage(), QString("Error message"));
    QCOMPARE(error_toast->getDuration(), 5000);
    error_toast->close();
}

void TestToast::testShowHide() {
    toast_->message("Test show/hide");
    
    // Test show
    toast_->show();
    verifyToastVisible(toast_.get());
    
    // Test hide
    toast_->hide();
    waitForAnimation();
    verifyToastHidden(toast_.get());
}

void TestToast::testAutoHide() {
    toast_->message("Auto hide test").duration(500); // Short duration for testing
    
    QSignalSpy closed_spy(toast_.get(), &Toast::closed);
    
    toast_->show();
    verifyToastVisible(toast_.get());
    
    // Wait for auto-hide
    QVERIFY(closed_spy.wait(1000));
    QCOMPARE(closed_spy.count(), 1);
}

void TestToast::testPersistentToast() {
    toast_->message("Persistent test").duration(200).persistent(true);
    
    QSignalSpy closed_spy(toast_.get(), &Toast::closed);
    
    toast_->show();
    verifyToastVisible(toast_.get());
    
    // Wait longer than duration - should not auto-hide
    QTest::qWait(500);
    QCOMPARE(closed_spy.count(), 0);
    
    // Manually close
    toast_->close();
    QCOMPARE(closed_spy.count(), 1);
}

void TestToast::testActionButton() {
    bool action_called = false;
    
    toast_->message("Action test")
          .action("Click me", [&action_called]() {
              action_called = true;
          });
    
    QSignalSpy action_spy(toast_.get(), &Toast::actionClicked);
    
    toast_->show();
    
    // Simulate action button click
    // Note: In a real test, you would find and click the actual button
    toast_->onActionButtonClicked();
    
    QVERIFY(action_called);
    QCOMPARE(action_spy.count(), 1);
}

void TestToast::testClosedSignal() {
    QSignalSpy closed_spy(toast_.get(), &Toast::closed);
    
    bool callback_called = false;
    toast_->onClosed([&callback_called]() {
        callback_called = true;
    });
    
    toast_->show();
    toast_->close();
    
    QCOMPARE(closed_spy.count(), 1);
    QVERIFY(callback_called);
}

void TestToast::testEmptyMessage() {
    toast_->message("");
    QCOMPARE(toast_->getMessage(), QString(""));
    
    // Should still be able to show/hide
    toast_->show();
    verifyToastVisible(toast_.get());
    toast_->hide();
}

void TestToast::testVeryLongMessage() {
    QString long_message = QString("Very long message ").repeated(50);
    toast_->message(long_message);
    QCOMPARE(toast_->getMessage(), long_message);
    
    toast_->show();
    verifyToastVisible(toast_.get());
}

void TestToast::testInvalidDuration() {
    // Test negative duration
    toast_->duration(-1000);
    QCOMPARE(toast_->getDuration(), -1000); // Should accept any value
    
    // Test zero duration
    toast_->duration(0);
    QCOMPARE(toast_->getDuration(), 0);
}

void TestToast::testToastCreationPerformance() {
    QElapsedTimer timer;
    timer.start();
    
    const int num_toasts = 100;
    QList<Toast*> toasts;
    
    for (int i = 0; i < num_toasts; ++i) {
        auto* toast = new Toast();
        toast->message(QString("Performance test %1").arg(i));
        toasts.append(toast);
    }
    
    qint64 creation_time = timer.elapsed();
    
    // Clean up
    for (auto* toast : toasts) {
        delete toast;
    }
    
    // Should create 100 toasts in less than 1 second
    QVERIFY(creation_time < 1000);
    qDebug() << "Created" << num_toasts << "toasts in" << creation_time << "ms";
}

// **Helper methods**
void TestToast::waitForAnimation(int timeout_ms) {
    QTest::qWait(timeout_ms);
}

void TestToast::verifyToastVisible(Toast* toast) {
    // In a real implementation, you would check if the toast widget is visible
    // For now, we assume the toast is visible if it's not null
    QVERIFY(toast != nullptr);
}

void TestToast::verifyToastHidden(Toast* toast) {
    // In a real implementation, you would check if the toast widget is hidden
    // For now, we assume the toast is hidden after the hide operation
    QVERIFY(toast != nullptr);
}

QTEST_MAIN(TestToast)
#include "test_toast.moc"
