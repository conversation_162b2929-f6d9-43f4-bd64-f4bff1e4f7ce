{"type": "QWidget", "properties": {"windowTitle": "02 - JSON UI Loading | DeclarativeUI", "minimumSize": [500, 400]}, "layout": {"type": "QVBoxLayout", "properties": {"spacing": 15, "contentsMargins": [20, 20, 20, 20]}, "children": [{"type": "QLabel", "properties": {"text": "📄 JSON UI Loading Example", "alignment": "AlignCenter", "styleSheet": "font-size: 20px; font-weight: bold; color: #2c3e50;"}}, {"type": "QLabel", "properties": {"text": "✅ This UI was loaded from JSON!", "alignment": "AlignCenter", "styleSheet": "color: #27ae60; font-style: italic;"}}, {"type": "QLabel", "properties": {"text": "This demonstrates declarative UI definition using JSON.\nThe structure, properties, and layout are all defined in this JSON file.", "alignment": "AlignCenter", "wordWrap": true}}, {"type": "spacer", "properties": {"orientation": "vertical"}}, {"type": "QGroupBox", "properties": {"title": "Sample Form"}, "layout": {"type": "QVBoxLayout", "children": [{"type": "QLabel", "properties": {"text": "Name:"}}, {"type": "QLineEdit", "properties": {"objectName": "nameInput", "placeholderText": "Enter your name..."}}, {"type": "QLabel", "properties": {"text": "Message:"}}, {"type": "QTextEdit", "properties": {"objectName": "messageText", "placeholderText": "Enter a message...", "maximumHeight": 100}}]}}, {"type": "layout", "layoutType": "QHBoxLayout", "children": [{"type": "QPushButton", "properties": {"text": "👋 Greet", "styleSheet": "QPushButton { background-color: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 4px; } QPushButton:hover { background-color: #2980b9; }"}, "events": {"clicked": "greetUser"}}, {"type": "QPushButton", "properties": {"text": "🗑️ Clear", "styleSheet": "QPushButton { background-color: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 4px; } QPushButton:hover { background-color: #c0392b; }"}, "events": {"clicked": "clearFields"}}, {"type": "QPushButton", "properties": {"text": "🔄 Reload UI", "styleSheet": "QPushButton { background-color: #f39c12; color: white; border: none; padding: 8px 16px; border-radius: 4px; } QPushButton:hover { background-color: #e67e22; }"}, "events": {"clicked": "reloadUI"}}, {"type": "QPushButton", "properties": {"text": "ℹ️ Info", "styleSheet": "QPushButton { background-color: #95a5a6; color: white; border: none; padding: 8px 16px; border-radius: 4px; } QPushButton:hover { background-color: #7f8c8d; }"}, "events": {"clicked": "showInfo"}}, {"type": "spacer", "properties": {"orientation": "horizontal"}}]}, {"type": "spacer", "properties": {"orientation": "vertical"}}, {"type": "QLabel", "properties": {"text": "💡 Try editing this JSON file to see how declarative UI works!", "alignment": "AlignCenter", "styleSheet": "font-size: 12px; color: #7f8c8d; font-style: italic;"}}]}}