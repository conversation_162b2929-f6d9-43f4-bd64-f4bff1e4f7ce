#include "DiagnosticsEngine.hpp"
#include <QDebug>
#include <QJsonDocument>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QUuid>
#include <QReadLocker>
#include <QWriteLocker>
#include <QMutexLocker>
#include <algorithm>

namespace DeclarativeUI::HotReload {

QJsonObject DiagnosticInfo::toJson() const {
    QJsonObject json;
    json["id"] = id;
    json["severity"] = static_cast<int>(severity);
    json["category"] = static_cast<int>(category);
    json["title"] = title;
    json["description"] = description;
    json["file_path"] = file_path;
    json["line_number"] = line_number;
    json["column_number"] = column_number;
    json["timestamp"] = timestamp.toString(Qt::ISODate);
    json["metadata"] = metadata;
    json["suggested_fixes"] = QJsonArray::fromStringList(suggested_fixes);
    json["error_code"] = error_code;
    json["auto_recoverable"] = auto_recoverable;
    json["recovery_action"] = recovery_action;
    json["recovery_attempts"] = recovery_attempts;
    json["recovery_successful"] = recovery_successful;
    return json;
}

DiagnosticInfo DiagnosticInfo::fromJson(const QJsonObject& json) {
    DiagnosticInfo info;
    info.id = json["id"].toString();
    info.severity = static_cast<Severity>(json["severity"].toInt());
    info.category = static_cast<Category>(json["category"].toInt());
    info.title = json["title"].toString();
    info.description = json["description"].toString();
    info.file_path = json["file_path"].toString();
    info.line_number = json["line_number"].toInt(-1);
    info.column_number = json["column_number"].toInt(-1);
    info.timestamp = QDateTime::fromString(json["timestamp"].toString(), Qt::ISODate);
    info.metadata = json["metadata"].toObject();
    
    QJsonArray fixes_array = json["suggested_fixes"].toArray();
    for (const QJsonValue& value : fixes_array) {
        info.suggested_fixes.append(value.toString());
    }
    
    info.error_code = json["error_code"].toString();
    info.auto_recoverable = json["auto_recoverable"].toBool();
    info.recovery_action = json["recovery_action"].toString();
    info.recovery_attempts = json["recovery_attempts"].toInt();
    info.recovery_successful = json["recovery_successful"].toBool();
    
    return info;
}

DiagnosticsEngine::DiagnosticsEngine(QObject* parent) 
    : QObject(parent), auto_recovery_enabled_(true), auto_recovery_delay_ms_(2000), max_diagnostic_history_(1000) {
    
    recovery_timer_ = std::make_unique<QTimer>(this);
    recovery_timer_->setSingleShot(true);
    connect(recovery_timer_.get(), &QTimer::timeout, this, &DiagnosticsEngine::onRecoveryTimerTimeout);
    
    initializeBuiltinRecoveryStrategies();
    initializeErrorCodeDescriptions();
}

void DiagnosticsEngine::reportError(const DiagnosticInfo& diagnostic) {
    DiagnosticInfo info = diagnostic;
    if (info.id.isEmpty()) {
        info.id = generateDiagnosticId();
    }
    
    {
        QWriteLocker locker(&diagnostics_lock_);
        diagnostics_history_.push_back(info);
        active_diagnostics_[info.id] = info;
        
        // Prune old diagnostics if needed
        if (diagnostics_history_.size() > static_cast<size_t>(max_diagnostic_history_)) {
            diagnostics_history_.pop_front();
        }
    }
    
    updateErrorPatterns(info);
    
    emit diagnosticReported(info);
    
    if (info.severity == DiagnosticInfo::Critical) {
        emit criticalErrorDetected(info);
    }
    
    // Schedule recovery attempt if auto-recovery is enabled and error is recoverable
    if (auto_recovery_enabled_ && info.auto_recoverable) {
        scheduleRecoveryAttempt(info.id);
    }
    
    qDebug() << "🔥 Diagnostic reported:" << info.title << "(" << info.id << ")";
}

void DiagnosticsEngine::reportError(const QString& title, const QString& description, const QString& file_path) {
    DiagnosticInfo info = DiagnosticInfo::createError(title, description, file_path);
    reportError(info);
}

void DiagnosticsEngine::reportWarning(const QString& title, const QString& description, const QString& file_path) {
    DiagnosticInfo info = DiagnosticInfo::createWarning(title, description, file_path);
    reportError(info);
}

void DiagnosticsEngine::reportInfo(const QString& title, const QString& description, const QString& file_path) {
    DiagnosticInfo info;
    info.severity = DiagnosticInfo::Info;
    info.title = title;
    info.description = description;
    info.file_path = file_path;
    reportError(info);
}

QList<DiagnosticInfo> DiagnosticsEngine::getAllDiagnostics() const {
    QReadLocker locker(&diagnostics_lock_);
    QList<DiagnosticInfo> result;
    for (const auto& diagnostic : diagnostics_history_) {
        result.append(diagnostic);
    }
    return result;
}

QList<DiagnosticInfo> DiagnosticsEngine::getDiagnosticsByFile(const QString& file_path) const {
    QReadLocker locker(&diagnostics_lock_);
    QList<DiagnosticInfo> result;
    for (const auto& diagnostic : diagnostics_history_) {
        if (diagnostic.file_path == file_path) {
            result.append(diagnostic);
        }
    }
    return result;
}

QList<DiagnosticInfo> DiagnosticsEngine::getDiagnosticsBySeverity(DiagnosticInfo::Severity severity) const {
    QReadLocker locker(&diagnostics_lock_);
    QList<DiagnosticInfo> result;
    for (const auto& diagnostic : diagnostics_history_) {
        if (diagnostic.severity == severity) {
            result.append(diagnostic);
        }
    }
    return result;
}

QList<DiagnosticInfo> DiagnosticsEngine::getDiagnosticsByCategory(DiagnosticInfo::Category category) const {
    QReadLocker locker(&diagnostics_lock_);
    QList<DiagnosticInfo> result;
    for (const auto& diagnostic : diagnostics_history_) {
        if (diagnostic.category == category) {
            result.append(diagnostic);
        }
    }
    return result;
}

QList<DiagnosticInfo> DiagnosticsEngine::getRecentDiagnostics(int minutes) const {
    QReadLocker locker(&diagnostics_lock_);
    QList<DiagnosticInfo> result;
    QDateTime cutoff = QDateTime::currentDateTime().addSecs(-minutes * 60);
    
    for (const auto& diagnostic : diagnostics_history_) {
        if (diagnostic.timestamp >= cutoff) {
            result.append(diagnostic);
        }
    }
    return result;
}

void DiagnosticsEngine::registerRecoveryStrategy(const RecoveryStrategy& strategy) {
    QMutexLocker locker(&recovery_lock_);
    recovery_strategies_.push_back(strategy);
    qDebug() << "🔥 Registered recovery strategy:" << strategy.name;
}

void DiagnosticsEngine::enableAutoRecovery(bool enabled) {
    auto_recovery_enabled_ = enabled;
    qDebug() << "🔥 Auto recovery" << (enabled ? "enabled" : "disabled");
}

bool DiagnosticsEngine::isAutoRecoveryEnabled() const {
    return auto_recovery_enabled_;
}

void DiagnosticsEngine::attemptRecovery(const QString& diagnostic_id) {
    QReadLocker diagnostics_locker(&diagnostics_lock_);
    auto it = active_diagnostics_.find(diagnostic_id);
    if (it == active_diagnostics_.end()) {
        qWarning() << "Diagnostic not found for recovery:" << diagnostic_id;
        return;
    }
    
    DiagnosticInfo diagnostic = it->second;
    diagnostics_locker.unlock();
    
    QMutexLocker recovery_locker(&recovery_lock_);
    
    for (const auto& strategy : recovery_strategies_) {
        if (!strategy.enabled) continue;
        
        if (strategy.condition_check && !strategy.condition_check(diagnostic)) {
            continue;
        }
        
        qDebug() << "🔥 Attempting recovery with strategy:" << strategy.name;
        
        if (executeRecoveryStrategy(strategy, diagnostic)) {
            emit errorRecovered(diagnostic_id, strategy.name);
            
            // Update diagnostic as recovered
            QWriteLocker update_locker(&diagnostics_lock_);
            auto update_it = active_diagnostics_.find(diagnostic_id);
            if (update_it != active_diagnostics_.end()) {
                update_it->second.recovery_successful = true;
                update_it->second.recovery_action = strategy.name;
            }
            
            qDebug() << "🔥 Recovery successful for:" << diagnostic_id;
            return;
        }
    }
    
    emit recoveryFailed(diagnostic_id, "No suitable recovery strategy found");
    qWarning() << "🔥 Recovery failed for:" << diagnostic_id;
}

void DiagnosticsEngine::attemptRecoveryForFile(const QString& file_path) {
    QList<DiagnosticInfo> file_diagnostics = getDiagnosticsByFile(file_path);
    for (const auto& diagnostic : file_diagnostics) {
        if (diagnostic.auto_recoverable && !diagnostic.recovery_successful) {
            attemptRecovery(diagnostic.id);
        }
    }
}

QJsonObject DiagnosticsEngine::generateDiagnosticReport() const {
    QJsonObject report;
    
    QReadLocker locker(&diagnostics_lock_);
    
    // Summary statistics
    QJsonObject summary;
    int error_count = 0, warning_count = 0, info_count = 0;
    
    for (const auto& diagnostic : diagnostics_history_) {
        switch (diagnostic.severity) {
            case DiagnosticInfo::Error:
            case DiagnosticInfo::Critical:
                error_count++;
                break;
            case DiagnosticInfo::Warning:
                warning_count++;
                break;
            case DiagnosticInfo::Info:
                info_count++;
                break;
        }
    }
    
    summary["total_diagnostics"] = static_cast<int>(diagnostics_history_.size());
    summary["errors"] = error_count;
    summary["warnings"] = warning_count;
    summary["info"] = info_count;
    summary["generation_time"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    report["summary"] = summary;
    
    // Recent diagnostics
    QList<DiagnosticInfo> recent = getRecentDiagnostics(60);
    report["recent_diagnostics"] = diagnosticsToJsonArray(recent);
    
    // Error patterns
    QJsonObject patterns;
    for (const auto& [pattern, count] : error_patterns_) {
        patterns[QString::fromStdString(pattern)] = count;
    }
    report["error_patterns"] = patterns;
    
    // Recovery statistics
    QJsonObject recovery_stats;
    int total_recoverable = 0, successful_recoveries = 0;
    for (const auto& diagnostic : diagnostics_history_) {
        if (diagnostic.auto_recoverable) {
            total_recoverable++;
            if (diagnostic.recovery_successful) {
                successful_recoveries++;
            }
        }
    }
    recovery_stats["total_recoverable"] = total_recoverable;
    recovery_stats["successful_recoveries"] = successful_recoveries;
    recovery_stats["recovery_rate"] = total_recoverable > 0 ? 
        (double(successful_recoveries) / double(total_recoverable)) : 0.0;
    
    report["recovery_statistics"] = recovery_stats;
    
    return report;
}

QStringList DiagnosticsEngine::getCommonErrorPatterns() const {
    QStringList patterns;
    
    // Sort error patterns by frequency
    std::vector<std::pair<int, QString>> sorted_patterns;
    for (const auto& [pattern, count] : error_patterns_) {
        sorted_patterns.emplace_back(count, QString::fromStdString(pattern));
    }
    
    std::sort(sorted_patterns.begin(), sorted_patterns.end(), 
              [](const auto& a, const auto& b) { return a.first > b.first; });
    
    for (const auto& [count, pattern] : sorted_patterns) {
        patterns.append(QString("%1 (%2 occurrences)").arg(pattern).arg(count));
    }
    
    return patterns;
}

QStringList DiagnosticsEngine::getSuggestedFixes(const QString& diagnostic_id) const {
    QReadLocker locker(&diagnostics_lock_);
    auto it = active_diagnostics_.find(diagnostic_id);
    if (it != active_diagnostics_.end()) {
        return it->second.suggested_fixes;
    }
    return QStringList();
}

QString DiagnosticsEngine::getErrorCodeDescription(const QString& error_code) const {
    auto it = error_code_descriptions_.find(error_code.toStdString());
    if (it != error_code_descriptions_.end()) {
        return QString::fromStdString(it->second);
    }
    return QString("Unknown error code: %1").arg(error_code);
}

void DiagnosticsEngine::setMaxDiagnosticHistory(int max_count) {
    max_diagnostic_history_ = max_count;
    pruneOldDiagnostics();
}

int DiagnosticsEngine::getMaxDiagnosticHistory() const {
    return max_diagnostic_history_;
}

void DiagnosticsEngine::setAutoRecoveryDelay(int delay_ms) {
    auto_recovery_delay_ms_ = delay_ms;
}

int DiagnosticsEngine::getAutoRecoveryDelay() const {
    return auto_recovery_delay_ms_;
}

void DiagnosticsEngine::clearDiagnostics() {
    QWriteLocker locker(&diagnostics_lock_);
    diagnostics_history_.clear();
    active_diagnostics_.clear();
    error_patterns_.clear();
    qDebug() << "🔥 All diagnostics cleared";
}

void DiagnosticsEngine::clearDiagnosticsForFile(const QString& file_path) {
    QWriteLocker locker(&diagnostics_lock_);
    
    // Remove from history
    diagnostics_history_.erase(
        std::remove_if(diagnostics_history_.begin(), diagnostics_history_.end(),
                      [&file_path](const DiagnosticInfo& info) {
                          return info.file_path == file_path;
                      }),
        diagnostics_history_.end());
    
    // Remove from active diagnostics
    for (auto it = active_diagnostics_.begin(); it != active_diagnostics_.end();) {
        if (it->second.file_path == file_path) {
            it = active_diagnostics_.erase(it);
        } else {
            ++it;
        }
    }
    
    qDebug() << "🔥 Diagnostics cleared for file:" << file_path;
}

void DiagnosticsEngine::clearOldDiagnostics(int days) {
    QWriteLocker locker(&diagnostics_lock_);
    QDateTime cutoff = QDateTime::currentDateTime().addDays(-days);
    
    size_t original_size = diagnostics_history_.size();
    
    diagnostics_history_.erase(
        std::remove_if(diagnostics_history_.begin(), diagnostics_history_.end(),
                      [&cutoff](const DiagnosticInfo& info) {
                          return info.timestamp < cutoff;
                      }),
        diagnostics_history_.end());
    
    // Also clean up active diagnostics
    for (auto it = active_diagnostics_.begin(); it != active_diagnostics_.end();) {
        if (it->second.timestamp < cutoff) {
            it = active_diagnostics_.erase(it);
        } else {
            ++it;
        }
    }
    
    size_t removed_count = original_size - diagnostics_history_.size();
    qDebug() << "🔥 Removed" << removed_count << "old diagnostics";
}

bool DiagnosticsEngine::exportDiagnostics(const QString& file_path) const {
    QJsonObject report = generateDiagnosticReport();
    QJsonDocument doc(report);

    QFile file(file_path);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open file for export:" << file_path;
        return false;
    }

    file.write(doc.toJson());
    qDebug() << "🔥 Diagnostics exported to:" << file_path;
    return true;
}

bool DiagnosticsEngine::importDiagnostics(const QString& file_path) {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to open file for import:" << file_path;
        return false;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error during import:" << error.errorString();
        return false;
    }

    QJsonObject report = doc.object();
    QJsonArray diagnostics_array = report["recent_diagnostics"].toArray();

    for (const QJsonValue& value : diagnostics_array) {
        DiagnosticInfo info = DiagnosticInfo::fromJson(value.toObject());
        reportError(info);
    }

    qDebug() << "🔥 Diagnostics imported from:" << file_path;
    return true;
}

void DiagnosticsEngine::initializeBuiltinRecoveryStrategies() {
    // File not found recovery
    RecoveryStrategy file_not_found;
    file_not_found.name = "FileNotFoundRecovery";
    file_not_found.description = "Attempts to recover from missing file errors";
    file_not_found.condition_check = [](const DiagnosticInfo& info) {
        return info.category == DiagnosticInfo::FileSystem &&
               info.description.contains("not found", Qt::CaseInsensitive);
    };
    file_not_found.recovery_action = [](const DiagnosticInfo& info) {
        // Try to create missing directories
        QFileInfo file_info(info.file_path);
        QDir dir = file_info.absoluteDir();
        if (!dir.exists()) {
            return dir.mkpath(dir.absolutePath());
        }
        return false;
    };
    registerRecoveryStrategy(file_not_found);

    // Permission error recovery
    RecoveryStrategy permission_error;
    permission_error.name = "PermissionErrorRecovery";
    permission_error.description = "Attempts to recover from permission errors";
    permission_error.condition_check = [](const DiagnosticInfo& info) {
        return info.description.contains("permission", Qt::CaseInsensitive) ||
               info.description.contains("access denied", Qt::CaseInsensitive);
    };
    permission_error.recovery_action = [](const DiagnosticInfo& info) {
        // Try to change file permissions (limited recovery)
        QFile file(info.file_path);
        return file.setPermissions(QFile::ReadOwner | QFile::WriteOwner | QFile::ReadGroup);
    };
    registerRecoveryStrategy(permission_error);

    // Compilation error recovery
    RecoveryStrategy compilation_error;
    compilation_error.name = "CompilationErrorRecovery";
    compilation_error.description = "Attempts to recover from compilation errors";
    compilation_error.condition_check = [](const DiagnosticInfo& info) {
        return info.category == DiagnosticInfo::Compilation;
    };
    compilation_error.recovery_action = [](const DiagnosticInfo& info) {
        // For now, just log the attempt - more sophisticated recovery could be added
        qDebug() << "🔥 Attempting compilation error recovery for:" << info.file_path;
        return false; // Placeholder - would need specific implementation
    };
    registerRecoveryStrategy(compilation_error);
}

void DiagnosticsEngine::initializeErrorCodeDescriptions() {
    error_code_descriptions_["E001"] = "File not found or inaccessible";
    error_code_descriptions_["E002"] = "Permission denied";
    error_code_descriptions_["E003"] = "Compilation failed";
    error_code_descriptions_["E004"] = "Syntax error";
    error_code_descriptions_["E005"] = "Memory allocation failed";
    error_code_descriptions_["E006"] = "Network connection failed";
    error_code_descriptions_["E007"] = "Configuration error";
    error_code_descriptions_["E008"] = "Dependency not found";
    error_code_descriptions_["E009"] = "Version mismatch";
    error_code_descriptions_["E010"] = "Timeout occurred";

    error_code_descriptions_["W001"] = "Deprecated feature used";
    error_code_descriptions_["W002"] = "Performance warning";
    error_code_descriptions_["W003"] = "Memory usage high";
    error_code_descriptions_["W004"] = "Potential compatibility issue";
    error_code_descriptions_["W005"] = "Configuration suboptimal";
}

QString DiagnosticsEngine::generateDiagnosticId() const {
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

void DiagnosticsEngine::updateErrorPatterns(const DiagnosticInfo& diagnostic) {
    QString pattern = QString("%1:%2").arg(diagnostic.title, diagnostic.description);
    error_patterns_[pattern.toStdString()]++;

    // Emit signal if pattern frequency reaches threshold
    int frequency = error_patterns_[pattern.toStdString()];
    if (frequency > 1 && frequency % 5 == 0) {
        emit errorPatternDetected(pattern, frequency);
    }
}

void DiagnosticsEngine::scheduleRecoveryAttempt(const QString& diagnostic_id) {
    if (!recovery_timer_->isActive()) {
        recovery_timer_->setInterval(auto_recovery_delay_ms_);
        recovery_timer_->setProperty("diagnostic_id", diagnostic_id);
        recovery_timer_->start();
    }
}

bool DiagnosticsEngine::executeRecoveryStrategy(const RecoveryStrategy& strategy, const DiagnosticInfo& diagnostic) {
    if (!strategy.recovery_action) {
        return false;
    }

    try {
        return strategy.recovery_action(diagnostic);
    } catch (const std::exception& e) {
        qWarning() << "Recovery strategy" << strategy.name << "failed with exception:" << e.what();
        return false;
    }
}

void DiagnosticsEngine::pruneOldDiagnostics() {
    QWriteLocker locker(&diagnostics_lock_);
    while (diagnostics_history_.size() > static_cast<size_t>(max_diagnostic_history_)) {
        diagnostics_history_.pop_front();
    }
}

QJsonArray DiagnosticsEngine::diagnosticsToJsonArray(const QList<DiagnosticInfo>& diagnostics) const {
    QJsonArray array;
    for (const auto& diagnostic : diagnostics) {
        array.append(diagnostic.toJson());
    }
    return array;
}

void DiagnosticsEngine::onRecoveryTimerTimeout() {
    QString diagnostic_id = recovery_timer_->property("diagnostic_id").toString();
    if (!diagnostic_id.isEmpty()) {
        attemptRecovery(diagnostic_id);
    }
}

} // namespace DeclarativeUI::HotReload
