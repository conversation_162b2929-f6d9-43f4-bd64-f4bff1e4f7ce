#pragma once

#include "IFormatProcessor.hpp"
#include <QObject>
#include <QString>
#include <QStringList>
#include <QMutex>
#include <QReadWriteLock>
#include <memory>
#include <unordered_map>
#include <unordered_set>

namespace DeclarativeUI::HotReload::FormatSupport {

/**
 * @brief Registry for managing format processors
 */
class FormatProcessorRegistry : public QObject {
    Q_OBJECT

public:
    static FormatProcessorRegistry& instance();
    
    // Processor registration
    void registerProcessor(std::unique_ptr<IFormatProcessorFactory> factory);
    
    template<typename ProcessorType>
    void registerProcessor() {
        registerProcessor(std::make_unique<FormatProcessorFactory<ProcessorType>>());
    }
    
    // Processor creation and management
    std::unique_ptr<IFormatProcessor> createProcessor(const QString& format_name) const;
    std::unique_ptr<IFormatProcessor> createProcessorForFile(const QString& file_path) const;
    std::unique_ptr<IFormatProcessor> createProcessorForExtension(const QString& extension) const;
    
    // Query methods
    bool hasProcessor(const QString& format_name) const;
    bool canProcessFile(const QString& file_path) const;
    bool canProcessExtension(const QString& extension) const;
    
    QStringList getRegisteredFormats() const;
    QStringList getSupportedExtensions() const;
    QStringList getProcessorsForExtension(const QString& extension) const;
    
    // Configuration
    void setDefaultConfig(const QString& format_name, const ProcessingConfig& config);
    ProcessingConfig getDefaultConfig(const QString& format_name) const;
    
    // Cache management
    void enableCaching(bool enabled);
    void clearCache();
    void clearCacheForFormat(const QString& format_name);

    // Cache access methods (for ProcessorHandle)
    bool getCachedResult(const QString& cache_key, ProcessingResult& result) const;
    void setCachedResult(const QString& cache_key, const ProcessingResult& result, const QJsonObject& metadata);
    void updateStats(const QString& format_name, qint64 processing_time, bool success, bool cache_hit) const;
    
    // Performance and monitoring
    struct ProcessorStats {
        QString format_name;
        int files_processed = 0;
        qint64 total_processing_time_ms = 0;
        qint64 average_processing_time_ms = 0;
        int cache_hits = 0;
        int cache_misses = 0;
        int errors = 0;
    };
    
    ProcessorStats getStats(const QString& format_name) const;
    QList<ProcessorStats> getAllStats() const;
    void resetStats();

signals:
    void processorRegistered(const QString& format_name, const QStringList& extensions);
    void processorUnregistered(const QString& format_name);
    void processingStarted(const QString& format_name, const QString& file_path);
    void processingFinished(const QString& format_name, const QString& file_path, bool success);
    void cacheHit(const QString& format_name, const QString& file_path);
    void cacheMiss(const QString& format_name, const QString& file_path);

private:
    explicit FormatProcessorRegistry(QObject* parent = nullptr);
    ~FormatProcessorRegistry() override = default;
    
    // Disable copy and move
    FormatProcessorRegistry(const FormatProcessorRegistry&) = delete;
    FormatProcessorRegistry& operator=(const FormatProcessorRegistry&) = delete;
    FormatProcessorRegistry(FormatProcessorRegistry&&) = delete;
    FormatProcessorRegistry& operator=(FormatProcessorRegistry&&) = delete;
    
    void registerBuiltinProcessors();
    void updateExtensionMappings();
    QString getExtensionFromPath(const QString& file_path) const;
    
    // Thread-safe data structures
    mutable QReadWriteLock registry_lock_;
    mutable QMutex stats_lock_;
    mutable QMutex cache_lock_;
    
    // Registry data
    std::unordered_map<QString, std::unique_ptr<IFormatProcessorFactory>> factories_;
    std::unordered_map<QString, QStringList> extension_to_processors_;
    std::unordered_map<QString, ProcessingConfig> default_configs_;
    
    // Performance tracking
    mutable std::unordered_map<QString, ProcessorStats> stats_;
    
    // Cache management
    struct CacheEntry {
        QString processed_content;
        QJsonObject metadata;
        QDateTime created_time;
        QString cache_key;
    };
    
    bool caching_enabled_ = true;
    std::unordered_map<QString, CacheEntry> cache_;
    size_t max_cache_size_ = 1000;
    qint64 max_cache_age_ms_ = 300000; // 5 minutes
    
    // Cache methods
    void cleanupExpiredCache();
};

/**
 * @brief RAII helper for automatic processor cleanup and stats tracking
 */
class ProcessorHandle {
public:
    ProcessorHandle(std::unique_ptr<IFormatProcessor> processor, const QString& format_name);
    ~ProcessorHandle();
    
    IFormatProcessor* operator->() const { return processor_.get(); }
    IFormatProcessor& operator*() const { return *processor_; }
    IFormatProcessor* get() const { return processor_.get(); }
    
    bool isValid() const { return processor_ != nullptr; }
    
    // Processing with automatic stats tracking
    ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig());
    ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig());

private:
    std::unique_ptr<IFormatProcessor> processor_;
    QString format_name_;
    QElapsedTimer timer_;
};

} // namespace DeclarativeUI::HotReload::FormatSupport
