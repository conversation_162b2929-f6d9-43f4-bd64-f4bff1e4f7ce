# Enhanced Hot-Reload System Demo

This demo application showcases the comprehensive enhancements made to the DeclarativeUI Framework's hot-reload system. It provides a hands-on demonstration of all the advanced features including configuration management, file filtering, real-time monitoring, selective reload capabilities, and error recovery mechanisms.

## Features Demonstrated

### 1. Enhanced Configuration System
- **Profile-based configuration** with development, testing, and production profiles
- **JSON serialization** for easy configuration persistence and sharing
- **Runtime configuration updates** without application restart
- **Environment variable integration** for deployment flexibility

### 2. Advanced File Filtering
- **Multiple match types**: Glob patterns, regex, file size, modification time
- **Rule priority system** with ordered evaluation
- **Performance optimization** with intelligent caching
- **Statistics tracking** for filter performance analysis

### 3. Hot-Reload Dashboard Widget
- **Real-time monitoring** of file system activity and reload operations
- **Performance analytics** with CPU, memory, and timing metrics
- **Interactive controls** for start/stop/pause operations
- **Visual feedback** with charts and status indicators

### 4. Selective Reload Mechanisms
- **Component-level granularity** for targeted reloading
- **CSS live injection** for real-time stylesheet updates
- **Resource hot-swapping** for images, icons, and other assets
- **Dependency tracking** with automatic cascading updates

### 5. Enhanced Error Recovery System
- **Automatic recovery strategies** based on error type and severity
- **User notifications** with clear, actionable messages
- **Graceful degradation** to maintain application stability
- **Error categorization** and recovery history tracking

## Building and Running

### Prerequisites

- Qt 6.0 or later
- CMake 3.16 or later
- C++20 compatible compiler
- DeclarativeUI Framework (built with enhanced features)

### Build Instructions

1. **Navigate to the demo directory:**
   ```bash
   cd examples/enhanced_hotreload_demo
   ```

2. **Create build directory:**
   ```bash
   mkdir build
   cd build
   ```

3. **Configure with CMake:**
   ```bash
   cmake ..
   ```

4. **Build the demo:**
   ```bash
   cmake --build .
   ```

5. **Run the demo:**
   ```bash
   ./EnhancedHotReloadDemo
   ```

## Using the Demo

### Initial Setup

1. **Launch the application** - The demo will start with a pre-configured development profile
2. **Explore the dashboard** - The left panel shows the real-time monitoring dashboard
3. **Review configuration** - The right panel displays the current configuration settings
4. **Check the activity log** - Monitor system events and test results

### Testing Features

#### Configuration Management
- Click **"Load Config"** to load a custom configuration file
- Click **"Save Config"** to save the current configuration
- Modify settings and observe real-time updates

#### File Filtering
- Click **"Test File Filter"** to see the filtering system in action
- Observe how different file types are processed
- Check the activity log for filter statistics

#### Monitoring and Dashboard
- Click **"Start Monitoring"** to begin file system watching
- Observe real-time updates in the dashboard
- Monitor performance metrics and system health
- Click **"Stop Monitoring"** to pause the system

#### Selective Reload
- Click **"Test Selective Reload"** to demonstrate component-level reloading
- Watch as individual components are reloaded without full restart
- Check the activity log for reload results

#### Error Recovery
- Click **"Test Error Recovery"** to simulate error scenarios
- Observe how the system automatically attempts recovery
- Review recovery strategies and success rates

### Configuration Profiles

The demo includes three pre-configured profiles:

#### Development Profile
- **Fast updates** (100ms interval)
- **Debug logging enabled**
- **Auto-reload active**
- **Comprehensive file watching**

#### Testing Profile
- **Moderate updates** (500ms interval)
- **Reduced logging**
- **Focused on source files**
- **Balanced performance**

#### Production Profile
- **Conservative updates** (2000ms interval)
- **Minimal logging**
- **Manual reload only**
- **Maximum stability**

### File Filter Rules

The demo demonstrates various filtering rules:

1. **Build Exclusions**: `build/**/*` - Excludes all build artifacts
2. **Temporary Files**: `*.{tmp,bak,cache,log}` - Excludes temporary files
3. **Version Control**: `.git/**/*` - Excludes git repository files
4. **Size Limits**: Files larger than 10MB are excluded
5. **Source Inclusions**: `*.{cpp,hpp,h,c,qml,js,css}` - Includes source files

### Dashboard Features

The dashboard provides:

- **System Status**: Current monitoring state and health indicators
- **Performance Metrics**: Real-time CPU, memory, and timing data
- **File Activity**: Recent file changes and watch status
- **Reload History**: Timeline of reload operations with success/failure status
- **Configuration Panel**: Runtime setting adjustments
- **Control Interface**: Start, stop, pause, and reset operations

### Error Recovery Scenarios

The demo simulates various error types:

- **File Not Found**: Missing files during reload operations
- **Compilation Errors**: Syntax errors and build failures
- **Runtime Errors**: Application crashes and exceptions
- **Configuration Errors**: Invalid settings and missing values
- **Network Errors**: Connection failures and timeouts

## Customization

### Adding Custom Components

```cpp
// Register your components for selective reload
reload_manager_.registerComponent("my_widget", my_widget_ptr);
reload_manager_.registerComponent("my_toolbar", my_toolbar_ptr);

// Define dependencies
reload_manager_.addDependency("main_window", "my_widget");
```

### Custom Filter Rules

```cpp
// Add custom filtering rules
FilterRule custom_rule;
custom_rule.pattern = "my_files/*.custom";
custom_rule.match_type = FilterRule::MatchType::Glob;
custom_rule.type = FilterRule::Type::Include;
custom_rule.priority = 60;
filter_.addRule(custom_rule);
```

### Custom Error Handlers

```cpp
// Register custom error recovery strategies
recovery_manager_.registerErrorHandler(ErrorType::CustomError,
    [](const ErrorContext& context) -> RecoveryResult {
        // Your custom recovery logic here
        return RecoveryResult::success("Custom recovery completed");
    });
```

## Configuration File Format

The demo uses JSON configuration files with the following structure:

```json
{
  "version": "1.0",
  "active_profile": "development",
  "profiles": [...],
  "filter_rules": [...],
  "dashboard_settings": {...},
  "error_recovery": {...},
  "selective_reload": {...}
}
```

See `demo_config.json` for a complete example with all available options.

## Performance Considerations

- **File System Watching**: Adjust watch intervals based on your development needs
- **Filter Complexity**: Complex regex patterns may impact performance
- **Dashboard Updates**: Higher update frequencies increase CPU usage
- **History Size**: Larger history buffers consume more memory
- **Caching**: Enable caching for better performance with large file sets

## Troubleshooting

### Common Issues

1. **High CPU Usage**: Reduce dashboard update interval or file watch frequency
2. **Memory Consumption**: Decrease history buffer size or enable cleanup
3. **Slow Filtering**: Optimize filter rules or enable caching
4. **Failed Reloads**: Check file permissions and error recovery settings
5. **Missing Features**: Ensure all enhanced components are properly linked

### Debug Mode

Enable debug logging in the configuration to get detailed information about:
- File system events and changes
- Filter rule evaluation and performance
- Reload operations and timing
- Error detection and recovery attempts
- Dashboard updates and metrics

## Integration with Your Project

To integrate the enhanced hot-reload system into your own Qt application:

1. **Include the headers** for the components you need
2. **Initialize the system** with your preferred configuration
3. **Register your components** for selective reload
4. **Connect the dashboard** to your main window
5. **Configure error recovery** for your specific needs

See the main.cpp file for a complete integration example.

## Further Reading

- [Enhanced Hot-Reload Features Documentation](../../docs/ENHANCED_HOT_RELOAD_FEATURES.md)
- [DeclarativeUI Framework Documentation](../../docs/)
- [Qt Hot-Reload Best Practices](../../docs/BEST_PRACTICES.md)

## Support

For questions, issues, or feature requests related to the enhanced hot-reload system, please refer to the main project documentation or create an issue in the project repository.
