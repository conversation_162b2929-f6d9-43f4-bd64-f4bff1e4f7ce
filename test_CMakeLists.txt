cmake_minimum_required(VERSION 3.16)

# Simple test for data binding
add_executable(test_data_binding test_data_binding.cpp)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Set Qt6 properties
set_target_properties(test_data_binding PROPERTIES
    AUTOMOC ON
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
)

# Link libraries
target_link_libraries(test_data_binding
    Qt6::Core
    Qt6::Widgets
    DeclarativeUI
)

# Add compile definitions
if(BUILD_DATA_BINDING)
    target_compile_definitions(test_data_binding PRIVATE DECLARATIVE_UI_DATA_BINDING_ENABLED)
endif()

# Include directories
target_include_directories(test_data_binding PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
