#pragma once

#include <QObject>
#include <QWidget>
#include <QColor>
#include <QFont>
#include <QStringList>
#include <QJsonObject>
#include <QJsonArray>
#include <QAccessible>
#include <QRect>

namespace DeclarativeUI::Accessibility {

/**
 * @brief Accessibility validation severity levels
 */
enum class ValidationSeverity {
    Info,       // Informational - suggestions for improvement
    Warning,    // Warning - potential accessibility issues
    Error,      // Error - accessibility violations that should be fixed
    Critical    // Critical - severe accessibility violations
};

/**
 * @brief Accessibility validation categories
 */
enum class ValidationCategory {
    ColorContrast,      // Color contrast and visibility
    KeyboardNavigation, // Keyboard accessibility
    ScreenReader,       // Screen reader compatibility
    FocusManagement,    // Focus indicators and management
    TextAlternatives,   // Alt text and labels
    Structure,          // Semantic structure
    Timing,             // Time-based content
    Seizures,           // Seizure and vestibular disorders
    InputAssistance,    // Input assistance and error handling
    Compatible          // Compatibility with assistive technologies
};

/**
 * @brief WCAG compliance levels
 */
enum class WCAGLevel {
    A,      // Level A (minimum)
    AA,     // Level AA (standard)
    AAA     // Level AAA (enhanced)
};

/**
 * @brief Accessibility validation issue
 */
struct ValidationIssue {
    QString id;                         // Unique issue identifier
    QString title;                      // Short description
    QString description;                // Detailed description
    ValidationSeverity severity;        // Issue severity
    ValidationCategory category;        // Issue category
    WCAGLevel wcagLevel;               // WCAG level requirement
    QString wcagCriterion;             // Specific WCAG criterion (e.g., "1.4.3")
    QWidget* widget = nullptr;         // Associated widget
    QRect location;                    // Screen location of issue
    QStringList suggestions;           // Suggested fixes
    QJsonObject metadata;              // Additional metadata
    
    QJsonObject toJson() const;
    static ValidationIssue fromJson(const QJsonObject& json);
};

/**
 * @brief Accessibility validation report
 */
struct ValidationReport {
    QString applicationName;
    QString version;
    QDateTime timestamp;
    WCAGLevel targetLevel = WCAGLevel::AA;
    
    QList<ValidationIssue> issues;
    
    // Summary statistics
    int totalIssues() const { return issues.size(); }
    int criticalIssues() const;
    int errorIssues() const;
    int warningIssues() const;
    int infoIssues() const;
    
    double complianceScore() const;  // 0.0 to 1.0
    bool isCompliant(WCAGLevel level) const;
    
    QJsonObject toJson() const;
    static ValidationReport fromJson(const QJsonObject& json);
    
    void saveToFile(const QString& filename) const;
    static ValidationReport loadFromFile(const QString& filename);
};

/**
 * @brief Accessibility validation configuration
 */
struct ValidationConfig {
    WCAGLevel targetLevel = WCAGLevel::AA;
    bool checkColorContrast = true;
    bool checkKeyboardNavigation = true;
    bool checkScreenReaderSupport = true;
    bool checkFocusIndicators = true;
    bool checkTextAlternatives = true;
    bool checkSemanticStructure = true;
    bool checkTimingRequirements = true;
    bool checkSeizureRisks = true;
    bool checkInputAssistance = true;
    bool checkCompatibility = true;
    
    // Thresholds
    double minimumContrastRatio = 4.5;  // WCAG AA standard
    int minimumClickTargetSize = 44;    // pixels
    int maximumTabIndex = 100;          // reasonable tab order limit
    
    // Custom rules
    QStringList customRules;
    QJsonObject customThresholds;
    
    QJsonObject toJson() const;
    static ValidationConfig fromJson(const QJsonObject& json);
};

/**
 * @brief Comprehensive accessibility validator
 */
class AccessibilityValidator : public QObject {
    Q_OBJECT
    
public:
    explicit AccessibilityValidator(QObject* parent = nullptr);
    ~AccessibilityValidator() override = default;
    
    // Configuration
    void setConfig(const ValidationConfig& config);
    ValidationConfig getConfig() const { return config_; }
    
    void setTargetWCAGLevel(WCAGLevel level);
    WCAGLevel getTargetWCAGLevel() const { return config_.targetLevel; }
    
    // Validation operations
    ValidationReport validateWidget(QWidget* widget);
    ValidationReport validateApplication();
    ValidationReport validateWidgetTree(QWidget* root);
    
    // Specific validation checks
    QList<ValidationIssue> checkColorContrast(QWidget* widget);
    QList<ValidationIssue> checkKeyboardNavigation(QWidget* widget);
    QList<ValidationIssue> checkScreenReaderSupport(QWidget* widget);
    QList<ValidationIssue> checkFocusIndicators(QWidget* widget);
    QList<ValidationIssue> checkTextAlternatives(QWidget* widget);
    QList<ValidationIssue> checkSemanticStructure(QWidget* widget);
    QList<ValidationIssue> checkTimingRequirements(QWidget* widget);
    QList<ValidationIssue> checkSeizureRisks(QWidget* widget);
    QList<ValidationIssue> checkInputAssistance(QWidget* widget);
    QList<ValidationIssue> checkCompatibility(QWidget* widget);
    
    // Color contrast utilities
    static double calculateContrastRatio(const QColor& foreground, const QColor& background);
    static bool meetsContrastRequirement(const QColor& foreground, const QColor& background, WCAGLevel level);
    static QColor suggestContrastColor(const QColor& background, WCAGLevel level, bool preferDark = true);
    
    // Widget analysis utilities
    static bool isWidgetFocusable(QWidget* widget);
    static bool hasAccessibleName(QWidget* widget);
    static bool hasAccessibleDescription(QWidget* widget);
    static QString getWidgetRole(QWidget* widget);
    static QStringList getWidgetStates(QWidget* widget);
    
    // Navigation analysis
    QList<QWidget*> getTabOrder(QWidget* container);
    bool hasValidTabOrder(QWidget* container);
    QList<QWidget*> findKeyboardTraps(QWidget* container);
    
    // Screen reader analysis
    bool hasScreenReaderSupport(QWidget* widget);
    QString generateScreenReaderText(QWidget* widget);
    bool hasLiveRegionSupport(QWidget* widget);
    
    // Automated testing
    void runAutomatedTests(QWidget* widget);
    void simulateKeyboardNavigation(QWidget* container);
    void simulateScreenReaderInteraction(QWidget* widget);
    
    // Report generation
    void generateHTMLReport(const ValidationReport& report, const QString& filename);
    // Note: PDF generation would require additional dependencies
    void generateCSVReport(const ValidationReport& report, const QString& filename);
    
    // Real-time validation
    void enableRealTimeValidation(bool enabled = true);
    void setValidationInterval(int milliseconds);
    
signals:
    void validationStarted(QWidget* widget);
    void validationProgress(int percentage);
    void validationCompleted(const ValidationReport& report);
    void issueFound(const ValidationIssue& issue);
    void realTimeIssueDetected(const ValidationIssue& issue);
    
private slots:
    void performRealTimeValidation();
    void onWidgetChanged(QWidget* widget);
    
private:
    // Core validation logic
    void validateWidgetRecursive(QWidget* widget, QList<ValidationIssue>& issues);
    ValidationIssue createIssue(const QString& id, const QString& title, const QString& description,
                               ValidationSeverity severity, ValidationCategory category,
                               WCAGLevel wcagLevel, const QString& wcagCriterion,
                               QWidget* widget = nullptr);
    
    // Color analysis
    QColor getWidgetForegroundColor(QWidget* widget);
    QColor getWidgetBackgroundColor(QWidget* widget);
    bool hasTransparentBackground(QWidget* widget);
    QList<QPair<QColor, QColor>> extractColorPairs(QWidget* widget);
    
    // Focus analysis
    bool hasFocusIndicator(QWidget* widget);
    bool isFocusIndicatorVisible(QWidget* widget);
    QRect getFocusIndicatorRect(QWidget* widget);
    
    // Text analysis
    QString extractVisibleText(QWidget* widget);
    bool hasAlternativeText(QWidget* widget);
    bool hasDescriptiveText(QWidget* widget);
    int calculateTextLength(QWidget* widget);
    
    // Layout analysis
    QRect getWidgetBounds(QWidget* widget);
    bool isWidgetVisible(QWidget* widget);
    bool hasMinimumSize(QWidget* widget, int minWidth, int minHeight);
    bool overlapsWithOtherWidgets(QWidget* widget);
    
    // Interaction analysis
    bool supportsKeyboardInteraction(QWidget* widget);
    bool hasClickHandler(QWidget* widget);
    bool hasHoverEffects(QWidget* widget);
    QStringList getAvailableActions(QWidget* widget);
    
    // Animation and motion analysis
    bool hasAnimations(QWidget* widget);
    bool hasAutoplayingContent(QWidget* widget);
    bool respectsReducedMotion(QWidget* widget);
    
    // Timing analysis
    bool hasTimeouts(QWidget* widget);
    bool allowsTimeExtension(QWidget* widget);
    bool hasAutoRefresh(QWidget* widget);
    
    // Error handling analysis
    bool hasErrorMessages(QWidget* widget);
    bool hasInputValidation(QWidget* widget);
    bool providesErrorCorrection(QWidget* widget);
    
    // Assistive technology compatibility
    bool supportsScreenReaders(QWidget* widget);
    bool supportsVoiceControl(QWidget* widget);
    bool supportsMagnification(QWidget* widget);
    
    // Report generation helpers
    QString generateHTMLContent(const ValidationReport& report);
    QString generateCSSStyles();
    QString formatIssueForHTML(const ValidationIssue& issue);
    QString formatIssueForCSV(const ValidationIssue& issue);
    
    // Real-time validation helpers
    void setupRealTimeValidation();
    void trackWidgetChanges(QWidget* widget);
    void untrackWidgetChanges(QWidget* widget);
    
private:
    ValidationConfig config_;
    
    // Real-time validation
    bool real_time_validation_enabled_ = false;
    QTimer* validation_timer_ = nullptr;
    QList<QWidget*> tracked_widgets_;
    
    // Validation state
    bool validation_in_progress_ = false;
    QWidget* current_validation_root_ = nullptr;
    int validation_progress_ = 0;
    
    // Caching
    QHash<QWidget*, QList<ValidationIssue>> issue_cache_;
    QHash<QWidget*, QDateTime> cache_timestamps_;
    
    // Statistics
    int total_widgets_validated_ = 0;
    int total_issues_found_ = 0;
    QDateTime last_validation_time_;
};

} // namespace DeclarativeUI::Accessibility
