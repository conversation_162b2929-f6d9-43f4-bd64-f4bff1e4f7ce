#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QJsonDocument>
#include <memory>

#include "../../src/HotReload/EnhancedErrorRecoverySystem.hpp"
#include "../../src/HotReload/DiagnosticsEngine.hpp"

using namespace DeclarativeUI::HotReload;

class TestEnhancedErrorRecoverySystem : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Configuration tests
    void testErrorRecoveryConfigSerialization();
    void testSetConfiguration();
    void testGetConfiguration();

    // Recovery result tests
    void testRecoveryResultSerialization();
    void testRecoveryResultCreation();

    // Notification manager tests
    void testErrorNotificationManager();
    void testNotificationTypes();
    void testProgressIndicators();

    // Degradation manager tests
    void testGracefulDegradationManager();
    void testFeatureDegradation();
    void testSafeMode();

    // Recovery system tests
    void testRecoveryStrategyRegistration();
    void testRecoveryStrategyExecution();
    void testAutomaticRecovery();
    void testRecoveryTimeout();
    void testBatchRecovery();

    // Integration tests
    void testDiagnosticsIntegration();
    void testNotificationIntegration();
    void testDegradationIntegration();

    // Analytics tests
    void testRecoveryStatistics();
    void testRecoveryHistory();
    void testPerformanceMetrics();

private:
    std::unique_ptr<EnhancedErrorRecoverySystem> recovery_system_;
    std::unique_ptr<DiagnosticsEngine> diagnostics_engine_;
    std::unique_ptr<ErrorNotificationManager> notification_manager_;
    std::unique_ptr<GracefulDegradationManager> degradation_manager_;
    QTemporaryDir temp_dir_;
};

void TestEnhancedErrorRecoverySystem::initTestCase() {
    QVERIFY(temp_dir_.isValid());
}

void TestEnhancedErrorRecoverySystem::cleanupTestCase() {
    // Cleanup handled by destructors
}

void TestEnhancedErrorRecoverySystem::init() {
    // Create fresh instances for each test
    recovery_system_ = std::make_unique<EnhancedErrorRecoverySystem>();
    diagnostics_engine_ = std::make_unique<DiagnosticsEngine>();
    notification_manager_ = std::make_unique<ErrorNotificationManager>();
    degradation_manager_ = std::make_unique<GracefulDegradationManager>();
    
    // Connect components
    recovery_system_->setDiagnosticsEngine(diagnostics_engine_.get());
    recovery_system_->setNotificationManager(notification_manager_.get());
    recovery_system_->setDegradationManager(degradation_manager_.get());
}

void TestEnhancedErrorRecoverySystem::cleanup() {
    recovery_system_.reset();
    diagnostics_engine_.reset();
    notification_manager_.reset();
    degradation_manager_.reset();
}

void TestEnhancedErrorRecoverySystem::testErrorRecoveryConfigSerialization() {
    ErrorRecoveryConfig config;
    config.auto_recovery_enabled = false;
    config.recovery_delay_ms = 2000;
    config.max_recovery_attempts = 5;
    config.show_system_notifications = false;
    
    // Test serialization
    QJsonObject json = config.toJson();
    QCOMPARE(json["auto_recovery_enabled"].toBool(), false);
    QCOMPARE(json["recovery_delay_ms"].toInt(), 2000);
    QCOMPARE(json["max_recovery_attempts"].toInt(), 5);
    QCOMPARE(json["show_system_notifications"].toBool(), false);
    
    // Test deserialization
    ErrorRecoveryConfig restored_config = ErrorRecoveryConfig::fromJson(json);
    QCOMPARE(restored_config.auto_recovery_enabled, false);
    QCOMPARE(restored_config.recovery_delay_ms, 2000);
    QCOMPARE(restored_config.max_recovery_attempts, 5);
    QCOMPARE(restored_config.show_system_notifications, false);
}

void TestEnhancedErrorRecoverySystem::testSetConfiguration() {
    ErrorRecoveryConfig config;
    config.auto_recovery_enabled = false;
    config.recovery_delay_ms = 3000;
    
    recovery_system_->setConfiguration(config);
    
    ErrorRecoveryConfig retrieved_config = recovery_system_->getConfiguration();
    QCOMPARE(retrieved_config.auto_recovery_enabled, false);
    QCOMPARE(retrieved_config.recovery_delay_ms, 3000);
}

void TestEnhancedErrorRecoverySystem::testGetConfiguration() {
    // Test default configuration
    ErrorRecoveryConfig config = recovery_system_->getConfiguration();
    QVERIFY(config.auto_recovery_enabled);
    QVERIFY(config.graceful_degradation_enabled);
    QVERIFY(config.user_notifications_enabled);
}

void TestEnhancedErrorRecoverySystem::testRecoveryResultSerialization() {
    RecoveryResult result = RecoveryResult::createSuccess("test-op-123", "Test Strategy");
    result.target_id = "test-target";
    result.duration_ms = 1500;
    result.actions_taken.append("Action 1");
    result.actions_taken.append("Action 2");
    result.metadata["key"] = "value";
    
    // Test serialization
    QJsonObject json = result.toJson();
    QCOMPARE(json["status"].toInt(), static_cast<int>(RecoveryResult::Success));
    QCOMPARE(json["operation_id"].toString(), "test-op-123");
    QCOMPARE(json["strategy_name"].toString(), "Test Strategy");
    QCOMPARE(json["target_id"].toString(), "test-target");
    QCOMPARE(json["duration_ms"].toInt(), 1500);
    
    QJsonArray actions = json["actions_taken"].toArray();
    QCOMPARE(actions.size(), 2);
    QCOMPARE(actions[0].toString(), "Action 1");
    QCOMPARE(actions[1].toString(), "Action 2");
    
    // Test deserialization
    RecoveryResult restored_result = RecoveryResult::fromJson(json);
    QCOMPARE(restored_result.status, RecoveryResult::Success);
    QCOMPARE(restored_result.operation_id, "test-op-123");
    QCOMPARE(restored_result.strategy_name, "Test Strategy");
    QCOMPARE(restored_result.target_id, "test-target");
    QCOMPARE(restored_result.duration_ms, 1500);
    QCOMPARE(restored_result.actions_taken.size(), 2);
    QCOMPARE(restored_result.actions_taken[0], "Action 1");
    QCOMPARE(restored_result.actions_taken[1], "Action 2");
}

void TestEnhancedErrorRecoverySystem::testRecoveryResultCreation() {
    // Test success result creation
    RecoveryResult success_result = RecoveryResult::createSuccess("op-1", "Strategy A");
    QCOMPARE(success_result.status, RecoveryResult::Success);
    QCOMPARE(success_result.operation_id, "op-1");
    QCOMPARE(success_result.strategy_name, "Strategy A");
    QVERIFY(success_result.isSuccess());
    
    // Test failure result creation
    RecoveryResult failure_result = RecoveryResult::createFailure("op-2", "Test error");
    QCOMPARE(failure_result.status, RecoveryResult::Failed);
    QCOMPARE(failure_result.operation_id, "op-2");
    QCOMPARE(failure_result.error_message, "Test error");
    QVERIFY(!failure_result.isSuccess());
}

void TestEnhancedErrorRecoverySystem::testErrorNotificationManager() {
    QSignalSpy notification_spy(notification_manager_.get(), &ErrorNotificationManager::notificationShown);
    
    // Test basic notification
    notification_manager_->showNotification(ErrorNotificationManager::Info, "Test Title", "Test Message");
    
    QCOMPARE(notification_spy.count(), 1);
    QList<QVariant> arguments = notification_spy.takeFirst();
    QCOMPARE(arguments.at(0).toInt(), static_cast<int>(ErrorNotificationManager::Info));
    QCOMPARE(arguments.at(1).toString(), "Test Title");
    QCOMPARE(arguments.at(2).toString(), "Test Message");
}

void TestEnhancedErrorRecoverySystem::testNotificationTypes() {
    QSignalSpy notification_spy(notification_manager_.get(), &ErrorNotificationManager::notificationShown);
    
    // Test different notification types
    notification_manager_->showNotification(ErrorNotificationManager::Warning, "Warning", "Warning message");
    notification_manager_->showNotification(ErrorNotificationManager::Error, "Error", "Error message");
    notification_manager_->showNotification(ErrorNotificationManager::Critical, "Critical", "Critical message");
    notification_manager_->showNotification(ErrorNotificationManager::Recovery, "Recovery", "Recovery message");
    notification_manager_->showNotification(ErrorNotificationManager::Success, "Success", "Success message");
    
    QCOMPARE(notification_spy.count(), 5);
    
    // Verify each notification type
    for (int i = 0; i < 5; ++i) {
        QList<QVariant> arguments = notification_spy.at(i);
        QVERIFY(arguments.at(0).toInt() >= 0 && arguments.at(0).toInt() <= 5);
    }
}

void TestEnhancedErrorRecoverySystem::testProgressIndicators() {
    // Test progress dialog
    notification_manager_->showProgressDialog("Test Progress", "Processing...", 100);
    
    // Test progress updates
    notification_manager_->updateProgress(25, "Step 1");
    notification_manager_->updateProgress(50, "Step 2");
    notification_manager_->updateProgress(75, "Step 3");
    notification_manager_->updateProgress(100, "Complete");
    
    // Test hiding progress
    notification_manager_->hideProgress();
    
    // No assertions needed - just verify no crashes
    QVERIFY(true);
}

void TestEnhancedErrorRecoverySystem::testGracefulDegradationManager() {
    QSignalSpy degradation_enabled_spy(degradation_manager_.get(), &GracefulDegradationManager::degradationEnabled);
    QSignalSpy degradation_disabled_spy(degradation_manager_.get(), &GracefulDegradationManager::degradationDisabled);
    
    // Test enabling degradation
    degradation_manager_->enableDegradation("test_feature", GracefulDegradationManager::Moderate, "Test reason");
    
    QCOMPARE(degradation_enabled_spy.count(), 1);
    QList<QVariant> arguments = degradation_enabled_spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), "test_feature");
    QCOMPARE(arguments.at(1).toInt(), static_cast<int>(GracefulDegradationManager::Moderate));
    QCOMPARE(arguments.at(2).toString(), "Test reason");
    
    // Test feature status
    QVERIFY(degradation_manager_->isFeatureEnabled("test_feature")); // Moderate level still allows feature
    QCOMPARE(degradation_manager_->getCurrentDegradationLevel(), GracefulDegradationManager::Moderate);
    
    // Test disabling degradation
    degradation_manager_->disableDegradation("test_feature");
    
    QCOMPARE(degradation_disabled_spy.count(), 1);
    QCOMPARE(degradation_disabled_spy.takeFirst().at(0).toString(), "test_feature");
    QCOMPARE(degradation_manager_->getCurrentDegradationLevel(), GracefulDegradationManager::None);
}

void TestEnhancedErrorRecoverySystem::testFeatureDegradation() {
    // Test different degradation levels
    degradation_manager_->enableDegradation("feature1", GracefulDegradationManager::Minimal, "Minor issue");
    degradation_manager_->enableDegradation("feature2", GracefulDegradationManager::Moderate, "Moderate issue");
    degradation_manager_->enableDegradation("feature3", GracefulDegradationManager::Severe, "Severe issue");
    
    // Test feature availability
    QVERIFY(degradation_manager_->isFeatureEnabled("feature1"));
    QVERIFY(degradation_manager_->isFeatureEnabled("feature2"));
    QVERIFY(!degradation_manager_->isFeatureEnabled("feature3")); // Severe level disables feature
    
    // Test feature lists
    QStringList disabled_features = degradation_manager_->getDisabledFeatures();
    QStringList degraded_features = degradation_manager_->getDegradedFeatures();
    
    QVERIFY(disabled_features.contains("feature3"));
    QVERIFY(degraded_features.contains("feature1"));
    QVERIFY(degraded_features.contains("feature2"));
    QVERIFY(!degraded_features.contains("feature3")); // Severe is disabled, not just degraded
}

void TestEnhancedErrorRecoverySystem::testSafeMode() {
    QSignalSpy safe_mode_entered_spy(degradation_manager_.get(), &GracefulDegradationManager::safeModeEntered);
    QSignalSpy safe_mode_exited_spy(degradation_manager_.get(), &GracefulDegradationManager::safeModeExited);
    
    // Test entering safe mode
    degradation_manager_->enterSafeMode("Critical system error");
    
    QCOMPARE(safe_mode_entered_spy.count(), 1);
    QCOMPARE(safe_mode_entered_spy.takeFirst().at(0).toString(), "Critical system error");
    QVERIFY(degradation_manager_->isInSafeMode());
    QCOMPARE(degradation_manager_->getCurrentDegradationLevel(), GracefulDegradationManager::SafeMode);
    
    // Test exiting safe mode
    degradation_manager_->exitSafeMode();
    
    QCOMPARE(safe_mode_exited_spy.count(), 1);
    QVERIFY(!degradation_manager_->isInSafeMode());
    QCOMPARE(degradation_manager_->getCurrentDegradationLevel(), GracefulDegradationManager::None);
}

void TestEnhancedErrorRecoverySystem::testRecoveryStrategyRegistration() {
    // Test initial strategies (built-in)
    QStringList initial_strategies = recovery_system_->getAvailableStrategies();
    QVERIFY(initial_strategies.size() > 0);

    // Create a custom strategy
    AdvancedRecoveryStrategy custom_strategy("Custom Strategy", "Custom recovery action", "custom");
    custom_strategy.priority = 100;
    custom_strategy.condition_check = [](const DiagnosticInfo&) { return true; };
    custom_strategy.recovery_action = [](const DiagnosticInfo&) -> RecoveryResult {
        return RecoveryResult::createSuccess("test-op", "Custom Strategy");
    };

    // Register the strategy
    recovery_system_->registerRecoveryStrategy(custom_strategy);

    QStringList strategies_after_registration = recovery_system_->getAvailableStrategies();
    QCOMPARE(strategies_after_registration.size(), initial_strategies.size() + 1);
    QVERIFY(strategies_after_registration.contains("Custom Strategy"));

    // Test enabling/disabling strategy
    recovery_system_->enableRecoveryStrategy("Custom Strategy", false);
    QStringList enabled_strategies = recovery_system_->getEnabledStrategies();
    QVERIFY(!enabled_strategies.contains("Custom Strategy"));

    recovery_system_->enableRecoveryStrategy("Custom Strategy", true);
    enabled_strategies = recovery_system_->getEnabledStrategies();
    QVERIFY(enabled_strategies.contains("Custom Strategy"));

    // Test unregistering strategy
    recovery_system_->unregisterRecoveryStrategy("Custom Strategy");
    QStringList strategies_after_unregistration = recovery_system_->getAvailableStrategies();
    QCOMPARE(strategies_after_unregistration.size(), initial_strategies.size());
    QVERIFY(!strategies_after_unregistration.contains("Custom Strategy"));
}

void TestEnhancedErrorRecoverySystem::testRecoveryStrategyExecution() {
    QSignalSpy recovery_started_spy(recovery_system_.get(), &EnhancedErrorRecoverySystem::recoveryStarted);
    QSignalSpy recovery_completed_spy(recovery_system_.get(), &EnhancedErrorRecoverySystem::recoveryCompleted);

    // Create a test diagnostic
    DiagnosticInfo diagnostic;
    diagnostic.id = "test-diagnostic-001";
    diagnostic.title = "Test Error";
    diagnostic.description = "Test error description";
    diagnostic.severity = DiagnosticInfo::Error;
    diagnostic.category = DiagnosticInfo::Compilation;
    diagnostic.auto_recoverable = true;

    // Add diagnostic to engine
    diagnostics_engine_->reportError(diagnostic);

    // Attempt recovery
    auto future = recovery_system_->attemptRecovery(diagnostic.id);
    RecoveryResult result = future.result();

    // Verify signals were emitted
    QVERIFY(recovery_started_spy.count() > 0);
    QVERIFY(recovery_completed_spy.count() > 0);

    // Verify result
    QVERIFY(result.isSuccess());
    QCOMPARE(result.target_id, diagnostic.id);
    QVERIFY(!result.strategy_name.isEmpty());
}

void TestEnhancedErrorRecoverySystem::testAutomaticRecovery() {
    QSignalSpy recovery_started_spy(recovery_system_.get(), &EnhancedErrorRecoverySystem::recoveryStarted);

    // Create a recoverable diagnostic
    DiagnosticInfo diagnostic;
    diagnostic.id = "auto-recovery-test";
    diagnostic.title = "Auto Recovery Test";
    diagnostic.description = "Test automatic recovery";
    diagnostic.severity = DiagnosticInfo::Error;
    diagnostic.category = DiagnosticInfo::FileSystem;
    diagnostic.auto_recoverable = true;
    diagnostic.file_path = temp_dir_.filePath("test_file.txt");

    // Report diagnostic (should trigger automatic recovery)
    diagnostics_engine_->reportError(diagnostic);

    // Wait a bit for automatic recovery to start
    QTest::qWait(100);

    // Verify automatic recovery was triggered
    QVERIFY(recovery_started_spy.count() > 0);
}

void TestEnhancedErrorRecoverySystem::testRecoveryTimeout() {
    // Set a very short timeout for testing
    ErrorRecoveryConfig config = recovery_system_->getConfiguration();
    config.recovery_timeout_ms = 100; // 100ms timeout
    recovery_system_->setConfiguration(config);

    QSignalSpy recovery_failed_spy(recovery_system_.get(), &EnhancedErrorRecoverySystem::recoveryFailed);

    // Create a strategy that takes longer than the timeout
    AdvancedRecoveryStrategy slow_strategy("Slow Strategy", "Takes too long", "slow");
    slow_strategy.priority = 100;
    slow_strategy.condition_check = [](const DiagnosticInfo&) { return true; };
    slow_strategy.recovery_action = [](const DiagnosticInfo&) -> RecoveryResult {
        QThread::msleep(200); // Sleep longer than timeout
        return RecoveryResult::createSuccess("test-op", "Slow Strategy");
    };

    recovery_system_->registerRecoveryStrategy(slow_strategy);

    // Create a test diagnostic
    DiagnosticInfo diagnostic;
    diagnostic.id = "timeout-test";
    diagnostic.title = "Timeout Test";
    diagnostic.description = "Test recovery timeout";
    diagnostic.severity = DiagnosticInfo::Error;
    diagnostic.auto_recoverable = true;

    diagnostics_engine_->reportError(diagnostic);

    // Wait for timeout to occur
    QTest::qWait(300);

    // Verify timeout was handled
    QVERIFY(recovery_failed_spy.count() > 0);
}

void TestEnhancedErrorRecoverySystem::testBatchRecovery() {
    // Create multiple diagnostics
    QStringList diagnostic_ids;
    for (int i = 0; i < 3; ++i) {
        DiagnosticInfo diagnostic;
        diagnostic.id = QString("batch-test-%1").arg(i);
        diagnostic.title = QString("Batch Test %1").arg(i);
        diagnostic.description = QString("Batch test diagnostic %1").arg(i);
        diagnostic.severity = DiagnosticInfo::Warning;
        diagnostic.auto_recoverable = true;

        diagnostics_engine_->reportError(diagnostic);
        diagnostic_ids.append(diagnostic.id);
    }

    // Attempt batch recovery
    auto future = recovery_system_->attemptBatchRecovery(diagnostic_ids);
    QList<RecoveryResult> results = future.result();

    // Verify results
    QCOMPARE(results.size(), 3);
    for (const auto& result : results) {
        QVERIFY(result.isSuccess());
        QVERIFY(diagnostic_ids.contains(result.target_id));
    }
}

void TestEnhancedErrorRecoverySystem::testDiagnosticsIntegration() {
    QSignalSpy critical_error_spy(recovery_system_.get(), &EnhancedErrorRecoverySystem::criticalErrorRequiresAttention);

    // Create a critical diagnostic
    DiagnosticInfo critical_diagnostic;
    critical_diagnostic.id = "critical-integration-test";
    critical_diagnostic.title = "Critical Integration Test";
    critical_diagnostic.description = "Test critical error handling";
    critical_diagnostic.severity = DiagnosticInfo::Critical;
    critical_diagnostic.category = DiagnosticInfo::Memory;

    // Report critical diagnostic
    diagnostics_engine_->reportError(critical_diagnostic);

    // Simulate critical error detection
    emit diagnostics_engine_->criticalErrorDetected(critical_diagnostic);

    // Verify critical error signal was emitted
    QCOMPARE(critical_error_spy.count(), 1);
    QList<QVariant> arguments = critical_error_spy.takeFirst();
    DiagnosticInfo received_diagnostic = arguments.at(0).value<DiagnosticInfo>();
    QCOMPARE(received_diagnostic.id, critical_diagnostic.id);
}

void TestEnhancedErrorRecoverySystem::testNotificationIntegration() {
    QSignalSpy notification_spy(notification_manager_.get(), &ErrorNotificationManager::notificationShown);

    // Create a diagnostic that should trigger notifications
    DiagnosticInfo diagnostic;
    diagnostic.id = "notification-integration-test";
    diagnostic.title = "Notification Integration Test";
    diagnostic.description = "Test notification integration";
    diagnostic.severity = DiagnosticInfo::Error;
    diagnostic.auto_recoverable = true;

    // Report diagnostic and trigger recovery
    diagnostics_engine_->reportError(diagnostic);
    auto future = recovery_system_->attemptRecovery(diagnostic.id);
    RecoveryResult result = future.result();

    // Verify notifications were shown
    QVERIFY(notification_spy.count() > 0);

    // Check for recovery-related notifications
    bool found_recovery_notification = false;
    for (int i = 0; i < notification_spy.count(); ++i) {
        QList<QVariant> arguments = notification_spy.at(i);
        int type = arguments.at(0).toInt();
        if (type == static_cast<int>(ErrorNotificationManager::Recovery) ||
            type == static_cast<int>(ErrorNotificationManager::Success)) {
            found_recovery_notification = true;
            break;
        }
    }
    QVERIFY(found_recovery_notification);
}

void TestEnhancedErrorRecoverySystem::testDegradationIntegration() {
    QSignalSpy degradation_triggered_spy(recovery_system_.get(), &EnhancedErrorRecoverySystem::gracefulDegradationTriggered);

    // Create a critical diagnostic that should trigger degradation
    DiagnosticInfo critical_diagnostic;
    critical_diagnostic.id = "degradation-integration-test";
    critical_diagnostic.title = "Degradation Integration Test";
    critical_diagnostic.description = "Test degradation integration";
    critical_diagnostic.severity = DiagnosticInfo::Critical;
    critical_diagnostic.category = DiagnosticInfo::Configuration;

    // Report critical diagnostic
    diagnostics_engine_->reportError(critical_diagnostic);

    // Simulate critical error detection (should trigger degradation)
    emit diagnostics_engine_->criticalErrorDetected(critical_diagnostic);

    // Verify degradation was triggered
    QVERIFY(degradation_triggered_spy.count() > 0);

    // Verify degradation manager state
    QVERIFY(degradation_manager_->getCurrentDegradationLevel() > GracefulDegradationManager::None);
}

void TestEnhancedErrorRecoverySystem::testRecoveryStatistics() {
    // Perform some recovery operations to generate statistics
    for (int i = 0; i < 5; ++i) {
        DiagnosticInfo diagnostic;
        diagnostic.id = QString("stats-test-%1").arg(i);
        diagnostic.title = QString("Stats Test %1").arg(i);
        diagnostic.description = QString("Statistics test diagnostic %1").arg(i);
        diagnostic.severity = (i % 2 == 0) ? DiagnosticInfo::Warning : DiagnosticInfo::Error;
        diagnostic.auto_recoverable = true;

        diagnostics_engine_->reportError(diagnostic);

        auto future = recovery_system_->attemptRecovery(diagnostic.id);
        RecoveryResult result = future.result();
        Q_UNUSED(result)
    }

    // Get statistics
    QJsonObject stats = recovery_system_->getRecoveryStatistics();

    // Verify statistics contain expected data
    QVERIFY(stats.contains("total_recoveries"));
    QVERIFY(stats.contains("successful_recoveries"));
    QVERIFY(stats.contains("failed_recoveries"));
    QVERIFY(stats.contains("success_rate"));

    QVERIFY(stats["total_recoveries"].toInt() >= 5);
    QVERIFY(stats["success_rate"].toDouble() >= 0.0);
    QVERIFY(stats["success_rate"].toDouble() <= 1.0);
}

void TestEnhancedErrorRecoverySystem::testRecoveryHistory() {
    // Perform a recovery operation
    DiagnosticInfo diagnostic;
    diagnostic.id = "history-test";
    diagnostic.title = "History Test";
    diagnostic.description = "Test recovery history";
    diagnostic.severity = DiagnosticInfo::Error;
    diagnostic.auto_recoverable = true;

    diagnostics_engine_->reportError(diagnostic);

    auto future = recovery_system_->attemptRecovery(diagnostic.id);
    RecoveryResult result = future.result();

    // Get recovery history
    QList<RecoveryResult> history = recovery_system_->getRecoveryHistory();

    // Verify history contains our recovery
    QVERIFY(history.size() > 0);

    bool found_our_recovery = false;
    for (const auto& historical_result : history) {
        if (historical_result.target_id == diagnostic.id) {
            found_our_recovery = true;
            break;
        }
    }
    QVERIFY(found_our_recovery);
}

void TestEnhancedErrorRecoverySystem::testPerformanceMetrics() {
    // Get most effective strategies
    QStringList effective_strategies = recovery_system_->getMostEffectiveStrategies();
    QVERIFY(effective_strategies.size() >= 0); // May be empty if no recoveries yet

    // Get most problematic files
    QStringList problematic_files = recovery_system_->getMostProblematicFiles();
    QVERIFY(problematic_files.size() >= 0); // May be empty if no file-related issues

    // Get overall success rate
    double success_rate = recovery_system_->getOverallRecoverySuccessRate();
    QVERIFY(success_rate >= 0.0 && success_rate <= 1.0);
}

QTEST_MAIN(TestEnhancedErrorRecoverySystem)
#include "test_enhanced_error_recovery_system.moc"
