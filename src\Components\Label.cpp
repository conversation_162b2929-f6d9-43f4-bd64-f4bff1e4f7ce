// Components/Label.cpp
#include "Label.hpp"

namespace DeclarativeUI::Components {

// **Implementation**
Label::Label(QObject *parent)
    : UIElement(parent), label_widget_(nullptr) {}

Label &Label::text(const QString &text) {
    return static_cast<Label &>(setProperty("text", text));
}

Label &Label::pixmap(const QPixmap &pixmap) {
    return static_cast<Label &>(setProperty("pixmap", pixmap));
}

Label &Label::movie(QMovie *movie) {
    return static_cast<Label &>(
        setProperty("movie", QVariant::fromValue(movie)));
}

Label &Label::alignment(Qt::Alignment alignment) {
    return static_cast<Label &>(
        setProperty("alignment", static_cast<int>(alignment)));
}

Label &Label::wordWrap(bool wrap) {
    return static_cast<Label &>(setProperty("wordWrap", wrap));
}

Label &Label::font(const QFont &font) {
    return static_cast<Label &>(setProperty("font", font));
}

Label &Label::color(const QColor &color) {
    // Set text color via stylesheet
    QString style = QString("QLabel { color: %1; }").arg(color.name());
    return static_cast<Label &>(setProperty("styleSheet", style));
}

Label &Label::backgroundColor(const QColor &color) {
    QString style =
        QString("QLabel { background-color: %1; }").arg(color.name());
    return static_cast<Label &>(setProperty("styleSheet", style));
}

Label &Label::borderColor(const QColor &color) {
    QString style =
        QString("QLabel { border: 1px solid %1; }").arg(color.name());
    return static_cast<Label &>(setProperty("styleSheet", style));
}

Label &Label::borderWidth(int width) {
    QString style = QString("QLabel { border-width: %1px; }").arg(width);
    return static_cast<Label &>(setProperty("styleSheet", style));
}

Label &Label::borderRadius(int radius) {
    QString style = QString("QLabel { border-radius: %1px; }").arg(radius);
    return static_cast<Label &>(setProperty("styleSheet", style));
}

Label &Label::padding(int padding) {
    QString style = QString("QLabel { padding: %1px; }").arg(padding);
    return static_cast<Label &>(setProperty("styleSheet", style));
}

Label &Label::margin(int margin) {
    QString style = QString("QLabel { margin: %1px; }").arg(margin);
    return static_cast<Label &>(setProperty("styleSheet", style));
}

Label &Label::linkActivation(bool enabled) {
    return static_cast<Label &>(setProperty("openExternalLinks", enabled));
}

Label &Label::onLinkActivated(
    std::function<void(const QString &)> handler) {
    link_activated_handler_ = std::move(handler);
    return *this;
}

Label &Label::onLinkHovered(
    std::function<void(const QString &)> handler) {
    link_hovered_handler_ = std::move(handler);
    return *this;
}

Label &Label::style(const QString &stylesheet) {
    return static_cast<Label &>(setProperty("styleSheet", stylesheet));
}

void Label::initialize() {
    if (!label_widget_) {
        label_widget_ = new QLabel();
        setWidget(label_widget_);

        // Connect signals
        if (link_activated_handler_) {
            connect(
                label_widget_, &QLabel::linkActivated, this,
                [this](const QString &link) { link_activated_handler_(link); });
        }

        if (link_hovered_handler_) {
            connect(
                label_widget_, &QLabel::linkHovered, this,
                [this](const QString &link) { link_hovered_handler_(link); });
        }
    }
}

QString Label::getText() const {
    return label_widget_ ? label_widget_->text() : QString();
}

QPixmap Label::getPixmap() const {
    if (label_widget_) {
        QPixmap pixmap = label_widget_->pixmap();
        if (!pixmap.isNull()) {
            return pixmap;
        }
    }
    return QPixmap();
}

Qt::Alignment Label::getAlignment() const {
    return label_widget_ ? label_widget_->alignment() : Qt::AlignLeft;
}

bool Label::getWordWrap() const {
    return label_widget_ ? label_widget_->wordWrap() : false;
}

void Label::setText(const QString &text) {
    if (label_widget_) {
        label_widget_->setText(text);
    }
}

void Label::setPixmap(const QPixmap &pixmap) {
    if (label_widget_) {
        label_widget_->setPixmap(pixmap);
    }
}

void Label::setAlignment(Qt::Alignment alignment) {
    if (label_widget_) {
        label_widget_->setAlignment(alignment);
    }
}

void Label::setWordWrap(bool wrap) {
    if (label_widget_) {
        label_widget_->setWordWrap(wrap);
    }
}

void Label::clear() {
    if (label_widget_) {
        label_widget_->clear();
    }
}

}  // namespace DeclarativeUI::Components
