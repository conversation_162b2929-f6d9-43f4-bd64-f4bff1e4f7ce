# DeclarativeUI Framework Package Configuration File
# This file is used by find_package(DeclarativeUI) to locate the framework

@PACKAGE_INIT@

# =============================================================================
# Package Information
# =============================================================================
set(DECLARATIVE_UI_VERSION "@DECLARATIVE_UI_VERSION@")
set(DECLARATIVE_UI_VERSION_MAJOR "@DECLARATIVE_UI_VERSION_MAJOR@")
set(DECLARATIVE_UI_VERSION_MINOR "@DECLARATIVE_UI_VERSION_MINOR@")
set(DECLARATIVE_UI_VERSION_PATCH "@DECLARATIVE_UI_VERSION_PATCH@")

# =============================================================================
# Feature Flags
# =============================================================================
set(DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED @BUILD_COMMAND_SYSTEM@)
set(DECLARATIVE_UI_ADAPTERS_ENABLED @BUILD_ADAPTERS@)

# =============================================================================
# Dependencies
# =============================================================================

# Find required Qt6 components
find_dependency(Qt6 REQUIRED COMPONENTS Core Widgets Network)

# Check for optional Qt6 components
find_dependency(Qt6 QUIET COMPONENTS Test)

# =============================================================================
# Imported Targets
# =============================================================================

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/DeclarativeUITargets.cmake")

# =============================================================================
# Interface Libraries
# =============================================================================

# Create interface library for easier usage
if(NOT TARGET DeclarativeUI::DeclarativeUI)
    add_library(DeclarativeUI::DeclarativeUI INTERFACE IMPORTED)
    target_link_libraries(DeclarativeUI::DeclarativeUI INTERFACE DeclarativeUI)
endif()

# Create interface library for command system (if enabled)
if(DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED AND NOT TARGET DeclarativeUI::Commands)
    add_library(DeclarativeUI::Commands INTERFACE IMPORTED)
    target_link_libraries(DeclarativeUI::Commands INTERFACE DeclarativeUI)
    target_compile_definitions(DeclarativeUI::Commands INTERFACE DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED)
endif()

# Create interface library for adapters (if enabled)
if(DECLARATIVE_UI_ADAPTERS_ENABLED AND NOT TARGET DeclarativeUI::Adapters)
    add_library(DeclarativeUI::Adapters INTERFACE IMPORTED)
    target_link_libraries(DeclarativeUI::Adapters INTERFACE DeclarativeUI)
    target_compile_definitions(DeclarativeUI::Adapters INTERFACE DECLARATIVE_UI_ADAPTERS_ENABLED)
endif()

# =============================================================================
# Variables for Backward Compatibility
# =============================================================================

set(DECLARATIVE_UI_FOUND TRUE)
set(DECLARATIVE_UI_INCLUDE_DIRS "@PACKAGE_DECLARATIVE_UI_INSTALL_INCLUDEDIR@")
set(DECLARATIVE_UI_LIBRARIES DeclarativeUI::DeclarativeUI)

# =============================================================================
# Helper Functions
# =============================================================================

# Function to check if a feature is available
function(declarative_ui_check_feature feature_name result_var)
    if(feature_name STREQUAL "CommandSystem")
        set(${result_var} ${DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED} PARENT_SCOPE)
    elseif(feature_name STREQUAL "Adapters")
        set(${result_var} ${DECLARATIVE_UI_ADAPTERS_ENABLED} PARENT_SCOPE)
    else()
        set(${result_var} FALSE PARENT_SCOPE)
    endif()
endfunction()

# =============================================================================
# Status Information
# =============================================================================

if(NOT DeclarativeUI_FIND_QUIETLY)
    message(STATUS "Found DeclarativeUI: ${DECLARATIVE_UI_VERSION}")
    message(STATUS "  Command System: ${DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED}")
    message(STATUS "  Adapters: ${DECLARATIVE_UI_ADAPTERS_ENABLED}")
endif()

# =============================================================================
# Component Validation
# =============================================================================

# Check requested components
set(_declarative_ui_missing_components)

foreach(_component ${DeclarativeUI_FIND_COMPONENTS})
    if(_component STREQUAL "CommandSystem")
        if(NOT DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED)
            list(APPEND _declarative_ui_missing_components ${_component})
        endif()
    elseif(_component STREQUAL "Adapters")
        if(NOT DECLARATIVE_UI_ADAPTERS_ENABLED)
            list(APPEND _declarative_ui_missing_components ${_component})
        endif()
    else()
        list(APPEND _declarative_ui_missing_components ${_component})
    endif()
endforeach()

if(_declarative_ui_missing_components)
    set(DeclarativeUI_FOUND FALSE)
    set(DeclarativeUI_NOT_FOUND_MESSAGE 
        "The following DeclarativeUI components were not found: ${_declarative_ui_missing_components}")
endif()

check_required_components(DeclarativeUI)
