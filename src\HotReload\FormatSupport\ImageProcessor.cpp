#include "ImageProcessor.hpp"
#include <QDebug>
#include <QFileInfo>
#include <QImageReader>
#include <QImageWriter>
#include <QStandardPaths>
#include <QDir>

namespace DeclarativeUI::HotReload::FormatSupport {

ImageProcessor::ImageProcessor(QObject* parent) 
    : IFormatProcessor(parent), max_cache_size_mb_(100) {
    initializeSupportedFormats();
    
    // Set cache size (in KB, so multiply by 1024 for MB)
    image_cache_.setMaxCost(max_cache_size_mb_ * 1024);
}

void ImageProcessor::initializeSupportedFormats() {
    // Get supported formats from Qt
    QList<QByteArray> supported_read_formats = QImageReader::supportedImageFormats();
    
    for (const QByteArray& format : supported_read_formats) {
        supported_formats_.append(QString::fromLatin1(format).toLower());
    }
    
    // Ensure common formats are included
    QStringList common_formats = {"png", "jpg", "jpeg", "gif", "bmp", "svg", "webp", "ico"};
    for (const QString& format : common_formats) {
        if (!supported_formats_.contains(format)) {
            supported_formats_.append(format);
        }
    }
}

QStringList ImageProcessor::getSupportedExtensions() const {
    return supported_formats_;
}

bool ImageProcessor::canProcess(const QString& file_path) const {
    QFileInfo file_info(file_path);
    QString extension = file_info.suffix().toLower();
    return supported_formats_.contains(extension);
}

ProcessingResult ImageProcessor::processFile(const QString& file_path, const ProcessingConfig& config) {
    startPerformanceMeasurement();
    emit processingStarted(file_path);
    
    try {
        if (!QFileInfo::exists(file_path)) {
            return ProcessingResult::createError("Image file does not exist: " + file_path);
        }
        
        if (!validateFile(file_path)) {
            return ProcessingResult::createError("Invalid image file: " + file_path);
        }
        
        // Load image and create metadata
        QJsonObject metadata = createImageMetadata(file_path);
        
        // Handle optimization if requested
        QString processed_content = file_path;
        if (config.enable_minification) {
            QString temp_path = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + 
                               "/optimized_" + QFileInfo(file_path).fileName();
            if (optimizeImage(file_path, temp_path, 85)) {
                processed_content = temp_path;
                qint64 original_size = getImageFileSize(file_path);
                qint64 optimized_size = getImageFileSize(temp_path);
                emit imageOptimized(file_path, original_size, optimized_size);
                metadata["optimized"] = true;
                metadata["optimized_path"] = temp_path;
                metadata["compression_ratio"] = double(optimized_size) / double(original_size);
            }
        }
        
        // Update cache and tracking
        updateFileTracking(file_path);
        
        ProcessingResult result = ProcessingResult::createSuccess(processed_content, metadata);
        result.processing_time_ms = endPerformanceMeasurement();
        
        emit processingFinished(file_path, result);
        return result;
        
    } catch (const std::exception& e) {
        QString error = QString("Image processing error: %1").arg(e.what());
        emit processingError(file_path, error);
        return ProcessingResult::createError(error);
    }
}

ProcessingResult ImageProcessor::processContent(const QString& content, const QString& file_path, const ProcessingConfig& config) {
    Q_UNUSED(config)
    
    try {
        // For images, content processing means handling base64 encoded data
        if (content.startsWith("data:image/")) {
            // Extract base64 data
            int comma_pos = content.indexOf(',');
            if (comma_pos == -1) {
                return ProcessingResult::createError("Invalid base64 image data format");
            }
            
            QByteArray base64_data = content.mid(comma_pos + 1).toUtf8();
            
            // Create temporary file
            QString temp_path = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + 
                               "/temp_" + QFileInfo(file_path).fileName();
            
            if (!base64ToImage(base64_data, temp_path)) {
                return ProcessingResult::createError("Failed to decode base64 image data");
            }
            
            // Process the temporary file
            return processFile(temp_path, config);
        }
        
        // If content is a file path, process it directly
        return processFile(content, config);
        
    } catch (const std::exception& e) {
        QString error = QString("Image content processing error: %1").arg(e.what());
        emit processingError(file_path, error);
        return ProcessingResult::createError(error);
    }
}

bool ImageProcessor::validateFile(const QString& file_path) const {
    if (!QFileInfo::exists(file_path)) {
        return false;
    }
    
    QImageReader reader(file_path);
    return reader.canRead();
}

bool ImageProcessor::validateContent(const QString& content) const {
    // For base64 content
    if (content.startsWith("data:image/")) {
        int comma_pos = content.indexOf(',');
        if (comma_pos == -1) return false;
        
        QByteArray base64_data = content.mid(comma_pos + 1).toUtf8();
        QByteArray image_data = QByteArray::fromBase64(base64_data);
        
        QBuffer buffer(&image_data);
        buffer.open(QIODevice::ReadOnly);
        
        QImageReader reader(&buffer);
        return reader.canRead();
    }
    
    // For file path content
    return validateFile(content);
}

ProcessingConfig ImageProcessor::getDefaultConfig() const {
    ProcessingConfig config;
    config.enable_live_injection = true;
    config.enable_caching = true;
    config.enable_minification = false; // Image optimization
    config.max_processing_time_ms = 2000;
    return config;
}

bool ImageProcessor::isAvailable() const {
    return !QImageReader::supportedImageFormats().isEmpty();
}

ProcessingResult ImageProcessor::prepareLiveInjection(const QString& content, const QString& file_path) {
    try {
        // Load image into cache for immediate access
        QPixmap pixmap = loadImageWithCache(file_path);
        if (pixmap.isNull()) {
            return ProcessingResult::createError("Failed to load image for live injection");
        }
        
        emit imageLoaded(file_path, pixmap);
        
        // Convert to base64 for web-like injection
        QByteArray base64_data = imageToBase64(file_path);
        QString data_url = QString("data:image/%1;base64,%2")
                          .arg(getImageFormat(file_path).toLower())
                          .arg(QString::fromLatin1(base64_data));
        
        QJsonObject metadata;
        metadata["injection_method"] = "data_url";
        metadata["cache_key"] = generateCacheKey(file_path);
        metadata["image_hash"] = calculateImageHash(file_path);
        
        ProcessingResult result = ProcessingResult::createSuccess(data_url, metadata);
        result.additional_data["pixmap"] = QVariant::fromValue(pixmap);
        result.additional_data["data_url"] = data_url;
        
        return result;
        
    } catch (const std::exception& e) {
        return ProcessingResult::createError("Live injection preparation failed: " + QString(e.what()));
    }
}

QSize ImageProcessor::getImageDimensions(const QString& file_path) const {
    QImageReader reader(file_path);
    return reader.size();
}

QString ImageProcessor::getImageFormat(const QString& file_path) const {
    QImageReader reader(file_path);
    return QString::fromLatin1(reader.format()).toUpper();
}

qint64 ImageProcessor::getImageFileSize(const QString& file_path) const {
    QFileInfo file_info(file_path);
    return file_info.size();
}

QString ImageProcessor::calculateImageHash(const QString& file_path) const {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }
    
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(&file);
    return QString::fromLatin1(hash.result().toHex());
}

bool ImageProcessor::optimizeImage(const QString& file_path, const QString& output_path, int quality) const {
    QPixmap pixmap(file_path);
    if (pixmap.isNull()) {
        return false;
    }
    
    QString format = getImageFormat(file_path);
    if (format == "PNG" && quality < 100) {
        // For PNG, we can't use quality, so we'll save as JPEG for optimization
        format = "JPEG";
    }
    
    return pixmap.save(output_path, format.toLatin1().constData(), quality);
}

QPixmap ImageProcessor::loadImageWithCache(const QString& file_path) {
    QMutexLocker locker(&cache_mutex_);
    
    QString cache_key = generateCacheKey(file_path);
    
    // Check if image is in cache and not modified
    if (image_cache_.contains(cache_key) && !isImageModified(file_path)) {
        return *image_cache_.object(cache_key);
    }
    
    // Load image from file
    QPixmap pixmap = loadImageFromFile(file_path);
    if (!pixmap.isNull()) {
        // Calculate cost (approximate memory usage in KB)
        int cost = (pixmap.width() * pixmap.height() * pixmap.depth()) / 8192;
        image_cache_.insert(cache_key, new QPixmap(pixmap), cost);
        
        updateFileTracking(file_path);
        emit imageCacheUpdated(file_path);
    }
    
    return pixmap;
}

void ImageProcessor::invalidateImageCache(const QString& file_path) {
    QMutexLocker locker(&cache_mutex_);
    QString cache_key = generateCacheKey(file_path);
    image_cache_.remove(cache_key);
    file_hashes_.remove(file_path);
    last_modified_times_.remove(file_path);
}

void ImageProcessor::clearImageCache() {
    QMutexLocker locker(&cache_mutex_);
    image_cache_.clear();
    file_hashes_.clear();
    last_modified_times_.clear();
    emit imageCacheCleared();
}

void ImageProcessor::setCacheSize(int max_cache_size_mb) {
    QMutexLocker locker(&cache_mutex_);
    max_cache_size_mb_ = max_cache_size_mb;
    image_cache_.setMaxCost(max_cache_size_mb * 1024);
}

int ImageProcessor::getCacheSize() const {
    return max_cache_size_mb_;
}

QStringList ImageProcessor::getCachedImages() const {
    QMutexLocker locker(&cache_mutex_);
    return file_hashes_.keys();
}

QJsonObject ImageProcessor::createImageMetadata(const QString& file_path) const {
    QJsonObject metadata = createMetadata(file_path);
    metadata["format"] = "Image";
    metadata["image_format"] = getImageFormat(file_path);
    
    QSize dimensions = getImageDimensions(file_path);
    metadata["width"] = dimensions.width();
    metadata["height"] = dimensions.height();
    metadata["file_size"] = getImageFileSize(file_path);
    metadata["hash"] = calculateImageHash(file_path);
    
    return metadata;
}

QString ImageProcessor::generateCacheKey(const QString& file_path) const {
    QFileInfo file_info(file_path);
    return file_info.absoluteFilePath() + "_" + QString::number(file_info.lastModified().toSecsSinceEpoch());
}

bool ImageProcessor::isImageModified(const QString& file_path) const {
    QFileInfo file_info(file_path);
    QDateTime current_modified = file_info.lastModified();
    
    auto it = last_modified_times_.find(file_path);
    if (it == last_modified_times_.end()) {
        return true; // Not tracked, consider modified
    }
    
    return current_modified != it.value();
}

QByteArray ImageProcessor::imageToBase64(const QString& file_path) const {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        return QByteArray();
    }
    
    return file.readAll().toBase64();
}

bool ImageProcessor::base64ToImage(const QByteArray& base64_data, const QString& output_path) const {
    QByteArray image_data = QByteArray::fromBase64(base64_data);
    
    QFile file(output_path);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }
    
    return file.write(image_data) == image_data.size();
}

QString ImageProcessor::detectImageFormat(const QString& file_path) const {
    QImageReader reader(file_path);
    return QString::fromLatin1(reader.format());
}

bool ImageProcessor::isValidImageFormat(const QString& format) const {
    return supported_formats_.contains(format.toLower());
}

QPixmap ImageProcessor::loadImageFromFile(const QString& file_path) const {
    return QPixmap(file_path);
}

void ImageProcessor::updateFileTracking(const QString& file_path) const {
    QFileInfo file_info(file_path);
    last_modified_times_[file_path] = file_info.lastModified();
    file_hashes_[file_path] = calculateImageHash(file_path);
}

} // namespace DeclarativeUI::HotReload::FormatSupport
