#include "DataGrid.hpp"
#include <QApplication>
#include <QDebug>
#include <QFileDialog>
#include <QTextStream>
#include <QJsonDocument>
#include <QHeaderView>
#include <algorithm>

namespace DeclarativeUI::Components {

DataGrid::DataGrid(QObject* parent)
    : UIElement(parent)
    , main_layout_(nullptr)
    , toolbar_layout_(nullptr)
    , search_edit_(nullptr)
    , page_size_combo_(nullptr)
    , info_label_(nullptr)
    , table_(nullptr)
    , pagination_layout_(nullptr)
    , first_page_btn_(nullptr)
    , prev_page_btn_(nullptr)
    , page_info_label_(nullptr)
    , next_page_btn_(nullptr)
    , last_page_btn_(nullptr)
    , selection_mode_(SelectionMode::Single)
    , sortable_(true)
    , filterable_(true)
    , paginated_(false)
    , page_size_(25)
    , striped_(true)
    , bordered_(true)
    , hoverable_(true)
    , sort_order_(SortOrder::None)
    , current_page_(1)
    , total_pages_(1)
{
    setupUI();
    connectSignals();
}

void DataGrid::initialize() {
    // DataGrid is initialized in constructor, no additional setup needed
    // This method satisfies the UIElement interface requirement
}

DataGrid& DataGrid::columns(const QList<Column>& columns) {
    columns_ = columns;
    updateTable();
    return *this;
}

DataGrid& DataGrid::addColumn(const Column& column) {
    columns_.append(column);
    updateTable();
    return *this;
}

DataGrid& DataGrid::addColumn(const QString& key, const QString& title, int width) {
    Column column;
    column.key = key;
    column.title = title;
    column.width = width;
    return addColumn(column);
}

DataGrid& DataGrid::removeColumn(const QString& key) {
    for (int i = 0; i < columns_.size(); ++i) {
        if (columns_[i].key == key) {
            columns_.removeAt(i);
            updateTable();
            break;
        }
    }
    return *this;
}

DataGrid& DataGrid::hideColumn(const QString& key) {
    for (auto& column : columns_) {
        if (column.key == key) {
            column.visible = false;
            updateTable();
            break;
        }
    }
    return *this;
}

DataGrid& DataGrid::showColumn(const QString& key) {
    for (auto& column : columns_) {
        if (column.key == key) {
            column.visible = true;
            updateTable();
            break;
        }
    }
    return *this;
}

DataGrid& DataGrid::data(const QJsonArray& data) {
    original_data_ = data;
    applyFilters();
    return *this;
}

DataGrid& DataGrid::addRow(const QJsonObject& row) {
    original_data_.append(row);
    applyFilters();
    return *this;
}

DataGrid& DataGrid::updateRow(int index, const QJsonObject& row) {
    if (index >= 0 && index < original_data_.size()) {
        original_data_[index] = row;
        applyFilters();
    }
    return *this;
}

DataGrid& DataGrid::removeRow(int index) {
    if (index >= 0 && index < original_data_.size()) {
        original_data_.removeAt(index);
        applyFilters();
    }
    return *this;
}

DataGrid& DataGrid::clearData() {
    original_data_ = QJsonArray();
    filtered_data_ = QJsonArray();
    current_page_data_ = QJsonArray();
    updateTable();
    return *this;
}

DataGrid& DataGrid::selectionMode(SelectionMode mode) {
    selection_mode_ = mode;
    if (table_) {
        switch (mode) {
            case SelectionMode::None:
                table_->setSelectionMode(QAbstractItemView::NoSelection);
                break;
            case SelectionMode::Single:
                table_->setSelectionMode(QAbstractItemView::SingleSelection);
                break;
            case SelectionMode::Multiple:
                table_->setSelectionMode(QAbstractItemView::MultiSelection);
                break;
        }
    }
    return *this;
}

DataGrid& DataGrid::sortable(bool enable) {
    sortable_ = enable;
    if (table_) {
        table_->setSortingEnabled(enable);
    }
    return *this;
}

DataGrid& DataGrid::filterable(bool enable) {
    filterable_ = enable;
    if (search_edit_) {
        search_edit_->setVisible(enable);
    }
    return *this;
}

DataGrid& DataGrid::paginated(bool enable) {
    paginated_ = enable;
    updatePagination();
    return *this;
}

DataGrid& DataGrid::pageSize(int size) {
    page_size_ = size;
    if (page_size_combo_) {
        page_size_combo_->setCurrentText(QString::number(size));
    }
    updatePagination();
    return *this;
}

DataGrid& DataGrid::striped(bool enable) {
    striped_ = enable;
    if (table_) {
        table_->setAlternatingRowColors(enable);
    }
    return *this;
}

DataGrid& DataGrid::bordered(bool enable) {
    bordered_ = enable;
    applyStyles();
    return *this;
}

DataGrid& DataGrid::hoverable(bool enable) {
    hoverable_ = enable;
    applyStyles();
    return *this;
}

DataGrid& DataGrid::onRowClicked(std::function<void(int, const QJsonObject&)> callback) {
    row_click_callback_ = callback;
    return *this;
}

DataGrid& DataGrid::onRowDoubleClicked(std::function<void(int, const QJsonObject&)> callback) {
    row_double_click_callback_ = callback;
    return *this;
}

DataGrid& DataGrid::onSelectionChanged(std::function<void(const QList<int>&)> callback) {
    selection_callback_ = callback;
    return *this;
}

DataGrid& DataGrid::onSortChanged(std::function<void(const QString&, SortOrder)> callback) {
    sort_callback_ = callback;
    return *this;
}

DataGrid& DataGrid::onFilterChanged(std::function<void(const QList<FilterCriteria>&)> callback) {
    filter_callback_ = callback;
    return *this;
}

DataGrid& DataGrid::onPageChanged(std::function<void(int)> callback) {
    page_callback_ = callback;
    return *this;
}

DataGrid& DataGrid::addFilter(const FilterCriteria& filter) {
    // Remove existing filter for the same column
    removeFilter(filter.column);
    filters_.append(filter);
    applyFilters();
    return *this;
}

DataGrid& DataGrid::removeFilter(const QString& column) {
    for (int i = filters_.size() - 1; i >= 0; --i) {
        if (filters_[i].column == column) {
            filters_.removeAt(i);
        }
    }
    applyFilters();
    return *this;
}

DataGrid& DataGrid::clearFilters() {
    filters_.clear();
    if (search_edit_) {
        search_edit_->clear();
    }
    applyFilters();
    return *this;
}

DataGrid& DataGrid::sortBy(const QString& column, SortOrder order) {
    sort_column_ = column;
    sort_order_ = order;
    applySorting();
    return *this;
}

DataGrid& DataGrid::clearSort() {
    sort_column_.clear();
    sort_order_ = SortOrder::None;
    applySorting();
    return *this;
}

// Pagination methods
DataGrid& DataGrid::goToPage(int page) {
    if (page >= 1 && page <= total_pages_) {
        current_page_ = page;
        updatePageData();
    }
    return *this;
}

DataGrid& DataGrid::nextPage() {
    return goToPage(current_page_ + 1);
}

DataGrid& DataGrid::previousPage() {
    return goToPage(current_page_ - 1);
}

DataGrid& DataGrid::firstPage() {
    return goToPage(1);
}

DataGrid& DataGrid::lastPage() {
    return goToPage(total_pages_);
}

// Export methods
DataGrid& DataGrid::exportToCsv(const QString& filename) {
    QString file_path = filename.isEmpty() ? 
        QFileDialog::getSaveFileName(nullptr, "Export to CSV", "", "CSV Files (*.csv)") : 
        filename;
    
    if (!file_path.isEmpty()) {
        QFile file(file_path);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream stream(&file);
            
            // Write headers
            QStringList headers;
            for (const auto& column : columns_) {
                if (column.visible) {
                    headers << column.title;
                }
            }
            stream << headers.join(",") << "\n";
            
            // Write data
            for (int i = 0; i < filtered_data_.size(); ++i) {
                QJsonObject row = filtered_data_[i].toObject();
                QStringList values;
                for (const auto& column : columns_) {
                    if (column.visible) {
                        values << formatCellValue(row, column);
                    }
                }
                stream << values.join(",") << "\n";
            }
            
            qDebug() << "📊 Data exported to CSV:" << file_path;
        }
    }
    return *this;
}

DataGrid& DataGrid::exportToJson(const QString& filename) {
    QString file_path = filename.isEmpty() ? 
        QFileDialog::getSaveFileName(nullptr, "Export to JSON", "", "JSON Files (*.json)") : 
        filename;
    
    if (!file_path.isEmpty()) {
        QFile file(file_path);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QJsonDocument doc(filtered_data_);
            file.write(doc.toJson());
            qDebug() << "📊 Data exported to JSON:" << file_path;
        }
    }
    return *this;
}

// Getters
QJsonArray DataGrid::getData() const {
    return original_data_;
}

QJsonArray DataGrid::getFilteredData() const {
    return filtered_data_;
}

QList<int> DataGrid::getSelectedRows() const {
    QList<int> selected;
    if (table_) {
        auto selection = table_->selectionModel()->selectedRows();
        for (const auto& index : selection) {
            selected.append(index.row());
        }
    }
    return selected;
}

QJsonObject DataGrid::getRow(int index) const {
    if (index >= 0 && index < current_page_data_.size()) {
        return current_page_data_[index].toObject();
    }
    return QJsonObject();
}

int DataGrid::getCurrentPage() const {
    return current_page_;
}

int DataGrid::getTotalPages() const {
    return total_pages_;
}

int DataGrid::getTotalRows() const {
    return filtered_data_.size();
}

QList<DataGrid::Column> DataGrid::getColumns() const {
    return columns_;
}

// Static convenience methods
DataGrid* DataGrid::simple(const QJsonArray& data, const QStringList& columns) {
    auto* grid = new DataGrid();

    // Create columns from string list
    for (const QString& col : columns) {
        grid->addColumn(col, col);
    }

    return &grid->data(data);
}

DataGrid* DataGrid::sortable(const QJsonArray& data, const QStringList& columns) {
    auto* grid = simple(data, columns);
    return &grid->sortable(true);
}

DataGrid* DataGrid::paginated(const QJsonArray& data, const QStringList& columns, int pageSize) {
    auto* grid = simple(data, columns);
    return &grid->paginated(true).pageSize(pageSize);
}

// Private slots
void DataGrid::onHeaderClicked(int logicalIndex) {
    if (!sortable_ || logicalIndex >= columns_.size()) return;

    const Column& column = columns_[logicalIndex];
    if (!column.sortable) return;

    // Toggle sort order
    if (sort_column_ == column.key) {
        switch (sort_order_) {
            case SortOrder::None:
                sort_order_ = SortOrder::Ascending;
                break;
            case SortOrder::Ascending:
                sort_order_ = SortOrder::Descending;
                break;
            case SortOrder::Descending:
                sort_order_ = SortOrder::None;
                break;
        }
    } else {
        sort_column_ = column.key;
        sort_order_ = SortOrder::Ascending;
    }

    applySorting();

    if (sort_callback_) {
        sort_callback_(sort_column_, sort_order_);
    }

    emit sortChanged(sort_column_, sort_order_);
}

void DataGrid::onCellClicked(int row, int column) {
    Q_UNUSED(column);

    if (row >= 0 && row < current_page_data_.size()) {
        QJsonObject rowData = current_page_data_[row].toObject();

        if (row_click_callback_) {
            row_click_callback_(row, rowData);
        }

        emit rowClicked(row, rowData);
    }
}

void DataGrid::onCellDoubleClicked(int row, int column) {
    Q_UNUSED(column);

    if (row >= 0 && row < current_page_data_.size()) {
        QJsonObject rowData = current_page_data_[row].toObject();

        if (row_double_click_callback_) {
            row_double_click_callback_(row, rowData);
        }

        emit rowDoubleClicked(row, rowData);
    }
}

void DataGrid::onSelectionChangedInternal() {
    QList<int> selected = getSelectedRows();

    if (selection_callback_) {
        selection_callback_(selected);
    }

    emit selectionChanged(selected);
}

void DataGrid::onFilterTextChanged() {
    if (!search_edit_) return;

    QString search_text = search_edit_->text().trimmed();

    // Clear existing text filters
    for (int i = filters_.size() - 1; i >= 0; --i) {
        if (filters_[i].operator_ == "contains") {
            filters_.removeAt(i);
        }
    }

    // Add new text filter if not empty
    if (!search_text.isEmpty()) {
        for (const auto& column : columns_) {
            if (column.filterable) {
                FilterCriteria filter;
                filter.column = column.key;
                filter.operator_ = "contains";
                filter.value = search_text;
                filters_.append(filter);
            }
        }
    }

    applyFilters();

    if (filter_callback_) {
        filter_callback_(filters_);
    }

    emit filterChanged();
}

void DataGrid::onPageSizeChanged() {
    if (!page_size_combo_) return;

    bool ok;
    int new_size = page_size_combo_->currentText().toInt(&ok);
    if (ok && new_size > 0) {
        page_size_ = new_size;
        updatePagination();
    }
}

void DataGrid::onPreviousPageClicked() {
    previousPage();
}

void DataGrid::onNextPageClicked() {
    nextPage();
}

void DataGrid::onFirstPageClicked() {
    firstPage();
}

void DataGrid::onLastPageClicked() {
    lastPage();
}

} // namespace DeclarativeUI::Components
