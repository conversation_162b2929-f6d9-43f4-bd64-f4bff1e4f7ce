#pragma once

#include "../Core/UIElement.hpp"
#include <QComboBox>
#include <QListWidget>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QFrame>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QScrollArea>
#include <QStandardItemModel>
#include <QCompleter>
#include <functional>

namespace DeclarativeUI::Components {

/**
 * @brief Modern dropdown component with search, multi-select, and custom styling
 * 
 * Provides advanced dropdown functionality including:
 * - Searchable options
 * - Multi-selection support
 * - Custom item rendering
 * - Keyboard navigation
 * - Virtual scrolling for large datasets
 */
class Dropdown : public Core::UIElement {
    Q_OBJECT

public:
    enum class SelectionMode {
        Single,
        Multiple,
        Checkboxes
    };

    enum class SearchMode {
        None,
        StartsWith,
        Contains,
        Fuzzy
    };

    struct DropdownItem {
        QString text;
        QVariant value;
        QIcon icon;
        QString tooltip;
        bool enabled = true;
        bool separator = false;
        QVariantMap metadata;
    };

    explicit Dropdown(QObject* parent = nullptr);
    ~Dropdown() override = default;
    
    // **UIElement interface implementation**
    void initialize() override;

    // **Fluent interface for dropdown configuration**
    Dropdown& placeholder(const QString& text);
    Dropdown& searchable(bool enable = true);
    Dropdown& searchMode(SearchMode mode);
    Dropdown& selectionMode(SelectionMode mode);
    Dropdown& maxHeight(int height);
    Dropdown& maxVisibleItems(int count);
    Dropdown& clearable(bool enable = true);
    Dropdown& disabled(bool disable = false);
    Dropdown& loading(bool show_loading = true);
    Dropdown& virtualScrolling(bool enable = true);

    // **Item management**
    Dropdown& addItem(const QString& text, const QVariant& value = QVariant());
    Dropdown& addItem(const DropdownItem& item);
    Dropdown& addItems(const QStringList& items);
    Dropdown& addItems(const QList<DropdownItem>& items);
    Dropdown& addSeparator();
    Dropdown& removeItem(int index);
    Dropdown& removeItem(const QVariant& value);
    Dropdown& clearItems();

    // **Selection management**
    Dropdown& setSelectedIndex(int index);
    Dropdown& setSelectedValue(const QVariant& value);
    Dropdown& setSelectedValues(const QVariantList& values);
    Dropdown& selectAll();
    Dropdown& clearSelection();

    // **Event handlers**
    Dropdown& onSelectionChanged(std::function<void(const QVariantList&)> callback);
    Dropdown& onItemClicked(std::function<void(const DropdownItem&)> callback);
    Dropdown& onSearchChanged(std::function<void(const QString&)> callback);
    Dropdown& onDropdownOpened(std::function<void()> callback);
    Dropdown& onDropdownClosed(std::function<void()> callback);

    // **Custom rendering**
    Dropdown& itemRenderer(std::function<QWidget*(const DropdownItem&)> renderer);
    Dropdown& itemHeight(int height);
    Dropdown& itemSpacing(int spacing);

    // **Data binding**
    Dropdown& dataSource(std::function<QList<DropdownItem>()> source);
    Dropdown& asyncDataSource(std::function<void(std::function<void(const QList<DropdownItem>&)>)> source);

    // **Getters**
    QVariantList getSelectedValues() const;
    QList<int> getSelectedIndices() const;
    QList<DropdownItem> getItems() const;
    QString getSearchText() const;
    bool isOpen() const;
    SelectionMode getSelectionMode() const;
    SearchMode getSearchMode() const;

    // **Static convenience methods**
    static Dropdown* simple(const QStringList& items);
    static Dropdown* searchable(const QStringList& items);
    static Dropdown* multiSelect(const QStringList& items);

signals:
    void selectionChanged(const QVariantList& values);
    void itemClicked(int index);
    void searchTextChanged(const QString& text);
    void dropdownOpened();
    void dropdownClosed();

private slots:
    void onToggleDropdown();
    void onSearchTextChanged(const QString& text);
    void onItemSelectionChanged();
    void onClearButtonClicked();
    void onSelectAllClicked();

private:
    // **UI components**
    QFrame* dropdown_frame_;
    QHBoxLayout* main_layout_;
    QLineEdit* search_edit_;
    QPushButton* dropdown_button_;
    QPushButton* clear_button_;
    QFrame* dropdown_popup_;
    QVBoxLayout* popup_layout_;
    QScrollArea* scroll_area_;
    QListWidget* items_list_;
    QLabel* loading_label_;
    QPushButton* select_all_button_;

    // **Animation and effects**
    QPropertyAnimation* popup_animation_;
    QGraphicsOpacityEffect* popup_opacity_;

    // **Configuration**
    QString placeholder_text_;
    bool searchable_;
    SearchMode search_mode_;
    SelectionMode selection_mode_;
    int max_height_;
    int max_visible_items_;
    bool clearable_;
    bool disabled_;
    bool loading_;
    bool virtual_scrolling_;
    int item_height_;
    int item_spacing_;

    // **Data**
    QList<DropdownItem> items_;
    QList<DropdownItem> filtered_items_;
    QVariantList selected_values_;
    QString current_search_;
    bool dropdown_open_;

    // **Callbacks**
    std::function<void(const QVariantList&)> selection_callback_;
    std::function<void(const DropdownItem&)> item_click_callback_;
    std::function<void(const QString&)> search_callback_;
    std::function<void()> opened_callback_;
    std::function<void()> closed_callback_;
    std::function<QWidget*(const DropdownItem&)> item_renderer_;
    std::function<QList<DropdownItem>()> data_source_;

    // **Helper methods**
    void setupUI();
    void setupAnimations();
    void connectSignals();
    void updateItemsList();
    void filterItems(const QString& search_text);
    void updateSelection();
    void updatePlaceholder();
    void showDropdown();
    void hideDropdown();
    void positionPopup();
    QWidget* createItemWidget(const DropdownItem& item, int index);
    bool matchesSearch(const DropdownItem& item, const QString& search) const;
    QString getDisplayText() const;
    void applyStyles();
};

} // namespace DeclarativeUI::Components
