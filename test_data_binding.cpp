#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QLineEdit>
#include <QSpinBox>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QMessageBox>
#include <QDebug>

#ifdef DECLARATIVE_UI_DATA_BINDING_ENABLED
#include "src/DataBinding/ValidationEngine.hpp"
#include "src/DataBinding/DataBindingEngine.hpp"
#include "src/DataBinding/BindingUtils.hpp"
using namespace DeclarativeUI::DataBinding;
#endif

class SimpleDataBindingTest : public QWidget {
    Q_OBJECT

public:
    explicit SimpleDataBindingTest(QWidget* parent = nullptr) : QWidget(parent) {
        setupUI();
#ifdef DECLARATIVE_UI_DATA_BINDING_ENABLED
        setupDataBinding();
#endif
    }

private slots:
    void onValidateClicked() {
#ifdef DECLARATIVE_UI_DATA_BINDING_ENABLED
        // Test validation
        auto& engine = ValidationEngine::instance();
        
        // Get values from widgets
        QString name = m_nameEdit->text();
        QString email = m_emailEdit->text();
        int age = m_ageSpinBox->value();
        
        // Validate individual fields
        auto nameResult = engine.validateField("name", name);
        auto emailResult = engine.validateField("email", email);
        auto ageResult = engine.validateField("age", age);
        
        // Update status
        QString status = QString("Name: %1, Email: %2, Age: %3")
            .arg(nameResult.is_valid ? "✓" : "✗")
            .arg(emailResult.is_valid ? "✓" : "✗")
            .arg(ageResult.is_valid ? "✓" : "✗");
        
        m_statusLabel->setText(status);
        
        // Show detailed results
        QStringList errors;
        if (!nameResult.is_valid) {
            errors << "Name: " + nameResult.error_message;
        }
        if (!emailResult.is_valid) {
            errors << "Email: " + emailResult.error_message;
        }
        if (!ageResult.is_valid) {
            errors << "Age: " + ageResult.error_message;
        }
        
        if (errors.isEmpty()) {
            QMessageBox::information(this, "Validation", "All fields are valid!");
        } else {
            QMessageBox::warning(this, "Validation Errors", errors.join("\n"));
        }
#else
        QMessageBox::information(this, "Info", "Data binding is not enabled in this build.");
#endif
    }
    
    void onTestBindingClicked() {
#ifdef DECLARATIVE_UI_DATA_BINDING_ENABLED
        // Test data binding
        auto& bindingEngine = DataBindingEngine::instance();
        
        // Create form data
        auto formData = std::make_shared<FormData>();
        
        // Bind widgets to form data
        BindingConfig nameConfig;
        nameConfig.property_name = "name";
        nameConfig.field_name = "name";
        bindingEngine.bindWidget(m_nameEdit, nameConfig);
        
        BindingConfig emailConfig;
        emailConfig.property_name = "email";
        emailConfig.field_name = "email";
        bindingEngine.bindWidget(m_emailEdit, emailConfig);
        
        BindingConfig ageConfig;
        ageConfig.property_name = "age";
        ageConfig.field_name = "age";
        bindingEngine.bindWidget(m_ageSpinBox, ageConfig);
        
        // Bind form
        bindingEngine.bindForm(this, formData);
        
        m_statusLabel->setText("Data binding configured successfully!");
        QMessageBox::information(this, "Binding", "Data binding has been set up. Try changing values!");
#else
        QMessageBox::information(this, "Info", "Data binding is not enabled in this build.");
#endif
    }

private:
    void setupUI() {
        setWindowTitle("Simple Data Binding Test");
        setMinimumSize(400, 300);
        
        auto* layout = new QVBoxLayout(this);
        
        // Form group
        auto* formGroup = new QGroupBox("Test Form");
        auto* formLayout = new QFormLayout(formGroup);
        
        m_nameEdit = new QLineEdit();
        m_nameEdit->setPlaceholderText("Enter your name");
        formLayout->addRow("Name:", m_nameEdit);
        
        m_emailEdit = new QLineEdit();
        m_emailEdit->setPlaceholderText("Enter your email");
        formLayout->addRow("Email:", m_emailEdit);
        
        m_ageSpinBox = new QSpinBox();
        m_ageSpinBox->setRange(1, 120);
        m_ageSpinBox->setValue(25);
        formLayout->addRow("Age:", m_ageSpinBox);
        
        layout->addWidget(formGroup);
        
        // Buttons
        auto* buttonLayout = new QHBoxLayout();
        
        auto* validateButton = new QPushButton("Validate");
        auto* bindingButton = new QPushButton("Test Binding");
        
        buttonLayout->addWidget(validateButton);
        buttonLayout->addWidget(bindingButton);
        buttonLayout->addStretch();
        
        layout->addLayout(buttonLayout);
        
        // Status
        m_statusLabel = new QLabel("Ready");
        layout->addWidget(m_statusLabel);
        
        // Connect signals
        connect(validateButton, &QPushButton::clicked, this, &SimpleDataBindingTest::onValidateClicked);
        connect(bindingButton, &QPushButton::clicked, this, &SimpleDataBindingTest::onTestBindingClicked);
    }
    
#ifdef DECLARATIVE_UI_DATA_BINDING_ENABLED
    void setupDataBinding() {
        // Setup validation rules
        auto& engine = ValidationEngine::instance();
        
        // Name validation: required, minimum length
        ValidationRule nameRule;
        nameRule.required().minLength(2);
        engine.addRule("name", std::move(nameRule));
        
        // Email validation: required, valid email format
        ValidationRule emailRule;
        emailRule.required().email();
        engine.addRule("email", std::move(emailRule));
        
        // Age validation: range check
        ValidationRule ageRule;
        ageRule.required().range(18, 100);
        engine.addRule("age", std::move(ageRule));
        
        qDebug() << "Validation rules configured";
    }
#endif

private:
    QLineEdit* m_nameEdit;
    QLineEdit* m_emailEdit;
    QSpinBox* m_ageSpinBox;
    QLabel* m_statusLabel;
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    SimpleDataBindingTest window;
    window.show();
    
    return app.exec();
}

#include "test_data_binding.moc"
