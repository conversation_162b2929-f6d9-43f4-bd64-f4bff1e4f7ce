#include "SelectiveReloadManager.hpp"
#include "../Core/Exceptions.hpp"
#include "../JSON/ComponentRegistry.hpp"

#include <QDebug>
#include <QFile>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonParseError>
#include <QCryptographicHash>
#include <QApplication>
#include <QMetaProperty>
#include <QThread>
#include <QCoreApplication>

#include <algorithm>
#include <chrono>

namespace DeclarativeUI::HotReload {

SelectiveReloadManager::SelectiveReloadManager(QObject* parent)
    : QObject(parent) {
    
    setupCSSProcessor();
    setupTimers();
    connectSignals();
    
    session_timer_.start();
    
    qDebug() << "🎯 SelectiveReloadManager initialized";
}

SelectiveReloadManager::~SelectiveReloadManager() {
    stopMonitoring();
    clearRollbackPoints();
    
    qDebug() << "🎯 SelectiveReloadManager destroyed";
}

void SelectiveReloadManager::setConfiguration(const SelectiveReloadConfig& config) {
    config_ = config;
    
    // Update timers based on new configuration
    if (reload_timer_) {
        reload_timer_->setInterval(config_.reload_debounce_ms);
    }
    
    // Update CSS processor configuration
    if (css_processor_) {
        css_processor_->enableAutoInjection(config_.auto_detect_changes);
    }
    
    emit configurationChanged();
    qDebug() << "🎯 Configuration updated";
}

SelectiveReloadConfig SelectiveReloadManager::getConfiguration() const {
    return config_;
}

void SelectiveReloadManager::setHotReloadManager(HotReloadManager* manager) {
    hot_reload_manager_ = manager;
    
    if (manager) {
        // Connect to HotReloadManager signals for coordination
        connect(manager, &HotReloadManager::fileReloaded,
                this, &SelectiveReloadManager::onFileChanged);
        
        qDebug() << "🎯 Connected to HotReloadManager";
    }
}

void SelectiveReloadManager::setAdvancedFilter(AdvancedFileFilter* filter) {
    advanced_filter_ = filter;
    
    if (filter) {
        qDebug() << "🎯 Connected to AdvancedFileFilter";
    }
}

void SelectiveReloadManager::setUILoader(JSON::JSONUILoader* loader) {
    ui_loader_ = loader;
    
    if (loader) {
        // Connect to loader signals for component tracking
        connect(loader, &JSON::JSONUILoader::loadingFinished,
                this, [this](const QString& source) {
                    // Auto-register components from loaded UI
                    detectChangesInFile(source);
                });
        
        qDebug() << "🎯 Connected to JSONUILoader";
    }
}

void SelectiveReloadManager::registerComponent(const QString& component_id, QWidget* widget, 
                                               const QJsonObject& config, const QString& source_file) {
    if (component_id.isEmpty() || !widget) {
        qWarning() << "🎯 Invalid component registration parameters";
        return;
    }
    
    QWriteLocker locker(&components_lock_);
    
    ComponentTrackingInfo info;
    info.component_id = component_id;
    info.component_type = widget->metaObject()->className();
    info.widget_instance = widget;
    info.parent_widget = widget->parentWidget();
    info.source_file = source_file;
    info.original_config = config;
    info.current_config = config;
    info.last_updated = QDateTime::currentDateTime();
    info.is_dynamic = false;
    info.reload_count = 0;
    
    // Extract bound properties from config
    if (config.contains("properties")) {
        QJsonObject props = config["properties"].toObject();
        for (auto it = props.begin(); it != props.end(); ++it) {
            info.bound_properties.append(it.key());
        }
    }
    
    // Extract event handlers from config
    if (config.contains("events")) {
        QJsonObject events = config["events"].toObject();
        for (auto it = events.begin(); it != events.end(); ++it) {
            info.event_handlers.append(it.key());
        }
    }
    
    // Set object name for easier identification
    widget->setObjectName(component_id);
    
    tracked_components_[component_id] = std::move(info);
    
    qDebug() << "🎯 Registered component:" << component_id << "type:" << info.component_type;
}

void SelectiveReloadManager::unregisterComponent(const QString& component_id) {
    QWriteLocker locker(&components_lock_);
    
    auto it = tracked_components_.find(component_id);
    if (it != tracked_components_.end()) {
        tracked_components_.erase(it);
        qDebug() << "🎯 Unregistered component:" << component_id;
    }
}

void SelectiveReloadManager::updateComponentConfig(const QString& component_id, const QJsonObject& new_config) {
    QWriteLocker locker(&components_lock_);
    
    auto it = tracked_components_.find(component_id);
    if (it != tracked_components_.end()) {
        it->second.current_config = new_config;
        it->second.last_updated = QDateTime::currentDateTime();
        
        qDebug() << "🎯 Updated component config:" << component_id;
    }
}

ComponentTrackingInfo SelectiveReloadManager::getComponentInfo(const QString& component_id) const {
    QReadLocker locker(&components_lock_);
    
    auto it = tracked_components_.find(component_id);
    if (it != tracked_components_.end()) {
        return it->second;
    }
    
    return ComponentTrackingInfo{};
}

QStringList SelectiveReloadManager::getRegisteredComponents() const {
    QReadLocker locker(&components_lock_);
    
    QStringList components;
    for (const auto& [id, info] : tracked_components_) {
        components.append(id);
    }
    
    return components;
}

void SelectiveReloadManager::registerStylesheet(const QString& stylesheet_id, const QString& file_path, const QString& scope) {
    if (stylesheet_id.isEmpty() || file_path.isEmpty()) {
        qWarning() << "🎯 Invalid stylesheet registration parameters";
        return;
    }
    
    QWriteLocker locker(&stylesheets_lock_);
    
    StylesheetTrackingInfo info;
    info.stylesheet_id = stylesheet_id;
    info.file_path = file_path;
    info.injection_scope = scope;
    info.last_modified = QDateTime::currentDateTime();
    info.is_injected = false;
    
    // Load initial CSS content
    QFile file(file_path);
    if (file.open(QIODevice::ReadOnly)) {
        info.css_content = QString::fromUtf8(file.readAll());
        file.close();
    }
    
    tracked_stylesheets_[stylesheet_id] = std::move(info);
    
    qDebug() << "🎯 Registered stylesheet:" << stylesheet_id << "scope:" << scope;
}

void SelectiveReloadManager::unregisterStylesheet(const QString& stylesheet_id) {
    QWriteLocker locker(&stylesheets_lock_);
    
    auto it = tracked_stylesheets_.find(stylesheet_id);
    if (it != tracked_stylesheets_.end()) {
        // Remove injected CSS if it exists
        if (it->second.is_injected && css_processor_) {
            css_processor_->removeInjectedCSS(stylesheet_id);
        }
        
        tracked_stylesheets_.erase(it);
        qDebug() << "🎯 Unregistered stylesheet:" << stylesheet_id;
    }
}

void SelectiveReloadManager::updateStylesheet(const QString& stylesheet_id, const QString& css_content) {
    QWriteLocker locker(&stylesheets_lock_);
    
    auto it = tracked_stylesheets_.find(stylesheet_id);
    if (it != tracked_stylesheets_.end()) {
        it->second.css_content = css_content;
        it->second.last_modified = QDateTime::currentDateTime();
        
        // Process CSS content
        if (css_processor_) {
            auto result = css_processor_->processContent(css_content, it->second.file_path, 
                                                        css_processor_->getDefaultConfig());
            if (result.success) {
                it->second.compiled_content = result.processed_content;
            }
        }
        
        qDebug() << "🎯 Updated stylesheet:" << stylesheet_id;
    }
}

StylesheetTrackingInfo SelectiveReloadManager::getStylesheetInfo(const QString& stylesheet_id) const {
    QReadLocker locker(&stylesheets_lock_);
    
    auto it = tracked_stylesheets_.find(stylesheet_id);
    if (it != tracked_stylesheets_.end()) {
        return it->second;
    }
    
    return StylesheetTrackingInfo{};
}

QStringList SelectiveReloadManager::getRegisteredStylesheets() const {
    QReadLocker locker(&stylesheets_lock_);
    
    QStringList stylesheets;
    for (const auto& [id, info] : tracked_stylesheets_) {
        stylesheets.append(id);
    }
    
    return stylesheets;
}

void SelectiveReloadManager::registerResource(const QString& resource_id, const QString& file_path, const QString& type) {
    if (resource_id.isEmpty() || file_path.isEmpty()) {
        qWarning() << "🎯 Invalid resource registration parameters";
        return;
    }
    
    QWriteLocker locker(&resources_lock_);
    
    ResourceTrackingInfo info;
    info.resource_id = resource_id;
    info.file_path = file_path;
    info.resource_type = type;
    info.last_modified = QDateTime::currentDateTime();
    info.is_cached = false;
    
    // Get file information
    QFileInfo file_info(file_path);
    if (file_info.exists()) {
        info.file_size = file_info.size();
        info.last_modified = file_info.lastModified();
        
        // Calculate checksum for integrity verification
        QFile file(file_path);
        if (file.open(QIODevice::ReadOnly)) {
            QCryptographicHash hash(QCryptographicHash::Md5);
            hash.addData(file.readAll());
            info.checksum = hash.result().toHex();
            file.close();
        }
    }
    
    tracked_resources_[resource_id] = std::move(info);
    
    qDebug() << "🎯 Registered resource:" << resource_id << "type:" << type;
}

void SelectiveReloadManager::unregisterResource(const QString& resource_id) {
    QWriteLocker locker(&resources_lock_);
    
    auto it = tracked_resources_.find(resource_id);
    if (it != tracked_resources_.end()) {
        tracked_resources_.erase(it);
        qDebug() << "🎯 Unregistered resource:" << resource_id;
    }
}

void SelectiveReloadManager::updateResource(const QString& resource_id) {
    QWriteLocker locker(&resources_lock_);
    
    auto it = tracked_resources_.find(resource_id);
    if (it != tracked_resources_.end()) {
        // Update file information
        QFileInfo file_info(it->second.file_path);
        if (file_info.exists()) {
            it->second.file_size = file_info.size();
            it->second.last_modified = file_info.lastModified();
            
            // Recalculate checksum
            QFile file(it->second.file_path);
            if (file.open(QIODevice::ReadOnly)) {
                QCryptographicHash hash(QCryptographicHash::Md5);
                hash.addData(file.readAll());
                it->second.checksum = hash.result().toHex();
                file.close();
            }
        }
        
        qDebug() << "🎯 Updated resource:" << resource_id;
    }
}

ResourceTrackingInfo SelectiveReloadManager::getResourceInfo(const QString& resource_id) const {
    QReadLocker locker(&resources_lock_);
    
    auto it = tracked_resources_.find(resource_id);
    if (it != tracked_resources_.end()) {
        return it->second;
    }
    
    return ResourceTrackingInfo{};
}

QStringList SelectiveReloadManager::getRegisteredResources() const {
    QReadLocker locker(&resources_lock_);
    
    QStringList resources;
    for (const auto& [id, info] : tracked_resources_) {
        resources.append(id);
    }
    
    return resources;
}

void SelectiveReloadManager::setupCSSProcessor() {
    css_processor_ = std::make_unique<FormatSupport::CSSProcessor>(this);
    
    // Configure CSS processor for live injection
    css_processor_->enableAutoInjection(true);
    css_processor_->setTargetApplication(qApp);
    
    // Connect CSS processor signals
    connect(css_processor_.get(), &FormatSupport::CSSProcessor::cssInjected,
            this, [this](const QString& identifier, const QString& css_content) {
                Q_UNUSED(css_content)
                emit stylesheetReloaded(identifier, true);
            });
    
    connect(css_processor_.get(), &FormatSupport::CSSProcessor::injectionFailed,
            this, [this](const QString& identifier, const QString& error) {
                Q_UNUSED(error)
                emit stylesheetReloaded(identifier, false);
            });
    
    qDebug() << "🎯 CSS processor initialized";
}

void SelectiveReloadManager::setupTimers() {
    // Reload debounce timer
    reload_timer_ = std::make_unique<QTimer>(this);
    reload_timer_->setSingleShot(true);
    reload_timer_->setInterval(config_.reload_debounce_ms);
    connect(reload_timer_.get(), &QTimer::timeout,
            this, &SelectiveReloadManager::processReloadQueue);
    
    // Performance metrics timer
    metrics_timer_ = std::make_unique<QTimer>(this);
    metrics_timer_->setInterval(5000); // Update every 5 seconds
    connect(metrics_timer_.get(), &QTimer::timeout,
            this, &SelectiveReloadManager::updatePerformanceMetrics);
    
    qDebug() << "🎯 Timers initialized";
}

void SelectiveReloadManager::connectSignals() {
    // Connect internal signals for coordination
    connect(this, &SelectiveReloadManager::reloadStarted,
            this, [this](const QString& target_id, SelectiveReloadType type) {
                Q_UNUSED(target_id)
                reload_counts_[type]++;
            });
    
    qDebug() << "🎯 Internal signals connected";
}

SelectiveReloadResult SelectiveReloadManager::reloadComponent(const QString& component_id) {
    if (!config_.enabled) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Component,
                                                 "Selective reload is disabled");
    }

    emit reloadStarted(component_id, SelectiveReloadType::Component);

    QElapsedTimer timer;
    timer.start();

    try {
        auto result = performComponentReload(component_id);
        result.duration_ms = timer.elapsed();

        emit reloadCompleted(component_id, SelectiveReloadType::Component, result.duration_ms);
        emit componentReloaded(component_id, result.success);

        if (result.success) {
            logReloadOperation(component_id, SelectiveReloadType::Component, true, result.duration_ms);

            // Cascade to dependencies if enabled
            if (config_.cascade_dependencies) {
                cascadeReload(component_id);
            }
        } else {
            handleReloadError(component_id, SelectiveReloadType::Component, result.error_message);
        }

        return result;

    } catch (const std::exception& e) {
        auto result = SelectiveReloadResult::createError(component_id, SelectiveReloadType::Component, e.what());
        result.duration_ms = timer.elapsed();

        emit reloadFailed(component_id, SelectiveReloadType::Component, result.error_message);
        emit componentReloaded(component_id, false);

        handleReloadError(component_id, SelectiveReloadType::Component, result.error_message);

        return result;
    }
}

SelectiveReloadResult SelectiveReloadManager::reloadComponentProperty(const QString& component_id,
                                                                     const QString& property_name,
                                                                     const QVariant& new_value) {
    if (!config_.enabled) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Property,
                                                 "Selective reload is disabled");
    }

    emit reloadStarted(component_id, SelectiveReloadType::Property);

    QElapsedTimer timer;
    timer.start();

    try {
        auto result = performPropertyReload(component_id, property_name, new_value);
        result.duration_ms = timer.elapsed();

        emit reloadCompleted(component_id, SelectiveReloadType::Property, result.duration_ms);

        if (result.success) {
            logReloadOperation(component_id, SelectiveReloadType::Property, true, result.duration_ms);
        } else {
            handleReloadError(component_id, SelectiveReloadType::Property, result.error_message);
        }

        return result;

    } catch (const std::exception& e) {
        auto result = SelectiveReloadResult::createError(component_id, SelectiveReloadType::Property, e.what());
        result.duration_ms = timer.elapsed();

        emit reloadFailed(component_id, SelectiveReloadType::Property, result.error_message);
        handleReloadError(component_id, SelectiveReloadType::Property, result.error_message);

        return result;
    }
}

SelectiveReloadResult SelectiveReloadManager::reloadStylesheet(const QString& stylesheet_id) {
    if (!config_.enabled) {
        return SelectiveReloadResult::createError(stylesheet_id, SelectiveReloadType::Stylesheet,
                                                 "Selective reload is disabled");
    }

    emit reloadStarted(stylesheet_id, SelectiveReloadType::Stylesheet);

    QElapsedTimer timer;
    timer.start();

    try {
        auto result = performStylesheetReload(stylesheet_id);
        result.duration_ms = timer.elapsed();

        emit reloadCompleted(stylesheet_id, SelectiveReloadType::Stylesheet, result.duration_ms);
        emit stylesheetReloaded(stylesheet_id, result.success);

        if (result.success) {
            logReloadOperation(stylesheet_id, SelectiveReloadType::Stylesheet, true, result.duration_ms);

            // Cascade to affected components if enabled
            if (config_.cascade_dependencies) {
                cascadeReload(stylesheet_id);
            }
        } else {
            handleReloadError(stylesheet_id, SelectiveReloadType::Stylesheet, result.error_message);
        }

        return result;

    } catch (const std::exception& e) {
        auto result = SelectiveReloadResult::createError(stylesheet_id, SelectiveReloadType::Stylesheet, e.what());
        result.duration_ms = timer.elapsed();

        emit reloadFailed(stylesheet_id, SelectiveReloadType::Stylesheet, result.error_message);
        emit stylesheetReloaded(stylesheet_id, false);

        handleReloadError(stylesheet_id, SelectiveReloadType::Stylesheet, result.error_message);

        return result;
    }
}

SelectiveReloadResult SelectiveReloadManager::reloadResource(const QString& resource_id) {
    if (!config_.enabled) {
        return SelectiveReloadResult::createError(resource_id, SelectiveReloadType::Resource,
                                                 "Selective reload is disabled");
    }

    emit reloadStarted(resource_id, SelectiveReloadType::Resource);

    QElapsedTimer timer;
    timer.start();

    try {
        auto result = performResourceReload(resource_id);
        result.duration_ms = timer.elapsed();

        emit reloadCompleted(resource_id, SelectiveReloadType::Resource, result.duration_ms);
        emit resourceReloaded(resource_id, result.success);

        if (result.success) {
            logReloadOperation(resource_id, SelectiveReloadType::Resource, true, result.duration_ms);

            // Cascade to dependent components if enabled
            if (config_.cascade_dependencies) {
                cascadeReload(resource_id);
            }
        } else {
            handleReloadError(resource_id, SelectiveReloadType::Resource, result.error_message);
        }

        return result;

    } catch (const std::exception& e) {
        auto result = SelectiveReloadResult::createError(resource_id, SelectiveReloadType::Resource, e.what());
        result.duration_ms = timer.elapsed();

        emit reloadFailed(resource_id, SelectiveReloadType::Resource, result.error_message);
        emit resourceReloaded(resource_id, false);

        handleReloadError(resource_id, SelectiveReloadType::Resource, result.error_message);

        return result;
    }
}

SelectiveReloadResult SelectiveReloadManager::reloadComponentsByType(const QString& component_type) {
    QStringList matching_components;

    {
        QReadLocker locker(&components_lock_);
        for (const auto& [id, info] : tracked_components_) {
            if (info.component_type == component_type) {
                matching_components.append(id);
            }
        }
    }

    if (matching_components.isEmpty()) {
        return SelectiveReloadResult::createError("", SelectiveReloadType::Component,
                                                 QString("No components found of type: %1").arg(component_type));
    }

    QElapsedTimer timer;
    timer.start();

    int success_count = 0;
    QStringList errors;

    for (const QString& component_id : matching_components) {
        auto result = reloadComponent(component_id);
        if (result.success) {
            success_count++;
        } else {
            errors.append(QString("%1: %2").arg(component_id, result.error_message));
        }
    }

    auto result = SelectiveReloadResult::createSuccess("", SelectiveReloadType::Component, timer.elapsed());
    result.affected_components = matching_components;
    result.metadata["success_count"] = success_count;
    result.metadata["total_count"] = matching_components.size();
    result.metadata["component_type"] = component_type;

    if (!errors.isEmpty()) {
        result.error_message = errors.join("; ");
        result.success = success_count > 0; // Partial success if some components reloaded
    }

    return result;
}

SelectiveReloadResult SelectiveReloadManager::reloadComponentsBySelector(const QString& css_selector) {
    // For now, implement basic ID-based selection
    // Future enhancement: implement full CSS selector parsing

    QStringList matching_components;

    if (css_selector.startsWith("#")) {
        // ID selector
        QString component_id = css_selector.mid(1);
        if (isValidComponent(component_id)) {
            matching_components.append(component_id);
        }
    } else {
        // Type selector - match by component type
        QReadLocker locker(&components_lock_);
        for (const auto& [id, info] : tracked_components_) {
            if (info.component_type.contains(css_selector, Qt::CaseInsensitive)) {
                matching_components.append(id);
            }
        }
    }

    if (matching_components.isEmpty()) {
        return SelectiveReloadResult::createError("", SelectiveReloadType::Component,
                                                 QString("No components found matching selector: %1").arg(css_selector));
    }

    QElapsedTimer timer;
    timer.start();

    int success_count = 0;
    QStringList errors;

    for (const QString& component_id : matching_components) {
        auto result = reloadComponent(component_id);
        if (result.success) {
            success_count++;
        } else {
            errors.append(QString("%1: %2").arg(component_id, result.error_message));
        }
    }

    auto result = SelectiveReloadResult::createSuccess("", SelectiveReloadType::Component, timer.elapsed());
    result.affected_components = matching_components;
    result.metadata["success_count"] = success_count;
    result.metadata["total_count"] = matching_components.size();
    result.metadata["css_selector"] = css_selector;

    if (!errors.isEmpty()) {
        result.error_message = errors.join("; ");
        result.success = success_count > 0;
    }

    return result;
}

QList<SelectiveReloadResult> SelectiveReloadManager::reloadTargets(const QList<SelectiveReloadTarget>& targets) {
    QList<SelectiveReloadResult> results;

    if (!config_.enabled) {
        for (const auto& target : targets) {
            results.append(SelectiveReloadResult::createError(target.target_id, target.type,
                                                             "Selective reload is disabled"));
        }
        return results;
    }

    // Build optimized reload plan
    auto reload_plan = buildReloadPlan(targets);

    // Execute reloads according to plan
    for (const auto& target : reload_plan) {
        if (!target.enabled) {
            continue;
        }

        SelectiveReloadResult result;

        switch (target.type) {
            case SelectiveReloadType::Component:
                result = reloadComponent(target.target_id);
                break;
            case SelectiveReloadType::Stylesheet:
                result = reloadStylesheet(target.target_id);
                break;
            case SelectiveReloadType::Resource:
                result = reloadResource(target.target_id);
                break;
            case SelectiveReloadType::Property:
                // Property reloads require additional metadata
                if (target.metadata.contains("property_name") && target.metadata.contains("property_value")) {
                    QString property_name = target.metadata["property_name"].toString();
                    QVariant property_value = target.metadata["property_value"].toVariant();
                    result = reloadComponentProperty(target.target_id, property_name, property_value);
                } else {
                    result = SelectiveReloadResult::createError(target.target_id, target.type,
                                                               "Property reload requires property_name and property_value in metadata");
                }
                break;
            default:
                result = SelectiveReloadResult::createError(target.target_id, target.type,
                                                           "Unsupported reload type");
                break;
        }

        results.append(result);

        // Stop on critical errors if configured
        if (!result.success && target.priority > 90) {
            qWarning() << "🎯 Critical reload failed, stopping batch operation:" << target.target_id;
            break;
        }
    }

    return results;
}

QList<SelectiveReloadResult> SelectiveReloadManager::reloadDependencies(const QString& target_id) {
    QStringList dependencies = getDependencies(target_id);
    QList<SelectiveReloadTarget> targets;

    for (const QString& dep_id : dependencies) {
        SelectiveReloadTarget target;
        target.target_id = dep_id;
        target.type = SelectiveReloadType::Component; // Default, could be enhanced
        target.priority = 50;
        targets.append(target);
    }

    return reloadTargets(targets);
}

QList<SelectiveReloadResult> SelectiveReloadManager::reloadAffectedComponents(const QString& file_path) {
    QList<SelectiveReloadTarget> targets;

    // Find components that depend on this file
    {
        QReadLocker locker(&components_lock_);
        for (const auto& [id, info] : tracked_components_) {
            if (info.source_file == file_path) {
                SelectiveReloadTarget target;
                target.target_id = id;
                target.type = SelectiveReloadType::Component;
                target.file_path = file_path;
                target.priority = 70;
                targets.append(target);
            }
        }
    }

    // Find stylesheets that depend on this file
    {
        QReadLocker locker(&stylesheets_lock_);
        for (const auto& [id, info] : tracked_stylesheets_) {
            if (info.file_path == file_path) {
                SelectiveReloadTarget target;
                target.target_id = id;
                target.type = SelectiveReloadType::Stylesheet;
                target.file_path = file_path;
                target.priority = 80;
                targets.append(target);
            }
        }
    }

    // Find resources that depend on this file
    {
        QReadLocker locker(&resources_lock_);
        for (const auto& [id, info] : tracked_resources_) {
            if (info.file_path == file_path) {
                SelectiveReloadTarget target;
                target.target_id = id;
                target.type = SelectiveReloadType::Resource;
                target.file_path = file_path;
                target.priority = 60;
                targets.append(target);
            }
        }
    }

    return reloadTargets(targets);
}

void SelectiveReloadManager::enableAutoDetection(bool enabled) {
    auto_detection_enabled_ = enabled;

    if (enabled && monitoring_enabled_) {
        qDebug() << "🎯 Auto-detection enabled";
    } else {
        qDebug() << "🎯 Auto-detection disabled";
    }
}

void SelectiveReloadManager::startMonitoring() {
    if (monitoring_enabled_) {
        return;
    }

    monitoring_enabled_ = true;

    if (metrics_timer_) {
        metrics_timer_->start();
    }

    emit monitoringStateChanged(true);
    qDebug() << "🎯 Monitoring started";
}

void SelectiveReloadManager::stopMonitoring() {
    if (!monitoring_enabled_) {
        return;
    }

    monitoring_enabled_ = false;

    if (metrics_timer_) {
        metrics_timer_->stop();
    }

    if (reload_timer_) {
        reload_timer_->stop();
    }

    emit monitoringStateChanged(false);
    qDebug() << "🎯 Monitoring stopped";
}

void SelectiveReloadManager::pauseMonitoring() {
    if (metrics_timer_) {
        metrics_timer_->stop();
    }

    if (reload_timer_) {
        reload_timer_->stop();
    }

    qDebug() << "🎯 Monitoring paused";
}

void SelectiveReloadManager::addDependency(const QString& source_id, const QString& dependent_id) {
    QMutexLocker locker(&dependency_lock_);

    dependency_graph_[source_id].append(dependent_id);

    qDebug() << "🎯 Added dependency:" << source_id << "->" << dependent_id;
}

void SelectiveReloadManager::removeDependency(const QString& source_id, const QString& dependent_id) {
    QMutexLocker locker(&dependency_lock_);

    auto it = dependency_graph_.find(source_id);
    if (it != dependency_graph_.end()) {
        it->second.removeAll(dependent_id);

        if (it->second.isEmpty()) {
            dependency_graph_.erase(it);
        }
    }

    qDebug() << "🎯 Removed dependency:" << source_id << "->" << dependent_id;
}

QStringList SelectiveReloadManager::getDependencies(const QString& target_id) const {
    QMutexLocker locker(&dependency_lock_);

    auto it = dependency_graph_.find(target_id);
    if (it != dependency_graph_.end()) {
        return it->second;
    }

    return QStringList{};
}

QStringList SelectiveReloadManager::getDependents(const QString& target_id) const {
    QMutexLocker locker(&dependency_lock_);

    QStringList dependents;
    for (const auto& [source_id, deps] : dependency_graph_) {
        if (deps.contains(target_id)) {
            dependents.append(source_id);
        }
    }

    return dependents;
}

void SelectiveReloadManager::createRollbackPoint(const QString& target_id) {
    if (!config_.create_rollback_points) {
        return;
    }

    QJsonObject rollback_data;

    // Save component state
    if (isValidComponent(target_id)) {
        auto info = getComponentInfo(target_id);
        rollback_data["type"] = "component";
        rollback_data["config"] = info.current_config;
        rollback_data["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    }
    // Save stylesheet state
    else if (isValidStylesheet(target_id)) {
        auto info = getStylesheetInfo(target_id);
        rollback_data["type"] = "stylesheet";
        rollback_data["css_content"] = info.css_content;
        rollback_data["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    }
    // Save resource state
    else if (isValidResource(target_id)) {
        auto info = getResourceInfo(target_id);
        rollback_data["type"] = "resource";
        rollback_data["file_path"] = info.file_path;
        rollback_data["checksum"] = info.checksum;
        rollback_data["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    }

    if (!rollback_data.isEmpty()) {
        rollback_points_[target_id] = rollback_data;
        qDebug() << "🎯 Created rollback point for:" << target_id;
    }
}

bool SelectiveReloadManager::rollbackTarget(const QString& target_id) {
    auto it = rollback_points_.find(target_id);
    if (it == rollback_points_.end()) {
        qWarning() << "🎯 No rollback point found for:" << target_id;
        return false;
    }

    QJsonObject rollback_data = it->second;
    QString type = rollback_data["type"].toString();

    try {
        if (type == "component") {
            QJsonObject config = rollback_data["config"].toObject();
            updateComponentConfig(target_id, config);
            auto result = reloadComponent(target_id);

            emit rollbackPerformed(target_id, result.success);
            return result.success;
        }
        else if (type == "stylesheet") {
            QString css_content = rollback_data["css_content"].toString();
            updateStylesheet(target_id, css_content);
            auto result = reloadStylesheet(target_id);

            emit rollbackPerformed(target_id, result.success);
            return result.success;
        }
        else if (type == "resource") {
            // For resources, we can only update tracking info
            updateResource(target_id);
            auto result = reloadResource(target_id);

            emit rollbackPerformed(target_id, result.success);
            return result.success;
        }

        return false;

    } catch (const std::exception& e) {
        qWarning() << "🎯 Rollback failed for" << target_id << ":" << e.what();
        emit rollbackPerformed(target_id, false);
        return false;
    }
}

void SelectiveReloadManager::clearRollbackPoints() {
    rollback_points_.clear();
    qDebug() << "🎯 Cleared all rollback points";
}

QJsonObject SelectiveReloadManager::getPerformanceMetrics() const {
    QJsonObject metrics;

    metrics["session_uptime_ms"] = session_timer_.elapsed();
    metrics["monitoring_enabled"] = monitoring_enabled_.load();
    metrics["auto_detection_enabled"] = auto_detection_enabled_.load();

    // Reload counts by type
    QJsonObject reload_counts;
    for (const auto& [type, count] : reload_counts_) {
        QString type_name;
        switch (type) {
            case SelectiveReloadType::Component: type_name = "component"; break;
            case SelectiveReloadType::Stylesheet: type_name = "stylesheet"; break;
            case SelectiveReloadType::Resource: type_name = "resource"; break;
            case SelectiveReloadType::Property: type_name = "property"; break;
            case SelectiveReloadType::Layout: type_name = "layout"; break;
            case SelectiveReloadType::Event: type_name = "event"; break;
            case SelectiveReloadType::State: type_name = "state"; break;
            case SelectiveReloadType::Theme: type_name = "theme"; break;
            case SelectiveReloadType::Translation: type_name = "translation"; break;
        }
        reload_counts[type_name] = count;
    }
    metrics["reload_counts"] = reload_counts;

    // Average reload times
    QJsonObject avg_times;
    for (const auto& [target_id, time] : reload_times_) {
        avg_times[target_id] = static_cast<double>(time);
    }
    metrics["average_reload_times_ms"] = avg_times;

    // Error counts
    QJsonObject errors;
    for (const auto& [target_id, count] : error_counts_) {
        errors[target_id] = count;
    }
    metrics["error_counts"] = errors;

    // Tracking statistics
    metrics["tracked_components_count"] = static_cast<int>(tracked_components_.size());
    metrics["tracked_stylesheets_count"] = static_cast<int>(tracked_stylesheets_.size());
    metrics["tracked_resources_count"] = static_cast<int>(tracked_resources_.size());
    metrics["dependency_relationships_count"] = static_cast<int>(dependency_graph_.size());
    metrics["rollback_points_count"] = static_cast<int>(rollback_points_.size());

    return metrics;
}

QJsonObject SelectiveReloadManager::getReloadStatistics() const {
    QJsonObject stats;

    int total_reloads = 0;
    for (const auto& [type, count] : reload_counts_) {
        total_reloads += count;
    }

    stats["total_reloads"] = total_reloads;
    stats["session_duration_ms"] = session_timer_.elapsed();

    if (total_reloads > 0) {
        stats["average_reloads_per_minute"] = static_cast<double>(total_reloads) /
                                             (session_timer_.elapsed() / 60000.0);
    } else {
        stats["average_reloads_per_minute"] = 0.0;
    }

    // Most active components
    QJsonArray active_components;
    {
        QReadLocker locker(&components_lock_);
        for (const auto& [id, info] : tracked_components_) {
            if (info.reload_count > 0) {
                QJsonObject comp_stats;
                comp_stats["id"] = id;
                comp_stats["type"] = info.component_type;
                comp_stats["reload_count"] = info.reload_count;
                comp_stats["last_updated"] = info.last_updated.toString(Qt::ISODate);
                active_components.append(comp_stats);
            }
        }
    }
    stats["active_components"] = active_components;

    return stats;
}

void SelectiveReloadManager::resetStatistics() {
    reload_counts_.clear();
    reload_times_.clear();
    error_counts_.clear();

    // Reset component reload counts
    {
        QWriteLocker locker(&components_lock_);
        for (auto& [id, info] : tracked_components_) {
            info.reload_count = 0;
        }
    }

    session_timer_.restart();

    qDebug() << "🎯 Statistics reset";
}

// Implementation methods
SelectiveReloadResult SelectiveReloadManager::performComponentReload(const QString& component_id) {
    if (!isValidComponent(component_id)) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Component,
                                                 "Component not found or invalid");
    }

    auto info = getComponentInfo(component_id);
    if (!info.widget_instance) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Component,
                                                 "Widget instance is null");
    }

    try {
        // Create rollback point if enabled
        if (config_.create_rollback_points) {
            createRollbackPoint(component_id);
        }

        // Reload component from current configuration
        updateComponentWidget(info.widget_instance, info.current_config);

        // Update tracking info
        {
            QWriteLocker locker(&components_lock_);
            auto it = tracked_components_.find(component_id);
            if (it != tracked_components_.end()) {
                it->second.last_updated = QDateTime::currentDateTime();
                it->second.reload_count++;
            }
        }

        return SelectiveReloadResult::createSuccess(component_id, SelectiveReloadType::Component);

    } catch (const std::exception& e) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Component, e.what());
    }
}

SelectiveReloadResult SelectiveReloadManager::performStylesheetReload(const QString& stylesheet_id) {
    if (!isValidStylesheet(stylesheet_id)) {
        return SelectiveReloadResult::createError(stylesheet_id, SelectiveReloadType::Stylesheet,
                                                 "Stylesheet not found or invalid");
    }

    auto info = getStylesheetInfo(stylesheet_id);

    try {
        // Create rollback point if enabled
        if (config_.create_rollback_points) {
            createRollbackPoint(stylesheet_id);
        }

        // Reload stylesheet content from file
        QFile file(info.file_path);
        if (file.open(QIODevice::ReadOnly)) {
            QString new_css_content = QString::fromUtf8(file.readAll());
            file.close();

            // Update stylesheet content
            updateStylesheet(stylesheet_id, new_css_content);

            // Inject CSS using processor
            if (css_processor_) {
                bool success = false;

                if (info.injection_scope == "widget" && info.target_widget) {
                    success = css_processor_->injectCSS(info.compiled_content.isEmpty() ?
                                                       new_css_content : info.compiled_content,
                                                       stylesheet_id);
                } else {
                    success = css_processor_->injectCSS(info.compiled_content.isEmpty() ?
                                                       new_css_content : info.compiled_content,
                                                       stylesheet_id);
                }

                if (!success) {
                    return SelectiveReloadResult::createError(stylesheet_id, SelectiveReloadType::Stylesheet,
                                                             "Failed to inject CSS");
                }
            }

            // Update tracking info
            {
                QWriteLocker locker(&stylesheets_lock_);
                auto it = tracked_stylesheets_.find(stylesheet_id);
                if (it != tracked_stylesheets_.end()) {
                    it->second.is_injected = true;
                    it->second.last_modified = QDateTime::currentDateTime();
                }
            }

            return SelectiveReloadResult::createSuccess(stylesheet_id, SelectiveReloadType::Stylesheet);

        } else {
            return SelectiveReloadResult::createError(stylesheet_id, SelectiveReloadType::Stylesheet,
                                                     "Failed to read stylesheet file");
        }

    } catch (const std::exception& e) {
        return SelectiveReloadResult::createError(stylesheet_id, SelectiveReloadType::Stylesheet, e.what());
    }
}

SelectiveReloadResult SelectiveReloadManager::performResourceReload(const QString& resource_id) {
    if (!isValidResource(resource_id)) {
        return SelectiveReloadResult::createError(resource_id, SelectiveReloadType::Resource,
                                                 "Resource not found or invalid");
    }

    auto info = getResourceInfo(resource_id);

    try {
        // Create rollback point if enabled
        if (config_.create_rollback_points) {
            createRollbackPoint(resource_id);
        }

        // Update resource information
        updateResource(resource_id);

        // Update dependent components
        for (const QString& component_id : info.dependent_components) {
            if (isValidComponent(component_id)) {
                auto comp_info = getComponentInfo(component_id);
                if (comp_info.widget_instance) {
                    updateResourceInWidget(comp_info.widget_instance, resource_id, info.file_path);
                }
            }
        }

        return SelectiveReloadResult::createSuccess(resource_id, SelectiveReloadType::Resource);

    } catch (const std::exception& e) {
        return SelectiveReloadResult::createError(resource_id, SelectiveReloadType::Resource, e.what());
    }
}

SelectiveReloadResult SelectiveReloadManager::performPropertyReload(const QString& component_id,
                                                                   const QString& property_name,
                                                                   const QVariant& value) {
    if (!isValidComponent(component_id)) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Property,
                                                 "Component not found or invalid");
    }

    auto info = getComponentInfo(component_id);
    if (!info.widget_instance) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Property,
                                                 "Widget instance is null");
    }

    try {
        // Create rollback point if enabled
        if (config_.create_rollback_points) {
            createRollbackPoint(component_id);
        }

        // Set property on widget
        bool success = info.widget_instance->setProperty(property_name.toUtf8().constData(), value);

        if (success) {
            // Update configuration
            QJsonObject updated_config = info.current_config;
            if (!updated_config.contains("properties")) {
                updated_config["properties"] = QJsonObject{};
            }

            QJsonObject properties = updated_config["properties"].toObject();
            properties[property_name] = QJsonValue::fromVariant(value);
            updated_config["properties"] = properties;

            updateComponentConfig(component_id, updated_config);

            return SelectiveReloadResult::createSuccess(component_id, SelectiveReloadType::Property);
        } else {
            return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Property,
                                                     QString("Failed to set property: %1").arg(property_name));
        }

    } catch (const std::exception& e) {
        return SelectiveReloadResult::createError(component_id, SelectiveReloadType::Property, e.what());
    }
}

void SelectiveReloadManager::cascadeReload(const QString& source_id) {
    QStringList dependents = getDependents(source_id);

    if (!dependents.isEmpty()) {
        emit dependencyReloadTriggered(source_id, dependents);

        for (const QString& dependent_id : dependents) {
            // Determine reload type based on what the dependent is
            if (isValidComponent(dependent_id)) {
                reloadComponent(dependent_id);
            } else if (isValidStylesheet(dependent_id)) {
                reloadStylesheet(dependent_id);
            } else if (isValidResource(dependent_id)) {
                reloadResource(dependent_id);
            }
        }
    }
}

QList<SelectiveReloadTarget> SelectiveReloadManager::buildReloadPlan(const QList<SelectiveReloadTarget>& targets) const {
    QList<SelectiveReloadTarget> plan = targets;

    // Sort by priority (higher priority first)
    std::sort(plan.begin(), plan.end());

    // Remove duplicates
    auto last = std::unique(plan.begin(), plan.end());
    plan.erase(last, plan.end());

    return plan;
}

bool SelectiveReloadManager::isValidComponent(const QString& component_id) const {
    QReadLocker locker(&components_lock_);
    return tracked_components_.find(component_id) != tracked_components_.end();
}

bool SelectiveReloadManager::isValidStylesheet(const QString& stylesheet_id) const {
    QReadLocker locker(&stylesheets_lock_);
    return tracked_stylesheets_.find(stylesheet_id) != tracked_stylesheets_.end();
}

bool SelectiveReloadManager::isValidResource(const QString& resource_id) const {
    QReadLocker locker(&resources_lock_);
    return tracked_resources_.find(resource_id) != tracked_resources_.end();
}

void SelectiveReloadManager::updateComponentWidget(QWidget* widget, const QJsonObject& config) {
    if (!widget || config.isEmpty()) {
        return;
    }

    // Apply properties
    if (config.contains("properties")) {
        QJsonObject properties = config["properties"].toObject();

        for (auto it = properties.begin(); it != properties.end(); ++it) {
            QString property_name = it.key();
            QJsonValue property_value = it.value();

            // Convert JSON value to QVariant
            QVariant variant_value;
            if (property_value.isString()) {
                variant_value = property_value.toString();
            } else if (property_value.isDouble()) {
                variant_value = property_value.toDouble();
            } else if (property_value.isBool()) {
                variant_value = property_value.toBool();
            } else if (property_value.isArray()) {
                // Handle arrays (e.g., for size, position)
                QJsonArray array = property_value.toArray();
                if (array.size() == 2) {
                    QSize size(array[0].toInt(), array[1].toInt());
                    variant_value = size;
                }
            }

            // Set property on widget
            widget->setProperty(property_name.toUtf8().constData(), variant_value);
        }
    }

    // Apply stylesheet
    if (config.contains("styleSheet")) {
        QString stylesheet = config["styleSheet"].toString();
        widget->setStyleSheet(stylesheet);
    }
}

void SelectiveReloadManager::updateResourceInWidget(QWidget* widget, const QString& resource_id, const QString& new_path) {
    if (!widget) {
        return;
    }

    // This is a simplified implementation
    // In a real application, you would need to handle different resource types
    // and update the appropriate widget properties

    auto resource_info = getResourceInfo(resource_id);

    if (resource_info.resource_type == "image") {
        // Update image resources (e.g., for QLabel pixmaps, button icons)
        if (auto* label = qobject_cast<QLabel*>(widget)) {
            QPixmap pixmap(new_path);
            if (!pixmap.isNull()) {
                label->setPixmap(pixmap);
            }
        }
        // Add more widget types as needed
    }
    // Handle other resource types (fonts, data files, etc.)
}

// Slots implementation
void SelectiveReloadManager::onFileChanged(const QString& file_path) {
    if (!auto_detection_enabled_ || !monitoring_enabled_) {
        return;
    }

    // Detect what changed in the file and trigger appropriate reloads
    detectChangesInFile(file_path);
}

void SelectiveReloadManager::onComponentPropertyChanged(const QString& component_id, const QString& property_name) {
    Q_UNUSED(property_name)

    if (!auto_detection_enabled_ || !monitoring_enabled_) {
        return;
    }

    // Queue component for reload
    SelectiveReloadTarget target;
    target.target_id = component_id;
    target.type = SelectiveReloadType::Component;
    target.priority = 50;

    {
        QMutexLocker locker(&queue_mutex_);
        reload_queue_.push_back(target);
    }

    // Start debounce timer
    if (reload_timer_ && !reload_timer_->isActive()) {
        reload_timer_->start();
    }
}

void SelectiveReloadManager::onStylesheetChanged(const QString& stylesheet_id) {
    if (!auto_detection_enabled_ || !monitoring_enabled_) {
        return;
    }

    // Queue stylesheet for reload
    SelectiveReloadTarget target;
    target.target_id = stylesheet_id;
    target.type = SelectiveReloadType::Stylesheet;
    target.priority = 80;

    {
        QMutexLocker locker(&queue_mutex_);
        reload_queue_.push_back(target);
    }

    // Start debounce timer
    if (reload_timer_ && !reload_timer_->isActive()) {
        reload_timer_->start();
    }
}

void SelectiveReloadManager::onResourceChanged(const QString& resource_id) {
    if (!auto_detection_enabled_ || !monitoring_enabled_) {
        return;
    }

    // Queue resource for reload
    SelectiveReloadTarget target;
    target.target_id = resource_id;
    target.type = SelectiveReloadType::Resource;
    target.priority = 60;

    {
        QMutexLocker locker(&queue_mutex_);
        reload_queue_.push_back(target);
    }

    // Start debounce timer
    if (reload_timer_ && !reload_timer_->isActive()) {
        reload_timer_->start();
    }
}

void SelectiveReloadManager::processReloadQueue() {
    std::vector<SelectiveReloadTarget> targets_to_process;

    {
        QMutexLocker locker(&queue_mutex_);
        targets_to_process = std::move(reload_queue_);
        reload_queue_.clear();
    }

    if (!targets_to_process.empty()) {
        QList<SelectiveReloadTarget> qt_targets;
        for (const auto& target : targets_to_process) {
            qt_targets.append(target);
        }

        auto results = reloadTargets(qt_targets);

        // Log results
        for (const auto& result : results) {
            if (result.success) {
                qDebug() << "🎯 Queued reload succeeded:" << result.target_id;
            } else {
                qWarning() << "🎯 Queued reload failed:" << result.target_id << result.error_message;
            }
        }
    }
}

void SelectiveReloadManager::performScheduledReloads() {
    // This method can be used for scheduled/periodic reloads
    // Currently not implemented, but could be extended for future use
}

void SelectiveReloadManager::updatePerformanceMetrics() {
    if (!monitoring_enabled_) {
        return;
    }

    // Update internal performance counters
    // This is called periodically by the metrics timer

    // Could emit performance update signals here if needed
}

void SelectiveReloadManager::detectChangesInFile(const QString& file_path) {
    // Load current file content
    QJsonObject new_content = loadJsonFromFile(file_path);
    if (new_content.isEmpty()) {
        return; // Not a JSON file or failed to load
    }

    // Find affected targets and queue them for reload
    auto affected_results = reloadAffectedComponents(file_path);

    qDebug() << "🎯 Detected changes in file:" << file_path
             << "- affected targets:" << affected_results.size();
}

QJsonObject SelectiveReloadManager::loadJsonFromFile(const QString& file_path) const {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        return QJsonObject{};
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (doc.isNull()) {
        return QJsonObject{};
    }

    return doc.object();
}

void SelectiveReloadManager::handleReloadError(const QString& target_id, SelectiveReloadType type, const QString& error) {
    error_counts_[target_id]++;

    qWarning() << "🎯 Reload error for" << target_id << "(" << static_cast<int>(type) << "):" << error;

    // Attempt error recovery if configured
    if (config_.create_rollback_points) {
        attemptErrorRecovery(target_id);
    }
}

void SelectiveReloadManager::attemptErrorRecovery(const QString& target_id) {
    qDebug() << "🎯 Attempting error recovery for:" << target_id;

    // Try to rollback to last known good state
    if (rollbackTarget(target_id)) {
        qDebug() << "🎯 Error recovery successful for:" << target_id;
    } else {
        qWarning() << "🎯 Error recovery failed for:" << target_id;
    }
}

void SelectiveReloadManager::logReloadOperation(const QString& target_id, SelectiveReloadType type, bool success, qint64 duration_ms) {
    reload_times_[target_id] = duration_ms;

    QString type_name;
    switch (type) {
        case SelectiveReloadType::Component: type_name = "Component"; break;
        case SelectiveReloadType::Stylesheet: type_name = "Stylesheet"; break;
        case SelectiveReloadType::Resource: type_name = "Resource"; break;
        case SelectiveReloadType::Property: type_name = "Property"; break;
        default: type_name = "Unknown"; break;
    }

    qDebug() << "🎯 Reload operation:" << type_name << target_id
             << (success ? "SUCCESS" : "FAILED") << "in" << duration_ms << "ms";
}

} // namespace DeclarativeUI::HotReload

#include "SelectiveReloadManager.moc"
