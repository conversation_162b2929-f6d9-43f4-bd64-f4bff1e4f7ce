#include "SCSSProcessor.hpp"
#include <QDebug>
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QJsonArray>

namespace DeclarativeUI::HotReload::FormatSupport {

SCSSProcessor::SCSSProcessor(QObject* parent) : IFormatProcessor(parent) {
    initializeRegexPatterns();
    findSassExecutable();
    
    dependency_watcher_ = std::make_unique<QFileSystemWatcher>(this);
    connect(dependency_watcher_.get(), &QFileSystemWatcher::fileChanged,
            this, &SCSSProcessor::onDependencyFileChanged);
}

void SCSSProcessor::initializeRegexPatterns() {
    // SCSS/CSS import patterns
    import_regex_.setPattern(R"(@import\s+['"](.*?)['"];?)");
    
    // SCSS variable patterns
    variable_regex_.setPattern(R"(\$([a-zA-Z_][a-zA-Z0-9_-]*)\s*:\s*([^;]+);)");
    
    // Comment patterns
    comment_regex_.setPattern(R"(//.*$|/\*.*?\*/)");
    comment_regex_.setPatternOptions(QRegularExpression::MultilineOption | QRegularExpression::DotMatchesEverythingOption);
    
    // CSS selector patterns
    selector_regex_.setPattern(R"([.#]?[a-zA-Z_][a-zA-Z0-9_-]*\s*\{)");
}

void SCSSProcessor::findSassExecutable() {
    // Try common locations for sass/scss executable
    QStringList possible_paths = {
        "sass",
        "scss",
        "node-sass",
        QCoreApplication::applicationDirPath() + "/sass",
        QCoreApplication::applicationDirPath() + "/scss",
        "/usr/local/bin/sass",
        "/usr/bin/sass"
    };
    
    for (const QString& path : possible_paths) {
        QProcess test_process;
        test_process.start(path, QStringList() << "--version");
        test_process.waitForFinished(3000);
        
        if (test_process.exitCode() == 0) {
            sass_executable_ = path;
            qDebug() << "Found SASS executable:" << path;
            return;
        }
    }
    
    qWarning() << "SASS executable not found. SCSS compilation will be limited.";
}

bool SCSSProcessor::canProcess(const QString& file_path) const {
    QFileInfo file_info(file_path);
    QString extension = file_info.suffix().toLower();
    return getSupportedExtensions().contains(extension);
}

ProcessingResult SCSSProcessor::processFile(const QString& file_path, const ProcessingConfig& config) {
    startPerformanceMeasurement();
    emit processingStarted(file_path);
    
    try {
        if (!QFileInfo::exists(file_path)) {
            return ProcessingResult::createError("SCSS/CSS file does not exist: " + file_path);
        }
        
        QString content = readFileContent(file_path);
        if (content.isEmpty()) {
            return ProcessingResult::createError("Failed to read SCSS/CSS file: " + file_path);
        }
        
        return processContent(content, file_path, config);
        
    } catch (const std::exception& e) {
        QString error = QString("SCSS/CSS processing error: %1").arg(e.what());
        emit processingError(file_path, error);
        return ProcessingResult::createError(error);
    }
}

ProcessingResult SCSSProcessor::processContent(const QString& content, const QString& file_path, const ProcessingConfig& config) {
    try {
        QString processed_content = content;
        QJsonObject metadata = createSCSSMetadata(file_path, content);
        
        // Extract imports and variables
        QStringList imports = extractImports(content);
        QStringList variables = extractVariables(content);
        
        metadata["imports"] = QJsonArray::fromStringList(imports);
        metadata["variables"] = QJsonArray::fromStringList(variables);
        
        // Handle SCSS compilation
        QString file_extension = extractFileExtension(file_path);
        if (file_extension == "scss" || file_extension == "sass") {
            if (isAvailable()) {
                QString compiled_css = compileSCSS(content, file_path);
                if (!compiled_css.isEmpty()) {
                    processed_content = compiled_css;
                    metadata["compiled"] = true;
                    emit scssCompiled(file_path, compiled_css);
                } else {
                    return ProcessingResult::createError("SCSS compilation failed");
                }
            } else {
                qWarning() << "SCSS compiler not available, processing as plain CSS";
                metadata["compiled"] = false;
            }
        }
        
        // Handle CSS minification
        if (config.enable_minification) {
            QString minified_css = minifyCSS(processed_content);
            if (!minified_css.isEmpty()) {
                processed_content = minified_css;
                metadata["minified"] = true;
                emit cssMinified(file_path, minified_css);
            }
        }
        
        // Validate CSS syntax
        QString error_message;
        if (!hasValidCSSSyntax(processed_content, error_message)) {
            return ProcessingResult::createError("CSS syntax error: " + error_message);
        }
        
        // Track dependencies
        updateDependencyTracking(file_path, getDependencies(file_path));
        
        ProcessingResult result = ProcessingResult::createSuccess(processed_content, metadata);
        result.processing_time_ms = endPerformanceMeasurement();
        
        emit processingFinished(file_path, result);
        emit variablesExtracted(file_path, variables);
        
        return result;
        
    } catch (const std::exception& e) {
        QString error = QString("SCSS/CSS content processing error: %1").arg(e.what());
        emit processingError(file_path, error);
        return ProcessingResult::createError(error);
    }
}

bool SCSSProcessor::validateFile(const QString& file_path) const {
    if (!QFileInfo::exists(file_path)) {
        return false;
    }
    
    QString content = readFileContent(file_path);
    return validateContent(content);
}

bool SCSSProcessor::validateContent(const QString& content) const {
    QString error_message;
    
    // For SCSS content, we need to compile it first to validate
    if (content.contains('$') || content.contains('@')) {
        // Looks like SCSS, try to compile
        if (isAvailable()) {
            QString compiled_css = compileSCSS(content, "temp.scss");
            return hasValidCSSSyntax(compiled_css, error_message);
        }
    }
    
    // Validate as CSS
    return hasValidCSSSyntax(content, error_message);
}

ProcessingConfig SCSSProcessor::getDefaultConfig() const {
    ProcessingConfig config;
    config.enable_transpilation = true; // SCSS to CSS
    config.enable_minification = false;
    config.enable_source_maps = false;
    config.enable_live_injection = true;
    config.enable_caching = true;
    config.max_processing_time_ms = 5000;
    return config;
}

QStringList SCSSProcessor::getRequiredDependencies() const {
    QStringList deps;
    if (!sass_executable_.isEmpty()) {
        deps.append("SASS Compiler: " + sass_executable_);
    }
    return deps;
}

bool SCSSProcessor::isAvailable() const {
    return !sass_executable_.isEmpty();
}

ProcessingResult SCSSProcessor::prepareLiveInjection(const QString& content, const QString& file_path) {
    try {
        QString processed_content = content;
        
        // Compile SCSS if needed
        QString file_extension = extractFileExtension(file_path);
        if (file_extension == "scss" || file_extension == "sass") {
            if (isAvailable()) {
                processed_content = compileSCSS(content, file_path);
                if (processed_content.isEmpty()) {
                    return ProcessingResult::createError("SCSS compilation failed for live injection");
                }
            }
        }
        
        // Validate CSS
        QString error_message;
        if (!hasValidCSSSyntax(processed_content, error_message)) {
            return ProcessingResult::createError("CSS validation failed: " + error_message);
        }
        
        QJsonObject metadata;
        metadata["injection_method"] = "css_style_injection";
        metadata["content_type"] = "text/css";
        
        ProcessingResult result = ProcessingResult::createSuccess(processed_content, metadata);
        result.additional_data["css_content"] = processed_content;
        
        return result;
        
    } catch (const std::exception& e) {
        return ProcessingResult::createError("Live injection preparation failed: " + QString(e.what()));
    }
}

QString SCSSProcessor::compileSCSS(const QString& scss_content, const QString& file_path) const {
    if (sass_executable_.isEmpty()) {
        return QString();
    }
    
    QStringList options;
    options << "--stdin";
    options << "--no-source-map";
    
    // Add include paths
    for (const QString& include_path : include_paths_) {
        options << "--load-path" << include_path;
    }
    
    return runSassCompiler(scss_content, file_path, options);
}

QString SCSSProcessor::minifyCSS(const QString& css_content) const {
    QString minified = css_content;
    
    // Remove comments
    minified = removeComments(minified);
    
    // Remove extra whitespace
    minified = minified.simplified();
    
    // Remove spaces around certain characters
    minified.replace(" {", "{");
    minified.replace("{ ", "{");
    minified.replace(" }", "}");
    minified.replace("} ", "}");
    minified.replace("; ", ";");
    minified.replace(" ;", ";");
    minified.replace(": ", ":");
    minified.replace(" :", ":");
    
    return minified;
}

QStringList SCSSProcessor::extractImports(const QString& content) const {
    QStringList imports;
    QRegularExpressionMatchIterator iterator = import_regex_.globalMatch(content);
    
    while (iterator.hasNext()) {
        QRegularExpressionMatch match = iterator.next();
        QString import_path = match.captured(1);
        if (!import_path.isEmpty()) {
            imports.append(import_path);
        }
    }
    
    return imports;
}

QStringList SCSSProcessor::extractVariables(const QString& scss_content) const {
    QStringList variables;
    QRegularExpressionMatchIterator iterator = variable_regex_.globalMatch(scss_content);
    
    while (iterator.hasNext()) {
        QRegularExpressionMatch match = iterator.next();
        QString variable_name = match.captured(1);
        QString variable_value = match.captured(2).trimmed();
        
        if (!variable_name.isEmpty()) {
            variables.append(QString("$%1: %2").arg(variable_name, variable_value));
        }
    }
    
    return variables;
}

QString SCSSProcessor::resolveImportPath(const QString& import_path, const QString& base_path) const {
    // Try relative path first
    QString resolved = resolveRelativeImport(import_path, base_path);
    if (!resolved.isEmpty()) {
        return resolved;
    }
    
    // Try node_modules
    resolved = resolveNodeModuleImport(import_path);
    if (!resolved.isEmpty()) {
        return resolved;
    }
    
    // Try include paths
    for (const QString& include_path : include_paths_) {
        QString full_path = QDir(include_path).absoluteFilePath(import_path);
        
        // Try with .scss extension
        if (QFileInfo::exists(full_path + ".scss")) {
            return full_path + ".scss";
        }
        
        // Try with .css extension
        if (QFileInfo::exists(full_path + ".css")) {
            return full_path + ".css";
        }
        
        // Try as directory with _index.scss
        if (QFileInfo::exists(full_path + "/_index.scss")) {
            return full_path + "/_index.scss";
        }
    }
    
    return QString();
}

bool SCSSProcessor::hasValidCSSSyntax(const QString& css_content, QString& error_message) const {
    // Basic CSS syntax validation
    QString normalized = normalizeCSS(css_content);
    
    // Check for balanced braces
    int brace_count = 0;
    for (const QChar& ch : normalized) {
        if (ch == '{') {
            brace_count++;
        } else if (ch == '}') {
            brace_count--;
            if (brace_count < 0) {
                error_message = "Unmatched closing brace";
                return false;
            }
        }
    }
    
    if (brace_count != 0) {
        error_message = "Unmatched opening brace";
        return false;
    }
    
    return true;
}

QString SCSSProcessor::generateSourceMap(const QString& scss_content, const QString& css_content, const QString& file_path) const {
    Q_UNUSED(scss_content)
    Q_UNUSED(css_content)
    Q_UNUSED(file_path)
    // Source map generation would require more complex implementation
    return QString();
}

QStringList SCSSProcessor::getDependencies(const QString& file_path) const {
    QStringList dependencies;
    QString content = readFileContent(file_path);
    QStringList imports = extractImports(content);

    QFileInfo file_info(file_path);
    QString base_path = file_info.absolutePath();

    for (const QString& import : imports) {
        QString resolved_path = resolveImportPath(import, base_path);
        if (!resolved_path.isEmpty() && QFileInfo::exists(resolved_path)) {
            dependencies.append(resolved_path);
            // Note: Cannot emit signal from const method, would need to be done elsewhere
        }
    }

    return dependencies;
}

void SCSSProcessor::watchDependencies(const QString& file_path) {
    QStringList dependencies = getDependencies(file_path);
    for (const QString& dependency : dependencies) {
        if (!dependency_watcher_->files().contains(dependency)) {
            dependency_watcher_->addPath(dependency);
        }
    }
}

void SCSSProcessor::unwatchDependencies(const QString& file_path) {
    auto it = file_dependencies_.find(file_path);
    if (it != file_dependencies_.end()) {
        for (const QString& dependency : it.value()) {
            dependency_watcher_->removePath(dependency);
        }
        file_dependencies_.erase(it);
    }
}

void SCSSProcessor::setSassExecutable(const QString& path) {
    sass_executable_ = path;
}

QString SCSSProcessor::getSassExecutable() const {
    return sass_executable_;
}

void SCSSProcessor::setIncludePaths(const QStringList& paths) {
    include_paths_ = paths;
}

QStringList SCSSProcessor::getIncludePaths() const {
    return include_paths_;
}

QString SCSSProcessor::runSassCompiler(const QString& input_content, const QString& file_path, const QStringList& options) const {
    Q_UNUSED(file_path)

    QProcess sass_process;
    sass_process.start(sass_executable_, options);

    if (!sass_process.waitForStarted(3000)) {
        qWarning() << "Failed to start SASS compiler";
        return QString();
    }

    sass_process.write(input_content.toUtf8());
    sass_process.closeWriteChannel();

    if (!sass_process.waitForFinished(10000)) {
        qWarning() << "SASS compiler timeout";
        sass_process.kill();
        return QString();
    }

    if (sass_process.exitCode() != 0) {
        QString error = QString::fromUtf8(sass_process.readAllStandardError());
        qWarning() << "SASS compilation error:" << error;
        return QString();
    }

    return QString::fromUtf8(sass_process.readAllStandardOutput());
}

QJsonObject SCSSProcessor::createSCSSMetadata(const QString& file_path, const QString& content) const {
    QJsonObject metadata = createMetadata(file_path);
    metadata["format"] = "SCSS/CSS";
    metadata["file_extension"] = extractFileExtension(file_path);
    metadata["line_count"] = content.split('\n').size();
    metadata["has_variables"] = content.contains('$');
    metadata["has_imports"] = content.contains("@import");
    return metadata;
}

QString SCSSProcessor::removeComments(const QString& content) const {
    QString result = content;
    result.remove(comment_regex_);
    return result;
}

QString SCSSProcessor::normalizeCSS(const QString& css_content) const {
    QString normalized = removeComments(css_content);
    normalized = normalized.simplified();
    return normalized;
}

bool SCSSProcessor::isValidSCSSFile(const QString& file_path) const {
    QString extension = extractFileExtension(file_path);
    return extension == "scss" || extension == "sass";
}

bool SCSSProcessor::isValidCSSFile(const QString& file_path) const {
    QString extension = extractFileExtension(file_path);
    return extension == "css";
}

QString SCSSProcessor::extractFileExtension(const QString& file_path) const {
    QFileInfo file_info(file_path);
    return file_info.suffix().toLower();
}

QStringList SCSSProcessor::parseImportStatement(const QString& import_line) const {
    QRegularExpressionMatch match = import_regex_.match(import_line.trimmed());
    QStringList parts;

    if (match.hasMatch()) {
        parts.append(match.captured(1));
    }

    return parts;
}

QString SCSSProcessor::resolveRelativeImport(const QString& import_path, const QString& base_path) const {
    QDir base_dir(base_path);
    QString full_path = base_dir.absoluteFilePath(import_path);

    // Try with .scss extension
    if (QFileInfo::exists(full_path + ".scss")) {
        return full_path + ".scss";
    }

    // Try with .css extension
    if (QFileInfo::exists(full_path + ".css")) {
        return full_path + ".css";
    }

    // Try with underscore prefix (SCSS partial)
    QFileInfo file_info(full_path);
    QString partial_path = file_info.absolutePath() + "/_" + file_info.fileName();

    if (QFileInfo::exists(partial_path + ".scss")) {
        return partial_path + ".scss";
    }

    return QString();
}

QString SCSSProcessor::resolveNodeModuleImport(const QString& import_path) const {
    // Look for node_modules directory
    QString current_dir = QDir::currentPath();
    QDir dir(current_dir);

    while (dir.exists()) {
        QString node_modules_path = dir.absoluteFilePath("node_modules");
        if (QDir(node_modules_path).exists()) {
            QString module_path = QDir(node_modules_path).absoluteFilePath(import_path);

            // Try with .scss extension
            if (QFileInfo::exists(module_path + ".scss")) {
                return module_path + ".scss";
            }

            // Try with .css extension
            if (QFileInfo::exists(module_path + ".css")) {
                return module_path + ".css";
            }

            // Try as directory with index file
            if (QFileInfo::exists(module_path + "/index.scss")) {
                return module_path + "/index.scss";
            }
        }

        if (!dir.cdUp()) {
            break;
        }
    }

    return QString();
}

void SCSSProcessor::updateDependencyTracking(const QString& file_path, const QStringList& dependencies) {
    file_dependencies_[file_path] = dependencies;
}

void SCSSProcessor::onDependencyFileChanged(const QString& path) {
    // Find which files depend on this changed file
    for (auto it = file_dependencies_.begin(); it != file_dependencies_.end(); ++it) {
        if (it.value().contains(path)) {
            emit dependencyChanged(it.key(), path);
        }
    }
}

} // namespace DeclarativeUI::HotReload::FormatSupport
