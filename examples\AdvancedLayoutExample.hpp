#pragma once

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QGroupBox>
#include <QTextEdit>
#include <QSplitter>
#include <QTimer>
#include <QResizeEvent>
#include <memory>

#include "../src/Layout/AdvancedLayoutEngine.hpp"

/**
 * @brief Comprehensive example demonstrating advanced layout capabilities
 * 
 * This example showcases:
 * - CSS Grid-like layouts with named areas and flexible tracks
 * - Flexbox-style layouts with flexible item arrangement
 * - Responsive design with breakpoints and adaptive layouts
 * - Constraint-based positioning with relationships between elements
 * - Interactive controls for real-time layout parameter adjustment
 * - Performance monitoring and debugging tools
 */
class AdvancedLayoutExample : public QMainWindow {
    Q_OBJECT

public:
    explicit AdvancedLayoutExample(QWidget* parent = nullptr);
    ~AdvancedLayoutExample() override;

protected:
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onGridLayoutChanged();
    void onFlexLayoutChanged();
    void onResponsiveLayoutChanged();
    void onConstraintLayoutChanged();
    void onBreakpointChanged(int breakpoint);
    void onLayoutUpdated(QWidget* container);
    void resetAllLayouts();
    void toggleDebugMode();
    void exportLayoutConfiguration();
    void importLayoutConfiguration();

private:
    void setupUI();
    void setupGridLayoutDemo();
    void setupFlexLayoutDemo();
    void setupResponsiveLayoutDemo();
    void setupConstraintLayoutDemo();
    void setupControlPanels();
    void setupStatusBar();
    void updateLayoutStatus();
    void createGridDemoContent();
    void createFlexDemoContent();
    void createResponsiveDemoContent();
    void createConstraintDemoContent();

    // **Core Components**
    std::unique_ptr<DeclarativeUI::Layout::AdvancedLayoutEngine> layout_engine_;
    QTabWidget* demo_tabs_;
    QWidget* status_widget_;
    QLabel* status_label_;
    QTimer* update_timer_;

    // **Grid Layout Demo**
    QWidget* grid_demo_container_;
    std::shared_ptr<DeclarativeUI::Layout::GridLayout> grid_layout_;
    QGroupBox* grid_controls_;
    QSlider* grid_row_gap_slider_;
    QSlider* grid_col_gap_slider_;
    QComboBox* grid_justify_content_;
    QComboBox* grid_align_content_;
    QComboBox* grid_justify_items_;
    QComboBox* grid_align_items_;
    QCheckBox* grid_named_areas_;
    std::vector<QWidget*> grid_demo_widgets_;

    // **Flex Layout Demo**
    QWidget* flex_demo_container_;
    std::shared_ptr<DeclarativeUI::Layout::FlexLayout> flex_layout_;
    QGroupBox* flex_controls_;
    QComboBox* flex_direction_;
    QComboBox* flex_wrap_;
    QComboBox* flex_justify_content_;
    QComboBox* flex_align_items_;
    QComboBox* flex_align_content_;
    QSlider* flex_gap_slider_;
    std::vector<QWidget*> flex_demo_widgets_;

    // **Responsive Layout Demo**
    QWidget* responsive_demo_container_;
    std::shared_ptr<DeclarativeUI::Layout::ResponsiveLayout> responsive_layout_;
    QGroupBox* responsive_controls_;
    QLabel* current_breakpoint_label_;
    QSlider* container_width_slider_;
    QCheckBox* auto_responsive_;
    std::vector<QWidget*> responsive_demo_widgets_;

    // **Constraint Layout Demo**
    QWidget* constraint_demo_container_;
    std::shared_ptr<DeclarativeUI::Layout::ConstraintLayout> constraint_layout_;
    QGroupBox* constraint_controls_;
    QComboBox* constraint_type_;
    QComboBox* constraint_attribute_;
    QPushButton* add_constraint_btn_;
    QPushButton* remove_constraint_btn_;
    QTextEdit* constraint_list_;
    std::vector<QWidget*> constraint_demo_widgets_;

    // **Global Controls**
    QGroupBox* global_controls_;
    QPushButton* reset_button_;
    QPushButton* debug_button_;
    QPushButton* export_button_;
    QPushButton* import_button_;
    QCheckBox* enable_animations_;
    QSlider* animation_duration_;

    // **Performance Monitoring**
    QLabel* performance_label_;
    QTimer* performance_timer_;
    int layout_update_count_;
    qint64 last_update_time_;
};

/**
 * @brief Custom widget for demonstrating layout features
 */
class LayoutDemoWidget : public QWidget {
    Q_OBJECT

public:
    explicit LayoutDemoWidget(const QString& text, const QColor& color = Qt::blue, QWidget* parent = nullptr);

    void setText(const QString& text);
    void setColor(const QColor& color);
    void setFlexProperties(double grow, double shrink, int basis);
    void setGridPosition(int row, int col, int row_span = 1, int col_span = 1);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;

signals:
    void clicked();
    void propertiesChanged();

private:
    QString text_;
    QColor color_;
    
    // **Flex Properties**
    double flex_grow_ = 0.0;
    double flex_shrink_ = 1.0;
    int flex_basis_ = -1;
    
    // **Grid Properties**
    int grid_row_ = 1;
    int grid_col_ = 1;
    int grid_row_span_ = 1;
    int grid_col_span_ = 1;
    
    bool is_selected_ = false;
};

/**
 * @brief Layout configuration data structure
 */
struct LayoutConfiguration {
    // **Grid Configuration**
    struct GridConfig {
        std::vector<DeclarativeUI::Layout::GridLayout::GridTrack> row_tracks;
        std::vector<DeclarativeUI::Layout::GridLayout::GridTrack> col_tracks;
        std::vector<QString> template_areas;
        int row_gap = 16;
        int col_gap = 16;
        DeclarativeUI::Layout::GridLayout::GridAlignment justify_content = DeclarativeUI::Layout::GridLayout::GridAlignment::Start;
        DeclarativeUI::Layout::GridLayout::GridAlignment align_content = DeclarativeUI::Layout::GridLayout::GridAlignment::Start;
        DeclarativeUI::Layout::GridLayout::GridAlignment justify_items = DeclarativeUI::Layout::GridLayout::GridAlignment::Stretch;
        DeclarativeUI::Layout::GridLayout::GridAlignment align_items = DeclarativeUI::Layout::GridLayout::GridAlignment::Stretch;
    } grid;
    
    // **Flex Configuration**
    struct FlexConfig {
        DeclarativeUI::Layout::FlexLayout::FlexDirection direction = DeclarativeUI::Layout::FlexLayout::FlexDirection::Row;
        DeclarativeUI::Layout::FlexLayout::FlexWrap wrap = DeclarativeUI::Layout::FlexLayout::FlexWrap::NoWrap;
        DeclarativeUI::Layout::FlexLayout::FlexAlignment justify_content = DeclarativeUI::Layout::FlexLayout::FlexAlignment::Start;
        DeclarativeUI::Layout::FlexLayout::FlexAlignment align_items = DeclarativeUI::Layout::FlexLayout::FlexAlignment::Stretch;
        DeclarativeUI::Layout::FlexLayout::FlexAlignment align_content = DeclarativeUI::Layout::FlexLayout::FlexAlignment::Start;
        int gap = 8;
    } flex;
    
    // **Responsive Configuration**
    struct ResponsiveConfig {
        std::vector<std::pair<QString, std::pair<int, int>>> breakpoints;  // name, (min_width, max_width)
        std::unordered_map<QString, std::unordered_map<QString, QVariant>> responsive_properties;
    } responsive;
    
    // **Constraint Configuration**
    struct ConstraintConfig {
        std::vector<DeclarativeUI::Layout::ConstraintLayout::Constraint> constraints;
    } constraint;
    
    // **Global Configuration**
    bool debug_mode = false;
    bool animations_enabled = true;
    int animation_duration = 300;
};

/**
 * @brief Layout configuration serializer/deserializer
 */
class LayoutConfigurationManager {
public:
    static bool saveConfiguration(const LayoutConfiguration& config, const QString& filename);
    static bool loadConfiguration(LayoutConfiguration& config, const QString& filename);
    static QString configurationToJson(const LayoutConfiguration& config);
    static bool configurationFromJson(LayoutConfiguration& config, const QString& json);
    
private:
    static QJsonObject gridConfigToJson(const LayoutConfiguration::GridConfig& config);
    static QJsonObject flexConfigToJson(const LayoutConfiguration::FlexConfig& config);
    static QJsonObject responsiveConfigToJson(const LayoutConfiguration::ResponsiveConfig& config);
    static QJsonObject constraintConfigToJson(const LayoutConfiguration::ConstraintConfig& config);
    
    static void gridConfigFromJson(LayoutConfiguration::GridConfig& config, const QJsonObject& json);
    static void flexConfigFromJson(LayoutConfiguration::FlexConfig& config, const QJsonObject& json);
    static void responsiveConfigFromJson(LayoutConfiguration::ResponsiveConfig& config, const QJsonObject& json);
    static void constraintConfigFromJson(LayoutConfiguration::ConstraintConfig& config, const QJsonObject& json);
};
