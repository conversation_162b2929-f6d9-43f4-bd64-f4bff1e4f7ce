#pragma once

#include <QObject>
#include <QString>
#include <QVariant>
#include <QJsonObject>
#include <QVersionNumber>
#include <memory>
#include <functional>

namespace DeclarativeUI::Plugins {

/**
 * @brief Plugin metadata containing information about the plugin
 */
struct PluginMetadata {
    QString name;
    QString description;
    QString author;
    QVersionNumber version;
    QVersionNumber minFrameworkVersion;
    QVersionNumber maxFrameworkVersion;
    QStringList dependencies;
    QStringList tags;
    QString license;
    QString website;
    
    bool isValid() const {
        return !name.isEmpty() && version.isNull() == false;
    }
    
    QJsonObject toJson() const;
    static PluginMetadata fromJson(const QJsonObject& json);
};

/**
 * @brief Plugin capability flags
 */
enum class PluginCapability {
    Components = 0x01,      // Provides UI components
    Commands = 0x02,        // Provides commands
    Themes = 0x04,          // Provides themes
    Layouts = 0x08,         // Provides layout engines
    Validators = 0x10,      // Provides data validators
    Animations = 0x20,      // Provides animations
    FileFormats = 0x40,     // Provides file format support
    StateProviders = 0x80,  // Provides state management
    EventHandlers = 0x100   // Provides event handlers
};
Q_DECLARE_FLAGS(PluginCapabilities, PluginCapability)
Q_DECLARE_OPERATORS_FOR_FLAGS(PluginCapabilities)

/**
 * @brief Plugin lifecycle states
 */
enum class PluginState {
    Unloaded,
    Loading,
    Loaded,
    Initializing,
    Active,
    Deactivating,
    Error,
    Disabled
};

/**
 * @brief Base interface for all DeclarativeUI plugins
 */
class IPlugin : public QObject {
    Q_OBJECT
    
public:
    explicit IPlugin(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IPlugin() = default;
    
    // Core plugin information
    virtual PluginMetadata getMetadata() const = 0;
    virtual PluginCapabilities getCapabilities() const = 0;
    virtual PluginState getState() const = 0;
    
    // Lifecycle methods
    virtual bool initialize() = 0;
    virtual bool activate() = 0;
    virtual bool deactivate() = 0;
    virtual void cleanup() = 0;
    
    // Configuration
    virtual QJsonObject getDefaultConfiguration() const { return {}; }
    virtual bool configure(const QJsonObject& config) { Q_UNUSED(config); return true; }
    virtual QJsonObject getCurrentConfiguration() const { return {}; }
    
    // Dependency management
    virtual QStringList getRequiredPlugins() const { return {}; }
    virtual QStringList getOptionalPlugins() const { return {}; }
    virtual bool checkDependencies() const { return true; }
    
    // Error handling
    virtual QString getLastError() const { return last_error_; }
    
signals:
    void stateChanged(PluginState newState);
    void errorOccurred(const QString& error);
    void configurationChanged(const QJsonObject& newConfig);
    
protected:
    void setError(const QString& error) {
        last_error_ = error;
        emit errorOccurred(error);
    }
    
    void setState(PluginState state) {
        if (state_ != state) {
            state_ = state;
            emit stateChanged(state);
        }
    }
    
private:
    PluginState state_ = PluginState::Unloaded;
    QString last_error_;
};

/**
 * @brief Interface for plugins that provide UI components
 */
class IComponentPlugin : public IPlugin {
    Q_OBJECT
    
public:
    explicit IComponentPlugin(QObject* parent = nullptr) : IPlugin(parent) {}
    
    // Component registration
    virtual QStringList getProvidedComponents() const = 0;
    virtual std::unique_ptr<QObject> createComponent(const QString& componentType) const = 0;
    virtual QJsonObject getComponentSchema(const QString& componentType) const = 0;
    
    // Component metadata
    virtual QString getComponentCategory(const QString& componentType) const = 0;
    virtual QString getComponentDescription(const QString& componentType) const = 0;
    virtual QStringList getComponentTags(const QString& componentType) const = 0;
};

/**
 * @brief Interface for plugins that provide commands
 */
class ICommandPlugin : public IPlugin {
    Q_OBJECT
    
public:
    explicit ICommandPlugin(QObject* parent = nullptr) : IPlugin(parent) {}
    
    // Command registration
    virtual QStringList getProvidedCommands() const = 0;
    virtual std::unique_ptr<QObject> createCommand(const QString& commandType) const = 0;
    virtual QJsonObject getCommandSchema(const QString& commandType) const = 0;
    
    // Command metadata
    virtual QString getCommandCategory(const QString& commandType) const = 0;
    virtual QString getCommandDescription(const QString& commandType) const = 0;
    virtual QStringList getCommandTags(const QString& commandType) const = 0;
};

/**
 * @brief Interface for plugins that provide themes
 */
class IThemePlugin : public IPlugin {
    Q_OBJECT
    
public:
    explicit IThemePlugin(QObject* parent = nullptr) : IPlugin(parent) {}
    
    // Theme registration
    virtual QStringList getProvidedThemes() const = 0;
    virtual QJsonObject getThemeDefinition(const QString& themeName) const = 0;
    virtual QString getThemeStyleSheet(const QString& themeName) const = 0;
    
    // Theme metadata
    virtual QString getThemeDescription(const QString& themeName) const = 0;
    virtual QStringList getThemeTags(const QString& themeName) const = 0;
    virtual bool isThemeDark(const QString& themeName) const = 0;
};

/**
 * @brief Factory function type for creating plugin instances
 */
using PluginFactory = std::function<std::unique_ptr<IPlugin>()>;

/**
 * @brief Plugin export macro for C-style plugin loading
 */
#define DECLARATIVE_UI_PLUGIN_EXPORT extern "C" Q_DECL_EXPORT

/**
 * @brief Standard plugin factory function signature
 */
#define DECLARATIVE_UI_PLUGIN_FACTORY_FUNCTION \
    DECLARATIVE_UI_PLUGIN_EXPORT DeclarativeUI::Plugins::IPlugin* createPlugin()

} // namespace DeclarativeUI::Plugins

Q_DECLARE_METATYPE(DeclarativeUI::Plugins::PluginMetadata)
Q_DECLARE_METATYPE(DeclarativeUI::Plugins::PluginState)
Q_DECLARE_METATYPE(DeclarativeUI::Plugins::PluginCapabilities)
