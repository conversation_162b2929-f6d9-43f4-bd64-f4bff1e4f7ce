{"version": 3, "configurePresets": [{"name": "default", "displayName": "<PERSON><PERSON><PERSON>g", "description": "Default build configuration", "generator": "Ninja", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "BUILD_EXAMPLES": "ON", "BUILD_TESTS": "ON", "BUILD_COMMAND_SYSTEM": "ON", "BUILD_ADAPTERS": "ON", "BUILD_COMMAND_EXAMPLES": "ON", "BUILD_COMMAND_TESTS": "ON"}}, {"name": "debug", "displayName": "Debug Config", "description": "Debug build with all features", "generator": "Ninja", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "BUILD_EXAMPLES": "ON", "BUILD_TESTS": "ON", "BUILD_COMMAND_SYSTEM": "ON", "BUILD_ADAPTERS": "ON", "BUILD_COMMAND_EXAMPLES": "ON", "BUILD_COMMAND_TESTS": "ON", "ENABLE_COMMAND_DEBUG": "ON", "DECLARATIVE_UI_DEBUG": "ON"}}, {"name": "release", "displayName": "Release Config", "description": "Optimized release build", "generator": "Ninja", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "BUILD_EXAMPLES": "ON", "BUILD_TESTS": "ON"}}, {"name": "minimal", "displayName": "Minimal Config", "description": "Library only, no examples or tests", "generator": "Ninja", "binaryDir": "${sourceDir}/build-minimal", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "BUILD_EXAMPLES": "OFF", "BUILD_TESTS": "OFF", "BUILD_COMMAND_SYSTEM": "OFF", "BUILD_ADAPTERS": "OFF", "BUILD_COMMAND_EXAMPLES": "OFF", "BUILD_COMMAND_TESTS": "OFF"}}, {"name": "command-dev", "displayName": "Command Development", "description": "Command system development with debug features", "generator": "Ninja", "binaryDir": "${sourceDir}/build-command", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "BUILD_EXAMPLES": "ON", "BUILD_TESTS": "ON", "BUILD_COMMAND_SYSTEM": "ON", "BUILD_ADAPTERS": "ON", "BUILD_COMMAND_EXAMPLES": "ON", "BUILD_COMMAND_TESTS": "ON", "ENABLE_COMMAND_DEBUG": "ON", "DECLARATIVE_UI_DEBUG": "ON"}}, {"name": "legacy-only", "displayName": "Legacy Components Only", "description": "Build only legacy component system", "generator": "Ninja", "binaryDir": "${sourceDir}/build-legacy", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "BUILD_EXAMPLES": "ON", "BUILD_TESTS": "ON", "BUILD_COMMAND_SYSTEM": "OFF", "BUILD_ADAPTERS": "OFF", "BUILD_COMMAND_EXAMPLES": "OFF", "BUILD_COMMAND_TESTS": "OFF"}}], "buildPresets": [{"name": "default", "configurePreset": "default"}, {"name": "debug", "configurePreset": "debug"}, {"name": "release", "configurePreset": "release"}, {"name": "minimal", "configurePreset": "minimal"}, {"name": "command-dev", "configurePreset": "command-dev"}, {"name": "legacy-only", "configurePreset": "legacy-only"}], "testPresets": [{"name": "default", "configurePreset": "default", "output": {"outputOnFailure": true}}, {"name": "debug", "configurePreset": "debug", "output": {"outputOnFailure": true}}, {"name": "command-dev", "configurePreset": "command-dev", "output": {"outputOnFailure": true}}, {"name": "legacy-only", "configurePreset": "legacy-only", "output": {"outputOnFailure": true}}]}