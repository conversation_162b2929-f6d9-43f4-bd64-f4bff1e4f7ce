#pragma once

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTabWidget>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QSlider>
#include <QProgressBar>
#include <QSpinBox>
#include <QGroupBox>
#include <QScrollArea>
#include <QFrame>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QAction>
#include <QTimer>
#include <QElapsedTimer>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QFileDialog>
#include <QMessageBox>
#include <QApplication>
#include <QScreen>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QThread>
#include <QThreadPool>
#include <QRunnable>
#include <QMutex>
#include <QWaitCondition>
#include <QRandomGenerator>
#include <QPainter>
#include <QPixmap>
#include <QOpenGLWidget>

#include "../src/Profiling/PerformanceProfiler.hpp"
#include "../src/Profiling/PerformanceDashboard.hpp"

#include <memory>
#include <vector>
#include <thread>
#include <chrono>
#include <atomic>
#include <random>

/**
 * @brief Comprehensive example application demonstrating Performance Profiling Tools
 * 
 * This example showcases:
 * - Real-time performance monitoring and profiling
 * - CPU, memory, render, and I/O performance tracking
 * - Bottleneck detection and analysis
 * - Optimization recommendations
 * - Memory leak detection
 * - Render performance profiling
 * - Interactive performance dashboard
 * - Performance report generation
 * - Stress testing and benchmarking
 */
namespace DeclarativeUI::Profiling::Examples {

/**
 * @brief Main window for the performance profiling example
 */
class PerformanceProfilingExample : public QMainWindow {
    Q_OBJECT

public:
    explicit PerformanceProfilingExample(QWidget* parent = nullptr);
    ~PerformanceProfilingExample();

private slots:
    // **Profiling Control**
    void startProfiling();
    void stopProfiling();
    void pauseProfiling();
    void resumeProfiling();
    void clearProfilingData();

    // **Dashboard Control**
    void showPerformanceDashboard();
    void hidePerformanceDashboard();
    void configureProfiling();

    // **Stress Testing**
    void startCPUStressTest();
    void startMemoryStressTest();
    void startRenderStressTest();
    void startIOStressTest();
    void startCombinedStressTest();
    void stopAllStressTests();

    // **Benchmarking**
    void runWidgetCreationBenchmark();
    void runLayoutBenchmark();
    void runPaintBenchmark();
    void runMemoryAllocationBenchmark();
    void runThreadingBenchmark();

    // **Memory Testing**
    void createMemoryLeaks();
    void detectMemoryLeaks();
    void clearMemoryLeaks();
    void profileMemoryUsage();

    // **Render Testing**
    void profileRenderPerformance();
    void testFrameRate();
    void analyzeRenderBottlenecks();
    void optimizeRenderPerformance();

    // **Report Generation**
    void generatePerformanceReport();
    void exportProfilingData();
    void loadProfilingSession();
    void saveProfilingSession();

    // **Optimization**
    void applyOptimizationRecommendations();
    void analyzePerformanceBottlenecks();
    void showOptimizationSuggestions();

    // **Event Handlers**
    void onProfilingStarted();
    void onProfilingStopped();
    void onBottleneckDetected(const BottleneckInfo& bottleneck);
    void onOptimizationRecommendation(const OptimizationRecommendation& recommendation);
    void onPerformanceWarning(const QString& message, const QJsonObject& details);

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupProfilingControlPanel();
    void setupStressTestPanel();
    void setupBenchmarkPanel();
    void setupMemoryTestPanel();
    void setupRenderTestPanel();
    void setupReportPanel();
    void setupOptimizationPanel();

    void createStressTestWidgets();
    void createBenchmarkWidgets();
    void updateProfilingStatus();
    void updatePerformanceMetrics();

    // **Stress Test Workers**
    class CPUStressWorker;
    class MemoryStressWorker;
    class RenderStressWorker;
    class IOStressWorker;

    void startCPUStressWorker();
    void startMemoryStressWorker();
    void startRenderStressWorker();
    void startIOStressWorker();

    // **Benchmark Functions**
    void benchmarkWidgetCreation(int widget_count);
    void benchmarkLayoutUpdates(int update_count);
    void benchmarkPaintOperations(int paint_count);
    void benchmarkMemoryAllocations(int allocation_count);
    void benchmarkThreadOperations(int thread_count);

    // **Memory Leak Simulation**
    void simulateMemoryLeak(size_t leak_size_mb);
    void simulateMemoryFragmentation();
    void simulateMemoryPressure();

    // **Render Performance Testing**
    void testComplexRendering();
    void testAnimationPerformance();
    void testWidgetHierarchyPerformance();

    // **UI Components**
    QWidget* central_widget_;
    QSplitter* main_splitter_;
    QTabWidget* main_tabs_;

    // **Profiling Control Panel**
    QWidget* profiling_control_panel_;
    QPushButton* start_profiling_button_;
    QPushButton* stop_profiling_button_;
    QPushButton* pause_profiling_button_;
    QPushButton* clear_data_button_;
    QPushButton* show_dashboard_button_;
    QPushButton* configure_button_;
    QLabel* profiling_status_label_;
    QLabel* session_time_label_;

    // **Stress Test Panel**
    QWidget* stress_test_panel_;
    QPushButton* cpu_stress_button_;
    QPushButton* memory_stress_button_;
    QPushButton* render_stress_button_;
    QPushButton* io_stress_button_;
    QPushButton* combined_stress_button_;
    QPushButton* stop_stress_button_;
    QProgressBar* stress_progress_;
    QLabel* stress_status_label_;

    // **Benchmark Panel**
    QWidget* benchmark_panel_;
    QPushButton* widget_benchmark_button_;
    QPushButton* layout_benchmark_button_;
    QPushButton* paint_benchmark_button_;
    QPushButton* memory_benchmark_button_;
    QPushButton* threading_benchmark_button_;
    QTextEdit* benchmark_results_text_;

    // **Memory Test Panel**
    QWidget* memory_test_panel_;
    QPushButton* create_leaks_button_;
    QPushButton* detect_leaks_button_;
    QPushButton* clear_leaks_button_;
    QPushButton* profile_memory_button_;
    QTextEdit* memory_report_text_;
    QLabel* memory_usage_label_;

    // **Render Test Panel**
    QWidget* render_test_panel_;
    QPushButton* profile_render_button_;
    QPushButton* test_framerate_button_;
    QPushButton* analyze_bottlenecks_button_;
    QPushButton* optimize_render_button_;
    QTextEdit* render_report_text_;
    QLabel* fps_label_;

    // **Report Panel**
    QWidget* report_panel_;
    QPushButton* generate_report_button_;
    QPushButton* export_data_button_;
    QPushButton* load_session_button_;
    QPushButton* save_session_button_;
    QComboBox* report_format_combo_;
    QTextEdit* report_preview_text_;

    // **Optimization Panel**
    QWidget* optimization_panel_;
    QPushButton* analyze_bottlenecks_button_;
    QPushButton* show_suggestions_button_;
    QPushButton* apply_optimizations_button_;
    QTextEdit* optimization_text_;
    QLabel* optimization_status_label_;

    // **Performance Metrics Display**
    QWidget* metrics_display_;
    QLabel* cpu_usage_label_;
    QLabel* memory_usage_label_;
    QLabel* frame_rate_label_;
    QLabel* io_throughput_label_;
    QProgressBar* cpu_progress_;
    QProgressBar* memory_progress_;
    QProgressBar* frame_rate_progress_;

    // **Profiling Components**
    std::shared_ptr<PerformanceProfiler> profiler_;
    std::shared_ptr<RenderProfiler> render_profiler_;
    std::shared_ptr<MemoryLeakDetector> leak_detector_;
    std::unique_ptr<PerformanceDashboard> dashboard_;

    // **Stress Test Workers**
    std::vector<std::unique_ptr<QThread>> stress_test_threads_;
    std::atomic<bool> stress_tests_running_{false};

    // **Memory Leak Simulation**
    std::vector<void*> simulated_leaks_;
    std::mutex leaks_mutex_;

    // **State**
    bool profiling_active_ = false;
    bool profiling_paused_ = false;
    QElapsedTimer session_timer_;
    QString current_session_file_;

    // **Timers**
    std::unique_ptr<QTimer> metrics_update_timer_;
    std::unique_ptr<QTimer> status_update_timer_;

    // **Configuration**
    struct ProfilingConfig {
        int sampling_interval_ms = 100;
        bool enable_cpu_profiling = true;
        bool enable_memory_profiling = true;
        bool enable_render_profiling = true;
        bool enable_io_profiling = true;
        bool enable_leak_detection = true;
        double cpu_threshold = 80.0;
        qint64 memory_threshold_mb = 512;
        double fps_threshold = 30.0;
        bool auto_generate_reports = true;
        QString reports_directory = "profiling_reports";
    } config_;

    // **Statistics**
    struct SessionStatistics {
        int total_snapshots = 0;
        int total_bottlenecks = 0;
        int total_recommendations = 0;
        int total_warnings = 0;
        double peak_cpu_usage = 0.0;
        qint64 peak_memory_usage_mb = 0;
        double min_frame_rate = 0.0;
        qint64 session_duration_ms = 0;
    } session_stats_;
};

/**
 * @brief CPU stress test worker
 */
class PerformanceProfilingExample::CPUStressWorker : public QObject, public QRunnable {
    Q_OBJECT

public:
    explicit CPUStressWorker(std::atomic<bool>* running_flag);
    void run() override;

signals:
    void progressUpdated(int percentage);
    void finished();

private:
    std::atomic<bool>* running_flag_;
    void performCPUIntensiveTask();
};

/**
 * @brief Memory stress test worker
 */
class PerformanceProfilingExample::MemoryStressWorker : public QObject, public QRunnable {
    Q_OBJECT

public:
    explicit MemoryStressWorker(std::atomic<bool>* running_flag);
    void run() override;

signals:
    void progressUpdated(int percentage);
    void memoryAllocated(qint64 bytes);
    void finished();

private:
    std::atomic<bool>* running_flag_;
    void performMemoryIntensiveTask();
    std::vector<std::vector<char>> memory_blocks_;
};

/**
 * @brief Render stress test worker
 */
class PerformanceProfilingExample::RenderStressWorker : public QObject, public QRunnable {
    Q_OBJECT

public:
    explicit RenderStressWorker(QWidget* target_widget, std::atomic<bool>* running_flag);
    void run() override;

signals:
    void progressUpdated(int percentage);
    void frameRendered();
    void finished();

private:
    QWidget* target_widget_;
    std::atomic<bool>* running_flag_;
    void performRenderIntensiveTask();
};

/**
 * @brief I/O stress test worker
 */
class PerformanceProfilingExample::IOStressWorker : public QObject, public QRunnable {
    Q_OBJECT

public:
    explicit IOStressWorker(std::atomic<bool>* running_flag);
    void run() override;

signals:
    void progressUpdated(int percentage);
    void bytesProcessed(qint64 bytes);
    void finished();

private:
    std::atomic<bool>* running_flag_;
    void performIOIntensiveTask();
    QString temp_directory_;
};

/**
 * @brief Performance benchmark utilities
 */
class PerformanceBenchmarkUtilities {
public:
    struct BenchmarkResult {
        QString test_name;
        qint64 total_time_ms;
        qint64 average_time_ms;
        qint64 min_time_ms;
        qint64 max_time_ms;
        int iterations;
        double operations_per_second;
        QJsonObject metadata;
    };

    static BenchmarkResult benchmarkFunction(const QString& name, 
                                            std::function<void()> func, 
                                            int iterations = 1000);

    static BenchmarkResult benchmarkWidgetCreation(const QString& widget_type, 
                                                   std::function<QWidget*()> factory, 
                                                   int count = 1000);

    static BenchmarkResult benchmarkMemoryOperations(const QString& operation_name,
                                                     std::function<void()> operation,
                                                     int iterations = 1000);

    static QString formatBenchmarkResult(const BenchmarkResult& result);
    static QJsonObject benchmarkResultToJson(const BenchmarkResult& result);
};

} // namespace DeclarativeUI::Profiling::Examples
