#!/bin/bash
# Validate example code for best practices and consistency

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

errors=0
warnings=0

echo "🔍 Validating example code..."

for file in "$@"; do
    if [[ ! -f "$file" ]]; then
        continue
    fi
    
    echo "Checking: $file"
    
    # Check for main function in .cpp files
    if [[ "$file" == *.cpp ]]; then
        if ! grep -n "int main(" "$file" >/dev/null 2>&1; then
            echo -e "${RED}❌ Error: Missing main() function in example $file${NC}"
            ((errors++))
        fi
    fi
    
    # Check for proper includes
    if ! grep -n "#include.*DeclarativeUI\|#include.*Components\|#include.*QApplication" "$file" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Warning: Missing DeclarativeUI includes in $file${NC}"
        ((warnings++))
    fi
    
    # Check for QApplication usage
    if grep -n "int main(" "$file" >/dev/null 2>&1; then
        if ! grep -n "QApplication" "$file" >/dev/null 2>&1; then
            echo -e "${RED}❌ Error: Missing QApplication in example $file${NC}"
            ((errors++))
        fi
        
        if ! grep -n "app.exec()" "$file" >/dev/null 2>&1; then
            echo -e "${RED}❌ Error: Missing app.exec() in example $file${NC}"
            ((errors++))
        fi
    fi
    
    # Check for proper error handling
    if grep -n "try\|catch" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Error handling found in $file${NC}"
    else
        echo -e "${YELLOW}⚠️  Warning: Consider adding error handling to $file${NC}"
        ((warnings++))
    fi
    
    # Check for comments and documentation
    if grep -n "//\|/\*" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Comments found in $file${NC}"
    else
        echo -e "${YELLOW}⚠️  Warning: Consider adding comments to explain the example in $file${NC}"
        ((warnings++))
    fi
    
    # Check for modern C++ features
    if grep -n "auto\|std::make_unique\|std::make_shared" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Using modern C++ features in $file${NC}"
    fi
    
    # Check for DeclarativeUI patterns
    if grep -n "DeclarativeBuilder\|Components::" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Using DeclarativeUI patterns in $file${NC}"
    fi
    
    # Check for proper widget showing
    if grep -n "show()\|exec()" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Widget display found in $file${NC}"
    else
        echo -e "${YELLOW}⚠️  Warning: Widget might not be displayed in $file${NC}"
        ((warnings++))
    fi
    
    # Check for resource cleanup
    if grep -n "QObject\|QWidget" "$file" >/dev/null 2>&1; then
        if grep -n "std::unique_ptr\|parent" "$file" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Good: Proper resource management in $file${NC}"
        else
            echo -e "${YELLOW}⚠️  Warning: Consider proper resource management in $file${NC}"
            ((warnings++))
        fi
    fi
    
    # Check for example-specific patterns
    filename=$(basename "$file")
    case "$filename" in
        *hello*|*basic*)
            # Basic examples should be simple
            if [ $(wc -l < "$file") -gt 50 ]; then
                echo -e "${YELLOW}⚠️  Warning: Basic example $file might be too complex (>50 lines)${NC}"
                ((warnings++))
            fi
            ;;
        *advanced*|*comprehensive*)
            # Advanced examples should demonstrate multiple features
            if ! grep -n "StateManager\|HotReload\|JSON" "$file" >/dev/null 2>&1; then
                echo -e "${YELLOW}⚠️  Warning: Advanced example $file should demonstrate more features${NC}"
                ((warnings++))
            fi
            ;;
        *command*)
            # Command examples should use command system
            if ! grep -n "Command\|CommandBuilder" "$file" >/dev/null 2>&1; then
                echo -e "${RED}❌ Error: Command example $file should use command system${NC}"
                ((errors++))
            fi
            ;;
    esac
    
done

echo ""
echo "📊 Summary:"
echo "   Errors: $errors"
echo "   Warnings: $warnings"

if [ $errors -gt 0 ]; then
    echo -e "${RED}❌ Example validation failed with $errors errors${NC}"
    exit 1
elif [ $warnings -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Example validation completed with $warnings warnings${NC}"
    exit 0
else
    echo -e "${GREEN}✅ Example validation passed${NC}"
    exit 0
fi
