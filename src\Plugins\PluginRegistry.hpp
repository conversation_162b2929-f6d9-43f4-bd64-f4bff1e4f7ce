#pragma once

#include "PluginInterface.hpp"
#include <QObject>
#include <QString>
#include <QHash>
#include <QStringList>
#include <QJsonObject>
#include <memory>
#include <functional>

namespace DeclarativeUI::Plugins {

/**
 * @brief Registry entry for a registered plugin
 */
struct PluginRegistryEntry {
    std::unique_ptr<IPlugin> plugin;
    PluginMetadata metadata;
    PluginCapabilities capabilities;
    QString filePath;
    QJsonObject configuration;
    bool isActive = false;
    
    // Component/Command mappings
    QHash<QString, QString> componentMappings;  // componentType -> pluginName
    QHash<QString, QString> commandMappings;    // commandType -> pluginName
    QHash<QString, QString> themeMappings;      // themeName -> pluginName
};

/**
 * @brief Central registry for managing loaded plugins and their capabilities
 */
class PluginRegistry : public QObject {
    Q_OBJECT
    
public:
    static PluginRegistry& instance();
    
    // Plugin registration
    bool registerPlugin(std::unique_ptr<IPlugin> plugin, const QString& filePath = QString());
    bool unregisterPlugin(const QString& pluginName);
    void unregisterAllPlugins();
    
    // Plugin queries
    bool isPluginRegistered(const QString& pluginName) const;
    IPlugin* getPlugin(const QString& pluginName) const;
    QStringList getRegisteredPlugins() const;
    QStringList getActivePlugins() const;
    QStringList getPluginsByCapability(PluginCapability capability) const;
    
    // Plugin metadata
    PluginMetadata getPluginMetadata(const QString& pluginName) const;
    PluginCapabilities getPluginCapabilities(const QString& pluginName) const;
    PluginState getPluginState(const QString& pluginName) const;
    QString getPluginFilePath(const QString& pluginName) const;
    
    // Plugin activation
    bool activatePlugin(const QString& pluginName);
    bool deactivatePlugin(const QString& pluginName);
    bool isPluginActive(const QString& pluginName) const;
    
    // Component registration (from IComponentPlugin)
    QStringList getAvailableComponents() const;
    QString getComponentProvider(const QString& componentType) const;
    std::unique_ptr<QObject> createComponent(const QString& componentType) const;
    QJsonObject getComponentSchema(const QString& componentType) const;
    QStringList getComponentsByCategory(const QString& category) const;
    QStringList getComponentsByTag(const QString& tag) const;
    
    // Command registration (from ICommandPlugin)
    QStringList getAvailableCommands() const;
    QString getCommandProvider(const QString& commandType) const;
    std::unique_ptr<QObject> createCommand(const QString& commandType) const;
    QJsonObject getCommandSchema(const QString& commandType) const;
    QStringList getCommandsByCategory(const QString& category) const;
    QStringList getCommandsByTag(const QString& tag) const;
    
    // Theme registration (from IThemePlugin)
    QStringList getAvailableThemes() const;
    QString getThemeProvider(const QString& themeName) const;
    QJsonObject getThemeDefinition(const QString& themeName) const;
    QString getThemeStyleSheet(const QString& themeName) const;
    QStringList getThemesByTag(const QString& tag) const;
    QStringList getDarkThemes() const;
    QStringList getLightThemes() const;
    
    // Configuration management
    bool configurePlugin(const QString& pluginName, const QJsonObject& config);
    QJsonObject getPluginConfiguration(const QString& pluginName) const;
    QJsonObject getDefaultPluginConfiguration(const QString& pluginName) const;
    
    // Dependency management
    QStringList getPluginDependencies(const QString& pluginName) const;
    QStringList getPluginDependents(const QString& pluginName) const;
    bool checkDependencies(const QString& pluginName) const;
    QStringList resolveDependencyOrder(const QStringList& pluginNames) const;
    
    // Plugin discovery and validation
    bool validatePluginCompatibility(const QString& pluginName) const;
    QStringList getIncompatiblePlugins() const;
    QStringList getMissingDependencies(const QString& pluginName) const;
    
    // Error handling
    QString getLastError() const { return last_error_; }
    QStringList getPluginErrors() const;
    void clearErrors();
    
signals:
    void pluginRegistered(const QString& pluginName, IPlugin* plugin);
    void pluginUnregistered(const QString& pluginName);
    void pluginActivated(const QString& pluginName);
    void pluginDeactivated(const QString& pluginName);
    void pluginConfigurationChanged(const QString& pluginName, const QJsonObject& config);
    void pluginError(const QString& pluginName, const QString& error);
    void componentRegistered(const QString& componentType, const QString& pluginName);
    void commandRegistered(const QString& commandType, const QString& pluginName);
    void themeRegistered(const QString& themeName, const QString& pluginName);
    
private slots:
    void onPluginStateChanged(PluginState state);
    void onPluginError(const QString& error);
    void onPluginConfigurationChanged(const QJsonObject& config);
    
private:
    explicit PluginRegistry(QObject* parent = nullptr);
    ~PluginRegistry();
    
    // Internal registration helpers
    void registerPluginComponents(const QString& pluginName, IComponentPlugin* componentPlugin);
    void registerPluginCommands(const QString& pluginName, ICommandPlugin* commandPlugin);
    void registerPluginThemes(const QString& pluginName, IThemePlugin* themePlugin);
    
    void unregisterPluginComponents(const QString& pluginName);
    void unregisterPluginCommands(const QString& pluginName);
    void unregisterPluginThemes(const QString& pluginName);
    
    // Dependency resolution
    bool checkCircularDependencies(const QString& pluginName, QStringList& visited) const;
    void buildDependencyGraph();
    
    // Validation helpers
    bool validatePluginInterface(IPlugin* plugin) const;
    bool checkPluginConflicts(const QString& pluginName) const;
    
    void setError(const QString& error);
    
private:
    QMap<QString, PluginRegistryEntry*> plugins_;
    
    // Quick lookup maps
    QHash<QString, QString> component_providers_;  // componentType -> pluginName
    QHash<QString, QString> command_providers_;    // commandType -> pluginName
    QHash<QString, QString> theme_providers_;      // themeName -> pluginName
    
    // Dependency graph
    QHash<QString, QStringList> dependency_graph_;
    QHash<QString, QStringList> dependent_graph_;
    
    mutable QString last_error_;
    QStringList plugin_errors_;
    
    static PluginRegistry* instance_;
};

} // namespace DeclarativeUI::Plugins
