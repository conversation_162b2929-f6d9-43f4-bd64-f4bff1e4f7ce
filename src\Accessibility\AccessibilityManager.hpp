#pragma once

#include <QObject>
#include <QWidget>
#include <QAccessible>
#include <QAccessibleWidget>
#include <QKeySequence>
#include <QShortcut>
#include <QFocusFrame>
#include <QApplication>
#include <QTimer>
#include <functional>
#include <unordered_map>
#include <memory>

// Forward declarations for new accessibility components
namespace DeclarativeUI::Accessibility {
    class KeyboardNavigator;
    class ScreenReaderInterface;
    class HighContrastTheme;
    class AccessibilityValidator;
}

namespace DeclarativeUI::Accessibility {

/**
 * @brief Comprehensive accessibility manager for the DeclarativeUI framework
 * 
 * Provides accessibility features including:
 * - Screen reader support
 * - Keyboard navigation
 * - High contrast mode
 * - Focus management
 * - ARIA-like attributes
 * - Voice announcements
 */
class AccessibilityManager : public QObject {
    Q_OBJECT

public:
    enum class NavigationMode {
        Tab,        // Standard tab navigation
        Arrow,      // Arrow key navigation
        Spatial,    // Spatial navigation (up/down/left/right)
        Custom      // Custom navigation logic
    };

    enum class AnnouncementPriority {
        Low,        // Background information
        Medium,     // Standard announcements
        High,       // Important information
        Critical    // Urgent announcements
    };

    struct AccessibilityAttributes {
        QString role;           // ARIA role equivalent
        QString label;          // Accessible name
        QString description;    // Accessible description
        QString value;          // Current value
        QString help;           // Help text
        bool focusable = true;  // Can receive focus
        bool live = false;      // Live region
        QString live_politeness = "polite"; // "polite" or "assertive"
        QStringList states;     // Current states (checked, expanded, etc.)
        QStringList properties; // Properties (readonly, required, etc.)
    };

    explicit AccessibilityManager(QObject* parent = nullptr);
    ~AccessibilityManager() override = default;

    // **Global accessibility settings**
    static AccessibilityManager& instance();
    void enableAccessibility(bool enable = true);
    void enableScreenReader(bool enable = true);
    void enableHighContrast(bool enable = true);
    void enableKeyboardNavigation(bool enable = true);
    void enableFocusIndicator(bool enable = true);
    void enableVoiceAnnouncements(bool enable = true);

    // **Widget accessibility enhancement**
    void enhanceWidget(QWidget* widget, const AccessibilityAttributes& attributes);
    void setAccessibleName(QWidget* widget, const QString& name);
    void setAccessibleDescription(QWidget* widget, const QString& description);
    void setAccessibleRole(QWidget* widget, const QString& role);
    void setAccessibleValue(QWidget* widget, const QString& value);
    void setAccessibleHelp(QWidget* widget, const QString& help);

    // **State management**
    void setState(QWidget* widget, const QString& state, bool enabled = true);
    void setProperty(QWidget* widget, const QString& property, const QVariant& value);
    void updateLiveRegion(QWidget* widget, const QString& text);

    // **Focus management**
    void setFocusOrder(const QList<QWidget*>& widgets);
    void setNavigationMode(QWidget* container, NavigationMode mode);
    void setCustomNavigation(QWidget* widget, std::function<QWidget*(QWidget*, int)> navigator);
    QWidget* getNextFocusWidget(QWidget* current, bool forward = true);
    QWidget* getPreviousFocusWidget(QWidget* current);

    // **Keyboard shortcuts and navigation**
    void addGlobalShortcut(const QKeySequence& key, std::function<void()> action);
    void addWidgetShortcut(QWidget* widget, const QKeySequence& key, std::function<void()> action);
    void enableSkipLinks(QWidget* container);
    void addSkipLink(const QString& text, QWidget* target);

    // **Voice announcements**
    void announce(const QString& text, AnnouncementPriority priority = AnnouncementPriority::Medium);
    void announceWidgetState(QWidget* widget);
    void announceNavigation(QWidget* from, QWidget* to);

    // **High contrast and theming**
    void applyHighContrastTheme();
    void applyAccessibleColors(QWidget* widget);
    void increaseFontSize(int increment = 2);
    void resetFontSize();

    // **Screen reader integration**
    void notifyScreenReader(const QString& event, QWidget* widget = nullptr);
    void updateAccessibleTree();

    // **Validation and testing**
    QStringList validateAccessibility(QWidget* widget);
    void generateAccessibilityReport(QWidget* root, const QString& filename);
    bool isAccessibilityEnabled() const;

    // **Integration with new accessibility components**
    KeyboardNavigator* getKeyboardNavigator() const;
    ScreenReaderInterface* getScreenReaderInterface() const;
    HighContrastTheme* getHighContrastTheme() const;
    AccessibilityValidator* getAccessibilityValidator() const;

    // **Enhanced accessibility features**
    void enableAdvancedKeyboardNavigation(bool enabled = true);
    void enableAdvancedScreenReader(bool enabled = true);
    void enableHighContrastMode(bool enabled = true);
    void enableAccessibilityValidation(bool enabled = true);

    // **Theme and appearance**
    void setAccessibilityTheme(const QString& themeName);
    void applyHighContrastColors();
    void adjustFontSizeForAccessibility(int scaleFactor = 110);

    // **Comprehensive validation**
    void runFullAccessibilityAudit(QWidget* root = nullptr);
    void enableRealTimeValidation(bool enabled = true);

signals:
    void accessibilityStateChanged(bool enabled);
    void focusChanged(QWidget* from, QWidget* to);
    void announcementRequested(const QString& text, AnnouncementPriority priority);
    void navigationModeChanged(NavigationMode mode);

private slots:
    void onFocusChanged(QWidget* old, QWidget* now);
    void onApplicationStateChanged(Qt::ApplicationState state);
    void onAnnouncementTimer();

private:
    // **Core state**
    bool accessibility_enabled_;
    bool screen_reader_enabled_;
    bool high_contrast_enabled_;
    bool keyboard_navigation_enabled_;
    bool focus_indicator_enabled_;
    bool voice_announcements_enabled_;

    // **Widget tracking**
    std::unordered_map<QWidget*, AccessibilityAttributes> widget_attributes_;
    std::unordered_map<QWidget*, NavigationMode> navigation_modes_;
    std::unordered_map<QWidget*, std::function<QWidget*(QWidget*, int)>> custom_navigators_;
    QList<QWidget*> focus_order_;

    // **Shortcuts and navigation**
    QList<QShortcut*> global_shortcuts_;
    std::unordered_map<QWidget*, QList<QShortcut*>> widget_shortcuts_;
    QList<QPair<QString, QWidget*>> skip_links_;

    // **Voice announcements**
    QTimer* announcement_timer_;
    QStringList announcement_queue_;
    QString current_announcement_;

    // **Focus management**
    QFocusFrame* focus_frame_;
    QWidget* last_focused_widget_;

    // **Theming**
    int original_font_size_;
    QString original_stylesheet_;

    // **New accessibility components**
    std::unique_ptr<KeyboardNavigator> keyboard_navigator_;
    std::unique_ptr<ScreenReaderInterface> screen_reader_;
    std::unique_ptr<HighContrastTheme> high_contrast_theme_;
    std::unique_ptr<AccessibilityValidator> validator_;

    // **Helper methods**
    void setupGlobalShortcuts();
    void setupFocusTracking();
    void setupAnnouncementSystem();
    void applyAccessibilityAttributes(QWidget* widget, const AccessibilityAttributes& attributes);
    void updateFocusIndicator(QWidget* widget);
    void processAnnouncementQueue();
    QString getWidgetDescription(QWidget* widget);
    QWidget* findNextFocusableWidget(QWidget* start, bool forward);
    void validateWidgetAccessibility(QWidget* widget, QStringList& issues);
    void applyKeyboardNavigation(QWidget* widget);
    void enhanceForScreenReader(QWidget* widget);
};

/**
 * @brief Custom accessible interface for enhanced widgets
 */
class EnhancedAccessibleWidget : public QAccessibleWidget {
public:
    explicit EnhancedAccessibleWidget(QWidget* widget, const AccessibilityManager::AccessibilityAttributes& attributes);

    // QAccessibleInterface implementation
    QString text(QAccessible::Text t) const override;
    void setText(QAccessible::Text t, const QString& text) override;
    QAccessible::State state() const override;
    QAccessible::Role role() const override;
    QStringList actionNames() const override;
    void doAction(const QString& actionName) override;

private:
    AccessibilityManager::AccessibilityAttributes attributes_;
};

/**
 * @brief Accessibility helper macros and utilities
 */
#define ACCESSIBLE_WIDGET(widget, role, name) \
    DeclarativeUI::Accessibility::AccessibilityManager::instance().enhanceWidget(widget, { \
        .role = role, \
        .label = name \
    })

#define ACCESSIBLE_BUTTON(button, name) \
    ACCESSIBLE_WIDGET(button, "button", name)

#define ACCESSIBLE_EDIT(edit, name) \
    ACCESSIBLE_WIDGET(edit, "textbox", name)

#define ACCESSIBLE_LABEL(label, name) \
    ACCESSIBLE_WIDGET(label, "text", name)

#define ACCESSIBLE_LIST(list, name) \
    ACCESSIBLE_WIDGET(list, "list", name)

#define ACCESSIBLE_GRID(grid, name) \
    ACCESSIBLE_WIDGET(grid, "grid", name)

#define ANNOUNCE(text) \
    DeclarativeUI::Accessibility::AccessibilityManager::instance().announce(text)

#define ANNOUNCE_CRITICAL(text) \
    DeclarativeUI::Accessibility::AccessibilityManager::instance().announce(text, \
        DeclarativeUI::Accessibility::AccessibilityManager::AnnouncementPriority::Critical)

} // namespace DeclarativeUI::Accessibility
