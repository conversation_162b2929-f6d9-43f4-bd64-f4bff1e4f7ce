#pragma once

#include <QObject>
#include <QWidget>
#include <QApplication>
#include <QJsonObject>
#include <QJsonDocument>
#include <QColor>
#include <QFont>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <functional>
#include <unordered_map>

namespace DeclarativeUI::Theming {

/**
 * @brief Advanced theming system with dark/light mode and custom themes
 * 
 * Provides comprehensive theming capabilities including:
 * - Built-in light/dark themes
 * - Custom theme creation and loading
 * - CSS-like styling system
 * - Dynamic theme switching with animations
 * - Theme inheritance and variants
 * - Responsive theming based on system preferences
 */
class ThemeManager : public QObject {
    Q_OBJECT

public:
    enum class ThemeMode {
        Light,
        Dark,
        Auto,       // Follow system preference
        Custom
    };

    enum class ColorScheme {
        Default,
        Blue,
        Green,
        Purple,
        Orange,
        Red,
        Custom
    };

    struct ColorPalette {
        // Primary colors
        QColor primary;
        QColor primary_light;
        QColor primary_dark;
        
        // Secondary colors
        QColor secondary;
        QColor secondary_light;
        QColor secondary_dark;
        
        // Background colors
        QColor background;
        QColor surface;
        QColor card;
        
        // Text colors
        QColor text_primary;
        QColor text_secondary;
        QColor text_disabled;
        
        // State colors
        QColor success;
        QColor warning;
        QColor error;
        QColor info;
        
        // Border and divider colors
        QColor border;
        QColor divider;
        
        // Interactive colors
        QColor hover;
        QColor focus;
        QColor active;
        QColor disabled;
    };

    struct Typography {
        QFont heading1;
        QFont heading2;
        QFont heading3;
        QFont heading4;
        QFont heading5;
        QFont heading6;
        QFont body1;
        QFont body2;
        QFont caption;
        QFont button;
        QFont overline;
    };

    struct Spacing {
        int xs = 4;
        int sm = 8;
        int md = 16;
        int lg = 24;
        int xl = 32;
        int xxl = 48;
    };

    struct BorderRadius {
        int none = 0;
        int sm = 2;
        int md = 4;
        int lg = 8;
        int xl = 12;
        int full = 9999;
    };

    struct Shadows {
        QString none = "none";
        QString sm = "0 1px 2px rgba(0, 0, 0, 0.05)";
        QString md = "0 4px 6px rgba(0, 0, 0, 0.1)";
        QString lg = "0 10px 15px rgba(0, 0, 0, 0.1)";
        QString xl = "0 20px 25px rgba(0, 0, 0, 0.1)";
    };

    struct Theme {
        QString name;
        QString version = "1.0";
        QString author;
        QString description;
        ThemeMode mode;
        ColorScheme scheme;
        ColorPalette colors;
        Typography typography;
        Spacing spacing;
        BorderRadius border_radius;
        Shadows shadows;
        QJsonObject custom_properties;
        QString css_overrides;
    };

    explicit ThemeManager(QObject* parent = nullptr);
    ~ThemeManager() override = default;

    // **Global theme management**
    static ThemeManager& instance();
    void setTheme(const Theme& theme);
    void setThemeMode(ThemeMode mode);
    void setColorScheme(ColorScheme scheme);
    void switchTheme(const QString& theme_name, bool animated = true);

    // **Built-in themes**
    void applyLightTheme();
    void applyDarkTheme();
    void applyAutoTheme(); // Follow system preference
    void toggleTheme(bool animated = true);

    // **Custom theme management**
    void registerTheme(const Theme& theme);
    void unregisterTheme(const QString& name);
    bool loadThemeFromFile(const QString& filename);
    bool saveThemeToFile(const Theme& theme, const QString& filename);
    void createThemeVariant(const QString& base_theme, const QString& variant_name, 
                           const QJsonObject& overrides);

    // **Widget-specific theming**
    void applyThemeToWidget(QWidget* widget, const QString& component_type = "");
    void setWidgetThemeClass(QWidget* widget, const QString& theme_class);
    void addWidgetThemeProperty(QWidget* widget, const QString& property, const QVariant& value);
    void removeWidgetThemeProperty(QWidget* widget, const QString& property);

    // **CSS-like styling**
    void addGlobalStyle(const QString& selector, const QString& style);
    void removeGlobalStyle(const QString& selector);
    void addComponentStyle(const QString& component, const QString& style);
    QString generateStyleSheet() const;
    QString generateComponentStyleSheet(const QString& component) const;

    // **Dynamic theming**
    void enableDynamicTheming(bool enable = true);
    void setThemeTransitionDuration(int milliseconds);
    void addThemeChangeListener(std::function<void(const Theme&)> listener);
    void removeThemeChangeListener(std::function<void(const Theme&)> listener);

    // **System integration**
    void followSystemTheme(bool enable = true);
    void detectSystemTheme();
    bool isSystemDarkMode() const;

    // **Theme utilities**
    QColor getColor(const QString& color_name) const;
    QFont getFont(const QString& font_name) const;
    int getSpacing(const QString& size) const;
    int getBorderRadius(const QString& size) const;
    QString getShadow(const QString& size) const;
    QVariant getCustomProperty(const QString& property) const;

    // **Getters**
    Theme getCurrentTheme() const;
    ThemeMode getCurrentMode() const;
    ColorScheme getCurrentScheme() const;
    QStringList getAvailableThemes() const;
    bool isDarkMode() const;
    bool isLightMode() const;

    // **Theme validation and debugging**
    QStringList validateTheme(const Theme& theme) const;
    void debugTheme(const Theme& theme) const;
    QJsonObject themeToJson(const Theme& theme) const;
    Theme themeFromJson(const QJsonObject& json) const;

    // **Helper methods for theme building (public for ThemeBuilder access)**
    ColorPalette createLightPalette(ColorScheme scheme) const;
    ColorPalette createDarkPalette(ColorScheme scheme) const;
    Typography createDefaultTypography() const;

signals:
    void themeChanged(const Theme& theme);
    void themeModeChanged(ThemeMode mode);
    void colorSchemeChanged(ColorScheme scheme);
    void systemThemeChanged(bool is_dark);

private slots:
    void onSystemThemeChanged();
    void onThemeTransitionFinished();
    void onApplicationPaletteChanged();

private:
    // **Core state**
    Theme current_theme_;
    ThemeMode current_mode_;
    ColorScheme current_scheme_;
    bool dynamic_theming_enabled_;
    bool follow_system_theme_;
    int transition_duration_;

    // **Theme storage**
    std::unordered_map<QString, Theme> registered_themes_;
    std::unordered_map<QString, QString> global_styles_;
    std::unordered_map<QString, QString> component_styles_;
    std::unordered_map<QWidget*, QString> widget_theme_classes_;
    std::unordered_map<QWidget*, QJsonObject> widget_theme_properties_;

    // **Listeners and callbacks**
    QList<std::function<void(const Theme&)>> theme_listeners_;

    // **Animation and transitions**
    QTimer* transition_timer_;
    QPropertyAnimation* theme_animation_;
    QGraphicsOpacityEffect* transition_effect_;

    // **System integration**
    QTimer* system_theme_monitor_;

    // **Helper methods**
    void setupBuiltinThemes();
    void setupSystemThemeMonitoring();
    void applyThemeInternal(const Theme& theme, bool animated);
    void animateThemeTransition(const Theme& from_theme, const Theme& to_theme);
    void updateApplicationPalette(const ColorPalette& colors);
    void updateApplicationFont(const Typography& typography);
    void applyWidgetStyles();
    QString generateWidgetStyleSheet(QWidget* widget) const;
    QColor adjustColorForMode(const QColor& color, ThemeMode mode) const;
    void notifyThemeListeners(const Theme& theme);
    void validateAndFixTheme(Theme& theme);
};

/**
 * @brief Theme builder for creating custom themes fluently
 */
class ThemeBuilder {
public:
    ThemeBuilder(const QString& name);

    ThemeBuilder& mode(ThemeManager::ThemeMode mode);
    ThemeBuilder& scheme(ThemeManager::ColorScheme scheme);
    ThemeBuilder& author(const QString& author);
    ThemeBuilder& description(const QString& description);
    ThemeBuilder& version(const QString& version);

    // Color customization
    ThemeBuilder& primaryColor(const QColor& color);
    ThemeBuilder& secondaryColor(const QColor& color);
    ThemeBuilder& backgroundColor(const QColor& color);
    ThemeBuilder& textColor(const QColor& color);
    ThemeBuilder& accentColor(const QColor& color);

    // Typography customization
    ThemeBuilder& headingFont(const QFont& font);
    ThemeBuilder& bodyFont(const QFont& font);
    ThemeBuilder& buttonFont(const QFont& font);

    // Spacing and layout
    ThemeBuilder& baseSpacing(int spacing);
    ThemeBuilder& borderRadius(int radius);

    // Custom properties
    ThemeBuilder& customProperty(const QString& key, const QVariant& value);
    ThemeBuilder& cssOverride(const QString& css);

    // Build the theme
    ThemeManager::Theme build() const;

private:
    ThemeManager::Theme theme_;
};

/**
 * @brief Utility macros for theming
 */
#define THEME_COLOR(name) \
    DeclarativeUI::Theming::ThemeManager::instance().getColor(name)

#define THEME_FONT(name) \
    DeclarativeUI::Theming::ThemeManager::instance().getFont(name)

#define THEME_SPACING(size) \
    DeclarativeUI::Theming::ThemeManager::instance().getSpacing(size)

#define APPLY_THEME(widget) \
    DeclarativeUI::Theming::ThemeManager::instance().applyThemeToWidget(widget)

#define SET_THEME_CLASS(widget, class_name) \
    DeclarativeUI::Theming::ThemeManager::instance().setWidgetThemeClass(widget, class_name)

} // namespace DeclarativeUI::Theming
