# Clang-Tidy configuration for DeclarativeUI Framework

# Enable all checks by default, then disable specific ones
Checks: >
  *,
  -abseil-*,
  -altera-*,
  -android-*,
  -fuchsia-*,
  -google-*,
  -llvm-*,
  -llvmlibc-*,
  -zircon-*,
  -misc-non-private-member-variables-in-classes,
  -misc-const-correctness,
  -readability-magic-numbers,
  -readability-identifier-length,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-pro-type-reinterpret-cast,
  -modernize-use-trailing-return-type,
  -modernize-avoid-c-arrays,
  -hicpp-avoid-c-arrays,
  -hicpp-no-array-decay,
  -cert-err58-cpp,
  -bugprone-easily-swappable-parameters

# Warning options
WarningsAsErrors: ''
HeaderFilterRegex: '(src|tests)/.*\.(h|hpp)$'
AnalyzeTemporaryDtors: false
FormatStyle: file
User: DeclarativeUI

# Check options
CheckOptions:
  # Naming conventions
  - key: readability-identifier-naming.NamespaceCase
    value: CamelCase
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.StructCase
    value: CamelCase
  - key: readability-identifier-naming.TemplateParameterCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  - key: readability-identifier-naming.VariableCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberSuffix
    value: '_'
  - key: readability-identifier-naming.PrivateMemberSuffix
    value: '_'
  - key: readability-identifier-naming.ProtectedMemberSuffix
    value: '_'
  - key: readability-identifier-naming.EnumConstantCase
    value: CamelCase
  - key: readability-identifier-naming.ConstantCase
    value: CamelCase
  - key: readability-identifier-naming.StaticConstantCase
    value: CamelCase
  - key: readability-identifier-naming.GlobalConstantCase
    value: CamelCase
  - key: readability-identifier-naming.MacroDefinitionCase
    value: UPPER_CASE

  # Function length
  - key: readability-function-size.LineThreshold
    value: '80'
  - key: readability-function-size.StatementThreshold
    value: '50'
  - key: readability-function-size.BranchThreshold
    value: '10'
  - key: readability-function-size.ParameterThreshold
    value: '6'

  # Complexity
  - key: readability-function-cognitive-complexity.Threshold
    value: '15'

  # Performance
  - key: performance-for-range-copy.WarnOnAllAutoCopies
    value: 'true'
  - key: performance-unnecessary-copy-initialization.AllowedTypes
    value: 'QVariant;QString;QByteArray'

  # Modernize
  - key: modernize-loop-convert.MaxCopySize
    value: '16'
  - key: modernize-loop-convert.MinConfidence
    value: 'reasonable'
  - key: modernize-pass-by-value.IncludeStyle
    value: 'llvm'
  - key: modernize-replace-auto-ptr.IncludeStyle
    value: 'llvm'
  - key: modernize-use-nullptr.NullMacros
    value: 'NULL'

  # Bugprone
  - key: bugprone-argument-comment.StrictMode
    value: 'false'
  - key: bugprone-assert-side-effect.AssertMacros
    value: 'assert,Q_ASSERT'
  - key: bugprone-string-constructor.WarnOnLargeLength
    value: 'true'
  - key: bugprone-string-constructor.LargeLengthThreshold
    value: '0x800000'

  # Cert
  - key: cert-dcl16-c.NewSuffixes
    value: 'L;LL;LU;LLU'
  - key: cert-oop54-cpp.WarnOnlyIfThisHasSuspiciousField
    value: 'false'

  # Misc
  - key: misc-definitions-in-headers.UseHeaderFileExtension
    value: 'true'
  - key: misc-definitions-in-headers.HeaderFileExtensions
    value: ';h;hh;hpp;hxx'
  - key: misc-throw-by-value-catch-by-reference.CheckThrowTemporaries
    value: 'true'

  # Portability
  - key: portability-simd-intrinsics.Suggest
    value: 'false'

  # Readability
  - key: readability-braces-around-statements.ShortStatementLines
    value: '1'
  - key: readability-implicit-bool-conversion.AllowPointerConditions
    value: 'true'
  - key: readability-implicit-bool-conversion.AllowIntegerConditions
    value: 'false'
  - key: readability-inconsistent-declaration-parameter-name.IgnoreMacros
    value: 'true'
  - key: readability-redundant-declaration.IgnoreMacros
    value: 'true'
  - key: readability-simplify-boolean-expr.ChainedConditionalReturn
    value: 'false'
  - key: readability-simplify-boolean-expr.ChainedConditionalAssignment
    value: 'false'
  - key: readability-static-accessed-through-instance.NameSpecifierNestingThreshold
    value: '3'
  - key: readability-uppercase-literal-suffix.NewSuffixes
    value: ''
  - key: readability-uppercase-literal-suffix.IgnoreMacros
    value: 'true'

  # Cppcoreguidelines
  - key: cppcoreguidelines-explicit-virtual-functions.IgnoreDestructors
    value: 'true'
  - key: cppcoreguidelines-non-private-member-variables-in-classes.IgnoreClassesWithAllMemberVariablesBeingPublic
    value: 'true'
  - key: cppcoreguidelines-special-member-functions.AllowSoleDefaultDtor
    value: 'true'
  - key: cppcoreguidelines-special-member-functions.AllowMissingMoveFunctions
    value: 'true'

  # Hicpp
  - key: hicpp-function-size.LineThreshold
    value: '80'
  - key: hicpp-function-size.StatementThreshold
    value: '50'
  - key: hicpp-function-size.BranchThreshold
    value: '10'
  - key: hicpp-function-size.ParameterThreshold
    value: '6'
  - key: hicpp-member-init.IgnoreBaseClasses
    value: 'false'
  - key: hicpp-move-const-arg.CheckTriviallyCopyableMove
    value: 'true'
  - key: hicpp-named-parameter.IgnoreFailedSplit
    value: 'false'
  - key: hicpp-use-auto.MinTypeNameLength
    value: '5'
