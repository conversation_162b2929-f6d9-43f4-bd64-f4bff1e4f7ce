#pragma once

#include "PluginInterface.hpp"
#include "PluginLoader.hpp"
#include "PluginRegistry.hpp"
#include "PluginValidator.hpp"
#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QTimer>
#include <memory>

namespace DeclarativeUI::Plugins {

/**
 * @brief Plugin management configuration
 */
struct PluginManagerConfig {
    QStringList searchPaths;
    bool autoDiscovery = true;
    bool autoActivation = false;
    bool enableHotReload = false;
    int discoveryInterval = 30000; // 30 seconds
    QJsonObject securityPolicy;
    QStringList trustedPlugins;
    QStringList disabledPlugins;
    
    QJsonObject toJson() const;
    static PluginManagerConfig fromJson(const QJsonObject& json);
};

/**
 * @brief Plugin operation result
 */
struct PluginOperationResult {
    bool success = false;
    QString error;
    QStringList affectedPlugins;
    
    explicit operator bool() const { return success; }
};

/**
 * @brief High-level plugin management system
 * 
 * The PluginManager provides a unified interface for plugin discovery,
 * loading, validation, and lifecycle management. It orchestrates the
 * PluginLoader, PluginRegistry, and PluginValidator components.
 */
class PluginManager : public QObject {
    Q_OBJECT
    
public:
    static PluginManager& instance();
    
    // Initialization and configuration
    bool initialize(const PluginManagerConfig& config = PluginManagerConfig{});
    void shutdown();
    bool isInitialized() const { return initialized_; }
    
    void setConfiguration(const PluginManagerConfig& config);
    PluginManagerConfig getConfiguration() const { return config_; }
    
    // Plugin discovery and loading
    PluginOperationResult discoverPlugins();
    PluginOperationResult discoverPlugins(const QString& directory);
    PluginOperationResult discoverPlugins(const QStringList& directories);
    PluginOperationResult loadPlugin(const QString& pluginPath);
    PluginOperationResult loadPlugins(const QStringList& pluginPaths);
    PluginOperationResult unloadPlugin(const QString& pluginName);
    PluginOperationResult unloadAllPlugins();
    
    // Plugin activation and lifecycle
    PluginOperationResult activatePlugin(const QString& pluginName);
    PluginOperationResult deactivatePlugin(const QString& pluginName);
    PluginOperationResult activateAllPlugins();
    PluginOperationResult deactivateAllPlugins();
    
    // Plugin queries and information
    QStringList getAvailablePlugins() const;
    QStringList getLoadedPlugins() const;
    QStringList getActivePlugins() const;
    QStringList getFailedPlugins() const;
    
    PluginMetadata getPluginMetadata(const QString& pluginName) const;
    PluginState getPluginState(const QString& pluginName) const;
    QString getPluginError(const QString& pluginName) const;
    
    // Plugin capabilities
    QStringList getPluginsByCapability(PluginCapability capability) const;
    QStringList getAvailableComponents() const;
    QStringList getAvailableCommands() const;
    QStringList getAvailableThemes() const;
    
    // Component creation (delegated to registry)
    std::unique_ptr<QObject> createComponent(const QString& componentType) const;
    std::unique_ptr<QObject> createCommand(const QString& commandType) const;
    QJsonObject getThemeDefinition(const QString& themeName) const;
    QString getThemeStyleSheet(const QString& themeName) const;
    
    // Plugin validation and security
    bool validatePlugin(const QString& pluginPath) const;
    bool isPluginTrusted(const QString& pluginPath) const;
    void addTrustedPlugin(const QString& pluginPath);
    void removeTrustedPlugin(const QString& pluginPath);
    
    // Plugin configuration
    bool configurePlugin(const QString& pluginName, const QJsonObject& config);
    QJsonObject getPluginConfiguration(const QString& pluginName) const;
    
    // Dependency management
    QStringList getPluginDependencies(const QString& pluginName) const;
    QStringList getMissingDependencies(const QString& pluginName) const;
    PluginOperationResult resolveDependencies(const QString& pluginName);
    PluginOperationResult resolveDependencies(const QStringList& pluginNames);
    
    // Hot reload support
    void enableHotReload(bool enabled = true);
    bool isHotReloadEnabled() const { return config_.enableHotReload; }
    void reloadPlugin(const QString& pluginName);
    void reloadAllPlugins();
    
    // Plugin search paths
    void addSearchPath(const QString& path);
    void removeSearchPath(const QString& path);
    QStringList getSearchPaths() const;
    
    // Statistics and diagnostics
    QJsonObject getStatistics() const;
    QJsonObject getDiagnostics() const;
    QStringList getErrors() const;
    void clearErrors();
    
    // Import/Export
    QJsonObject exportConfiguration() const;
    bool importConfiguration(const QJsonObject& config);
    QJsonObject exportPluginList() const;
    bool importPluginList(const QJsonObject& pluginList);
    
    // Error handling
    QString getLastError() const { return last_error_; }
    
signals:
    void pluginDiscovered(const QString& pluginPath);
    void pluginLoaded(const QString& pluginName);
    void pluginUnloaded(const QString& pluginName);
    void pluginUnregistered(const QString& pluginName);
    void pluginActivated(const QString& pluginName);
    void pluginDeactivated(const QString& pluginName);
    void pluginError(const QString& pluginName, const QString& error);
    void configurationChanged(const PluginManagerConfig& config);
    void hotReloadTriggered(const QString& pluginName);
    
private slots:
    void onDiscoveryTimer();
    void onPluginLoaded(const QString& pluginPath, IPlugin* plugin);
    void onPluginUnloaded(const QString& pluginPath);
    void onPluginLoadFailed(const QString& pluginPath, const QString& error);
    void onPluginRegistered(const QString& pluginName, IPlugin* plugin);
    void onPluginUnregistered(const QString& pluginName);
    void onPluginActivated(const QString& pluginName);
    void onPluginDeactivated(const QString& pluginName);
    void onPluginErrorOccurred(const QString& pluginName, const QString& error);
    
private:
    explicit PluginManager(QObject* parent = nullptr);
    ~PluginManager();
    
    // Internal operations
    PluginOperationResult loadPluginInternal(const PluginInfo& info);
    bool activatePluginWithDependencies(const QString& pluginName, QStringList& activated);
    bool deactivatePluginWithDependents(const QString& pluginName, QStringList& deactivated);
    
    // Discovery helpers
    void startDiscoveryTimer();
    void stopDiscoveryTimer();
    QList<PluginInfo> getNewPlugins(const QList<PluginInfo>& discovered) const;
    
    // Validation helpers
    bool validatePluginSecurity(const QString& pluginPath) const;
    bool checkPluginCompatibility(const PluginMetadata& metadata) const;
    
    // Error handling
    void setError(const QString& error);
    void addError(const QString& error);
    
private:
    bool initialized_ = false;
    PluginManagerConfig config_;
    
    std::unique_ptr<PluginLoader> loader_;
    PluginRegistry* registry_;
    std::unique_ptr<PluginValidator> validator_;
    
    QTimer* discovery_timer_;
    QStringList discovered_plugins_;
    QStringList failed_plugins_;
    QStringList errors_;
    
    mutable QString last_error_;
    
    static PluginManager* instance_;
};

} // namespace DeclarativeUI::Plugins
