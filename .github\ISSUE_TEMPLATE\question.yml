name: ❓ Question
description: Ask a question about using DeclarativeUI Framework
title: "[Question]: "
labels: ["question", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for your question! Please provide as much detail as possible to help us give you the best answer.

  - type: textarea
    id: question
    attributes:
      label: Your Question
      description: What would you like to know about DeclarativeUI Framework?
      placeholder: Ask your question here...
    validations:
      required: true

  - type: dropdown
    id: category
    attributes:
      label: Question Category
      description: What area does your question relate to?
      options:
        - Getting Started
        - Core Framework Usage
        - Components Library
        - Command System
        - State Management
        - Hot Reload
        - JSON Support
        - Build System
        - Integration with Qt
        - Performance
        - Best Practices
        - Troubleshooting
        - API Design
        - Examples
        - Other
    validations:
      required: true

  - type: dropdown
    id: experience-level
    attributes:
      label: Experience Level
      description: What's your experience level with Qt and C++?
      options:
        - Beginner - New to Qt and/or C++
        - Intermediate - Some Qt/C++ experience
        - Advanced - Experienced Qt/C++ developer
        - Expert - Qt/C++ specialist
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: Context and Background
      description: Provide context about what you're trying to achieve
      placeholder: |
        - What are you trying to build?
        - What specific problem are you trying to solve?
        - What have you tried so far?

  - type: textarea
    id: code-sample
    attributes:
      label: Relevant Code (if applicable)
      description: Share any relevant code you're working with
      render: cpp
      placeholder: |
        #include "DeclarativeUI.hpp"
        
        // Your code here
        int main() {
            // What you're trying to do
            return 0;
        }

  - type: textarea
    id: expected-outcome
    attributes:
      label: Expected Outcome
      description: What result are you hoping to achieve?
      placeholder: Describe what you want to accomplish...

  - type: textarea
    id: current-behavior
    attributes:
      label: Current Behavior (if applicable)
      description: If something isn't working as expected, describe what's happening
      placeholder: What's currently happening instead?

  - type: input
    id: version
    attributes:
      label: DeclarativeUI Version
      description: What version of DeclarativeUI are you using?
      placeholder: e.g., 1.0.0, main branch

  - type: dropdown
    id: qt-version
    attributes:
      label: Qt Version
      description: What version of Qt are you using?
      options:
        - Qt 6.8
        - Qt 6.7
        - Qt 6.6
        - Qt 6.5
        - Qt 6.4
        - Qt 6.3
        - Qt 6.2
        - Other (specify in additional context)

  - type: dropdown
    id: platform
    attributes:
      label: Platform
      description: What platform are you developing on?
      options:
        - Windows
        - Linux
        - macOS
        - Multiple platforms
        - Other

  - type: textarea
    id: research
    attributes:
      label: Research Done
      description: What documentation, examples, or resources have you already checked?
      placeholder: |
        - Documentation sections reviewed
        - Examples tried
        - Stack Overflow searches
        - Other resources consulted

  - type: textarea
    id: additional-info
    attributes:
      label: Additional Information
      description: Any other information that might be relevant to your question
      placeholder: |
        - Related issues or discussions
        - Specific constraints or requirements
        - Timeline considerations

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues and discussions for similar questions
          required: true
        - label: I have checked the documentation and examples
          required: true
        - label: I have provided sufficient context and details
          required: true
        - label: I understand this is a community-supported project
          required: true
