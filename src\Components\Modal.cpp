#include "Modal.hpp"
#include <QApplication>
#include <QScreen>
#include <QKeyEvent>
#include <QMouseEvent>
#include <QDebug>

namespace DeclarativeUI::Components {

Modal::Modal(QObject* parent)
    : UIElement(parent)
    , modal_dialog_(nullptr)
    , backdrop_widget_(nullptr)
    , modal_frame_(nullptr)
    , main_layout_(nullptr)
    , header_layout_(nullptr)
    , content_layout_(nullptr)
    , button_layout_(nullptr)
    , title_label_(nullptr)
    , close_button_(nullptr)
    , content_widget_(nullptr)
    , content_label_(nullptr)
    , show_animation_(nullptr)
    , hide_animation_(nullptr)
    , opacity_effect_(nullptr)
    , backdrop_blur_effect_(nullptr)
    , modal_size_(Size::Medium)
    , custom_width_(600)
    , custom_height_(400)
    , animation_type_(Animation::Fade)
    , show_backdrop_(true)
    , backdrop_blur_(true)
    , closable_(true)
    , escape_to_close_(true)
    , click_outside_to_close_(false)
    , show_close_button_(true)
    , event_loop_(nullptr)
    , result_code_(0)
{
    setupUI();
    setupAnimations();
    connectSignals();
}

void Modal::initialize() {
    // Modal is initialized in constructor, no additional setup needed
    // This method satisfies the UIElement interface requirement
}

Modal& Modal::title(const QString& text) {
    title_text_ = text;
    if (title_label_) {
        title_label_->setText(text);
    }
    return *this;
}

Modal& Modal::content(QWidget* widget) {
    setContentWidget(widget);
    return *this;
}

Modal& Modal::content(const QString& html) {
    setContentHtml(html);
    return *this;
}

Modal& Modal::size(Size modal_size) {
    modal_size_ = modal_size;
    applySize();
    return *this;
}

Modal& Modal::customSize(int width, int height) {
    modal_size_ = Size::Custom;
    custom_width_ = width;
    custom_height_ = height;
    applySize();
    return *this;
}

Modal& Modal::animation(Animation anim_type) {
    animation_type_ = anim_type;
    return *this;
}

Modal& Modal::backdrop(bool show_backdrop) {
    show_backdrop_ = show_backdrop;
    return *this;
}

Modal& Modal::backdropBlur(bool blur_backdrop) {
    backdrop_blur_ = blur_backdrop;
    return *this;
}

Modal& Modal::closable(bool can_close) {
    closable_ = can_close;
    if (close_button_) {
        close_button_->setVisible(can_close && show_close_button_);
    }
    return *this;
}

Modal& Modal::escapeToClose(bool escape_closes) {
    escape_to_close_ = escape_closes;
    return *this;
}

Modal& Modal::clickOutsideToClose(bool click_closes) {
    click_outside_to_close_ = click_closes;
    return *this;
}

Modal& Modal::showCloseButton(bool show_button) {
    show_close_button_ = show_button;
    if (close_button_) {
        close_button_->setVisible(show_button && closable_);
    }
    return *this;
}

Modal& Modal::buttons(const QStringList& button_texts) {
    button_texts_ = button_texts;
    createButtons();
    return *this;
}

Modal& Modal::onButtonClicked(std::function<void(int)> callback) {
    button_callback_ = callback;
    return *this;
}

Modal& Modal::onClosed(std::function<void()> callback) {
    closed_callback_ = callback;
    return *this;
}

// Static convenience methods
Modal* Modal::alert(const QString& title, const QString& message) {
    auto* modal = new Modal();
    return &modal->title(title).content(message).buttons({"OK"});
}

Modal* Modal::confirm(const QString& title, const QString& message, 
                     std::function<void(bool)> callback) {
    auto* modal = new Modal();
    modal->title(title).content(message).buttons({"OK", "Cancel"});
    
    if (callback) {
        modal->onButtonClicked([callback](int index) {
            callback(index == 0); // OK = true, Cancel = false
        });
    }
    
    return modal;
}

Modal* Modal::custom(const QString& title, QWidget* content) {
    auto* modal = new Modal();
    return &modal->title(title).content(content);
}

void Modal::show() {
    if (!modal_dialog_) return;
    
    centerOnScreen();
    modal_dialog_->show();
    modal_dialog_->raise();
    modal_dialog_->activateWindow();
    
    if (show_animation_) {
        show_animation_->start();
    }
    
    qDebug() << "📱 Modal shown:" << title_text_;
}

void Modal::hide() {
    if (!modal_dialog_) return;
    
    if (hide_animation_) {
        hide_animation_->start();
    } else {
        close();
    }
}

void Modal::close() {
    if (modal_dialog_) {
        modal_dialog_->hide();
    }
    
    if (event_loop_ && event_loop_->isRunning()) {
        event_loop_->quit();
    }
    
    if (closed_callback_) {
        closed_callback_();
    }
    
    emit closed();
    deleteLater();
    
    qDebug() << "📱 Modal closed:" << title_text_;
}

int Modal::exec() {
    if (!modal_dialog_) return -1;
    
    show();
    
    event_loop_ = new QEventLoop(this);
    event_loop_->exec();
    
    return result_code_;
}

void Modal::setContentWidget(QWidget* widget) {
    if (content_widget_ && content_layout_) {
        content_layout_->removeWidget(content_widget_);
    }
    
    content_widget_ = widget;
    
    if (widget && content_layout_) {
        content_layout_->addWidget(widget);
    }
}

void Modal::setContentHtml(const QString& html) {
    if (!content_label_) {
        content_label_ = new QLabel();
        content_label_->setWordWrap(true);
        content_label_->setTextFormat(Qt::RichText);
    }
    
    content_label_->setText(html);
    setContentWidget(content_label_);
}

QWidget* Modal::getContentWidget() const {
    return content_widget_;
}

// Getters
QString Modal::getTitle() const { return title_text_; }
Modal::Size Modal::getSize() const { return modal_size_; }
Modal::Animation Modal::getAnimation() const { return animation_type_; }
bool Modal::hasBackdrop() const { return show_backdrop_; }
bool Modal::hasBackdropBlur() const { return backdrop_blur_; }
bool Modal::isClosable() const { return closable_; }

// Private slots
void Modal::onCloseButtonClicked() {
    if (closable_) {
        result_code_ = -1;
        hide();
    }
}

void Modal::onBackdropClicked() {
    if (click_outside_to_close_ && closable_) {
        result_code_ = -1;
        hide();
    }
}

void Modal::onButtonPressed() {
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button) return;
    
    int index = action_buttons_.indexOf(button);
    if (index >= 0) {
        result_code_ = index;
        
        if (button_callback_) {
            button_callback_(index);
        }
        
        emit buttonClicked(index);
        
        if (index == 0) {
            emit accepted();
        } else {
            emit rejected();
        }
        
        hide();
    }
}

void Modal::onAnimationFinished() {
    if (sender() == hide_animation_) {
        close();
    }
}

void Modal::setupUI() {
    // Create main dialog
    modal_dialog_ = new QDialog();
    modal_dialog_->setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    modal_dialog_->setAttribute(Qt::WA_TranslucentBackground);
    modal_dialog_->setModal(true);
    
    // Create main frame
    modal_frame_ = new QFrame(modal_dialog_);
    modal_frame_->setFrameStyle(QFrame::Box);
    modal_frame_->setStyleSheet(getModalStyleSheet());
    
    // Create layouts
    main_layout_ = new QVBoxLayout(modal_frame_);
    main_layout_->setContentsMargins(20, 20, 20, 20);
    main_layout_->setSpacing(15);
    
    // Header layout
    header_layout_ = new QHBoxLayout();
    header_layout_->setContentsMargins(0, 0, 0, 0);
    
    title_label_ = new QLabel();
    title_label_->setStyleSheet("font-size: 18px; font-weight: bold;");
    header_layout_->addWidget(title_label_);
    
    header_layout_->addStretch();
    
    close_button_ = new QPushButton("×");
    close_button_->setFixedSize(30, 30);
    close_button_->setStyleSheet("QPushButton { border: none; font-size: 18px; font-weight: bold; }");
    header_layout_->addWidget(close_button_);
    
    main_layout_->addLayout(header_layout_);
    
    // Content layout
    content_layout_ = new QVBoxLayout();
    content_layout_->setContentsMargins(0, 0, 0, 0);
    main_layout_->addLayout(content_layout_);
    
    // Button layout
    button_layout_ = new QHBoxLayout();
    button_layout_->setContentsMargins(0, 0, 0, 0);
    button_layout_->addStretch();
    main_layout_->addLayout(button_layout_);
    
    // Apply initial size
    applySize();
    
    // Set widget reference for UIElement
    setWidget(modal_dialog_);
}

void Modal::setupAnimations() {
    // Create opacity effect
    opacity_effect_ = new QGraphicsOpacityEffect();
    modal_frame_->setGraphicsEffect(opacity_effect_);
    
    // Show animation
    show_animation_ = new QPropertyAnimation(opacity_effect_, "opacity", this);
    show_animation_->setDuration(300);
    show_animation_->setStartValue(0.0);
    show_animation_->setEndValue(1.0);
    
    // Hide animation
    hide_animation_ = new QPropertyAnimation(opacity_effect_, "opacity", this);
    hide_animation_->setDuration(300);
    hide_animation_->setStartValue(1.0);
    hide_animation_->setEndValue(0.0);
}

void Modal::connectSignals() {
    if (close_button_) {
        connect(close_button_, &QPushButton::clicked, this, &Modal::onCloseButtonClicked);
    }
    
    if (hide_animation_) {
        connect(hide_animation_, &QPropertyAnimation::finished, this, &Modal::onAnimationFinished);
    }
}

void Modal::applySize() {
    if (!modal_frame_) return;

    QSize size = getSizeForType(modal_size_);
    modal_frame_->setFixedSize(size);

    if (modal_dialog_) {
        modal_dialog_->setFixedSize(size);
    }
}

void Modal::createButtons() {
    // Clear existing buttons
    for (auto* button : action_buttons_) {
        button_layout_->removeWidget(button);
        delete button;
    }
    action_buttons_.clear();

    // Create new buttons
    for (int i = 0; i < button_texts_.size(); ++i) {
        auto* button = new QPushButton(button_texts_[i]);
        button->setMinimumWidth(80);
        button->setStyleSheet("QPushButton { padding: 8px 16px; }");

        connect(button, &QPushButton::clicked, this, &Modal::onButtonPressed);

        action_buttons_.append(button);
        button_layout_->addWidget(button);
    }
}

void Modal::centerOnScreen() {
    if (!modal_dialog_) return;

    QScreen* screen = QApplication::primaryScreen();
    QRect screen_geometry = screen->availableGeometry();

    int x = screen_geometry.center().x() - modal_dialog_->width() / 2;
    int y = screen_geometry.center().y() - modal_dialog_->height() / 2;

    modal_dialog_->move(x, y);
}

QSize Modal::getSizeForType(Size size_type) const {
    switch (size_type) {
        case Size::Small:
            return QSize(400, 300);
        case Size::Medium:
            return QSize(600, 400);
        case Size::Large:
            return QSize(800, 600);
        case Size::ExtraLarge:
            return QSize(1000, 700);
        case Size::FullScreen: {
            QScreen* screen = QApplication::primaryScreen();
            return screen->availableGeometry().size();
        }
        case Size::Custom:
            return QSize(custom_width_, custom_height_);
    }
    return QSize(600, 400);
}

QString Modal::getModalStyleSheet() const {
    return R"(
        QFrame {
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        QLabel {
            color: #333;
        }
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
        }
        QPushButton:hover {
            background-color: #0056b3;
        }
        QPushButton:pressed {
            background-color: #004085;
        }
    )";
}

void Modal::keyPressEvent(QKeyEvent* event) {
    if (event->key() == Qt::Key_Escape && escape_to_close_ && closable_) {
        result_code_ = -1;
        hide();
        event->accept();
        return;
    }

    event->ignore();
}

void Modal::mousePressEvent(QMouseEvent* event) {
    if (click_outside_to_close_ && closable_) {
        // Check if click is outside modal frame
        if (modal_frame_ && !modal_frame_->geometry().contains(event->pos())) {
            result_code_ = -1;
            hide();
            event->accept();
            return;
        }
    }

    event->ignore();
}

bool Modal::eventFilter(QObject* obj, QEvent* event) {
    Q_UNUSED(obj);
    Q_UNUSED(event);
    return false;
}

} // namespace DeclarativeUI::Components
