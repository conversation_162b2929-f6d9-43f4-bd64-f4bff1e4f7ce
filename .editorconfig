# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# =============================================================================
# Default settings for all files
# =============================================================================
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 4

# =============================================================================
# C++ Source Files
# =============================================================================
[*.{c,cc,cpp,cxx,c++,h,hh,hpp,hxx,h++,inl,ipp,tpp}]
indent_style = space
indent_size = 4
max_line_length = 80
insert_final_newline = true
trim_trailing_whitespace = true

# =============================================================================
# Qt Specific Files
# =============================================================================

# Qt Project Files
[*.{pro,pri,prf}]
indent_style = space
indent_size = 4

# Qt UI Files
[*.ui]
indent_style = space
indent_size = 2

# Qt Resource Files
[*.qrc]
indent_style = space
indent_size = 2

# Qt Style Sheets
[*.qss]
indent_style = space
indent_size = 2

# Qt Translation Files
[*.ts]
indent_style = space
indent_size = 2

# =============================================================================
# Build System Files
# =============================================================================

# CMake Files
[{CMakeLists.txt,*.cmake,*.cmake.in}]
indent_style = space
indent_size = 2
max_line_length = 120

# Makefiles
[{Makefile,makefile,*.mk,*.mak}]
indent_style = tab
indent_size = 4

# =============================================================================
# Configuration Files
# =============================================================================

# JSON Files
[*.json]
indent_style = space
indent_size = 2
max_line_length = 120

# YAML Files
[*.{yml,yaml}]
indent_style = space
indent_size = 2
max_line_length = 120

# XML Files
[*.xml]
indent_style = space
indent_size = 2
max_line_length = 120

# INI Files
[*.{ini,cfg,conf,config}]
indent_style = space
indent_size = 4

# =============================================================================
# Documentation Files
# =============================================================================

# Markdown Files
[*.{md,markdown}]
indent_style = space
indent_size = 2
max_line_length = 120
trim_trailing_whitespace = false  # Preserve trailing spaces for line breaks

# reStructuredText Files
[*.{rst,rest}]
indent_style = space
indent_size = 3
max_line_length = 120

# AsciiDoc Files
[*.{adoc,asciidoc}]
indent_style = space
indent_size = 2
max_line_length = 120

# Doxygen Files
[*.{dox,doxygen}]
indent_style = space
indent_size = 2

# =============================================================================
# Web Technologies (for Qt WebEngine projects)
# =============================================================================

# HTML Files
[*.{html,htm}]
indent_style = space
indent_size = 2
max_line_length = 120

# CSS Files
[*.{css,scss,sass,less}]
indent_style = space
indent_size = 2
max_line_length = 120

# JavaScript Files
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2
max_line_length = 120

# =============================================================================
# Script Files
# =============================================================================

# Shell Scripts
[*.{sh,bash,zsh,fish}]
indent_style = space
indent_size = 2
max_line_length = 120

# Windows Batch Files
[*.{bat,cmd}]
indent_style = space
indent_size = 2
end_of_line = crlf

# PowerShell Scripts
[*.ps1]
indent_style = space
indent_size = 2
max_line_length = 120

# Python Scripts (for build tools)
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# =============================================================================
# Git and GitHub Files
# =============================================================================

# Git Configuration
[.git*]
indent_style = space
indent_size = 2

# GitHub Workflows
[.github/**/*.{yml,yaml}]
indent_style = space
indent_size = 2
max_line_length = 120

# GitHub Issue Templates
[.github/ISSUE_TEMPLATE/*.{yml,yaml}]
indent_style = space
indent_size = 2
max_line_length = 120

# =============================================================================
# Special Cases
# =============================================================================

# License Files
[{LICENSE,LICENCE,COPYING}*]
max_line_length = off
trim_trailing_whitespace = false

# Changelog Files
[{CHANGELOG,HISTORY,NEWS,RELEASES}*]
max_line_length = off

# Generated Files (preserve formatting)
[*.{qm,rcc,moc_*,ui_*,qrc_*}]
insert_final_newline = false
trim_trailing_whitespace = false

# Binary Files (should not be edited)
[*.{exe,dll,so,dylib,a,lib,obj,o,bin,zip,tar,gz,bz2,xz,7z,rar,pdf,png,jpg,jpeg,gif,bmp,ico,webp,mp3,mp4,avi,mov,wav}]
insert_final_newline = false
trim_trailing_whitespace = false
