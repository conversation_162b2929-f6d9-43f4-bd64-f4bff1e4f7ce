#!/usr/bin/env python3
"""
Validate JSON UI definition files for DeclarativeUI Framework
"""

import json
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional

# Colors for output
class Colors:
    RED = '\033[0;31m'
    YELLOW = '\033[1;33m'
    GREEN = '\033[0;32m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_error(message: str) -> None:
    print(f"{Colors.RED}❌ Error: {message}{Colors.NC}")

def print_warning(message: str) -> None:
    print(f"{Colors.YELLOW}⚠️  Warning: {message}{Colors.NC}")

def print_success(message: str) -> None:
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def print_info(message: str) -> None:
    print(f"{Colors.BLUE}ℹ️  {message}{Colors.NC}")

# Valid component types for DeclarativeUI
VALID_COMPONENT_TYPES = {
    # Core components
    'Button', 'Label', 'LineEdit', 'Container', 'Widget',
    
    # Input components
    'CheckBox', 'RadioButton', 'ComboBox', 'SpinBox', 'DoubleSpinBox',
    'Slider', 'Dial', 'TextEdit', 'PlainTextEdit',
    
    # Display components
    'ProgressBar', 'LCDNumber',
    
    # Container components
    'GroupBox', 'TabWidget', 'Frame', 'ScrollArea', 'Splitter',
    
    # Advanced components
    'TableView', 'TreeView', 'ListView', 'MenuBar', 'ToolBar',
    'StatusBar', 'DateTimeEdit', 'Calendar',
    
    # Dialog components
    'FileDialog', 'ColorDialog', 'FontDialog', 'MessageBox',
    
    # Specialized components
    'DockWidget', 'ToolButton'
}

# Valid layout types
VALID_LAYOUT_TYPES = {
    'VBox', 'HBox', 'Grid', 'Form', 'Stack'
}

# Valid property names (common ones)
VALID_PROPERTIES = {
    'text', 'enabled', 'visible', 'width', 'height', 'x', 'y',
    'style', 'tooltip', 'whatsThis', 'objectName', 'layout',
    'spacing', 'margins', 'alignment', 'minimum', 'maximum',
    'value', 'placeholder', 'readOnly', 'checked', 'items',
    'currentIndex', 'currentText', 'wordWrap', 'scaledContents'
}

def validate_json_syntax(file_path: str) -> Optional[Dict[str, Any]]:
    """Validate JSON syntax and return parsed data."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except json.JSONDecodeError as e:
        print_error(f"Invalid JSON syntax in {file_path}: {e}")
        return None
    except Exception as e:
        print_error(f"Error reading {file_path}: {e}")
        return None

def validate_component_structure(component: Dict[str, Any], path: str = "root") -> tuple[int, int]:
    """Validate component structure and return (errors, warnings)."""
    errors = 0
    warnings = 0
    
    # Check required fields
    if 'type' not in component:
        print_error(f"Missing 'type' field in component at {path}")
        errors += 1
        return errors, warnings
    
    component_type = component['type']
    
    # Validate component type
    if component_type not in VALID_COMPONENT_TYPES:
        print_warning(f"Unknown component type '{component_type}' at {path}")
        warnings += 1
    
    # Validate layout property
    if 'layout' in component:
        layout = component['layout']
        if layout not in VALID_LAYOUT_TYPES:
            print_warning(f"Unknown layout type '{layout}' at {path}")
            warnings += 1
    
    # Validate properties
    for prop_name, prop_value in component.items():
        if prop_name in ['type', 'children']:
            continue
            
        if prop_name not in VALID_PROPERTIES:
            print_warning(f"Unknown property '{prop_name}' at {path}")
            warnings += 1
        
        # Type-specific validations
        if prop_name == 'enabled' and not isinstance(prop_value, bool):
            print_error(f"Property 'enabled' should be boolean at {path}")
            errors += 1
        
        if prop_name == 'visible' and not isinstance(prop_value, bool):
            print_error(f"Property 'visible' should be boolean at {path}")
            errors += 1
        
        if prop_name in ['width', 'height', 'x', 'y'] and not isinstance(prop_value, (int, float)):
            print_error(f"Property '{prop_name}' should be numeric at {path}")
            errors += 1
    
    # Validate children
    if 'children' in component:
        if not isinstance(component['children'], list):
            print_error(f"'children' should be an array at {path}")
            errors += 1
        else:
            for i, child in enumerate(component['children']):
                if not isinstance(child, dict):
                    print_error(f"Child {i} should be an object at {path}")
                    errors += 1
                else:
                    child_errors, child_warnings = validate_component_structure(
                        child, f"{path}.children[{i}]"
                    )
                    errors += child_errors
                    warnings += child_warnings
    
    return errors, warnings

def validate_ui_definition(data: Dict[str, Any], file_path: str) -> tuple[int, int]:
    """Validate complete UI definition."""
    errors = 0
    warnings = 0
    
    # Check for root component
    if 'component' in data:
        comp_errors, comp_warnings = validate_component_structure(data['component'])
        errors += comp_errors
        warnings += comp_warnings
    elif 'type' in data:
        # Direct component definition
        comp_errors, comp_warnings = validate_component_structure(data)
        errors += comp_errors
        warnings += comp_warnings
    else:
        print_error(f"No valid component structure found in {file_path}")
        errors += 1
    
    # Check for metadata
    if 'metadata' in data:
        metadata = data['metadata']
        if not isinstance(metadata, dict):
            print_error(f"'metadata' should be an object in {file_path}")
            errors += 1
        else:
            # Validate common metadata fields
            if 'version' in metadata and not isinstance(metadata['version'], str):
                print_warning(f"'metadata.version' should be a string in {file_path}")
                warnings += 1
            
            if 'description' in metadata and not isinstance(metadata['description'], str):
                print_warning(f"'metadata.description' should be a string in {file_path}")
                warnings += 1
    
    return errors, warnings

def main():
    """Main validation function."""
    if len(sys.argv) < 2:
        print_error("Usage: validate-json-ui.py <file1.json> [file2.json] ...")
        sys.exit(1)
    
    total_errors = 0
    total_warnings = 0
    files_checked = 0
    
    print("🔍 Validating JSON UI definition files...")
    
    for file_path in sys.argv[1:]:
        if not os.path.exists(file_path):
            print_error(f"File not found: {file_path}")
            total_errors += 1
            continue
        
        if not file_path.endswith('.json'):
            print_info(f"Skipping non-JSON file: {file_path}")
            continue
        
        print(f"\nChecking: {file_path}")
        files_checked += 1
        
        # Validate JSON syntax
        data = validate_json_syntax(file_path)
        if data is None:
            total_errors += 1
            continue
        
        # Validate UI definition structure
        errors, warnings = validate_ui_definition(data, file_path)
        total_errors += errors
        total_warnings += warnings
        
        if errors == 0 and warnings == 0:
            print_success(f"Valid UI definition: {file_path}")
        elif errors == 0:
            print_warning(f"UI definition has {warnings} warnings: {file_path}")
        else:
            print_error(f"UI definition has {errors} errors and {warnings} warnings: {file_path}")
    
    print(f"\n📊 Summary:")
    print(f"   Files checked: {files_checked}")
    print(f"   Total errors: {total_errors}")
    print(f"   Total warnings: {total_warnings}")
    
    if total_errors > 0:
        print_error(f"JSON UI validation failed with {total_errors} errors")
        sys.exit(1)
    elif total_warnings > 0:
        print_warning(f"JSON UI validation completed with {total_warnings} warnings")
        sys.exit(0)
    else:
        print_success("All JSON UI definitions are valid")
        sys.exit(0)

if __name__ == "__main__":
    main()
