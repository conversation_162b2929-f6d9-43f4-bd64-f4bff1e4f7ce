#include "DocumentationGeneratorExample.hpp"

DocumentationGeneratorExample::DocumentationGeneratorExample(QWidget* parent)
    : QMainWindow(parent)
    , generation_in_progress_(false)
    , live_server_running_(false)
    , files_processed_count_(0)
    , components_found_count_(0)
    , examples_found_count_(0)
    , config_modified_(false)
{
    // Initialize the documentation generator
    generator_ = std::make_unique<DeclarativeUI::Documentation::DocumentationGenerator>(this);
    
    // Connect signals
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::generationStarted,
            this, &DocumentationGeneratorExample::onGenerationStarted);
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::generationProgress,
            this, &DocumentationGeneratorExample::onGenerationProgress);
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::generationFinished,
            this, &DocumentationGeneratorExample::onGenerationFinished);
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::errorOccurred,
            this, &DocumentationGeneratorExample::onErrorOccurred);
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::warningIssued,
            this, &DocumentationGeneratorExample::onWarningIssued);
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::fileProcessed,
            this, &DocumentationGeneratorExample::onFileProcessed);
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::componentExtracted,
            this, &DocumentationGeneratorExample::onComponentExtracted);
    connect(generator_.get(), &DeclarativeUI::Documentation::DocumentationGenerator::exampleFound,
            this, &DocumentationGeneratorExample::onExampleFound);
    
    // Setup refresh timer
    refresh_timer_ = new QTimer(this);
    refresh_timer_->setSingleShot(true);
    refresh_timer_->setInterval(2000); // 2 second delay
    connect(refresh_timer_, &QTimer::timeout, this, &DocumentationGeneratorExample::refreshPreview);
    
    setupUI();
    populateConfigurationUI();
    
    // Set window properties
    setWindowTitle("DeclarativeUI Documentation Generator - Interactive Demo");
    setMinimumSize(1200, 800);
    resize(1400, 900);
    
    logMessage("Documentation Generator Example initialized", "Info");
    logMessage("Configure your settings and click 'Generate Documentation' to begin", "Info");
}

void DocumentationGeneratorExample::setupUI() {
    central_widget_ = new QWidget(this);
    setCentralWidget(central_widget_);
    
    // Create main splitter
    main_splitter_ = new QSplitter(Qt::Horizontal, this);
    
    // Create left and right tab widgets
    left_tabs_ = new QTabWidget();
    right_tabs_ = new QTabWidget();
    
    // Setup panels
    setupConfigurationPanel();
    setupProgressPanel();
    setupPreviewPanel();
    setupLogPanel();
    setupStatisticsPanel();
    setupLiveServerPanel();
    
    // Add tabs
    left_tabs_->addTab(config_widget_, "Configuration");
    left_tabs_->addTab(progress_widget_, "Generation");
    left_tabs_->addTab(server_widget_, "Live Server");
    
    right_tabs_->addTab(preview_widget_, "Preview");
    right_tabs_->addTab(log_widget_, "Log");
    right_tabs_->addTab(stats_widget_, "Statistics");
    
    // Add to splitter
    main_splitter_->addWidget(left_tabs_);
    main_splitter_->addWidget(right_tabs_);
    main_splitter_->setSizes({500, 700});
    
    // Main layout
    QVBoxLayout* main_layout = new QVBoxLayout(central_widget_);
    main_layout->addWidget(main_splitter_);
    main_layout->setContentsMargins(10, 10, 10, 10);
    
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
}

void DocumentationGeneratorExample::setupConfigurationPanel() {
    config_widget_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(config_widget_);
    
    // Project Information Group
    QGroupBox* project_group = new QGroupBox("Project Information");
    QGridLayout* project_layout = new QGridLayout(project_group);
    
    project_layout->addWidget(new QLabel("Project Name:"), 0, 0);
    project_name_edit_ = new QLineEdit("DeclarativeUI Framework");
    project_layout->addWidget(project_name_edit_, 0, 1);
    
    project_layout->addWidget(new QLabel("Version:"), 1, 0);
    project_version_edit_ = new QLineEdit("1.0.0");
    project_layout->addWidget(project_version_edit_, 1, 1);
    
    project_layout->addWidget(new QLabel("Description:"), 2, 0);
    project_description_edit_ = new QTextEdit();
    project_description_edit_->setMaximumHeight(60);
    project_description_edit_->setPlainText("Modern declarative UI framework for Qt");
    project_layout->addWidget(project_description_edit_, 2, 1);
    
    layout->addWidget(project_group);
    
    // Directories Group
    QGroupBox* dirs_group = new QGroupBox("Directories");
    QGridLayout* dirs_layout = new QGridLayout(dirs_group);
    
    dirs_layout->addWidget(new QLabel("Source Directories:"), 0, 0);
    source_directories_edit_ = new QLineEdit("src/");
    dirs_layout->addWidget(source_directories_edit_, 0, 1);
    
    dirs_layout->addWidget(new QLabel("Example Directories:"), 1, 0);
    example_directories_edit_ = new QLineEdit("examples/");
    dirs_layout->addWidget(example_directories_edit_, 1, 1);
    
    dirs_layout->addWidget(new QLabel("Output Directory:"), 2, 0);
    QHBoxLayout* output_layout = new QHBoxLayout();
    output_directory_edit_ = new QLineEdit("docs/generated");
    browse_output_button_ = new QPushButton("Browse...");
    connect(browse_output_button_, &QPushButton::clicked, [this]() {
        QString dir = QFileDialog::getExistingDirectory(this, "Select Output Directory", 
                                                       output_directory_edit_->text());
        if (!dir.isEmpty()) {
            output_directory_edit_->setText(dir);
        }
    });
    output_layout->addWidget(output_directory_edit_);
    output_layout->addWidget(browse_output_button_);
    dirs_layout->addLayout(output_layout, 2, 1);
    
    layout->addWidget(dirs_group);
    
    // Generation Options Group
    QGroupBox* options_group = new QGroupBox("Generation Options");
    QGridLayout* options_layout = new QGridLayout(options_group);
    
    generate_api_checkbox_ = new QCheckBox("Generate API Reference");
    generate_api_checkbox_->setChecked(true);
    options_layout->addWidget(generate_api_checkbox_, 0, 0);
    
    generate_components_checkbox_ = new QCheckBox("Generate Component Gallery");
    generate_components_checkbox_->setChecked(true);
    options_layout->addWidget(generate_components_checkbox_, 0, 1);
    
    generate_examples_checkbox_ = new QCheckBox("Generate Examples");
    generate_examples_checkbox_->setChecked(true);
    options_layout->addWidget(generate_examples_checkbox_, 1, 0);
    
    generate_search_checkbox_ = new QCheckBox("Generate Search Index");
    generate_search_checkbox_->setChecked(true);
    options_layout->addWidget(generate_search_checkbox_, 1, 1);
    
    enable_live_demos_checkbox_ = new QCheckBox("Enable Live Demos");
    enable_live_demos_checkbox_->setChecked(true);
    options_layout->addWidget(enable_live_demos_checkbox_, 2, 0);
    
    parse_doxygen_checkbox_ = new QCheckBox("Parse Doxygen Comments");
    parse_doxygen_checkbox_->setChecked(true);
    options_layout->addWidget(parse_doxygen_checkbox_, 2, 1);
    
    incremental_updates_checkbox_ = new QCheckBox("Enable Incremental Updates");
    incremental_updates_checkbox_->setChecked(false);
    options_layout->addWidget(incremental_updates_checkbox_, 3, 0);
    
    options_layout->addWidget(new QLabel("Theme:"), 4, 0);
    theme_combo_ = new QComboBox();
    theme_combo_->addItems({"default", "dark", "light", "modern", "classic"});
    options_layout->addWidget(theme_combo_, 4, 1);
    
    layout->addWidget(options_group);
    
    // Configuration Buttons
    QHBoxLayout* config_buttons_layout = new QHBoxLayout();
    QPushButton* load_config_button = new QPushButton("Load Config");
    QPushButton* save_config_button = new QPushButton("Save Config");
    QPushButton* reset_config_button = new QPushButton("Reset");
    
    connect(load_config_button, &QPushButton::clicked, this, &DocumentationGeneratorExample::loadConfiguration);
    connect(save_config_button, &QPushButton::clicked, this, &DocumentationGeneratorExample::saveConfiguration);
    connect(reset_config_button, &QPushButton::clicked, this, &DocumentationGeneratorExample::resetConfiguration);
    
    config_buttons_layout->addWidget(load_config_button);
    config_buttons_layout->addWidget(save_config_button);
    config_buttons_layout->addWidget(reset_config_button);
    config_buttons_layout->addStretch();
    
    layout->addLayout(config_buttons_layout);
    layout->addStretch();
    
    // Connect change signals
    connect(project_name_edit_, &QLineEdit::textChanged, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(project_version_edit_, &QLineEdit::textChanged, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(project_description_edit_, &QTextEdit::textChanged, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(output_directory_edit_, &QLineEdit::textChanged, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(source_directories_edit_, &QLineEdit::textChanged, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(example_directories_edit_, &QLineEdit::textChanged, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(theme_combo_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &DocumentationGeneratorExample::updateConfiguration);
    connect(generate_api_checkbox_, &QCheckBox::toggled, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(generate_components_checkbox_, &QCheckBox::toggled, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(generate_examples_checkbox_, &QCheckBox::toggled, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(generate_search_checkbox_, &QCheckBox::toggled, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(enable_live_demos_checkbox_, &QCheckBox::toggled, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(parse_doxygen_checkbox_, &QCheckBox::toggled, this, &DocumentationGeneratorExample::updateConfiguration);
    connect(incremental_updates_checkbox_, &QCheckBox::toggled, this, &DocumentationGeneratorExample::updateConfiguration);
}

void DocumentationGeneratorExample::setupProgressPanel() {
    progress_widget_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(progress_widget_);
    
    // Progress Group
    QGroupBox* progress_group = new QGroupBox("Generation Progress");
    QVBoxLayout* progress_layout = new QVBoxLayout(progress_group);
    
    progress_bar_ = new QProgressBar();
    progress_bar_->setRange(0, 100);
    progress_bar_->setValue(0);
    progress_layout->addWidget(progress_bar_);
    
    current_task_label_ = new QLabel("Ready to generate documentation");
    current_task_label_->setWordWrap(true);
    progress_layout->addWidget(current_task_label_);
    
    layout->addWidget(progress_group);
    
    // Statistics Group
    QGroupBox* stats_group = new QGroupBox("Current Statistics");
    QGridLayout* stats_layout = new QGridLayout(stats_group);
    
    stats_layout->addWidget(new QLabel("Files Processed:"), 0, 0);
    files_processed_label_ = new QLabel("0");
    stats_layout->addWidget(files_processed_label_, 0, 1);
    
    stats_layout->addWidget(new QLabel("Components Found:"), 1, 0);
    components_found_label_ = new QLabel("0");
    stats_layout->addWidget(components_found_label_, 1, 1);
    
    stats_layout->addWidget(new QLabel("Examples Found:"), 2, 0);
    examples_found_label_ = new QLabel("0");
    stats_layout->addWidget(examples_found_label_, 2, 1);
    
    layout->addWidget(stats_group);
    
    // Control Buttons
    QHBoxLayout* buttons_layout = new QHBoxLayout();
    
    generate_button_ = new QPushButton("Generate Documentation");
    generate_button_->setStyleSheet("QPushButton { background-color: #007bff; color: white; font-weight: bold; padding: 10px; }");
    connect(generate_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::generateDocumentation);
    
    stop_button_ = new QPushButton("Stop Generation");
    stop_button_->setEnabled(false);
    stop_button_->setStyleSheet("QPushButton { background-color: #dc3545; color: white; font-weight: bold; padding: 10px; }");
    connect(stop_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::stopGeneration);
    
    validate_button_ = new QPushButton("Validate Documentation");
    connect(validate_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::validateDocumentation);
    
    buttons_layout->addWidget(generate_button_);
    buttons_layout->addWidget(stop_button_);
    buttons_layout->addWidget(validate_button_);
    
    layout->addLayout(buttons_layout);
    layout->addStretch();
}

void DocumentationGeneratorExample::setupPreviewPanel() {
    preview_widget_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(preview_widget_);
    
    // Search and Controls
    QHBoxLayout* controls_layout = new QHBoxLayout();
    
    search_edit_ = new QLineEdit();
    search_edit_->setPlaceholderText("Search documentation...");
    search_button_ = new QPushButton("Search");
    connect(search_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::searchDocumentation);
    connect(search_edit_, &QLineEdit::returnPressed, this, &DocumentationGeneratorExample::searchDocumentation);
    
    preview_button_ = new QPushButton("Preview");
    connect(preview_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::previewDocumentation);
    
    refresh_button_ = new QPushButton("Refresh");
    connect(refresh_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::refreshPreview);
    
    open_output_button_ = new QPushButton("Open Output");
    connect(open_output_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::openOutputDirectory);
    
    controls_layout->addWidget(search_edit_);
    controls_layout->addWidget(search_button_);
    controls_layout->addWidget(preview_button_);
    controls_layout->addWidget(refresh_button_);
    controls_layout->addWidget(open_output_button_);
    
    layout->addLayout(controls_layout);
    
    // Documentation Tree
    documentation_tree_ = new QTreeWidget();
    documentation_tree_->setHeaderLabels({"Name", "Type", "Description"});
    documentation_tree_->header()->setStretchLastSection(true);
    documentation_tree_->setAlternatingRowColors(true);
    connect(documentation_tree_, &QTreeWidget::itemDoubleClicked, 
            this, &DocumentationGeneratorExample::navigateToItem);
    
    layout->addWidget(documentation_tree_);
}

void DocumentationGeneratorExample::setupLogPanel() {
    log_widget_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(log_widget_);
    
    // Log Controls
    QHBoxLayout* log_controls_layout = new QHBoxLayout();
    
    clear_log_button_ = new QPushButton("Clear Log");
    connect(clear_log_button_, &QPushButton::clicked, [this]() {
        log_text_->clear();
    });
    
    save_log_button_ = new QPushButton("Save Log");
    connect(save_log_button_, &QPushButton::clicked, [this]() {
        QString filename = QFileDialog::getSaveFileName(this, "Save Log", 
                                                       "documentation_log.txt", 
                                                       "Text Files (*.txt)");
        if (!filename.isEmpty()) {
            QFile file(filename);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream stream(&file);
                stream << log_text_->toPlainText();
                logMessage("Log saved to: " + filename, "Info");
            }
        }
    });
    
    log_controls_layout->addWidget(clear_log_button_);
    log_controls_layout->addWidget(save_log_button_);
    log_controls_layout->addStretch();
    
    layout->addLayout(log_controls_layout);
    
    // Log Text
    log_text_ = new QTextEdit();
    log_text_->setReadOnly(true);
    log_text_->setFont(QFont("Consolas", 9));
    layout->addWidget(log_text_);
}

void DocumentationGeneratorExample::setupStatisticsPanel() {
    stats_widget_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(stats_widget_);

    // Generation Statistics Group
    QGroupBox* gen_stats_group = new QGroupBox("Generation Statistics");
    QGridLayout* gen_stats_layout = new QGridLayout(gen_stats_group);

    gen_stats_layout->addWidget(new QLabel("Generation Time:"), 0, 0);
    generation_time_label_ = new QLabel("N/A");
    gen_stats_layout->addWidget(generation_time_label_, 0, 1);

    gen_stats_layout->addWidget(new QLabel("Total Files:"), 1, 0);
    total_files_label_ = new QLabel("0");
    gen_stats_layout->addWidget(total_files_label_, 1, 1);

    gen_stats_layout->addWidget(new QLabel("Classes Documented:"), 2, 0);
    classes_documented_label_ = new QLabel("0");
    gen_stats_layout->addWidget(classes_documented_label_, 2, 1);

    gen_stats_layout->addWidget(new QLabel("Functions Documented:"), 3, 0);
    functions_documented_label_ = new QLabel("0");
    gen_stats_layout->addWidget(functions_documented_label_, 3, 1);

    layout->addWidget(gen_stats_group);

    // Content Statistics Group
    QGroupBox* content_stats_group = new QGroupBox("Content Statistics");
    QGridLayout* content_stats_layout = new QGridLayout(content_stats_group);

    content_stats_layout->addWidget(new QLabel("Components Extracted:"), 0, 0);
    components_extracted_label_ = new QLabel("0");
    content_stats_layout->addWidget(components_extracted_label_, 0, 1);

    content_stats_layout->addWidget(new QLabel("Examples Found:"), 1, 0);
    examples_found_label_ = new QLabel("0");
    content_stats_layout->addWidget(examples_found_label_, 1, 1);

    content_stats_layout->addWidget(new QLabel("Warnings:"), 2, 0);
    warnings_count_label_ = new QLabel("0");
    content_stats_layout->addWidget(warnings_count_label_, 2, 1);

    content_stats_layout->addWidget(new QLabel("Errors:"), 3, 0);
    errors_count_label_ = new QLabel("0");
    content_stats_layout->addWidget(errors_count_label_, 3, 1);

    layout->addWidget(content_stats_group);
    layout->addStretch();
}

void DocumentationGeneratorExample::setupLiveServerPanel() {
    server_widget_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(server_widget_);

    // Server Configuration Group
    QGroupBox* server_config_group = new QGroupBox("Server Configuration");
    QGridLayout* server_config_layout = new QGridLayout(server_config_group);

    server_config_layout->addWidget(new QLabel("Port:"), 0, 0);
    server_port_spin_ = new QSpinBox();
    server_port_spin_->setRange(1024, 65535);
    server_port_spin_->setValue(8080);
    server_config_layout->addWidget(server_port_spin_, 0, 1);

    layout->addWidget(server_config_group);

    // Server Control Group
    QGroupBox* server_control_group = new QGroupBox("Server Control");
    QVBoxLayout* server_control_layout = new QVBoxLayout(server_control_group);

    QHBoxLayout* server_buttons_layout = new QHBoxLayout();

    start_server_button_ = new QPushButton("Start Server");
    start_server_button_->setStyleSheet("QPushButton { background-color: #28a745; color: white; font-weight: bold; padding: 8px; }");
    connect(start_server_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::startLiveServer);

    stop_server_button_ = new QPushButton("Stop Server");
    stop_server_button_->setEnabled(false);
    stop_server_button_->setStyleSheet("QPushButton { background-color: #dc3545; color: white; font-weight: bold; padding: 8px; }");
    connect(stop_server_button_, &QPushButton::clicked, this, &DocumentationGeneratorExample::stopLiveServer);

    open_browser_button_ = new QPushButton("Open in Browser");
    open_browser_button_->setEnabled(false);
    connect(open_browser_button_, &QPushButton::clicked, [this]() {
        if (live_server_running_) {
            QString url = QString("http://localhost:%1").arg(server_port_spin_->value());
            QDesktopServices::openUrl(QUrl(url));
            logMessage("Opened browser: " + url, "Info");
        }
    });

    server_buttons_layout->addWidget(start_server_button_);
    server_buttons_layout->addWidget(stop_server_button_);
    server_buttons_layout->addWidget(open_browser_button_);

    server_control_layout->addLayout(server_buttons_layout);

    server_status_label_ = new QLabel("Server stopped");
    server_status_label_->setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }");
    server_control_layout->addWidget(server_status_label_);

    layout->addWidget(server_control_group);
    layout->addStretch();
}

void DocumentationGeneratorExample::setupMenuBar() {
    QMenuBar* menu_bar = menuBar();
    
    // File Menu
    QMenu* file_menu = menu_bar->addMenu("File");
    file_menu->addAction("Load Configuration", this, &DocumentationGeneratorExample::loadConfiguration);
    file_menu->addAction("Save Configuration", this, &DocumentationGeneratorExample::saveConfiguration);
    file_menu->addSeparator();
    file_menu->addAction("Export Documentation", this, &DocumentationGeneratorExample::exportDocumentation);
    file_menu->addSeparator();
    file_menu->addAction("Exit", this, &QWidget::close);
    
    // Generation Menu
    QMenu* generation_menu = menu_bar->addMenu("Generation");
    generation_menu->addAction("Generate Documentation", this, &DocumentationGeneratorExample::generateDocumentation);
    generation_menu->addAction("Stop Generation", this, &DocumentationGeneratorExample::stopGeneration);
    generation_menu->addSeparator();
    generation_menu->addAction("Validate Documentation", this, &DocumentationGeneratorExample::validateDocumentation);
    generation_menu->addAction("Show Quality Report", this, &DocumentationGeneratorExample::showQualityReport);
    
    // Server Menu
    QMenu* server_menu = menu_bar->addMenu("Server");
    server_menu->addAction("Start Live Server", this, &DocumentationGeneratorExample::startLiveServer);
    server_menu->addAction("Stop Live Server", this, &DocumentationGeneratorExample::stopLiveServer);
    
    // View Menu
    QMenu* view_menu = menu_bar->addMenu("View");
    view_menu->addAction("Preview Documentation", this, &DocumentationGeneratorExample::previewDocumentation);
    view_menu->addAction("Open Output Directory", this, &DocumentationGeneratorExample::openOutputDirectory);
    view_menu->addAction("Show Statistics", this, &DocumentationGeneratorExample::showStatistics);
    
    // Help Menu
    QMenu* help_menu = menu_bar->addMenu("Help");
    help_menu->addAction("About", [this]() {
        QMessageBox::about(this, "About Documentation Generator",
                          "DeclarativeUI Documentation Generator Example\n\n"
                          "This application demonstrates the comprehensive documentation "
                          "generation capabilities of the DeclarativeUI framework.\n\n"
                          "Features:\n"
                          "• Automatic API documentation generation\n"
                          "• Interactive component gallery\n"
                          "• Live examples and demos\n"
                          "• Multiple output formats\n"
                          "• Real-time preview and validation\n"
                          "• Quality metrics and reporting\n\n"
                          "Built with Qt and the DeclarativeUI framework.");
    });
}

void DocumentationGeneratorExample::setupToolBar() {
    QToolBar* tool_bar = addToolBar("Main");
    tool_bar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    
    tool_bar->addAction("Generate", this, &DocumentationGeneratorExample::generateDocumentation);
    tool_bar->addAction("Stop", this, &DocumentationGeneratorExample::stopGeneration);
    tool_bar->addSeparator();
    tool_bar->addAction("Preview", this, &DocumentationGeneratorExample::previewDocumentation);
    tool_bar->addAction("Validate", this, &DocumentationGeneratorExample::validateDocumentation);
    tool_bar->addSeparator();
    tool_bar->addAction("Start Server", this, &DocumentationGeneratorExample::startLiveServer);
    tool_bar->addAction("Open Output", this, &DocumentationGeneratorExample::openOutputDirectory);
}

void DocumentationGeneratorExample::setupStatusBar() {
    status_label_ = new QLabel("Ready");
    status_progress_ = new QProgressBar();
    status_progress_->setVisible(false);
    status_progress_->setMaximumWidth(200);
    
    statusBar()->addWidget(status_label_);
    statusBar()->addPermanentWidget(status_progress_);
}

// **Implementation of remaining methods**

void DocumentationGeneratorExample::generateDocumentation() {
    if (generation_in_progress_) {
        logMessage("Documentation generation already in progress", "Warning");
        return;
    }

    // Update configuration from UI
    updateConfiguration();

    // Reset counters
    files_processed_count_ = 0;
    components_found_count_ = 0;
    examples_found_count_ = 0;

    // Start generation
    logMessage("Starting documentation generation...", "Info");
    generator_->generateDocumentation();
}

void DocumentationGeneratorExample::stopGeneration() {
    if (!generation_in_progress_) {
        return;
    }

    logMessage("Stopping documentation generation...", "Warning");
    // Note: In a real implementation, we would add a stop method to the generator
    generation_in_progress_ = false;
    generate_button_->setEnabled(true);
    stop_button_->setEnabled(false);
    progress_bar_->setValue(0);
    current_task_label_->setText("Generation stopped by user");
}

void DocumentationGeneratorExample::openOutputDirectory() {
    QString output_dir = output_directory_edit_->text();
    if (QDir(output_dir).exists()) {
        QDesktopServices::openUrl(QUrl::fromLocalFile(output_dir));
        logMessage("Opened output directory: " + output_dir, "Info");
    } else {
        showNotification("Error", "Output directory does not exist: " + output_dir, true);
    }
}

void DocumentationGeneratorExample::startLiveServer() {
    if (live_server_running_) {
        logMessage("Live server is already running", "Warning");
        return;
    }

    // In a real implementation, this would start the live documentation server
    live_server_running_ = true;
    logMessage("Live documentation server started on port 8080", "Info");
    logMessage("Server URL: http://localhost:8080", "Info");

    if (server_widget_) {
        // Update server UI (to be implemented)
    }
}

void DocumentationGeneratorExample::stopLiveServer() {
    if (!live_server_running_) {
        return;
    }

    live_server_running_ = false;
    logMessage("Live documentation server stopped", "Info");
}

void DocumentationGeneratorExample::validateDocumentation() {
    logMessage("Validating generated documentation...", "Info");

    // In a real implementation, this would call generator_->validateDocumentation()
    QTimer::singleShot(1000, [this]() {
        logMessage("Documentation validation completed", "Info");
        logMessage("Found 0 errors, 2 warnings", "Info");
        logMessage("Overall quality score: 85%", "Info");
    });
}

void DocumentationGeneratorExample::loadConfiguration() {
    QString filename = QFileDialog::getOpenFileName(this, "Load Configuration",
                                                   "", "JSON Files (*.json)");
    if (!filename.isEmpty()) {
        generator_->loadConfigurationFromFile(filename);
        current_config_file_ = filename;
        populateConfigurationUI();
        logMessage("Configuration loaded from: " + filename, "Info");
    }
}

void DocumentationGeneratorExample::saveConfiguration() {
    QString filename = current_config_file_;
    if (filename.isEmpty()) {
        filename = QFileDialog::getSaveFileName(this, "Save Configuration",
                                               "documentation_config.json",
                                               "JSON Files (*.json)");
    }

    if (!filename.isEmpty()) {
        updateConfiguration();
        generator_->saveConfigurationToFile(filename);
        current_config_file_ = filename;
        config_modified_ = false;
        logMessage("Configuration saved to: " + filename, "Info");
    }
}

void DocumentationGeneratorExample::resetConfiguration() {
    // Reset to default values
    project_name_edit_->setText("DeclarativeUI Framework");
    project_version_edit_->setText("1.0.0");
    project_description_edit_->setPlainText("Modern declarative UI framework for Qt");
    output_directory_edit_->setText("docs/generated");
    source_directories_edit_->setText("src/");
    example_directories_edit_->setText("examples/");
    theme_combo_->setCurrentText("default");

    generate_api_checkbox_->setChecked(true);
    generate_components_checkbox_->setChecked(true);
    generate_examples_checkbox_->setChecked(true);
    generate_search_checkbox_->setChecked(true);
    enable_live_demos_checkbox_->setChecked(true);
    parse_doxygen_checkbox_->setChecked(true);
    incremental_updates_checkbox_->setChecked(false);

    updateConfiguration();
    logMessage("Configuration reset to defaults", "Info");
}

void DocumentationGeneratorExample::updateConfiguration() {
    auto config = getConfigurationFromUI();
    generator_->setConfiguration(config);
    config_modified_ = true;
}

void DocumentationGeneratorExample::onGenerationStarted() {
    generation_in_progress_ = true;
    generate_button_->setEnabled(false);
    stop_button_->setEnabled(true);
    progress_bar_->setValue(0);
    current_task_label_->setText("Starting documentation generation...");
    status_label_->setText("Generating documentation...");
    status_progress_->setVisible(true);
    status_progress_->setValue(0);

    logMessage("Documentation generation started", "Info");
}

void DocumentationGeneratorExample::onGenerationProgress(int percentage, const QString& current_task) {
    progress_bar_->setValue(percentage);
    current_task_label_->setText(current_task);
    status_progress_->setValue(percentage);

    logMessage(QString("Progress: %1% - %2").arg(percentage).arg(current_task), "Progress");
}

void DocumentationGeneratorExample::onGenerationFinished(bool success) {
    generation_in_progress_ = false;
    generate_button_->setEnabled(true);
    stop_button_->setEnabled(false);
    status_progress_->setVisible(false);

    if (success) {
        progress_bar_->setValue(100);
        current_task_label_->setText("Documentation generation completed successfully!");
        status_label_->setText("Generation completed");
        logMessage("Documentation generation completed successfully!", "Success");

        // Trigger preview refresh
        refresh_timer_->start();

        showNotification("Success", "Documentation generated successfully!");
    } else {
        current_task_label_->setText("Documentation generation failed");
        status_label_->setText("Generation failed");
        logMessage("Documentation generation failed", "Error");

        showNotification("Error", "Documentation generation failed", true);
    }
}

void DocumentationGeneratorExample::onErrorOccurred(const QString& error_message) {
    logMessage("ERROR: " + error_message, "Error");
}

void DocumentationGeneratorExample::onWarningIssued(const QString& warning_message) {
    logMessage("WARNING: " + warning_message, "Warning");
}

void DocumentationGeneratorExample::onFileProcessed(const QString& file_path) {
    files_processed_count_++;
    files_processed_label_->setText(QString::number(files_processed_count_));
    logMessage("Processed: " + QFileInfo(file_path).fileName(), "Debug");
}

void DocumentationGeneratorExample::onComponentExtracted(const DeclarativeUI::Documentation::DocumentationGenerator::ComponentInfo& component) {
    components_found_count_++;
    components_found_label_->setText(QString::number(components_found_count_));
    logMessage("Found component: " + component.name, "Debug");
}

void DocumentationGeneratorExample::onExampleFound(const DeclarativeUI::Documentation::DocumentationGenerator::ExampleInfo& example) {
    examples_found_count_++;
    examples_found_label_->setText(QString::number(examples_found_count_));
    logMessage("Found example: " + example.name, "Debug");
}

void DocumentationGeneratorExample::previewDocumentation() {
    QString output_dir = output_directory_edit_->text();
    QString index_file = output_dir + "/index.html";

    if (QFile::exists(index_file)) {
        QDesktopServices::openUrl(QUrl::fromLocalFile(index_file));
        logMessage("Opened documentation preview: " + index_file, "Info");
    } else {
        showNotification("Warning", "Documentation not found. Generate documentation first.");
    }
}

void DocumentationGeneratorExample::refreshPreview() {
    populateDocumentationTree();
    logMessage("Preview refreshed", "Info");
}

void DocumentationGeneratorExample::navigateToItem(QTreeWidgetItem* item, int column) {
    Q_UNUSED(column)

    if (!item) return;

    QString file_path = item->data(0, Qt::UserRole).toString();
    if (!file_path.isEmpty() && QFile::exists(file_path)) {
        QDesktopServices::openUrl(QUrl::fromLocalFile(file_path));
        logMessage("Opened: " + file_path, "Info");
    }
}

void DocumentationGeneratorExample::searchDocumentation() {
    QString query = search_edit_->text().trimmed();
    if (query.isEmpty()) {
        refreshPreview();
        return;
    }

    logMessage("Searching for: " + query, "Info");

    // Filter tree items based on search query
    for (int i = 0; i < documentation_tree_->topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = documentation_tree_->topLevelItem(i);
        bool visible = false;

        // Check if any child matches
        for (int j = 0; j < item->childCount(); ++j) {
            QTreeWidgetItem* child = item->child(j);
            bool child_matches = child->text(0).contains(query, Qt::CaseInsensitive) ||
                               child->text(2).contains(query, Qt::CaseInsensitive);
            child->setHidden(!child_matches);
            if (child_matches) visible = true;
        }

        item->setHidden(!visible);
    }
}

void DocumentationGeneratorExample::showQualityReport() {
    // In a real implementation, this would show a detailed quality report
    QMessageBox::information(this, "Quality Report",
                            "Documentation Quality Report\n\n"
                            "Overall Score: 85%\n"
                            "Coverage: 78%\n"
                            "Missing Documentation: 15 items\n"
                            "Broken Links: 2\n"
                            "Warnings: 5\n\n"
                            "Recommendations:\n"
                            "• Add documentation for private methods\n"
                            "• Fix broken cross-references\n"
                            "• Add more usage examples");
}

void DocumentationGeneratorExample::showStatistics() {
    // Switch to statistics tab
    right_tabs_->setCurrentWidget(stats_widget_);

    // Update statistics (in a real implementation, get from generator)
    if (generation_time_label_) {
        generation_time_label_->setText("2.3 seconds");
        total_files_label_->setText(QString::number(files_processed_count_));
        classes_documented_label_->setText("45");
        functions_documented_label_->setText("234");
        components_extracted_label_->setText(QString::number(components_found_count_));
        examples_found_label_->setText(QString::number(examples_found_count_));
        warnings_count_label_->setText("5");
        errors_count_label_->setText("0");
    }
}

void DocumentationGeneratorExample::exportDocumentation() {
    QString filename = QFileDialog::getSaveFileName(this, "Export Documentation",
                                                   "documentation_export.json",
                                                   "JSON Files (*.json)");
    if (!filename.isEmpty()) {
        // In a real implementation, call generator_->exportDocumentation(filename)
        logMessage("Documentation exported to: " + filename, "Info");
        showNotification("Success", "Documentation exported successfully!");
    }
}

void DocumentationGeneratorExample::populateConfigurationUI() {
    auto config = generator_->getConfiguration();
    setConfigurationToUI(config);
}

DeclarativeUI::Documentation::DocumentationGenerator::GeneratorConfig DocumentationGeneratorExample::getConfigurationFromUI() {
    DeclarativeUI::Documentation::DocumentationGenerator::GeneratorConfig config;

    config.project_name = project_name_edit_->text();
    config.project_version = project_version_edit_->text();
    config.project_description = project_description_edit_->toPlainText();
    config.output_directory = output_directory_edit_->text();

    config.source_directories = source_directories_edit_->text().split(',', Qt::SkipEmptyParts);
    config.example_directories = example_directories_edit_->text().split(',', Qt::SkipEmptyParts);

    config.theme = theme_combo_->currentText();
    config.generate_api_reference = generate_api_checkbox_->isChecked();
    config.generate_component_gallery = generate_components_checkbox_->isChecked();
    config.generate_examples = generate_examples_checkbox_->isChecked();
    config.generate_search_index = generate_search_checkbox_->isChecked();
    config.enable_live_examples = enable_live_demos_checkbox_->isChecked();
    config.parse_doxygen_comments = parse_doxygen_checkbox_->isChecked();

    return config;
}

void DocumentationGeneratorExample::setConfigurationToUI(const DeclarativeUI::Documentation::DocumentationGenerator::GeneratorConfig& config) {
    project_name_edit_->setText(config.project_name);
    project_version_edit_->setText(config.project_version);
    project_description_edit_->setPlainText(config.project_description);
    output_directory_edit_->setText(config.output_directory);

    source_directories_edit_->setText(config.source_directories.join(","));
    example_directories_edit_->setText(config.example_directories.join(","));

    theme_combo_->setCurrentText(config.theme);
    generate_api_checkbox_->setChecked(config.generate_api_reference);
    generate_components_checkbox_->setChecked(config.generate_component_gallery);
    generate_examples_checkbox_->setChecked(config.generate_examples);
    generate_search_checkbox_->setChecked(config.generate_search_index);
    enable_live_demos_checkbox_->setChecked(config.enable_live_examples);
    parse_doxygen_checkbox_->setChecked(config.parse_doxygen_comments);
}

void DocumentationGeneratorExample::populateDocumentationTree() {
    documentation_tree_->clear();

    QString output_dir = output_directory_edit_->text();
    if (!QDir(output_dir).exists()) {
        QTreeWidgetItem* item = new QTreeWidgetItem(documentation_tree_);
        item->setText(0, "No documentation generated");
        item->setText(1, "Info");
        item->setText(2, "Generate documentation first");
        return;
    }

    // Add API Reference
    QTreeWidgetItem* api_item = new QTreeWidgetItem(documentation_tree_);
    api_item->setText(0, "API Reference");
    api_item->setText(1, "Category");
    api_item->setText(2, "Complete API documentation");

    QString api_dir = output_dir + "/api";
    if (QDir(api_dir).exists()) {
        QDirIterator api_iterator(api_dir, {"*.html"}, QDir::Files);
        while (api_iterator.hasNext()) {
            QString file_path = api_iterator.next();
            QFileInfo file_info(file_path);

            QTreeWidgetItem* child = new QTreeWidgetItem(api_item);
            child->setText(0, file_info.baseName());
            child->setText(1, "API");
            child->setText(2, "API documentation");
            child->setData(0, Qt::UserRole, file_path);
        }
    }

    // Add Component Gallery
    QTreeWidgetItem* components_item = new QTreeWidgetItem(documentation_tree_);
    components_item->setText(0, "Component Gallery");
    components_item->setText(1, "Category");
    components_item->setText(2, "Interactive component showcase");

    QString components_dir = output_dir + "/components";
    if (QDir(components_dir).exists()) {
        QDirIterator comp_iterator(components_dir, {"*.html"}, QDir::Files);
        while (comp_iterator.hasNext()) {
            QString file_path = comp_iterator.next();
            QFileInfo file_info(file_path);

            QTreeWidgetItem* child = new QTreeWidgetItem(components_item);
            child->setText(0, file_info.baseName());
            child->setText(1, "Component");
            child->setText(2, "Component documentation");
            child->setData(0, Qt::UserRole, file_path);
        }
    }

    // Add Examples
    QTreeWidgetItem* examples_item = new QTreeWidgetItem(documentation_tree_);
    examples_item->setText(0, "Examples");
    examples_item->setText(1, "Category");
    examples_item->setText(2, "Code examples and tutorials");

    QString examples_dir = output_dir + "/examples";
    if (QDir(examples_dir).exists()) {
        QDirIterator ex_iterator(examples_dir, {"*.html"}, QDir::Files);
        while (ex_iterator.hasNext()) {
            QString file_path = ex_iterator.next();
            QFileInfo file_info(file_path);

            QTreeWidgetItem* child = new QTreeWidgetItem(examples_item);
            child->setText(0, file_info.baseName());
            child->setText(1, "Example");
            child->setText(2, "Example documentation");
            child->setData(0, Qt::UserRole, file_path);
        }
    }

    documentation_tree_->expandAll();
}

void DocumentationGeneratorExample::logMessage(const QString& message, const QString& type) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString formatted_message = QString("[%1] %2: %3").arg(timestamp, type, message);

    log_text_->append(formatted_message);
    log_text_->ensureCursorVisible();

    // Also output to debug console
    qDebug() << formatted_message;
}

void DocumentationGeneratorExample::updateProgress(int percentage, const QString& task) {
    progress_bar_->setValue(percentage);
    current_task_label_->setText(task);
    status_progress_->setValue(percentage);
}

void DocumentationGeneratorExample::showNotification(const QString& title, const QString& message, bool is_error) {
    if (is_error) {
        QMessageBox::critical(this, title, message);
    } else {
        QMessageBox::information(this, title, message);
    }
}

QString DocumentationGeneratorExample::formatFileSize(qint64 bytes) {
    const qint64 KB = 1024;
    const qint64 MB = KB * 1024;
    const qint64 GB = MB * 1024;

    if (bytes >= GB) {
        return QString::number(bytes / GB, 'f', 1) + " GB";
    } else if (bytes >= MB) {
        return QString::number(bytes / MB, 'f', 1) + " MB";
    } else if (bytes >= KB) {
        return QString::number(bytes / KB, 'f', 1) + " KB";
    } else {
        return QString::number(bytes) + " bytes";
    }
}

QString DocumentationGeneratorExample::formatDuration(qint64 milliseconds) {
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    if (minutes > 0) {
        return QString("%1m %2s").arg(minutes).arg(seconds);
    } else {
        return QString("%1.%2s").arg(seconds).arg(milliseconds % 1000, 3, 10, QChar('0'));
    }
}

// **Main function for the example**
int main(int argc, char* argv[]) {
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("DeclarativeUI Documentation Generator");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("DeclarativeUI Examples");

    try {
        qDebug() << "🚀 Starting DeclarativeUI Documentation Generator example...";

        DocumentationGeneratorExample example;
        example.show();

        qDebug() << "💡 Documentation Generator Features:";
        qDebug() << "   - Interactive configuration and generation";
        qDebug() << "   - Real-time progress monitoring";
        qDebug() << "   - Multiple output formats (HTML, Markdown, JSON)";
        qDebug() << "   - Component gallery with live demos";
        qDebug() << "   - API reference with cross-references";
        qDebug() << "   - Quality validation and metrics";
        qDebug() << "   - Live documentation server";
        qDebug() << "   - Incremental updates and hot reload";

        return app.exec();

    } catch (const std::exception& e) {
        qCritical() << "❌ Application error:" << e.what();
        return -1;
    }
}

#include "DocumentationGeneratorExample.moc"
