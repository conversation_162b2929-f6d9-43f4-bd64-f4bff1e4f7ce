#include "TranslationManager.hpp"
#include <QFileInfo>
#include <QJsonArray>
#include <QJsonParseError>
#include <QStandardPaths>
#include <QRegularExpression>
#include <QFileSystemWatcher>

namespace DeclarativeUI {
namespace I18n {

TranslationManager* TranslationManager::instance_ = nullptr;
QMutex TranslationManager::instance_mutex_;

TranslationManager& TranslationManager::instance() {
    QMutexLocker locker(&instance_mutex_);
    if (!instance_) {
        instance_ = new TranslationManager();
    }
    return *instance_;
}

TranslationManager::TranslationManager(QObject* parent)
    : QObject(parent)
    , current_language_("en")
    , current_locale_(QLocale::English)
    , hot_reload_enabled_(false)
    , cache_enabled_(true)
    , track_missing_translations_(true)
{
    // Set default fallback language
    fallback_languages_ << "en";
    
    qDebug() << "🌍 TranslationManager initialized";
}

bool TranslationManager::initialize(const QString& translations_dir) {
    translations_directory_ = translations_dir;
    
    // Create translations directory if it doesn't exist
    QDir dir(translations_directory_);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            qWarning() << "Failed to create translations directory:" << translations_directory_;
            return false;
        }
    }
    
    // Scan for available translation files
    scanForTranslationFiles();
    
    // Set system locale as default if available
    QLocale system_locale = QLocale::system();
    QString system_language = system_locale.name().left(2);
    
    if (available_languages_.contains(system_language)) {
        setLanguage(system_language);
    } else if (!available_languages_.isEmpty()) {
        setLanguage(available_languages_.first());
    }
    
    qDebug() << "🌍 Translation system initialized";
    qDebug() << "   Directory:" << translations_directory_;
    qDebug() << "   Available languages:" << available_languages_;
    qDebug() << "   Current language:" << current_language_;
    
    return true;
}

bool TranslationManager::setLanguage(const QString& language_code) {
    if (language_code == current_language_) {
        return true; // Already set
    }
    
    QString old_language = current_language_;
    
    // Validate language is available
    if (!available_languages_.contains(language_code)) {
        qWarning() << "Language not available:" << language_code;
        return false;
    }
    
    // Update current language
    current_language_ = language_code;
    current_locale_ = QLocale(language_code);
    
    // Clear cache to force reload
    clearCache();
    
    // Install Qt translators for .qm files
    auto it = qt_translators_.find(language_code);
    if (it != qt_translators_.end()) {
        QCoreApplication::installTranslator(it.value().get());
    }
    
    qDebug() << "🌍 Language changed from" << old_language << "to" << current_language_;
    emit languageChanged(current_language_, old_language);
    
    return true;
}

bool TranslationManager::setLanguage(const QLocale& locale) {
    return setLanguage(locale.name().left(2));
}

void TranslationManager::addFallbackLanguage(const QString& language_code) {
    if (!fallback_languages_.contains(language_code)) {
        fallback_languages_.append(language_code);
        qDebug() << "🌍 Added fallback language:" << language_code;
    }
}

void TranslationManager::setFallbackLanguages(const QStringList& languages) {
    fallback_languages_ = languages;
    qDebug() << "🌍 Set fallback languages:" << fallback_languages_;
}

bool TranslationManager::loadTranslationFile(const QString& file_path, const QString& language_code) {
    QFileInfo file_info(file_path);
    if (!file_info.exists()) {
        qWarning() << "Translation file not found:" << file_path;
        return false;
    }
    
    QString lang_code = language_code;
    if (lang_code.isEmpty()) {
        // Extract language code from filename
        QString basename = file_info.baseName();
        QRegularExpression lang_regex(R"(_([a-z]{2}(?:_[A-Z]{2})?)$)");
        auto match = lang_regex.match(basename);
        if (match.hasMatch()) {
            lang_code = match.captured(1).left(2);
        } else {
            qWarning() << "Could not determine language code from filename:" << file_path;
            return false;
        }
    }
    
    bool success = false;
    if (file_info.suffix().toLower() == "json") {
        success = loadJsonTranslationFile(file_path, lang_code);
    } else if (file_info.suffix().toLower() == "qm") {
        success = loadQtTranslationFile(file_path, lang_code);
    } else {
        qWarning() << "Unsupported translation file format:" << file_path;
        return false;
    }
    
    if (success && !available_languages_.contains(lang_code)) {
        available_languages_.append(lang_code);
        qDebug() << "🌍 Loaded translations for language:" << lang_code;
    }
    
    return success;
}

QString TranslationManager::translate(const QString& key, const TranslationParams& params) const {
    QString full_key = buildFullKey(key);
    QString result = resolveTranslation(full_key, params);
    
    if (result.isEmpty()) {
        if (track_missing_translations_ && !missing_translations_.contains(full_key)) {
            missing_translations_.append(full_key);
            emit const_cast<TranslationManager*>(this)->translationMissing(full_key, current_language_);
        }
        return key; // Return key as fallback
    }
    
    return result;
}

QString TranslationManager::translate(const QString& key, const QString& default_text, const TranslationParams& params) const {
    QString full_key = buildFullKey(key);
    QString result = resolveTranslation(full_key, params);
    
    if (result.isEmpty()) {
        if (track_missing_translations_ && !missing_translations_.contains(full_key)) {
            missing_translations_.append(full_key);
            emit const_cast<TranslationManager*>(this)->translationMissing(full_key, current_language_);
        }
        return default_text;
    }
    
    return result;
}

QString TranslationManager::translateWithContext(const TranslationContext& context, const QString& key, const TranslationParams& params) const {
    // Build context-aware key
    QString context_key = key;
    if (!context.namespace_prefix.isEmpty()) {
        context_key = context.namespace_prefix + "." + key;
    }
    if (!context.component_name.isEmpty()) {
        context_key = context.component_name + "." + context_key;
    }
    
    return translate(context_key, params);
}

QString TranslationManager::translatePlural(const QString& key, int count, const TranslationParams& params) const {
    TranslationParams plural_params = params;
    plural_params.count = count;
    
    // Try plural-specific key first
    PluralForm form = getPluralForm(count, current_language_);
    QString plural_key = key + ".plural." + QString::number(static_cast<int>(form));
    
    QString result = resolveTranslation(buildFullKey(plural_key), plural_params);
    if (!result.isEmpty()) {
        return result;
    }
    
    // Fallback to regular translation
    return translate(key, plural_params);
}

TranslationManager::TranslationContext TranslationManager::createContext(const QString& namespace_prefix, const QString& component_name) const {
    return TranslationContext(namespace_prefix, component_name);
}

void TranslationManager::pushContext(const TranslationContext& context) {
    context_stack_.push_back(context);
}

void TranslationManager::popContext() {
    if (!context_stack_.isEmpty()) {
        context_stack_.removeLast();
    }
}

TranslationManager::TranslationContext TranslationManager::getCurrentContext() const {
    return context_stack_.isEmpty() ? TranslationContext() : context_stack_.last();
}

bool TranslationManager::hasTranslation(const QString& key) const {
    QString full_key = buildFullKey(key);
    return !resolveTranslation(full_key, TranslationParams()).isEmpty();
}

void TranslationManager::enableHotReload(bool enable) {
    hot_reload_enabled_ = enable;
    if (enable) {
        // Set up file system watcher for hot reload
        // Implementation would go here
        qDebug() << "🌍 Hot reload enabled for translations";
    }
}

void TranslationManager::reloadTranslations() {
    translations_.clear();
    clearCache();
    scanForTranslationFiles();
    emit translationsReloaded();
    qDebug() << "🌍 Translations reloaded";
}

void TranslationManager::clearCache() {
    QMutexLocker locker(&cache_mutex_);
    translation_cache_.clear();
}

void TranslationManager::preloadTranslations(const QStringList& keys) {
    for (const QString& key : keys) {
        translate(key); // This will cache the translation
    }
}

// Private implementation methods

QString TranslationManager::resolveTranslation(const QString& full_key, const TranslationParams& params) const {
    // Check cache first
    if (cache_enabled_) {
        QMutexLocker locker(&cache_mutex_);
        auto cache_it = translation_cache_.find(full_key);
        if (cache_it != translation_cache_.end()) {
            return applyParameters(cache_it.value(), params);
        }
    }

    // Try current language first
    QString result = lookupTranslation(current_language_, full_key);

    // Try fallback languages if not found
    if (result.isEmpty()) {
        for (const QString& fallback_lang : fallback_languages_) {
            if (fallback_lang != current_language_) {
                result = lookupTranslation(fallback_lang, full_key);
                if (!result.isEmpty()) {
                    break;
                }
            }
        }
    }

    // Cache the result
    if (cache_enabled_ && !result.isEmpty()) {
        QMutexLocker locker(&cache_mutex_);
        translation_cache_[full_key] = result;
    }

    return applyParameters(result, params);
}

QString TranslationManager::lookupTranslation(const QString& language, const QString& key) const {
    auto lang_it = translations_.find(language);
    if (lang_it == translations_.end()) {
        return QString();
    }

    const QJsonObject& translations = lang_it.value();

    // Support nested keys with dot notation
    QStringList key_parts = key.split('.');
    QJsonValue current_value = translations;

    for (const QString& part : key_parts) {
        if (!current_value.isObject()) {
            return QString();
        }

        QJsonObject current_obj = current_value.toObject();
        if (!current_obj.contains(part)) {
            return QString();
        }

        current_value = current_obj[part];
    }

    return current_value.toString();
}

QString TranslationManager::applyParameters(const QString& text, const TranslationParams& params) const {
    if (text.isEmpty()) {
        return text;
    }

    QString result = text;

    // Apply named parameters {param_name}
    for (auto it = params.named_params.begin(); it != params.named_params.end(); ++it) {
        QString placeholder = QString("{%1}").arg(it.key());
        result.replace(placeholder, it.value().toString());
    }

    // Apply positional parameters {0}, {1}, etc.
    for (int i = 0; i < params.positional_params.size(); ++i) {
        QString placeholder = QString("{%1}").arg(i);
        result.replace(placeholder, params.positional_params[i].toString());
    }

    // Apply count parameter for pluralization
    if (params.count >= 0) {
        result.replace("{count}", QString::number(params.count));
        result.replace("{n}", QString::number(params.count));
    }

    return result;
}

TranslationManager::PluralForm TranslationManager::getPluralForm(int count, const QString& language) const {
    // Simplified pluralization rules - in a real implementation,
    // this would use ICU or similar for proper language-specific rules

    if (language == "en") {
        // English: 1 is singular, everything else is plural
        return (count == 1) ? PluralForm::One : PluralForm::Other;
    } else if (language == "ru" || language == "uk") {
        // Russian/Ukrainian: complex rules
        int mod10 = count % 10;
        int mod100 = count % 100;

        if (mod10 == 1 && mod100 != 11) {
            return PluralForm::One;
        } else if (mod10 >= 2 && mod10 <= 4 && (mod100 < 10 || mod100 >= 20)) {
            return PluralForm::Few;
        } else {
            return PluralForm::Many;
        }
    } else if (language == "pl") {
        // Polish: complex rules
        if (count == 1) {
            return PluralForm::One;
        } else if (count % 10 >= 2 && count % 10 <= 4 && (count % 100 < 10 || count % 100 >= 20)) {
            return PluralForm::Few;
        } else {
            return PluralForm::Many;
        }
    }

    // Default: treat 1 as singular, everything else as plural
    return (count == 1) ? PluralForm::One : PluralForm::Other;
}

QString TranslationManager::buildFullKey(const QString& key) const {
    if (context_stack_.isEmpty()) {
        return key;
    }

    const TranslationContext& context = context_stack_.last();
    QString full_key = key;

    if (!context.component_name.isEmpty()) {
        full_key = context.component_name + "." + full_key;
    }

    if (!context.namespace_prefix.isEmpty()) {
        full_key = context.namespace_prefix + "." + full_key;
    }

    return full_key;
}

bool TranslationManager::loadJsonTranslationFile(const QString& file_path, const QString& language_code) {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open translation file:" << file_path;
        return false;
    }

    QJsonParseError parse_error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &parse_error);

    if (parse_error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error in translation file" << file_path << ":" << parse_error.errorString();
        return false;
    }

    if (!doc.isObject()) {
        qWarning() << "Translation file must contain a JSON object:" << file_path;
        return false;
    }

    translations_[language_code] = doc.object();
    qDebug() << "🌍 Loaded JSON translations for" << language_code << "from" << file_path;

    return true;
}

bool TranslationManager::loadQtTranslationFile(const QString& file_path, const QString& language_code) {
    auto translator = std::make_unique<QTranslator>();

    if (!translator->load(file_path)) {
        qWarning() << "Could not load Qt translation file:" << file_path;
        return false;
    }

    qt_translators_[language_code] = std::move(translator);
    qDebug() << "🌍 Loaded Qt translations for" << language_code << "from" << file_path;

    return true;
}

void TranslationManager::scanForTranslationFiles() {
    QDir dir(translations_directory_);
    if (!dir.exists()) {
        qWarning() << "Translations directory does not exist:" << translations_directory_;
        return;
    }

    // Scan for JSON files
    QStringList json_filters;
    json_filters << "*.json";
    QFileInfoList json_files = dir.entryInfoList(json_filters, QDir::Files);

    for (const QFileInfo& file_info : json_files) {
        QString basename = file_info.baseName();
        QRegularExpression lang_regex(R"(_([a-z]{2}(?:_[A-Z]{2})?)$)");
        auto match = lang_regex.match(basename);

        if (match.hasMatch()) {
            QString lang_code = match.captured(1).left(2);
            loadTranslationFile(file_info.absoluteFilePath(), lang_code);
        }
    }

    // Scan for Qt .qm files
    QStringList qm_filters;
    qm_filters << "*.qm";
    QFileInfoList qm_files = dir.entryInfoList(qm_filters, QDir::Files);

    for (const QFileInfo& file_info : qm_files) {
        QString basename = file_info.baseName();
        QRegularExpression lang_regex(R"(_([a-z]{2}(?:_[A-Z]{2})?)$)");
        auto match = lang_regex.match(basename);

        if (match.hasMatch()) {
            QString lang_code = match.captured(1).left(2);
            loadTranslationFile(file_info.absoluteFilePath(), lang_code);
        }
    }

    qDebug() << "🌍 Scanned translations directory, found languages:" << available_languages_;
}

} // namespace I18n
} // namespace DeclarativeUI
