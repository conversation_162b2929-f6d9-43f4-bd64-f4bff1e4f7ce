#pragma once

#include "FileWatcher.hpp"
#include "AdvancedFileFilter.hpp"
#include "HotReloadConfig.hpp"
#include <QObject>
#include <memory>

namespace DeclarativeUI::HotReload {

/**
 * @brief Integration layer between FileWatcher and AdvancedFileFilter
 * 
 * This class bridges the existing FileWatcher with the new AdvancedFileFilter,
 * providing backward compatibility while enabling advanced filtering features.
 */
class FileFilterIntegration : public QObject {
    Q_OBJECT

public:
    explicit FileFilterIntegration(QObject* parent = nullptr);
    ~FileFilterIntegration() = default;

    // **FileWatcher integration**
    void setFileWatcher(FileWatcher* watcher);
    FileWatcher* getFileWatcher() const { return file_watcher_; }
    
    // **AdvancedFileFilter integration**
    void setAdvancedFilter(std::unique_ptr<AdvancedFileFilter> filter);
    AdvancedFileFilter* getAdvancedFilter() const { return advanced_filter_.get(); }
    
    // **Configuration integration**
    void setConfiguration(HotReloadConfig* config);
    void updateFromConfiguration();
    
    // **Filtering operations**
    bool shouldProcessFile(const QString& file_path) const;
    bool shouldProcessFile(const QString& file_path, const QFileInfo& file_info) const;
    QStringList filterFileList(const QStringList& files) const;
    
    // **Legacy FileFilter compatibility**
    void setLegacyFilter(const FileFilter& filter);
    FileFilter getLegacyFilter() const;
    void syncFiltersFromLegacy();
    void syncFiltersToLegacy();
    
    // **Performance and monitoring**
    void enablePerformanceMonitoring(bool enabled);
    FilterStats getFilteringStatistics() const;
    void resetStatistics();
    
    // **Preset management**
    void applyPreset(const QString& preset_name);
    QStringList getAvailablePresets() const;
    
    // **Rule management shortcuts**
    void addIncludeExtension(const QString& extension, int priority = 100);
    void addExcludePattern(const QString& pattern, int priority = 200);
    void addExcludeDirectory(const QString& directory, int priority = 250);
    void removeRule(const QString& pattern);
    
    // **Batch operations**
    void addCommonWebExtensions();
    void addCommonQtExtensions();
    void addCommonBuildExclusions();
    void addCommonIDEExclusions();
    
    // **Validation and diagnostics**
    bool validateConfiguration() const;
    QString getDiagnosticInfo() const;
    QStringList testFilterOnDirectory(const QString& directory_path) const;

signals:
    void filteringConfigurationChanged();
    void filteringStatisticsUpdated();
    void filteringError(const QString& error);
    void ruleAdded(const QString& pattern);
    void ruleRemoved(const QString& pattern);

private slots:
    void onConfigurationChanged();
    void onAdvancedFilterError(const QString& error);
    void onAdvancedFilterRuleAdded(const QString& pattern);
    void onAdvancedFilterRuleRemoved(const QString& pattern);

private:
    // **Core components**
    FileWatcher* file_watcher_ = nullptr;
    std::unique_ptr<AdvancedFileFilter> advanced_filter_;
    HotReloadConfig* config_ = nullptr;
    
    // **Legacy compatibility**
    FileFilter legacy_filter_;
    bool legacy_mode_enabled_ = false;
    
    // **Performance monitoring**
    bool performance_monitoring_enabled_ = true;
    
    // **Helper methods**
    void setupConnections();
    void updateLegacyFilterFromAdvanced();
    void updateAdvancedFilterFromLegacy();
    void applyConfigurationToFilter();
    FilterRule createRuleFromLegacyPattern(const QString& pattern, bool is_extension) const;
    
    // **Preset definitions**
    static QMap<QString, std::function<std::unique_ptr<AdvancedFileFilter>()>> createPresetMap();
};

/**
 * @brief Factory for creating integrated file filtering systems
 */
class IntegratedFilterFactory {
public:
    // **Create complete filtering system**
    static std::unique_ptr<FileFilterIntegration> createForWebDevelopment();
    static std::unique_ptr<FileFilterIntegration> createForQtDevelopment();
    static std::unique_ptr<FileFilterIntegration> createForGeneralDevelopment();
    static std::unique_ptr<FileFilterIntegration> createMinimal();
    
    // **Create from configuration**
    static std::unique_ptr<FileFilterIntegration> createFromConfig(const HotReloadConfig& config);
    static std::unique_ptr<FileFilterIntegration> createFromProfile(const ConfigProfile& profile);
    
    // **Migration helpers**
    static std::unique_ptr<FileFilterIntegration> migrateFromLegacyFilter(const FileFilter& legacy_filter);
    static FileFilter convertToLegacyFilter(const AdvancedFileFilter& advanced_filter);
};

/**
 * @brief Utility class for filter rule analysis and optimization
 */
class FilterAnalyzer {
public:
    struct AnalysisResult {
        int total_rules = 0;
        int include_rules = 0;
        int exclude_rules = 0;
        int redundant_rules = 0;
        int conflicting_rules = 0;
        QStringList optimization_suggestions;
        QStringList potential_issues;
        double estimated_performance_impact = 0.0;
    };
    
    static AnalysisResult analyzeFilter(const AdvancedFileFilter& filter);
    static QStringList findRedundantRules(const std::vector<FilterRule>& rules);
    static QStringList findConflictingRules(const std::vector<FilterRule>& rules);
    static QStringList generateOptimizationSuggestions(const AnalysisResult& analysis);
    static std::vector<FilterRule> optimizeRules(const std::vector<FilterRule>& rules);
};

} // namespace DeclarativeUI::HotReload
