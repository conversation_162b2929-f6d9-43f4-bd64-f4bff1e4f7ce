#include "HotReloadDashboard.hpp"
#include <QApplication>
#include <QHeaderView>
#include <QMessageBox>
#include <QFileDialog>
#include <QJsonDocument>
#include <QTextStream>
#include <QDateTime>
#include <QDebug>
#include <QSizePolicy>
#include <QFont>
#include <QPalette>
#include <QStyle>

#include <algorithm>
#include <chrono>

namespace DeclarativeUI::HotReload {

HotReloadDashboard::HotReloadDashboard(QWidget* parent)
    : QWidget(parent)
    , update_timer_(std::make_unique<QTimer>(this))
    , chart_colors_({"#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6", "#1abc9c"})
{
    setupUI();
    
    // **Setup update timer**
    connect(update_timer_.get(), &QTimer::timeout, this, &HotReloadDashboard::onUpdateTimer);
    update_timer_->setInterval(update_interval_ms_);
    
    // **Initialize session timer**
    session_timer_.start();
    current_metrics_.session_start_time = QDateTime::currentDateTime();
    
    // **Apply default theme**
    applyTheme(current_theme_);
    
    qDebug() << "🔥 Hot-Reload Dashboard initialized";
}

HotReloadDashboard::~HotReloadDashboard() {
    stopMonitoring();
}

void HotReloadDashboard::setupUI() {
    auto* main_layout = new QVBoxLayout(this);
    main_layout->setSpacing(10);
    main_layout->setContentsMargins(10, 10, 10, 10);
    
    // **Setup control panel**
    setupToolBar();
    main_layout->addWidget(control_panel_);
    
    // **Setup main tabs**
    main_tabs_ = new QTabWidget(this);
    main_tabs_->setTabPosition(QTabWidget::North);
    main_tabs_->setMovable(true);
    main_tabs_->setTabsClosable(false);
    
    setupOverviewTab();
    setupPerformanceTab();
    setupFileWatchTab();
    setupHistoryTab();
    setupConfigurationTab();
    setupDiagnosticsTab();
    
    main_layout->addWidget(main_tabs_);
    
    // **Setup status bar**
    setupStatusBar();
    main_layout->addWidget(createStatusBar());
    
    setLayout(main_layout);
    setWindowTitle("🔥 Hot-Reload Dashboard");
    setMinimumSize(1000, 700);
    resize(1200, 800);
}

void HotReloadDashboard::setupToolBar() {
    control_panel_ = new QWidget(this);
    auto* toolbar_layout = new QHBoxLayout(control_panel_);
    toolbar_layout->setSpacing(10);
    
    // **Control buttons**
    start_button_ = new QPushButton("▶ Start", control_panel_);
    start_button_->setToolTip("Start hot-reload monitoring");
    start_button_->setStyleSheet("QPushButton { background-color: #2ecc71; color: white; font-weight: bold; padding: 8px 16px; border-radius: 4px; }");
    connect(start_button_, &QPushButton::clicked, this, &HotReloadDashboard::onStartButtonClicked);
    
    stop_button_ = new QPushButton("⏹ Stop", control_panel_);
    stop_button_->setToolTip("Stop hot-reload monitoring");
    stop_button_->setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; padding: 8px 16px; border-radius: 4px; }");
    stop_button_->setEnabled(false);
    connect(stop_button_, &QPushButton::clicked, this, &HotReloadDashboard::onStopButtonClicked);
    
    pause_button_ = new QPushButton("⏸ Pause", control_panel_);
    pause_button_->setToolTip("Pause hot-reload monitoring");
    pause_button_->setStyleSheet("QPushButton { background-color: #f39c12; color: white; font-weight: bold; padding: 8px 16px; border-radius: 4px; }");
    pause_button_->setEnabled(false);
    connect(pause_button_, &QPushButton::clicked, this, &HotReloadDashboard::onPauseButtonClicked);
    
    reset_button_ = new QPushButton("🔄 Reset", control_panel_);
    reset_button_->setToolTip("Reset all metrics and history");
    connect(reset_button_, &QPushButton::clicked, this, &HotReloadDashboard::onResetButtonClicked);
    
    export_button_ = new QPushButton("📊 Export", control_panel_);
    export_button_->setToolTip("Export metrics and history to file");
    connect(export_button_, &QPushButton::clicked, this, &HotReloadDashboard::onExportButtonClicked);
    
    configure_button_ = new QPushButton("⚙ Configure", control_panel_);
    configure_button_->setToolTip("Open configuration dialog");
    connect(configure_button_, &QPushButton::clicked, this, &HotReloadDashboard::onConfigureButtonClicked);
    
    refresh_button_ = new QPushButton("🔄 Refresh", control_panel_);
    refresh_button_->setToolTip("Refresh dashboard display");
    connect(refresh_button_, &QPushButton::clicked, this, &HotReloadDashboard::onRefreshButtonClicked);
    
    // **Add buttons to layout**
    toolbar_layout->addWidget(start_button_);
    toolbar_layout->addWidget(stop_button_);
    toolbar_layout->addWidget(pause_button_);
    toolbar_layout->addSeparator();
    toolbar_layout->addWidget(reset_button_);
    toolbar_layout->addWidget(export_button_);
    toolbar_layout->addSeparator();
    toolbar_layout->addWidget(configure_button_);
    toolbar_layout->addWidget(refresh_button_);
    toolbar_layout->addStretch();
    
    // **Status indicators**
    monitoring_status_label_ = new QLabel("⚫ Stopped", control_panel_);
    monitoring_status_label_->setStyleSheet("QLabel { font-weight: bold; padding: 4px 8px; }");
    
    session_time_label_ = new QLabel("Session: 00:00:00", control_panel_);
    session_time_label_->setStyleSheet("QLabel { color: #7f8c8d; }");
    
    toolbar_layout->addWidget(monitoring_status_label_);
    toolbar_layout->addWidget(session_time_label_);
    
    control_panel_->setLayout(toolbar_layout);
}

QWidget* HotReloadDashboard::createStatusBar() {
    auto* status_widget = new QWidget(this);
    auto* status_layout = new QHBoxLayout(status_widget);
    status_layout->setContentsMargins(5, 5, 5, 5);
    
    // **Performance indicators**
    cpu_usage_bar_ = new QProgressBar(status_widget);
    cpu_usage_bar_->setMaximum(100);
    cpu_usage_bar_->setTextVisible(true);
    cpu_usage_bar_->setFormat("CPU: %p%");
    cpu_usage_bar_->setMaximumWidth(120);
    
    memory_usage_bar_ = new QProgressBar(status_widget);
    memory_usage_bar_->setMaximum(1024); // MB
    memory_usage_bar_->setTextVisible(true);
    memory_usage_bar_->setFormat("Memory: %v MB");
    memory_usage_bar_->setMaximumWidth(150);
    
    success_rate_label_ = new QLabel("Success Rate: 0%", status_widget);
    success_rate_label_->setStyleSheet("QLabel { color: #27ae60; font-weight: bold; }");
    
    total_operations_label_ = new QLabel("Operations: 0", status_widget);
    
    status_layout->addWidget(cpu_usage_bar_);
    status_layout->addWidget(memory_usage_bar_);
    status_layout->addWidget(success_rate_label_);
    status_layout->addWidget(total_operations_label_);
    status_layout->addStretch();
    
    return status_widget;
}

void HotReloadDashboard::setupOverviewTab() {
    overview_tab_ = new QWidget();
    auto* layout = new QVBoxLayout(overview_tab_);
    
    // **Key metrics grid**
    auto* metrics_frame = new QGroupBox("📊 Key Metrics", overview_tab_);
    auto* metrics_layout = new QGridLayout(metrics_frame);
    
    // **Create metric labels**
    watched_files_label_ = new QLabel("0", metrics_frame);
    watched_files_label_->setStyleSheet("QLabel { font-size: 24px; font-weight: bold; color: #3498db; }");
    auto* watched_files_title = new QLabel("Watched Files", metrics_frame);
    watched_files_title->setStyleSheet("QLabel { color: #7f8c8d; }");
    
    successful_reloads_label_ = new QLabel("0", metrics_frame);
    successful_reloads_label_->setStyleSheet("QLabel { font-size: 24px; font-weight: bold; color: #2ecc71; }");
    auto* successful_reloads_title = new QLabel("Successful Reloads", metrics_frame);
    successful_reloads_title->setStyleSheet("QLabel { color: #7f8c8d; }");
    
    failed_reloads_label_ = new QLabel("0", metrics_frame);
    failed_reloads_label_->setStyleSheet("QLabel { font-size: 24px; font-weight: bold; color: #e74c3c; }");
    auto* failed_reloads_title = new QLabel("Failed Reloads", metrics_frame);
    failed_reloads_title->setStyleSheet("QLabel { color: #7f8c8d; }");
    
    average_reload_time_label_ = new QLabel("0 ms", metrics_frame);
    average_reload_time_label_->setStyleSheet("QLabel { font-size: 24px; font-weight: bold; color: #f39c12; }");
    auto* average_reload_time_title = new QLabel("Average Reload Time", metrics_frame);
    average_reload_time_title->setStyleSheet("QLabel { color: #7f8c8d; }");
    
    last_reload_time_label_ = new QLabel("0 ms", metrics_frame);
    last_reload_time_label_->setStyleSheet("QLabel { font-size: 24px; font-weight: bold; color: #9b59b6; }");
    auto* last_reload_time_title = new QLabel("Last Reload Time", metrics_frame);
    last_reload_time_title->setStyleSheet("QLabel { color: #7f8c8d; }");
    
    performance_score_bar_ = new QProgressBar(metrics_frame);
    performance_score_bar_->setMaximum(100);
    performance_score_bar_->setTextVisible(true);
    performance_score_bar_->setFormat("Performance Score: %p%");
    performance_score_bar_->setStyleSheet("QProgressBar::chunk { background-color: #1abc9c; }");
    auto* performance_score_title = new QLabel("Overall Performance", metrics_frame);
    performance_score_title->setStyleSheet("QLabel { color: #7f8c8d; }");
    
    // **Layout metrics in grid**
    metrics_layout->addWidget(watched_files_label_, 0, 0, Qt::AlignCenter);
    metrics_layout->addWidget(watched_files_title, 1, 0, Qt::AlignCenter);
    metrics_layout->addWidget(successful_reloads_label_, 0, 1, Qt::AlignCenter);
    metrics_layout->addWidget(successful_reloads_title, 1, 1, Qt::AlignCenter);
    metrics_layout->addWidget(failed_reloads_label_, 0, 2, Qt::AlignCenter);
    metrics_layout->addWidget(failed_reloads_title, 1, 2, Qt::AlignCenter);
    metrics_layout->addWidget(average_reload_time_label_, 2, 0, Qt::AlignCenter);
    metrics_layout->addWidget(average_reload_time_title, 3, 0, Qt::AlignCenter);
    metrics_layout->addWidget(last_reload_time_label_, 2, 1, Qt::AlignCenter);
    metrics_layout->addWidget(last_reload_time_title, 3, 1, Qt::AlignCenter);
    metrics_layout->addWidget(performance_score_bar_, 2, 2);
    metrics_layout->addWidget(performance_score_title, 3, 2, Qt::AlignCenter);
    
    layout->addWidget(metrics_frame);
    
    // **Recent activity**
    auto* activity_frame = new QGroupBox("🕒 Recent Activity", overview_tab_);
    auto* activity_layout = new QVBoxLayout(activity_frame);
    
    recent_changes_list_ = new QListWidget(activity_frame);
    recent_changes_list_->setMaximumHeight(200);
    activity_layout->addWidget(recent_changes_list_);
    
    layout->addWidget(activity_frame);
    layout->addStretch();
    
    main_tabs_->addTab(overview_tab_, "📊 Overview");
}

void HotReloadDashboard::setupPerformanceTab() {
    performance_tab_ = new QWidget();
    auto* layout = new QVBoxLayout(performance_tab_);
    
    // **Performance charts placeholder**
    auto* charts_frame = new QGroupBox("📈 Performance Charts", performance_tab_);
    auto* charts_layout = new QVBoxLayout(charts_frame);
    
    performance_chart_widget_ = new QWidget(charts_frame);
    performance_chart_widget_->setMinimumHeight(300);
    performance_chart_widget_->setStyleSheet("QWidget { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; }");
    
    auto* chart_placeholder = new QLabel("📈 Performance charts will be displayed here\n(Requires Qt Charts module)", performance_chart_widget_);
    chart_placeholder->setAlignment(Qt::AlignCenter);
    chart_placeholder->setStyleSheet("QLabel { color: #6c757d; font-size: 14px; }");
    
    auto* chart_layout = new QVBoxLayout(performance_chart_widget_);
    chart_layout->addWidget(chart_placeholder);
    
    charts_layout->addWidget(performance_chart_widget_);
    layout->addWidget(charts_frame);
    
    // **Performance metrics table**
    auto* metrics_frame = new QGroupBox("📋 Performance Metrics", performance_tab_);
    auto* metrics_layout = new QVBoxLayout(metrics_frame);
    
    performance_metrics_table_ = new QTableWidget(0, 4, metrics_frame);
    performance_metrics_table_->setHorizontalHeaderLabels({"Metric", "Current", "Average", "Peak"});
    performance_metrics_table_->horizontalHeader()->setStretchLastSection(true);
    performance_metrics_table_->setAlternatingRowColors(true);
    performance_metrics_table_->setSelectionBehavior(QAbstractItemView::SelectRows);
    
    metrics_layout->addWidget(performance_metrics_table_);
    layout->addWidget(metrics_frame);
    
    main_tabs_->addTab(performance_tab_, "📈 Performance");
}

void HotReloadDashboard::setupFileWatchTab() {
    file_watch_tab_ = new QWidget();
    auto* layout = new QVBoxLayout(file_watch_tab_);
    
    // **Watched files table**
    auto* files_frame = new QGroupBox("👁 Watched Files", file_watch_tab_);
    auto* files_layout = new QVBoxLayout(files_frame);
    
    watched_files_table_ = new QTableWidget(0, 6, files_frame);
    watched_files_table_->setHorizontalHeaderLabels({"File Path", "Status", "Last Modified", "Size", "Changes", "Filter Status"});
    watched_files_table_->horizontalHeader()->setStretchLastSection(true);
    watched_files_table_->setAlternatingRowColors(true);
    watched_files_table_->setSelectionBehavior(QAbstractItemView::SelectRows);
    connect(watched_files_table_, &QTableWidget::itemSelectionChanged, this, &HotReloadDashboard::onWatchedFileSelected);
    
    files_layout->addWidget(watched_files_table_);
    layout->addWidget(files_frame);
    
    // **Filter status**
    auto* filter_frame = new QGroupBox("🔍 Filter Status", file_watch_tab_);
    auto* filter_layout = new QVBoxLayout(filter_frame);
    
    file_filter_status_ = new QTextEdit(filter_frame);
    file_filter_status_->setMaximumHeight(150);
    file_filter_status_->setReadOnly(true);
    filter_layout->addWidget(file_filter_status_);
    
    layout->addWidget(filter_frame);
    
    main_tabs_->addTab(file_watch_tab_, "👁 File Watch");
}

void HotReloadDashboard::setupHistoryTab() {
    history_tab_ = new QWidget();
    auto* layout = new QVBoxLayout(history_tab_);
    
    // **History table**
    auto* history_frame = new QGroupBox("📜 Reload History", history_tab_);
    auto* history_layout = new QVBoxLayout(history_frame);
    
    reload_history_table_ = new QTableWidget(0, 6, history_frame);
    reload_history_table_->setHorizontalHeaderLabels({"Timestamp", "File", "Duration", "Status", "Size", "Type"});
    reload_history_table_->horizontalHeader()->setStretchLastSection(true);
    reload_history_table_->setAlternatingRowColors(true);
    reload_history_table_->setSelectionBehavior(QAbstractItemView::SelectRows);
    reload_history_table_->setSortingEnabled(true);
    connect(reload_history_table_, &QTableWidget::itemSelectionChanged, this, &HotReloadDashboard::onHistoryItemSelected);
    
    history_layout->addWidget(reload_history_table_);
    layout->addWidget(history_frame);
    
    // **History details**
    auto* details_frame = new QGroupBox("📝 Details", history_tab_);
    auto* details_layout = new QVBoxLayout(details_frame);
    
    history_details_ = new QTextEdit(details_frame);
    history_details_->setMaximumHeight(150);
    history_details_->setReadOnly(true);
    details_layout->addWidget(history_details_);
    
    layout->addWidget(details_frame);
    
    main_tabs_->addTab(history_tab_, "📜 History");
}

void HotReloadDashboard::setupConfigurationTab() {
    configuration_tab_ = new QWidget();
    auto* layout = new QVBoxLayout(configuration_tab_);
    
    // **Update settings**
    auto* update_frame = new QGroupBox("🔄 Update Settings", configuration_tab_);
    auto* update_layout = new QGridLayout(update_frame);
    
    update_layout->addWidget(new QLabel("Update Interval (ms):"), 0, 0);
    update_interval_spin_ = new QSpinBox(update_frame);
    update_interval_spin_->setRange(100, 10000);
    update_interval_spin_->setValue(update_interval_ms_);
    update_interval_spin_->setSuffix(" ms");
    connect(update_interval_spin_, QOverload<int>::of(&QSpinBox::valueChanged), this, [this](int value) {
        setUpdateInterval(value);
    });
    update_layout->addWidget(update_interval_spin_, 0, 1);
    
    update_layout->addWidget(new QLabel("Max History Size:"), 1, 0);
    max_history_spin_ = new QSpinBox(update_frame);
    max_history_spin_->setRange(100, 10000);
    max_history_spin_->setValue(max_history_size_);
    connect(max_history_spin_, QOverload<int>::of(&QSpinBox::valueChanged), this, [this](int value) {
        setMaxHistorySize(value);
    });
    update_layout->addWidget(max_history_spin_, 1, 1);
    
    real_time_updates_checkbox_ = new QCheckBox("Enable Real-time Updates", update_frame);
    real_time_updates_checkbox_->setChecked(real_time_updates_enabled_);
    connect(real_time_updates_checkbox_, &QCheckBox::toggled, this, [this](bool checked) {
        enableRealTimeUpdates(checked);
    });
    update_layout->addWidget(real_time_updates_checkbox_, 2, 0, 1, 2);
    
    layout->addWidget(update_frame);
    
    // **Performance thresholds**
    auto* thresholds_frame = new QGroupBox("⚠ Performance Thresholds", configuration_tab_);
    auto* thresholds_layout = new QGridLayout(thresholds_frame);
    
    thresholds_layout->addWidget(new QLabel("CPU Threshold (%):"), 0, 0);
    cpu_threshold_slider_ = new QSlider(Qt::Horizontal, thresholds_frame);
    cpu_threshold_slider_->setRange(10, 100);
    cpu_threshold_slider_->setValue(static_cast<int>(cpu_threshold_));
    auto* cpu_threshold_label = new QLabel(QString::number(cpu_threshold_, 'f', 1) + "%", thresholds_frame);
    connect(cpu_threshold_slider_, &QSlider::valueChanged, this, [this, cpu_threshold_label](int value) {
        cpu_threshold_ = value;
        cpu_threshold_label->setText(QString::number(value) + "%");
        checkThresholds();
    });
    thresholds_layout->addWidget(cpu_threshold_slider_, 0, 1);
    thresholds_layout->addWidget(cpu_threshold_label, 0, 2);
    
    thresholds_layout->addWidget(new QLabel("Memory Threshold (MB):"), 1, 0);
    memory_threshold_slider_ = new QSlider(Qt::Horizontal, thresholds_frame);
    memory_threshold_slider_->setRange(64, 2048);
    memory_threshold_slider_->setValue(static_cast<int>(memory_threshold_mb_));
    auto* memory_threshold_label = new QLabel(QString::number(memory_threshold_mb_) + " MB", thresholds_frame);
    connect(memory_threshold_slider_, &QSlider::valueChanged, this, [this, memory_threshold_label](int value) {
        memory_threshold_mb_ = value;
        memory_threshold_label->setText(QString::number(value) + " MB");
        checkThresholds();
    });
    thresholds_layout->addWidget(memory_threshold_slider_, 1, 1);
    thresholds_layout->addWidget(memory_threshold_label, 1, 2);
    
    thresholds_layout->addWidget(new QLabel("Reload Time Threshold (ms):"), 2, 0);
    reload_time_threshold_slider_ = new QSlider(Qt::Horizontal, thresholds_frame);
    reload_time_threshold_slider_->setRange(100, 5000);
    reload_time_threshold_slider_->setValue(static_cast<int>(reload_time_threshold_ms_));
    auto* reload_time_threshold_label = new QLabel(QString::number(reload_time_threshold_ms_) + " ms", thresholds_frame);
    connect(reload_time_threshold_slider_, &QSlider::valueChanged, this, [this, reload_time_threshold_label](int value) {
        reload_time_threshold_ms_ = value;
        reload_time_threshold_label->setText(QString::number(value) + " ms");
        checkThresholds();
    });
    thresholds_layout->addWidget(reload_time_threshold_slider_, 2, 1);
    thresholds_layout->addWidget(reload_time_threshold_label, 2, 2);
    
    layout->addWidget(thresholds_frame);
    
    // **Visual settings**
    auto* visual_frame = new QGroupBox("🎨 Visual Settings", configuration_tab_);
    auto* visual_layout = new QGridLayout(visual_frame);
    
    visual_layout->addWidget(new QLabel("Theme:"), 0, 0);
    theme_combo_ = new QComboBox(visual_frame);
    theme_combo_->addItems({"default", "dark", "light", "blue", "green"});
    theme_combo_->setCurrentText(current_theme_);
    connect(theme_combo_, &QComboBox::currentTextChanged, this, [this](const QString& theme) {
        setTheme(theme);
    });
    visual_layout->addWidget(theme_combo_, 0, 1);
    
    layout->addWidget(visual_frame);
    layout->addStretch();
    
    main_tabs_->addTab(configuration_tab_, "⚙ Configuration");
}

void HotReloadDashboard::setupDiagnosticsTab() {
    diagnostics_tab_ = new QWidget();
    auto* layout = new QVBoxLayout(diagnostics_tab_);
    
    // **System status**
    auto* status_frame = new QGroupBox("🔧 System Status", diagnostics_tab_);
    auto* status_layout = new QVBoxLayout(status_frame);
    
    system_status_label_ = new QLabel("System status will be displayed here", status_frame);
    system_status_label_->setWordWrap(true);
    status_layout->addWidget(system_status_label_);
    
    layout->addWidget(status_frame);
    
    // **Error console**
    auto* error_frame = new QGroupBox("🚨 Error Console", diagnostics_tab_);
    auto* error_layout = new QVBoxLayout(error_frame);
    
    error_console_ = new QTextEdit(error_frame);
    error_console_->setReadOnly(true);
    error_console_->setMaximumHeight(200);
    error_layout->addWidget(error_console_);
    
    layout->addWidget(error_frame);
    
    // **Diagnostics table**
    auto* diagnostics_frame = new QGroupBox("📊 Diagnostics", diagnostics_tab_);
    auto* diagnostics_layout = new QVBoxLayout(diagnostics_frame);
    
    diagnostics_table_ = new QTableWidget(0, 3, diagnostics_frame);
    diagnostics_table_->setHorizontalHeaderLabels({"Component", "Status", "Details"});
    diagnostics_table_->horizontalHeader()->setStretchLastSection(true);
    diagnostics_table_->setAlternatingRowColors(true);
    
    diagnostics_layout->addWidget(diagnostics_table_);
    layout->addWidget(diagnostics_frame);
    
    main_tabs_->addTab(diagnostics_tab_, "🔧 Diagnostics");
}

// **Integration methods**
void HotReloadDashboard::setHotReloadManager(HotReloadManager* manager) {
    hot_reload_manager_ = manager;
    if (manager) {
        connect(manager, &HotReloadManager::reloadStarted, this, &HotReloadDashboard::onReloadStarted);
        connect(manager, &HotReloadManager::reloadCompleted, this, [this](const QString& file_path) {
            onReloadCompleted(file_path, 0); // Duration will be calculated from performance monitor
        });
        connect(manager, &HotReloadManager::reloadFailed, this, [this](const QString& file_path, const QString& error) {
            onReloadFailed(file_path, error);
        });
        qDebug() << "🔥 Dashboard connected to HotReloadManager";
    }
}

void HotReloadDashboard::setPerformanceMonitor(PerformanceMonitor* monitor) {
    performance_monitor_ = monitor;
    if (monitor) {
        connect(monitor, &PerformanceMonitor::performanceWarning, this, &HotReloadDashboard::onPerformanceWarning);
        qDebug() << "🔥 Dashboard connected to PerformanceMonitor";
    }
}

void HotReloadDashboard::setFileWatcher(FileWatcher* watcher) {
    file_watcher_ = watcher;
    if (watcher) {
        connect(watcher, &FileWatcher::fileChanged, this, &HotReloadDashboard::onFileChanged);
        qDebug() << "🔥 Dashboard connected to FileWatcher";
    }
}

void HotReloadDashboard::setConfiguration(HotReloadConfig* config) {
    config_ = config;
    if (config) {
        connect(config, &HotReloadConfig::configurationChanged, this, &HotReloadDashboard::onConfigurationUpdated);
        qDebug() << "🔥 Dashboard connected to HotReloadConfig";
    }
}

void HotReloadDashboard::setAdvancedFilter(AdvancedFileFilter* filter) {
    advanced_filter_ = filter;
    if (filter) {
        connect(filter, &AdvancedFileFilter::filteringError, this, [this](const QString& error) {
            onPerformanceWarning("Filter Error: " + error);
        });
        qDebug() << "🔥 Dashboard connected to AdvancedFileFilter";
    }
}

// **Dashboard control methods**
void HotReloadDashboard::startMonitoring() {
    if (monitoring_enabled_) return;

    monitoring_enabled_ = true;
    data_collection_active_.store(true);

    if (real_time_updates_enabled_) {
        update_timer_->start();
    }

    session_timer_.restart();
    current_metrics_.session_start_time = QDateTime::currentDateTime();

    // **Update UI state**
    start_button_->setEnabled(false);
    stop_button_->setEnabled(true);
    pause_button_->setEnabled(true);
    monitoring_status_label_->setText("🟢 Running");
    monitoring_status_label_->setStyleSheet("QLabel { color: #2ecc71; font-weight: bold; }");

    emit monitoringStarted();
    qDebug() << "🔥 Dashboard monitoring started";
}

void HotReloadDashboard::stopMonitoring() {
    if (!monitoring_enabled_) return;

    monitoring_enabled_ = false;
    data_collection_active_.store(false);
    update_timer_->stop();

    // **Update UI state**
    start_button_->setEnabled(true);
    stop_button_->setEnabled(false);
    pause_button_->setEnabled(false);
    monitoring_status_label_->setText("⚫ Stopped");
    monitoring_status_label_->setStyleSheet("QLabel { color: #e74c3c; font-weight: bold; }");

    emit monitoringStopped();
    qDebug() << "🔥 Dashboard monitoring stopped";
}

void HotReloadDashboard::pauseMonitoring() {
    if (!monitoring_enabled_) return;

    update_timer_->stop();
    data_collection_active_.store(false);

    monitoring_status_label_->setText("⏸ Paused");
    monitoring_status_label_->setStyleSheet("QLabel { color: #f39c12; font-weight: bold; }");

    emit monitoringPaused();
    qDebug() << "🔥 Dashboard monitoring paused";
}

void HotReloadDashboard::resetMetrics() {
    std::lock_guard<std::mutex> metrics_lock(metrics_mutex_);
    std::lock_guard<std::mutex> history_lock(history_mutex_);

    // **Reset metrics**
    current_metrics_ = DashboardMetrics{};
    current_metrics_.session_start_time = QDateTime::currentDateTime();

    // **Clear history**
    reload_history_.clear();
    file_watch_status_.clear();
    error_log_.clear();
    metrics_history_.clear();

    // **Reset UI**
    reload_history_table_->setRowCount(0);
    watched_files_table_->setRowCount(0);
    recent_changes_list_->clear();
    error_console_->clear();
    history_details_->clear();
    file_filter_status_->clear();

    // **Restart session timer**
    session_timer_.restart();

    refreshDisplay();
    qDebug() << "🔥 Dashboard metrics reset";
}

void HotReloadDashboard::refreshDisplay() {
    updateMetrics();
    updatePerformanceCharts();
    updateFileWatchStatus();
    updateReloadHistory();
    updateErrorLog();
    updateStatusIndicators();
}

// **Configuration methods**
void HotReloadDashboard::setUpdateInterval(int milliseconds) {
    update_interval_ms_ = milliseconds;
    update_timer_->setInterval(milliseconds);
    qDebug() << "🔥 Dashboard update interval set to" << milliseconds << "ms";
}

void HotReloadDashboard::setMaxHistorySize(int max_entries) {
    max_history_size_ = max_entries;

    // **Trim existing history if needed**
    std::lock_guard<std::mutex> lock(history_mutex_);
    while (reload_history_.size() > static_cast<size_t>(max_entries)) {
        reload_history_.pop_front();
    }
    while (metrics_history_.size() > static_cast<size_t>(max_entries)) {
        metrics_history_.pop_front();
    }

    qDebug() << "🔥 Dashboard max history size set to" << max_entries;
}

void HotReloadDashboard::enableRealTimeUpdates(bool enabled) {
    real_time_updates_enabled_ = enabled;

    if (enabled && monitoring_enabled_) {
        update_timer_->start();
    } else {
        update_timer_->stop();
    }

    qDebug() << "🔥 Dashboard real-time updates" << (enabled ? "enabled" : "disabled");
}

void HotReloadDashboard::setPerformanceThresholds(double cpu_threshold, qint64 memory_threshold_mb, qint64 reload_time_threshold_ms) {
    cpu_threshold_ = cpu_threshold;
    memory_threshold_mb_ = memory_threshold_mb;
    reload_time_threshold_ms_ = reload_time_threshold_ms;

    // **Update UI controls**
    cpu_threshold_slider_->setValue(static_cast<int>(cpu_threshold));
    memory_threshold_slider_->setValue(static_cast<int>(memory_threshold_mb));
    reload_time_threshold_slider_->setValue(static_cast<int>(reload_time_threshold_ms));

    checkThresholds();
    qDebug() << "🔥 Dashboard thresholds updated: CPU" << cpu_threshold << "%, Memory" << memory_threshold_mb << "MB, Reload" << reload_time_threshold_ms << "ms";
}

// **Data export methods**
void HotReloadDashboard::exportMetricsToFile(const QString& file_path) {
    QFile file(file_path);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "Export Error", "Could not open file for writing: " + file_path);
        return;
    }

    QTextStream stream(&file);

    if (file_path.endsWith(".json")) {
        // **Export as JSON**
        QJsonDocument doc(generateReport());
        stream << doc.toJson();
    } else {
        // **Export as text**
        stream << generateTextReport();
    }

    emit exportCompleted(file_path);
    QMessageBox::information(this, "Export Complete", "Metrics exported to: " + file_path);
}

QJsonObject HotReloadDashboard::generateReport() const {
    std::lock_guard<std::mutex> metrics_lock(metrics_mutex_);
    std::lock_guard<std::mutex> history_lock(history_mutex_);

    QJsonObject report;

    // **Session information**
    QJsonObject session;
    session["start_time"] = current_metrics_.session_start_time.toString(Qt::ISODate);
    session["duration_ms"] = session_timer_.elapsed();
    session["monitoring_enabled"] = monitoring_enabled_;
    report["session"] = session;

    // **Current metrics**
    QJsonObject metrics;
    metrics["watched_files"] = current_metrics_.watched_files_count;
    metrics["successful_reloads"] = current_metrics_.successful_reloads;
    metrics["failed_reloads"] = current_metrics_.failed_reloads;
    metrics["success_rate"] = current_metrics_.success_rate;
    metrics["average_reload_time_ms"] = static_cast<qint64>(current_metrics_.average_reload_time_ms);
    metrics["cpu_usage_percent"] = current_metrics_.cpu_usage_percent;
    metrics["memory_usage_mb"] = static_cast<qint64>(current_metrics_.memory_usage_mb);
    report["metrics"] = metrics;

    // **History**
    QJsonArray history;
    for (const auto& entry : reload_history_) {
        QJsonObject hist_entry;
        hist_entry["file_path"] = entry.file_path;
        hist_entry["timestamp"] = entry.timestamp.toString(Qt::ISODate);
        hist_entry["duration_ms"] = static_cast<qint64>(entry.duration_ms);
        hist_entry["success"] = entry.success;
        hist_entry["error_message"] = entry.error_message;
        history.append(hist_entry);
    }
    report["history"] = history;

    return report;
}

QString HotReloadDashboard::generateTextReport() const {
    QStringList lines;

    lines << "=== Hot-Reload Dashboard Report ===";
    lines << QString("Generated: %1").arg(QDateTime::currentDateTime().toString());
    lines << QString("Session Start: %1").arg(current_metrics_.session_start_time.toString());
    lines << QString("Session Duration: %1").arg(formatDuration(session_timer_.elapsed()));
    lines << "";

    lines << "=== Current Metrics ===";
    lines << QString("Watched Files: %1").arg(current_metrics_.watched_files_count);
    lines << QString("Successful Reloads: %1").arg(current_metrics_.successful_reloads);
    lines << QString("Failed Reloads: %1").arg(current_metrics_.failed_reloads);
    lines << QString("Success Rate: %1").arg(formatPercentage(current_metrics_.success_rate));
    lines << QString("Average Reload Time: %1").arg(formatDuration(current_metrics_.average_reload_time_ms));
    lines << QString("CPU Usage: %1").arg(formatPercentage(current_metrics_.cpu_usage_percent));
    lines << QString("Memory Usage: %1").arg(formatFileSize(current_metrics_.memory_usage_mb * 1024 * 1024));
    lines << "";

    lines << "=== Recent History ===";
    std::lock_guard<std::mutex> lock(history_mutex_);
    for (const auto& entry : reload_history_) {
        QString status = entry.success ? "✓" : "✗";
        lines << QString("%1 %2 - %3 (%4)")
                 .arg(status)
                 .arg(entry.timestamp.toString("hh:mm:ss"))
                 .arg(QFileInfo(entry.file_path).fileName())
                 .arg(formatDuration(entry.duration_ms));
    }

    return lines.join("\n");
}

// **Event handlers and slots**
void HotReloadDashboard::onUpdateTimer() {
    if (!data_collection_active_.load()) return;

    collectMetrics();
    updateMetrics();
    updateStatusIndicators();

    // **Update session time**
    qint64 elapsed = session_timer_.elapsed();
    current_metrics_.session_duration_ms = elapsed;

    int hours = elapsed / (1000 * 60 * 60);
    int minutes = (elapsed % (1000 * 60 * 60)) / (1000 * 60);
    int seconds = (elapsed % (1000 * 60)) / 1000;

    session_time_label_->setText(QString("Session: %1:%2:%3")
                                .arg(hours, 2, 10, QChar('0'))
                                .arg(minutes, 2, 10, QChar('0'))
                                .arg(seconds, 2, 10, QChar('0')));
}

void HotReloadDashboard::onReloadStarted(const QString& file_path) {
    current_metrics_.total_operations++;

    // **Add to recent activity**
    QString activity = QString("🔄 Reloading: %1").arg(QFileInfo(file_path).fileName());
    recent_changes_list_->insertItem(0, activity);

    // **Limit recent activity list**
    while (recent_changes_list_->count() > 20) {
        delete recent_changes_list_->takeItem(recent_changes_list_->count() - 1);
    }
}

void HotReloadDashboard::onReloadCompleted(const QString& file_path, qint64 duration_ms) {
    current_metrics_.successful_reloads++;
    current_metrics_.total_file_changes++;
    current_metrics_.last_reload_time_ms = duration_ms;

    // **Update average reload time**
    if (current_metrics_.successful_reloads > 0) {
        current_metrics_.average_reload_time_ms =
            (current_metrics_.average_reload_time_ms * (current_metrics_.successful_reloads - 1) + duration_ms) /
            current_metrics_.successful_reloads;
    }

    // **Update fastest/slowest times**
    if (current_metrics_.fastest_reload_ms == 0 || duration_ms < current_metrics_.fastest_reload_ms) {
        current_metrics_.fastest_reload_ms = duration_ms;
    }
    if (duration_ms > current_metrics_.slowest_reload_ms) {
        current_metrics_.slowest_reload_ms = duration_ms;
    }

    // **Update success rate**
    int total_reloads = current_metrics_.successful_reloads + current_metrics_.failed_reloads;
    if (total_reloads > 0) {
        current_metrics_.success_rate = static_cast<double>(current_metrics_.successful_reloads) / total_reloads;
    }

    // **Add to history**
    std::lock_guard<std::mutex> lock(history_mutex_);
    ReloadHistoryEntry entry;
    entry.file_path = file_path;
    entry.timestamp = QDateTime::currentDateTime();
    entry.duration_ms = duration_ms;
    entry.success = true;
    entry.operation_type = "reload";
    entry.file_size_bytes = QFileInfo(file_path).size();

    reload_history_.push_front(entry);

    // **Limit history size**
    while (reload_history_.size() > static_cast<size_t>(max_history_size_)) {
        reload_history_.pop_back();
    }

    // **Update recent activity**
    QString activity = QString("✅ Completed: %1 (%2)")
                      .arg(QFileInfo(file_path).fileName())
                      .arg(formatDuration(duration_ms));
    recent_changes_list_->item(0)->setText(activity);

    // **Check thresholds**
    if (duration_ms > reload_time_threshold_ms_) {
        emit thresholdExceeded("reload_time", duration_ms, reload_time_threshold_ms_);
    }

    updateReloadHistory();
}

void HotReloadDashboard::onReloadFailed(const QString& file_path, const QString& error) {
    current_metrics_.failed_reloads++;
    current_metrics_.error_count++;
    current_metrics_.last_error = error;
    current_metrics_.last_error_time = QDateTime::currentDateTime();

    // **Update success rate**
    int total_reloads = current_metrics_.successful_reloads + current_metrics_.failed_reloads;
    if (total_reloads > 0) {
        current_metrics_.success_rate = static_cast<double>(current_metrics_.successful_reloads) / total_reloads;
    }

    // **Add to history**
    std::lock_guard<std::mutex> lock(history_mutex_);
    ReloadHistoryEntry entry;
    entry.file_path = file_path;
    entry.timestamp = QDateTime::currentDateTime();
    entry.duration_ms = 0;
    entry.success = false;
    entry.error_message = error;
    entry.operation_type = "reload";
    entry.file_size_bytes = QFileInfo(file_path).size();

    reload_history_.push_front(entry);

    // **Limit history size**
    while (reload_history_.size() > static_cast<size_t>(max_history_size_)) {
        reload_history_.pop_back();
    }

    // **Update recent activity**
    QString activity = QString("❌ Failed: %1 - %2")
                      .arg(QFileInfo(file_path).fileName())
                      .arg(error);
    recent_changes_list_->item(0)->setText(activity);

    // **Add to error log**
    QString error_entry = QString("[%1] %2: %3")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                         .arg(QFileInfo(file_path).fileName())
                         .arg(error);
    error_log_.push_front(error_entry);

    // **Limit error log size**
    while (error_log_.size() > 100) {
        error_log_.pop_back();
    }

    updateReloadHistory();
    updateErrorLog();
}

void HotReloadDashboard::onFileChanged(const QString& file_path) {
    // **Update file watch status**
    bool found = false;
    for (auto& status : file_watch_status_) {
        if (status.file_path == file_path) {
            status.last_change_time = QDateTime::currentDateTime();
            status.change_count++;
            found = true;
            break;
        }
    }

    if (!found) {
        FileWatchStatus status;
        status.file_path = file_path;
        status.last_modified = QFileInfo(file_path).lastModified();
        status.file_size = QFileInfo(file_path).size();
        status.is_watched = true;
        status.is_included_by_filter = advanced_filter_ ? advanced_filter_->shouldIncludeFile(file_path) : true;
        status.change_count = 1;
        status.last_change_time = QDateTime::currentDateTime();
        status.status = "active";

        file_watch_status_.push_back(status);
    }

    updateFileWatchStatus();
}

void HotReloadDashboard::onPerformanceWarning(const QString& message) {
    current_metrics_.warning_count++;

    QString warning_entry = QString("[%1] WARNING: %2")
                           .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                           .arg(message);
    error_log_.push_front(warning_entry);

    // **Limit error log size**
    while (error_log_.size() > 100) {
        error_log_.pop_back();
    }

    updateErrorLog();
}

// **Button click handlers**
void HotReloadDashboard::onStartButtonClicked() {
    startMonitoring();
}

void HotReloadDashboard::onStopButtonClicked() {
    stopMonitoring();
}

void HotReloadDashboard::onPauseButtonClicked() {
    pauseMonitoring();
}

void HotReloadDashboard::onResetButtonClicked() {
    int ret = QMessageBox::question(this, "Reset Metrics",
                                   "Are you sure you want to reset all metrics and history?",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        resetMetrics();
    }
}

void HotReloadDashboard::onExportButtonClicked() {
    QString file_path = QFileDialog::getSaveFileName(this,
                                                    "Export Dashboard Data",
                                                    QString("hotreload_report_%1.json")
                                                    .arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
                                                    "JSON Files (*.json);;Text Files (*.txt)");
    if (!file_path.isEmpty()) {
        exportMetricsToFile(file_path);
    }
}

void HotReloadDashboard::onConfigureButtonClicked() {
    main_tabs_->setCurrentWidget(configuration_tab_);
}

void HotReloadDashboard::onRefreshButtonClicked() {
    refreshDisplay();
}

// **Utility methods**
QString HotReloadDashboard::formatDuration(qint64 milliseconds) const {
    if (milliseconds < 1000) {
        return QString("%1 ms").arg(milliseconds);
    } else if (milliseconds < 60000) {
        return QString("%1.%2 s").arg(milliseconds / 1000).arg((milliseconds % 1000) / 100);
    } else {
        int minutes = milliseconds / 60000;
        int seconds = (milliseconds % 60000) / 1000;
        return QString("%1m %2s").arg(minutes).arg(seconds);
    }
}

QString HotReloadDashboard::formatFileSize(qint64 bytes) const {
    if (bytes < 1024) {
        return QString("%1 B").arg(bytes);
    } else if (bytes < 1024 * 1024) {
        return QString("%1 KB").arg(bytes / 1024);
    } else if (bytes < 1024 * 1024 * 1024) {
        return QString("%1 MB").arg(bytes / (1024 * 1024));
    } else {
        return QString("%1 GB").arg(bytes / (1024 * 1024 * 1024));
    }
}

QString HotReloadDashboard::formatPercentage(double value) const {
    return QString("%1%").arg(value * 100, 0, 'f', 1);
}

QColor HotReloadDashboard::getStatusColor(const QString& status) const {
    if (status == "active") return QColor("#2ecc71");
    if (status == "paused") return QColor("#f39c12");
    if (status == "error") return QColor("#e74c3c");
    if (status == "excluded") return QColor("#95a5a6");
    return QColor("#3498db");
}

QColor HotReloadDashboard::getThresholdColor(double value, double threshold) const {
    if (value > threshold) return QColor("#e74c3c");
    if (value > threshold * 0.8) return QColor("#f39c12");
    return QColor("#2ecc71");
}

void HotReloadDashboard::setTheme(const QString& theme_name) {
    current_theme_ = theme_name;
    applyTheme(theme_name);
}

void HotReloadDashboard::applyTheme(const QString& theme_name) {
    QString stylesheet;

    if (theme_name == "dark") {
        stylesheet = R"(
            QWidget { background-color: #2c3e50; color: #ecf0f1; }
            QTabWidget::pane { border: 1px solid #34495e; background-color: #34495e; }
            QTabBar::tab { background-color: #34495e; color: #ecf0f1; padding: 8px 16px; margin: 2px; }
            QTabBar::tab:selected { background-color: #3498db; }
            QGroupBox { font-weight: bold; border: 2px solid #34495e; margin: 5px; padding-top: 10px; }
            QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }
            QTableWidget { background-color: #34495e; alternate-background-color: #2c3e50; }
            QHeaderView::section { background-color: #3498db; color: white; padding: 4px; border: none; }
        )";
    } else if (theme_name == "light") {
        stylesheet = R"(
            QWidget { background-color: #ffffff; color: #2c3e50; }
            QTabWidget::pane { border: 1px solid #bdc3c7; background-color: #ffffff; }
            QTabBar::tab { background-color: #ecf0f1; color: #2c3e50; padding: 8px 16px; margin: 2px; }
            QTabBar::tab:selected { background-color: #3498db; color: white; }
            QGroupBox { font-weight: bold; border: 2px solid #bdc3c7; margin: 5px; padding-top: 10px; }
            QTableWidget { background-color: #ffffff; alternate-background-color: #f8f9fa; }
            QHeaderView::section { background-color: #3498db; color: white; padding: 4px; border: none; }
        )";
    }

    if (!stylesheet.isEmpty()) {
        setStyleSheet(stylesheet);
    }
}

// **Data collection and UI update methods**
void HotReloadDashboard::collectMetrics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);

    // **Collect file watcher metrics**
    if (file_watcher_) {
        current_metrics_.watched_files_count = file_watcher_->getWatchedFilesCount();
        current_metrics_.watched_directories_count = file_watcher_->getWatchedDirectoriesCount();
    }

    // **Collect performance metrics**
    if (performance_monitor_) {
        auto perf_data = performance_monitor_->getCurrentMetrics();
        current_metrics_.cpu_usage_percent = perf_data.cpu_usage_percent;
        current_metrics_.memory_usage_mb = perf_data.memory_usage_mb;
        if (perf_data.memory_usage_mb > current_metrics_.peak_memory_mb) {
            current_metrics_.peak_memory_mb = perf_data.memory_usage_mb;
        }
    }

    // **Collect filter metrics**
    if (advanced_filter_) {
        auto filter_stats = advanced_filter_->getStatistics();
        current_metrics_.files_processed = filter_stats.total_files_processed;
        current_metrics_.files_included = filter_stats.files_included;
        current_metrics_.files_excluded = filter_stats.files_excluded;
        if (current_metrics_.files_processed > 0) {
            current_metrics_.filter_efficiency =
                static_cast<double>(current_metrics_.files_included) / current_metrics_.files_processed;
        }
    }

    // **Store metrics history**
    metrics_history_.push_front(current_metrics_);
    while (metrics_history_.size() > static_cast<size_t>(max_history_size_)) {
        metrics_history_.pop_back();
    }

    emit metricsUpdated(current_metrics_);
}

void HotReloadDashboard::updateMetrics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);

    // **Update overview metrics**
    watched_files_label_->setText(QString::number(current_metrics_.watched_files_count));
    successful_reloads_label_->setText(QString::number(current_metrics_.successful_reloads));
    failed_reloads_label_->setText(QString::number(current_metrics_.failed_reloads));
    average_reload_time_label_->setText(formatDuration(current_metrics_.average_reload_time_ms));
    last_reload_time_label_->setText(formatDuration(current_metrics_.last_reload_time_ms));

    // **Calculate and update performance score**
    double performance_score = 100.0;
    if (current_metrics_.success_rate < 1.0) {
        performance_score *= current_metrics_.success_rate;
    }
    if (current_metrics_.cpu_usage_percent > 50.0) {
        performance_score *= (100.0 - current_metrics_.cpu_usage_percent) / 50.0;
    }
    if (current_metrics_.average_reload_time_ms > 500) {
        performance_score *= std::max(0.1, 1.0 - (current_metrics_.average_reload_time_ms - 500) / 2000.0);
    }

    performance_score_bar_->setValue(static_cast<int>(performance_score));
    performance_score_bar_->setStyleSheet(QString("QProgressBar::chunk { background-color: %1; }")
                                         .arg(getThresholdColor(100 - performance_score, 20).name()));

    // **Update performance metrics table**
    updatePerformanceMetricsTable();
}

void HotReloadDashboard::updatePerformanceMetricsTable() {
    performance_metrics_table_->setRowCount(8);

    auto addMetricRow = [this](int row, const QString& metric, const QString& current,
                              const QString& average, const QString& peak) {
        performance_metrics_table_->setItem(row, 0, new QTableWidgetItem(metric));
        performance_metrics_table_->setItem(row, 1, new QTableWidgetItem(current));
        performance_metrics_table_->setItem(row, 2, new QTableWidgetItem(average));
        performance_metrics_table_->setItem(row, 3, new QTableWidgetItem(peak));
    };

    // **Calculate averages from history**
    double avg_cpu = 0.0, avg_memory = 0.0, avg_reload_time = 0.0;
    if (!metrics_history_.empty()) {
        for (const auto& metrics : metrics_history_) {
            avg_cpu += metrics.cpu_usage_percent;
            avg_memory += metrics.memory_usage_mb;
            avg_reload_time += metrics.average_reload_time_ms;
        }
        avg_cpu /= metrics_history_.size();
        avg_memory /= metrics_history_.size();
        avg_reload_time /= metrics_history_.size();
    }

    addMetricRow(0, "CPU Usage",
                formatPercentage(current_metrics_.cpu_usage_percent / 100.0),
                formatPercentage(avg_cpu / 100.0),
                formatPercentage(current_metrics_.cpu_usage_percent / 100.0));

    addMetricRow(1, "Memory Usage",
                formatFileSize(current_metrics_.memory_usage_mb * 1024 * 1024),
                formatFileSize(avg_memory * 1024 * 1024),
                formatFileSize(current_metrics_.peak_memory_mb * 1024 * 1024));

    addMetricRow(2, "Reload Time",
                formatDuration(current_metrics_.last_reload_time_ms),
                formatDuration(avg_reload_time),
                formatDuration(current_metrics_.slowest_reload_ms));

    addMetricRow(3, "Success Rate",
                formatPercentage(current_metrics_.success_rate),
                formatPercentage(current_metrics_.success_rate),
                formatPercentage(current_metrics_.success_rate));

    addMetricRow(4, "Files Watched",
                QString::number(current_metrics_.watched_files_count),
                QString::number(current_metrics_.watched_files_count),
                QString::number(current_metrics_.watched_files_count));

    addMetricRow(5, "Total Operations",
                QString::number(current_metrics_.total_operations),
                QString::number(current_metrics_.total_operations),
                QString::number(current_metrics_.total_operations));

    addMetricRow(6, "Filter Efficiency",
                formatPercentage(current_metrics_.filter_efficiency),
                formatPercentage(current_metrics_.filter_efficiency),
                formatPercentage(current_metrics_.filter_efficiency));

    addMetricRow(7, "Error Count",
                QString::number(current_metrics_.error_count),
                QString::number(current_metrics_.error_count),
                QString::number(current_metrics_.error_count));
}

void HotReloadDashboard::updateFileWatchStatus() {
    watched_files_table_->setRowCount(static_cast<int>(file_watch_status_.size()));

    for (size_t i = 0; i < file_watch_status_.size(); ++i) {
        const auto& status = file_watch_status_[i];

        watched_files_table_->setItem(static_cast<int>(i), 0, new QTableWidgetItem(status.file_path));

        auto* status_item = new QTableWidgetItem(status.status);
        status_item->setForeground(QBrush(getStatusColor(status.status)));
        watched_files_table_->setItem(static_cast<int>(i), 1, status_item);

        watched_files_table_->setItem(static_cast<int>(i), 2,
                                     new QTableWidgetItem(status.last_modified.toString("yyyy-MM-dd hh:mm:ss")));
        watched_files_table_->setItem(static_cast<int>(i), 3,
                                     new QTableWidgetItem(formatFileSize(status.file_size)));
        watched_files_table_->setItem(static_cast<int>(i), 4,
                                     new QTableWidgetItem(QString::number(status.change_count)));

        QString filter_status = status.is_included_by_filter ? "Included" : "Excluded";
        auto* filter_item = new QTableWidgetItem(filter_status);
        filter_item->setForeground(QBrush(status.is_included_by_filter ? QColor("#2ecc71") : QColor("#e74c3c")));
        watched_files_table_->setItem(static_cast<int>(i), 5, filter_item);
    }

    // **Update filter status text**
    if (advanced_filter_) {
        auto stats = advanced_filter_->getStatistics();
        QString filter_text = QString(
            "Filter Statistics:\n"
            "Total Files Processed: %1\n"
            "Files Included: %2\n"
            "Files Excluded: %3\n"
            "Filter Efficiency: %4\n"
            "Cache Hit Rate: %5\n"
            "Active Rules: %6"
        ).arg(stats.total_files_processed)
         .arg(stats.files_included)
         .arg(stats.files_excluded)
         .arg(formatPercentage(current_metrics_.filter_efficiency))
         .arg(formatPercentage(stats.cache_hit_rate))
         .arg(stats.active_rules_count);

        file_filter_status_->setText(filter_text);
    }
}

void HotReloadDashboard::updateReloadHistory() {
    std::lock_guard<std::mutex> lock(history_mutex_);

    reload_history_table_->setRowCount(static_cast<int>(reload_history_.size()));

    for (size_t i = 0; i < reload_history_.size(); ++i) {
        const auto& entry = reload_history_[i];

        reload_history_table_->setItem(static_cast<int>(i), 0,
                                      new QTableWidgetItem(entry.timestamp.toString("yyyy-MM-dd hh:mm:ss")));
        reload_history_table_->setItem(static_cast<int>(i), 1,
                                      new QTableWidgetItem(QFileInfo(entry.file_path).fileName()));
        reload_history_table_->setItem(static_cast<int>(i), 2,
                                      new QTableWidgetItem(formatDuration(entry.duration_ms)));

        QString status = entry.success ? "✅ Success" : "❌ Failed";
        auto* status_item = new QTableWidgetItem(status);
        status_item->setForeground(QBrush(entry.success ? QColor("#2ecc71") : QColor("#e74c3c")));
        reload_history_table_->setItem(static_cast<int>(i), 3, status_item);

        reload_history_table_->setItem(static_cast<int>(i), 4,
                                      new QTableWidgetItem(formatFileSize(entry.file_size_bytes)));
        reload_history_table_->setItem(static_cast<int>(i), 5,
                                      new QTableWidgetItem(entry.operation_type));
    }
}

void HotReloadDashboard::updateErrorLog() {
    QString error_text;
    for (const auto& error : error_log_) {
        error_text += error + "\n";
    }
    error_console_->setText(error_text);

    // **Scroll to bottom to show latest errors**
    error_console_->moveCursor(QTextCursor::End);
}

void HotReloadDashboard::updateStatusIndicators() {
    // **Update status bar indicators**
    cpu_usage_bar_->setValue(static_cast<int>(current_metrics_.cpu_usage_percent));
    cpu_usage_bar_->setStyleSheet(QString("QProgressBar::chunk { background-color: %1; }")
                                 .arg(getThresholdColor(current_metrics_.cpu_usage_percent, cpu_threshold_).name()));

    memory_usage_bar_->setValue(static_cast<int>(current_metrics_.memory_usage_mb));
    memory_usage_bar_->setStyleSheet(QString("QProgressBar::chunk { background-color: %1; }")
                                    .arg(getThresholdColor(current_metrics_.memory_usage_mb, memory_threshold_mb_).name()));

    success_rate_label_->setText(QString("Success Rate: %1").arg(formatPercentage(current_metrics_.success_rate)));
    total_operations_label_->setText(QString("Operations: %1").arg(current_metrics_.total_operations));

    // **Update diagnostics**
    updateDiagnosticsTable();
}

void HotReloadDashboard::updateDiagnosticsTable() {
    diagnostics_table_->setRowCount(5);

    auto addDiagnosticRow = [this](int row, const QString& component, const QString& status, const QString& details) {
        diagnostics_table_->setItem(row, 0, new QTableWidgetItem(component));

        auto* status_item = new QTableWidgetItem(status);
        if (status.contains("OK") || status.contains("Active")) {
            status_item->setForeground(QBrush(QColor("#2ecc71")));
        } else if (status.contains("Warning")) {
            status_item->setForeground(QBrush(QColor("#f39c12")));
        } else if (status.contains("Error") || status.contains("Failed")) {
            status_item->setForeground(QBrush(QColor("#e74c3c")));
        }
        diagnostics_table_->setItem(row, 1, status_item);
        diagnostics_table_->setItem(row, 2, new QTableWidgetItem(details));
    };

    // **Hot Reload Manager**
    QString hrm_status = hot_reload_manager_ ? "Active" : "Not Connected";
    QString hrm_details = hot_reload_manager_ ? "Connected and operational" : "Manager not set";
    addDiagnosticRow(0, "Hot Reload Manager", hrm_status, hrm_details);

    // **File Watcher**
    QString fw_status = file_watcher_ ? "Active" : "Not Connected";
    QString fw_details = file_watcher_ ?
        QString("Watching %1 files").arg(current_metrics_.watched_files_count) :
        "Watcher not set";
    addDiagnosticRow(1, "File Watcher", fw_status, fw_details);

    // **Performance Monitor**
    QString pm_status = performance_monitor_ ? "Active" : "Not Connected";
    QString pm_details = performance_monitor_ ?
        QString("CPU: %1%, Memory: %2 MB").arg(current_metrics_.cpu_usage_percent, 0, 'f', 1)
                                          .arg(current_metrics_.memory_usage_mb) :
        "Monitor not set";
    addDiagnosticRow(2, "Performance Monitor", pm_status, pm_details);

    // **Advanced Filter**
    QString af_status = advanced_filter_ ? "Active" : "Not Connected";
    QString af_details = advanced_filter_ ?
        QString("Efficiency: %1").arg(formatPercentage(current_metrics_.filter_efficiency)) :
        "Filter not set";
    addDiagnosticRow(3, "Advanced Filter", af_status, af_details);

    // **Configuration**
    QString config_status = config_ ? "Loaded" : "Not Connected";
    QString config_details = config_ ? "Configuration loaded and active" : "Config not set";
    addDiagnosticRow(4, "Configuration", config_status, config_details);

    // **Update system status label**
    int active_components = 0;
    if (hot_reload_manager_) active_components++;
    if (file_watcher_) active_components++;
    if (performance_monitor_) active_components++;
    if (advanced_filter_) active_components++;
    if (config_) active_components++;

    QString system_status = QString("System Status: %1/5 components active\n"
                                   "Monitoring: %2\n"
                                   "Session Duration: %3\n"
                                   "Total Operations: %4")
                           .arg(active_components)
                           .arg(monitoring_enabled_ ? "Enabled" : "Disabled")
                           .arg(formatDuration(session_timer_.elapsed()))
                           .arg(current_metrics_.total_operations);

    system_status_label_->setText(system_status);
}

void HotReloadDashboard::checkThresholds() {
    if (current_metrics_.cpu_usage_percent > cpu_threshold_) {
        emit thresholdExceeded("cpu_usage", current_metrics_.cpu_usage_percent, cpu_threshold_);
    }
    if (current_metrics_.memory_usage_mb > memory_threshold_mb_) {
        emit thresholdExceeded("memory_usage", current_metrics_.memory_usage_mb, memory_threshold_mb_);
    }
}

// **Selection handlers**
void HotReloadDashboard::onHistoryItemSelected() {
    int current_row = reload_history_table_->currentRow();
    if (current_row >= 0 && current_row < static_cast<int>(reload_history_.size())) {
        const auto& entry = reload_history_[current_row];

        QString details = QString(
            "File Path: %1\n"
            "Timestamp: %2\n"
            "Duration: %3\n"
            "Success: %4\n"
            "File Size: %5\n"
            "Operation Type: %6\n"
        ).arg(entry.file_path)
         .arg(entry.timestamp.toString("yyyy-MM-dd hh:mm:ss"))
         .arg(formatDuration(entry.duration_ms))
         .arg(entry.success ? "Yes" : "No")
         .arg(formatFileSize(entry.file_size_bytes))
         .arg(entry.operation_type);

        if (!entry.success && !entry.error_message.isEmpty()) {
            details += QString("Error: %1\n").arg(entry.error_message);
        }

        history_details_->setText(details);
    }
}

void HotReloadDashboard::onWatchedFileSelected() {
    int current_row = watched_files_table_->currentRow();
    if (current_row >= 0 && current_row < static_cast<int>(file_watch_status_.size())) {
        const auto& status = file_watch_status_[current_row];

        QString details = QString(
            "Selected File: %1\n"
            "Status: %2\n"
            "Last Modified: %3\n"
            "File Size: %4\n"
            "Change Count: %5\n"
            "Last Change: %6\n"
            "Filter Status: %7\n"
        ).arg(status.file_path)
         .arg(status.status)
         .arg(status.last_modified.toString("yyyy-MM-dd hh:mm:ss"))
         .arg(formatFileSize(status.file_size))
         .arg(status.change_count)
         .arg(status.last_change_time.toString("yyyy-MM-dd hh:mm:ss"))
         .arg(status.is_included_by_filter ? "Included" : "Excluded");

        // **Update file filter status with selected file details**
        QString current_text = file_filter_status_->toPlainText();
        file_filter_status_->setText(current_text + "\n\n" + details);
    }
}

void HotReloadDashboard::onConfigurationUpdated() {
    emit configurationChanged();
    refreshDisplay();
}

void HotReloadDashboard::updatePerformanceCharts() {
    // **Placeholder for chart updates**
    // **This would integrate with Qt Charts if available**
    performance_log_->append(QString("[%1] Performance update: CPU %2%, Memory %3 MB")
                            .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                            .arg(current_metrics_.cpu_usage_percent, 0, 'f', 1)
                            .arg(current_metrics_.memory_usage_mb));
}

} // namespace DeclarativeUI::HotReload
