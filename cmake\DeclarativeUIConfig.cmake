# DeclarativeUI Framework CMake Configuration
# This file provides configuration options and utilities for the DeclarativeUI Framework

# =============================================================================
# Version Information
# =============================================================================
set(DECLARATIVE_UI_VERSION_MAJOR 1)
set(DECLARATIVE_UI_VERSION_MINOR 0)
set(DECLARATIVE_UI_VERSION_PATCH 0)
set(DECLARATIVE_UI_VERSION "${DECLARATIVE_UI_VERSION_MAJOR}.${DECLARATIVE_UI_VERSION_MINOR}.${DECLARATIVE_UI_VERSION_PATCH}")

# =============================================================================
# Feature Detection
# =============================================================================

# Check for C++20 support
include(CheckCXXCompilerFlag)
check_cxx_compiler_flag("-std=c++20" COMPILER_SUPPORTS_CXX20)
if(NOT COMPILER_SUPPORTS_CXX20)
    message(FATAL_ERROR "The compiler ${CMAKE_CXX_COMPILER} has no C++20 support. Please use a different C++ compiler.")
endif()

# Check for Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network Test)
if(Qt6_VERSION VERSION_LESS "6.2.0")
    message(FATAL_ERROR "Qt6 version 6.2.0 or higher is required. Found version: ${Qt6_VERSION}")
endif()

# =============================================================================
# Compiler Configuration
# =============================================================================

# Set compiler-specific flags
if(MSVC)
    # MSVC specific flags
    set(DECLARATIVE_UI_CXX_FLAGS
        /W4                     # Warning level 4
        /permissive-           # Disable non-conforming code
        /Zc:__cplusplus        # Enable correct __cplusplus macro
        /Zc:preprocessor       # Enable conforming preprocessor
    )
    
    # Disable specific warnings
    set(DECLARATIVE_UI_CXX_FLAGS ${DECLARATIVE_UI_CXX_FLAGS}
        /wd4251                # DLL interface warning
        /wd4275                # DLL base class warning
    )
    
    # Add compile definitions
    set(DECLARATIVE_UI_COMPILE_DEFINITIONS
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
    
elseif(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    # GCC/Clang specific flags
    set(DECLARATIVE_UI_CXX_FLAGS
        -Wall                  # Enable most warnings
        -Wextra               # Enable extra warnings
        -Wpedantic            # Enable pedantic warnings
        -Wno-unused-parameter # Disable unused parameter warnings
        -Wno-missing-field-initializers # Disable missing field initializer warnings
    )
    
    # Additional flags for GCC
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        set(DECLARATIVE_UI_CXX_FLAGS ${DECLARATIVE_UI_CXX_FLAGS}
            -Wno-psabi            # Disable ABI warnings
        )
    endif()
    
    # Additional flags for Clang
    if(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
        set(DECLARATIVE_UI_CXX_FLAGS ${DECLARATIVE_UI_CXX_FLAGS}
            -Wno-unused-private-field
        )
    endif()
endif()

# =============================================================================
# Build Configuration
# =============================================================================

# Set default build type
if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
    message(STATUS "Setting build type to 'Release' as none was specified.")
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Choose the type of build." FORCE)
    set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# Enable compile commands export
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# =============================================================================
# Feature Flags
# =============================================================================

# Define feature flags based on build options
if(BUILD_COMMAND_SYSTEM)
    list(APPEND DECLARATIVE_UI_COMPILE_DEFINITIONS DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED)
endif()

if(BUILD_ADAPTERS)
    list(APPEND DECLARATIVE_UI_COMPILE_DEFINITIONS DECLARATIVE_UI_ADAPTERS_ENABLED)
endif()

if(ENABLE_COMMAND_DEBUG)
    list(APPEND DECLARATIVE_UI_COMPILE_DEFINITIONS DECLARATIVE_UI_COMMAND_DEBUG)
endif()

if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    list(APPEND DECLARATIVE_UI_COMPILE_DEFINITIONS DECLARATIVE_UI_DEBUG)
endif()

# =============================================================================
# Utility Functions
# =============================================================================

# Function to configure a DeclarativeUI target with standard settings
function(configure_declarative_ui_target target_name)
    # Set C++ standard
    target_compile_features(${target_name} PRIVATE cxx_std_20)
    
    # Apply compiler flags
    target_compile_options(${target_name} PRIVATE ${DECLARATIVE_UI_CXX_FLAGS})
    
    # Apply compile definitions
    target_compile_definitions(${target_name} PRIVATE ${DECLARATIVE_UI_COMPILE_DEFINITIONS})
    
    # Set target properties
    set_target_properties(${target_name} PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        CXX_EXTENSIONS OFF
    )
    
    # Link Qt6 libraries
    target_link_libraries(${target_name} PRIVATE
        Qt6::Core
        Qt6::Widgets
        Qt6::Network
    )
    
    # Set include directories
    target_include_directories(${target_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src
        ${CMAKE_BINARY_DIR}
    )
endfunction()

# Function to add a DeclarativeUI example
function(add_declarative_ui_example example_name source_file)
    add_executable(${example_name} ${source_file})
    configure_declarative_ui_target(${example_name})
    target_link_libraries(${example_name} PRIVATE DeclarativeUI)
    
    # Set output directory
    set_target_properties(${example_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/examples
    )
    
    # Copy resources if they exist
    if(EXISTS ${CMAKE_SOURCE_DIR}/examples/resources)
        add_custom_command(TARGET ${example_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${CMAKE_SOURCE_DIR}/examples/resources
            ${CMAKE_BINARY_DIR}/examples/resources
        )
    endif()
endfunction()

# Function to add a DeclarativeUI test
function(add_declarative_ui_test test_name source_file)
    add_executable(${test_name} ${source_file})
    configure_declarative_ui_target(${test_name})
    target_link_libraries(${test_name} PRIVATE
        DeclarativeUI
        Qt6::Test
    )
    
    # Set output directory
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    # Add to CTest
    add_test(NAME ${test_name} COMMAND ${test_name})
    
    # Set test properties
    set_tests_properties(${test_name} PROPERTIES
        TIMEOUT 30
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
endfunction()

# =============================================================================
# Installation Configuration
# =============================================================================

# Set installation directories
include(GNUInstallDirs)

set(DECLARATIVE_UI_INSTALL_BINDIR ${CMAKE_INSTALL_BINDIR})
set(DECLARATIVE_UI_INSTALL_LIBDIR ${CMAKE_INSTALL_LIBDIR})
set(DECLARATIVE_UI_INSTALL_INCLUDEDIR ${CMAKE_INSTALL_INCLUDEDIR}/DeclarativeUI)
set(DECLARATIVE_UI_INSTALL_CMAKEDIR ${CMAKE_INSTALL_LIBDIR}/cmake/DeclarativeUI)

# =============================================================================
# Package Configuration
# =============================================================================

# Configure package config file
include(CMakePackageConfigHelpers)

configure_package_config_file(
    ${CMAKE_SOURCE_DIR}/cmake/DeclarativeUIConfig.cmake.in
    ${CMAKE_BINARY_DIR}/DeclarativeUIConfig.cmake
    INSTALL_DESTINATION ${DECLARATIVE_UI_INSTALL_CMAKEDIR}
    PATH_VARS
        DECLARATIVE_UI_INSTALL_INCLUDEDIR
        DECLARATIVE_UI_INSTALL_LIBDIR
)

write_basic_package_version_file(
    ${CMAKE_BINARY_DIR}/DeclarativeUIConfigVersion.cmake
    VERSION ${DECLARATIVE_UI_VERSION}
    COMPATIBILITY SameMajorVersion
)

# =============================================================================
# Status Information
# =============================================================================

message(STATUS "DeclarativeUI Framework Configuration:")
message(STATUS "  Version: ${DECLARATIVE_UI_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: C++20")
message(STATUS "  Qt version: ${Qt6_VERSION}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  Build examples: ${BUILD_EXAMPLES}")
message(STATUS "  Build tests: ${BUILD_TESTS}")
message(STATUS "  Command system: ${BUILD_COMMAND_SYSTEM}")
message(STATUS "  Adapters: ${BUILD_ADAPTERS}")
message(STATUS "  Command debug: ${ENABLE_COMMAND_DEBUG}")
