#include "PluginInterface.hpp"
#include <QJsonDocument>
#include <QDebug>
#include <QJsonArray>

namespace DeclarativeUI::Plugins {

QJsonObject PluginMetadata::toJson() const {
    QJsonObject json;
    json["name"] = name;
    json["description"] = description;
    json["author"] = author;
    json["version"] = version.toString();
    json["minFrameworkVersion"] = minFrameworkVersion.toString();
    json["maxFrameworkVersion"] = maxFrameworkVersion.toString();
    
    QJsonArray depsArray;
    for (const QString& dep : dependencies) {
        depsArray.append(dep);
    }
    json["dependencies"] = depsArray;
    
    QJsonArray tagsArray;
    for (const QString& tag : tags) {
        tagsArray.append(tag);
    }
    json["tags"] = tagsArray;
    
    json["license"] = license;
    json["website"] = website;
    
    return json;
}

PluginMetadata PluginMetadata::fromJson(const QJsonObject& json) {
    PluginMetadata metadata;
    
    metadata.name = json["name"].toString();
    metadata.description = json["description"].toString();
    metadata.author = json["author"].toString();
    metadata.version = QVersionNumber::fromString(json["version"].toString());
    metadata.minFrameworkVersion = QVersionNumber::fromString(json["minFrameworkVersion"].toString());
    metadata.maxFrameworkVersion = QVersionNumber::fromString(json["maxFrameworkVersion"].toString());
    
    const QJsonArray depsArray = json["dependencies"].toArray();
    for (const QJsonValue& dep : depsArray) {
        metadata.dependencies.append(dep.toString());
    }
    
    const QJsonArray tagsArray = json["tags"].toArray();
    for (const QJsonValue& tag : tagsArray) {
        metadata.tags.append(tag.toString());
    }
    
    metadata.license = json["license"].toString();
    metadata.website = json["website"].toString();
    
    return metadata;
}

} // namespace DeclarativeUI::Plugins
