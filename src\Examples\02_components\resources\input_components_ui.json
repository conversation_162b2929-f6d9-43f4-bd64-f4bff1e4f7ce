{"type": "QWidget", "properties": {"windowTitle": "07 - Input Components | DeclarativeUI", "minimumSize": [700, 650]}, "layout": {"type": "QVBoxLayout", "properties": {"spacing": 15, "contentsMargins": [20, 20, 20, 20]}, "children": [{"type": "QLabel", "properties": {"text": "🎛️ Input Components Showcase", "alignment": "AlignCenter", "styleSheet": "font-size: 20px; font-weight: bold; color: #2c3e50;"}}, {"type": "QLabel", "properties": {"text": "Explore various input components and see how they interact with each other", "alignment": "AlignCenter", "styleSheet": "color: #7f8c8d; font-style: italic;"}}, {"type": "QGroupBox", "properties": {"title": "🎮 Input Controls"}, "layout": {"type": "QGridLayout", "children": [{"type": "QLabel", "properties": {"text": "CheckBox:"}, "gridPosition": [0, 0]}, {"type": "QCheckBox", "properties": {"objectName": "enableCheck", "text": "Enable Feature", "checked": false}, "gridPosition": [0, 1]}, {"type": "QLabel", "properties": {"text": "ComboBox:"}, "gridPosition": [1, 0]}, {"type": "QComboBox", "properties": {"objectName": "optionCombo", "items": ["Option 1", "Option 2", "Option 3", "Option 4", "Option 5"]}, "gridPosition": [1, 1]}, {"type": "QLabel", "properties": {"text": "SpinBox (0-100):"}, "gridPosition": [2, 0]}, {"type": "QSpinBox", "properties": {"objectName": "numberSpin", "minimum": 0, "maximum": 100, "value": 50}, "gridPosition": [2, 1]}, {"type": "QLabel", "properties": {"text": "DoubleSpinBox (0.0-10.0):"}, "gridPosition": [3, 0]}, {"type": "QDoubleSpinBox", "properties": {"objectName": "precisionSpin", "minimum": 0.0, "maximum": 10.0, "singleStep": 0.1, "value": 1.0, "decimals": 2}, "gridPosition": [3, 1]}, {"type": "QLabel", "properties": {"text": "Slider (synced with SpinBox):"}, "gridPosition": [4, 0]}, {"type": "QSlider", "properties": {"objectName": "valueSlider", "orientation": "Horizontal", "minimum": 0, "maximum": 100, "value": 50}, "gridPosition": [4, 1]}, {"type": "QLabel", "properties": {"text": "Progress (shows slider value):"}, "gridPosition": [5, 0]}, {"type": "QProgressBar", "properties": {"objectName": "valueProgress", "minimum": 0, "maximum": 100, "value": 50}, "gridPosition": [5, 1]}, {"type": "QLabel", "properties": {"text": "Dial (0-360°):"}, "gridPosition": [6, 0]}, {"type": "QDial", "properties": {"objectName": "rotaryDial", "minimum": 0, "maximum": 360, "value": 0, "wrapping": true}, "gridPosition": [6, 1]}]}}, {"type": "QGroupBox", "properties": {"title": "📊 Current State Summary"}, "layout": {"type": "QVBoxLayout", "children": [{"type": "QLabel", "properties": {"objectName": "summaryDisplay", "text": "📊 Current Values:\n• CheckBox: ❌ Unchecked\n• ComboBox: Option 1\n• SpinBox: 50\n• DoubleSpinBox: 1.00\n• Slider: 50\n• Dial: 0°", "styleSheet": "background-color: #ecf0f1; padding: 15px; border-radius: 8px; font-family: monospace; color: #2c3e50;"}}]}}, {"type": "layout", "layoutType": "QHBoxLayout", "children": [{"type": "QPushButton", "properties": {"text": "🔄 Reset All", "styleSheet": "QPushButton { background-color: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold; } QPushButton:hover { background-color: #2980b9; }"}, "events": {"clicked": "resetInputs"}}, {"type": "QPushButton", "properties": {"text": "🎲 Randomize", "styleSheet": "QPushButton { background-color: #e67e22; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold; } QPushButton:hover { background-color: #d35400; }"}, "events": {"clicked": "randomizeInputs"}}, {"type": "spacer", "properties": {"orientation": "horizontal"}}]}, {"type": "QGroupBox", "properties": {"title": "📝 Input Event Log"}, "layout": {"type": "QVBoxLayout", "children": [{"type": "QTextEdit", "properties": {"objectName": "inputLog", "readOnly": true, "maximumHeight": 120, "styleSheet": "QTextEdit { background-color: #2c3e50; color: #ecf0f1; font-family: 'Courier New', monospace; font-size: 11px; }"}}, {"type": "QPushButton", "properties": {"text": "🗑️ Clear Log", "styleSheet": "QPushButton { background-color: #e74c3c; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #c0392b; }"}, "events": {"clicked": "clearLog"}}]}}, {"type": "QLabel", "properties": {"text": "💡 Notice how SpinB<PERSON> and <PERSON>lider are synchronized, and all changes are logged in real-time", "alignment": "AlignCenter", "wordWrap": true, "styleSheet": "font-size: 12px; color: #7f8c8d; font-style: italic;"}}]}}