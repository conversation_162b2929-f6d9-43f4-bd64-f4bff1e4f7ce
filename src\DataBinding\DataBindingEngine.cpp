#include "DataBindingEngine.hpp"
#include <QDebug>
#include <QJsonObject>
#include <QJsonDocument>
#include <QMetaProperty>
#include <QApplication>

namespace DeclarativeUI {
namespace DataBinding {

DataBindingEngine* DataBindingEngine::instance_ = nullptr;
QMutex DataBindingEngine::instance_mutex_;

// FormData implementation

FormData::FormData(QObject* parent) : QObject(parent) {
    qDebug() << "📋 FormData created";
}

void FormData::setValue(const QString& field, const QVariant& value) {
    QVariant old_value = data_[field];
    if (old_value != value) {
        data_[field] = value;
        emit fieldChanged(field, value);
        
        // Validate if validation engine is available
        if (validation_engine_) {
            ValidationResult result = validation_engine_->validateField(field, value);
            emit validationChanged(field, result.is_valid);
            
            // Check overall form validity
            bool form_valid = isValid();
            emit formValidationChanged(form_valid);
        }
    }
}

QVariant FormData::getValue(const QString& field) const {
    auto it = data_.find(field);
    return (it != data_.end()) ? it->second : QVariant();
}

bool FormData::hasField(const QString& field) const {
    return data_.find(field) != data_.end();
}

void FormData::removeField(const QString& field) {
    data_.erase(field);
    cached_errors_.erase(field);
}

void FormData::clear() {
    data_.clear();
    cached_errors_.clear();
}

void FormData::setValidationEngine(std::shared_ptr<ValidationEngine> engine) {
    validation_engine_ = std::move(engine);
}

ValidationResult FormData::validateField(const QString& field) {
    if (!validation_engine_) {
        return ValidationResult::success();
    }
    
    QVariant value = getValue(field);
    ValidationResult result = validation_engine_->validateField(field, value);
    
    if (result.is_valid) {
        cached_errors_.erase(field);
    } else {
        cached_errors_[field] = result.error_message;
    }
    
    emit validationChanged(field, result.is_valid);
    return result;
}

bool FormData::validateAll() {
    if (!validation_engine_) {
        return true;
    }
    
    cached_errors_.clear();
    auto results = validation_engine_->validateAll(data_);
    
    bool all_valid = true;
    for (const auto& [field, result] : results) {
        if (!result.is_valid) {
            all_valid = false;
            cached_errors_[field] = result.error_message;
        }
        emit validationChanged(field, result.is_valid);
    }
    
    emit formValidationChanged(all_valid);
    return all_valid;
}

bool FormData::isValid() const {
    if (!validation_engine_) {
        return true;
    }
    
    return validation_engine_->isValid(data_);
}

QString FormData::getError(const QString& field) const {
    auto it = cached_errors_.find(field);
    return (it != cached_errors_.end()) ? it->second : QString();
}

QStringList FormData::getErrors() const {
    QStringList errors;
    for (const auto& [field, error] : cached_errors_) {
        errors.append(error);
    }
    return errors;
}

bool FormData::hasErrors() const {
    return !cached_errors_.empty();
}

void FormData::clearErrors() {
    cached_errors_.clear();
}

QStringList FormData::getFieldNames() const {
    QStringList names;
    for (const auto& [field, value] : data_) {
        names.append(field);
    }
    return names;
}

std::unordered_map<QString, QVariant> FormData::getAllData() const {
    return data_;
}

void FormData::setAllData(const std::unordered_map<QString, QVariant>& data) {
    data_ = data;
    
    // Emit change signals for all fields
    for (const auto& [field, value] : data_) {
        emit fieldChanged(field, value);
    }
    
    // Validate all if validation engine is available
    if (validation_engine_) {
        validateAll();
    }
}

QJsonObject FormData::toJson() const {
    QJsonObject json;
    for (const auto& [field, value] : data_) {
        json[field] = QJsonValue::fromVariant(value);
    }
    return json;
}

void FormData::fromJson(const QJsonObject& json) {
    data_.clear();
    for (auto it = json.begin(); it != json.end(); ++it) {
        data_[it.key()] = it.value().toVariant();
    }
    
    // Emit change signals
    for (const auto& [field, value] : data_) {
        emit fieldChanged(field, value);
    }
    
    // Validate all
    if (validation_engine_) {
        validateAll();
    }
}

// DataBindingEngine implementation

DataBindingEngine& DataBindingEngine::instance() {
    QMutexLocker locker(&instance_mutex_);
    if (!instance_) {
        instance_ = new DataBindingEngine();
    }
    return *instance_;
}

DataBindingEngine::DataBindingEngine(QObject* parent) : QObject(parent) {
    // Use the existing StateManager singleton
    state_manager_ = std::shared_ptr<Binding::StateManager>(&Binding::StateManager::instance(), [](Binding::StateManager*){});
    
    qDebug() << "🔗 DataBindingEngine created";
}

DataBindingEngine::~DataBindingEngine() {
    unbindAll();
}

void DataBindingEngine::bindWidget(QWidget* widget, const BindingConfig& config) {
    if (!widget) {
        qWarning() << "Cannot bind null widget";
        return;
    }
    
    // Unbind if already bound
    unbindWidget(widget);
    
    // Create bound widget
    BoundWidget bound_widget(widget, config);
    
    // Set default property if not specified
    if (bound_widget.config.widget_property.isEmpty()) {
        bound_widget.config.widget_property = getDefaultPropertyForWidget(widget);
    }
    
    // Setup the binding
    setupWidgetBinding(bound_widget);
    
    // Store the binding
    bound_widgets_[widget] = std::move(bound_widget);
    
    // Connect to widget destruction
    connect(widget, &QObject::destroyed, this, &DataBindingEngine::onWidgetDestroyed);
    
    emit widgetBound(widget, config.state_key);
    qDebug() << "🔗 Widget bound:" << widget->metaObject()->className() << "to state key:" << config.state_key;
}

void DataBindingEngine::bindWidget(QWidget* widget, const QString& state_key, const QString& property) {
    BindingConfig config(state_key, property);
    bindWidget(widget, config);
}

void DataBindingEngine::unbindWidget(QWidget* widget) {
    auto it = bound_widgets_.find(widget);
    if (it != bound_widgets_.end()) {
        disconnectWidgetSignals(it->second);
        bound_widgets_.erase(it);
        emit widgetUnbound(widget);
        qDebug() << "🔗 Widget unbound:" << widget->metaObject()->className();
    }
}

void DataBindingEngine::unbindAll() {
    for (auto& [widget, bound_widget] : bound_widgets_) {
        disconnectWidgetSignals(bound_widget);
        emit widgetUnbound(widget);
    }
    bound_widgets_.clear();
    bound_forms_.clear();
    qDebug() << "🔗 All widgets unbound";
}

void DataBindingEngine::bindForm(QWidget* form_widget, std::shared_ptr<FormData> form_data) {
    if (!form_widget || !form_data) {
        qWarning() << "Cannot bind null form widget or form data";
        return;
    }
    
    // Unbind if already bound
    unbindForm(form_widget);
    
    // Store form binding
    bound_forms_[form_widget] = form_data;
    
    // Setup form binding
    setupFormBinding(form_widget, form_data);
    
    qDebug() << "📋 Form bound:" << form_widget->metaObject()->className();
}

void DataBindingEngine::unbindForm(QWidget* form_widget) {
    auto it = bound_forms_.find(form_widget);
    if (it != bound_forms_.end()) {
        bound_forms_.erase(it);
        qDebug() << "📋 Form unbound:" << form_widget->metaObject()->className();
    }
}

std::shared_ptr<FormData> DataBindingEngine::getFormData(QWidget* form_widget) {
    auto it = bound_forms_.find(form_widget);
    return (it != bound_forms_.end()) ? it->second : nullptr;
}

void DataBindingEngine::setStateManager(std::shared_ptr<Binding::StateManager> state_manager) {
    state_manager_ = std::move(state_manager);
}

std::shared_ptr<Binding::StateManager> DataBindingEngine::getStateManager() const {
    return state_manager_;
}

void DataBindingEngine::setValidationEngine(std::shared_ptr<ValidationEngine> validation_engine) {
    validation_engine_ = std::move(validation_engine);
}

std::shared_ptr<ValidationEngine> DataBindingEngine::getValidationEngine() const {
    return validation_engine_;
}

void DataBindingEngine::syncWidgetToState(QWidget* widget) {
    auto it = bound_widgets_.find(widget);
    if (it != bound_widgets_.end()) {
        updateStateFromWidget(it->second);
    }
}

void DataBindingEngine::syncStateToWidget(QWidget* widget) {
    auto it = bound_widgets_.find(widget);
    if (it != bound_widgets_.end()) {
        updateWidgetFromState(it->second);
    }
}

void DataBindingEngine::syncAll() {
    for (auto& [widget, bound_widget] : bound_widgets_) {
        updateWidgetFromState(bound_widget);
    }
}

QStringList DataBindingEngine::getBoundWidgets() const {
    QStringList widgets;
    for (const auto& [widget, bound_widget] : bound_widgets_) {
        widgets.append(QString("%1 -> %2").arg(widget->metaObject()->className()).arg(bound_widget.config.state_key));
    }
    return widgets;
}

BindingConfig DataBindingEngine::getBindingConfig(QWidget* widget) const {
    auto it = bound_widgets_.find(widget);
    return (it != bound_widgets_.end()) ? it->second.config : BindingConfig();
}

bool DataBindingEngine::isWidgetBound(QWidget* widget) const {
    return bound_widgets_.find(widget) != bound_widgets_.end();
}

// Private slot implementations

void DataBindingEngine::onStateChanged(const QString& key, const QVariant& value) {
    // Update all widgets bound to this state key
    for (auto& [widget, bound_widget] : bound_widgets_) {
        if (bound_widget.config.state_key == key && !bound_widget.is_updating) {
            updateWidgetFromState(bound_widget);
        }
    }
}

void DataBindingEngine::onWidgetValueChanged() {
    QWidget* sender_widget = qobject_cast<QWidget*>(sender());
    if (!sender_widget) {
        return;
    }

    auto it = bound_widgets_.find(sender_widget);
    if (it != bound_widgets_.end() && !it->second.is_updating) {
        updateStateFromWidget(it->second);

        if (it->second.config.validate_on_change && validation_on_change_enabled_) {
            performValidation(it->second);
        }
    }
}

void DataBindingEngine::onWidgetDestroyed(QObject* widget) {
    QWidget* destroyed_widget = static_cast<QWidget*>(widget);
    unbindWidget(destroyed_widget);

    // Also remove from forms
    auto form_it = bound_forms_.find(destroyed_widget);
    if (form_it != bound_forms_.end()) {
        bound_forms_.erase(form_it);
    }
}

// Private helper method implementations

void DataBindingEngine::setupWidgetBinding(BoundWidget& bound_widget) {
    if (!state_manager_) {
        qWarning() << "No state manager available for binding";
        return;
    }

    // Connect to state changes
    // Note: This is a simplified connection - in a real implementation,
    // you'd need to connect to the specific state key changes

    // Connect widget signals
    connectWidgetSignals(bound_widget);

    // Initial sync from state to widget
    updateWidgetFromState(bound_widget);
}

void DataBindingEngine::connectWidgetSignals(BoundWidget& bound_widget) {
    QWidget* widget = bound_widget.widget;

    // Connect appropriate signals based on widget type
    if (auto* lineEdit = qobject_cast<QLineEdit*>(widget)) {
        bound_widget.widget_connection = connect(lineEdit, &QLineEdit::textChanged,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* textEdit = qobject_cast<QTextEdit*>(widget)) {
        bound_widget.widget_connection = connect(textEdit, &QTextEdit::textChanged,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* spinBox = qobject_cast<QSpinBox*>(widget)) {
        bound_widget.widget_connection = connect(spinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
        bound_widget.widget_connection = connect(doubleSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* comboBox = qobject_cast<QComboBox*>(widget)) {
        bound_widget.widget_connection = connect(comboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* checkBox = qobject_cast<QCheckBox*>(widget)) {
        bound_widget.widget_connection = connect(checkBox, &QCheckBox::toggled,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* radioButton = qobject_cast<QRadioButton*>(widget)) {
        bound_widget.widget_connection = connect(radioButton, &QRadioButton::toggled,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* slider = qobject_cast<QSlider*>(widget)) {
        bound_widget.widget_connection = connect(slider, &QSlider::valueChanged,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* dateEdit = qobject_cast<QDateEdit*>(widget)) {
        bound_widget.widget_connection = connect(dateEdit, &QDateEdit::dateChanged,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* timeEdit = qobject_cast<QTimeEdit*>(widget)) {
        bound_widget.widget_connection = connect(timeEdit, &QTimeEdit::timeChanged,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    } else if (auto* dateTimeEdit = qobject_cast<QDateTimeEdit*>(widget)) {
        bound_widget.widget_connection = connect(dateTimeEdit, &QDateTimeEdit::dateTimeChanged,
                                                this, &DataBindingEngine::onWidgetValueChanged);
    }
}

void DataBindingEngine::disconnectWidgetSignals(BoundWidget& bound_widget) {
    if (bound_widget.widget_connection) {
        disconnect(bound_widget.widget_connection);
        bound_widget.widget_connection = QMetaObject::Connection();
    }

    if (bound_widget.state_connection) {
        disconnect(bound_widget.state_connection);
        bound_widget.state_connection = QMetaObject::Connection();
    }
}

QString DataBindingEngine::getDefaultPropertyForWidget(QWidget* widget) const {
    if (qobject_cast<QLineEdit*>(widget) || qobject_cast<QTextEdit*>(widget)) {
        return "text";
    } else if (qobject_cast<QSpinBox*>(widget) || qobject_cast<QDoubleSpinBox*>(widget) || qobject_cast<QSlider*>(widget)) {
        return "value";
    } else if (qobject_cast<QComboBox*>(widget)) {
        return "currentIndex";
    } else if (qobject_cast<QCheckBox*>(widget) || qobject_cast<QRadioButton*>(widget)) {
        return "checked";
    } else if (qobject_cast<QDateEdit*>(widget)) {
        return "date";
    } else if (qobject_cast<QTimeEdit*>(widget)) {
        return "time";
    } else if (qobject_cast<QDateTimeEdit*>(widget)) {
        return "dateTime";
    } else if (qobject_cast<QLabel*>(widget)) {
        return "text";
    } else if (qobject_cast<QProgressBar*>(widget)) {
        return "value";
    }

    return "value";  // Default fallback
}

QVariant DataBindingEngine::getWidgetValue(QWidget* widget, const QString& property) const {
    return widget->property(property.toUtf8().constData());
}

void DataBindingEngine::setWidgetValue(QWidget* widget, const QString& property, const QVariant& value) {
    widget->setProperty(property.toUtf8().constData(), value);
}

void DataBindingEngine::updateWidgetFromState(BoundWidget& bound_widget) {
    if (!state_manager_ || bound_widget.is_updating) {
        return;
    }

    bound_widget.is_updating = true;

    try {
        // Get value from state (simplified - in real implementation, you'd use the StateManager API)
        QVariant state_value; // = state_manager_->getValue(bound_widget.config.state_key);

        // Apply transformation if available
        if (bound_widget.config.to_widget_transformer) {
            state_value = bound_widget.config.to_widget_transformer(state_value);
        }

        // Set widget value
        setWidgetValue(bound_widget.widget, bound_widget.config.widget_property, state_value);

    } catch (const std::exception& e) {
        emit bindingError(bound_widget.widget, QString("Failed to update widget from state: %1").arg(e.what()));
    }

    bound_widget.is_updating = false;
}

void DataBindingEngine::updateStateFromWidget(BoundWidget& bound_widget) {
    if (!state_manager_ || bound_widget.is_updating || !bound_widget.config.two_way) {
        return;
    }

    bound_widget.is_updating = true;

    try {
        // Get value from widget
        QVariant widget_value = getWidgetValue(bound_widget.widget, bound_widget.config.widget_property);

        // Apply transformation if available
        if (bound_widget.config.to_state_transformer) {
            widget_value = bound_widget.config.to_state_transformer(widget_value);
        }

        // Set state value (simplified - in real implementation, you'd use the StateManager API)
        // state_manager_->setValue(bound_widget.config.state_key, widget_value);

    } catch (const std::exception& e) {
        emit bindingError(bound_widget.widget, QString("Failed to update state from widget: %1").arg(e.what()));
    }

    bound_widget.is_updating = false;
}

void DataBindingEngine::performValidation(BoundWidget& bound_widget) {
    if (!validation_engine_) {
        return;
    }

    try {
        QVariant widget_value = getWidgetValue(bound_widget.widget, bound_widget.config.widget_property);
        QString field_name = bound_widget.config.state_key;

        ValidationResult result = validation_engine_->validateField(field_name, widget_value);
        emit validationTriggered(bound_widget.widget, field_name, result.is_valid);

        // You could also update widget styling based on validation result
        if (!result.is_valid) {
            // Add error styling
            bound_widget.widget->setProperty("validationError", true);
            bound_widget.widget->setToolTip(result.error_message);
        } else {
            // Remove error styling
            bound_widget.widget->setProperty("validationError", false);
            bound_widget.widget->setToolTip(QString());
        }

        // Force style update
        bound_widget.widget->style()->unpolish(bound_widget.widget);
        bound_widget.widget->style()->polish(bound_widget.widget);

    } catch (const std::exception& e) {
        emit bindingError(bound_widget.widget, QString("Validation failed: %1").arg(e.what()));
    }
}

void DataBindingEngine::setupFormBinding(QWidget* form_widget, std::shared_ptr<FormData> form_data) {
    // Scan form for input widgets and bind them automatically
    scanFormWidgets(form_widget, form_data);
}

void DataBindingEngine::scanFormWidgets(QWidget* parent, std::shared_ptr<FormData> form_data) {
    // Recursively scan for input widgets
    const QList<QWidget*> children = parent->findChildren<QWidget*>();

    for (QWidget* child : children) {
        QString field_name = extractFieldNameFromWidget(child);
        if (!field_name.isEmpty()) {
            // Create a binding config for this widget
            BindingConfig config(QString("form.%1").arg(field_name));

            // Bind the widget
            bindWidget(child, config);

            // Connect to form data
            connect(child, &QWidget::destroyed, this, [form_data, field_name]() {
                form_data->removeField(field_name);
            });
        }
    }
}

QString DataBindingEngine::extractFieldNameFromWidget(QWidget* widget) const {
    // Try to extract field name from object name
    QString object_name = widget->objectName();
    if (!object_name.isEmpty()) {
        return object_name;
    }

    // Try to extract from accessible name
    QString accessible_name = widget->accessibleName();
    if (!accessible_name.isEmpty()) {
        return accessible_name;
    }

    // Try to extract from tooltip or other properties
    QString tooltip = widget->toolTip();
    if (!tooltip.isEmpty() && tooltip.contains("field:")) {
        return tooltip.split("field:").last().trimmed();
    }

    return QString();
}

} // namespace DataBinding
} // namespace DeclarativeUI
