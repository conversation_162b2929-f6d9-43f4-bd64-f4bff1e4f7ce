// Core/UIElement.cpp
#include "UIElement.hpp"
#include <QApplication>
#include <QTimer>

namespace DeclarativeUI::Core {

UIElement::UIElement(QObject *parent) : QObject(parent) {
    // **Optimized initialization with lazy timer creation**
    try {
        // **Defer timer creation until first property update for better performance**
        // Timer will be created in onPropertyUpdated() if needed

        // **Pre-allocate common containers to avoid reallocations**
        properties_.reserve(8); // Most widgets have < 8 properties
        event_handlers_.reserve(4); // Most widgets have < 4 event handlers

        // **Initialize performance tracking**
        performance_metrics_.creation_time = std::chrono::steady_clock::now();

    } catch (const std::exception &e) {
        throw Exceptions::UIException("Failed to initialize UIElement: " +
                                      std::string(e.what()));
    }
}

UIElement &UIElement::bindProperty(
    const QString &property, const std::function<PropertyValue()> &binding) {
    if (!binding) {
        throw Exceptions::PropertyBindingException(
            property.toStdString() + ": binding function is null");
    }

    bindings_[property] = binding;

    // **Immediate update with exception handling**
    try {
        PropertyValue value = binding();
        setProperty(property, value);
    } catch (const std::exception &e) {
        throw Exceptions::PropertyBindingException(property.toStdString() +
                                                   ": " + e.what());
    }

    return *this;
}

UIElement &UIElement::onEvent(const QString &event,
                              const std::function<void()> &handler) {
    if (!handler) {
        throw std::invalid_argument("Event handler cannot be null");
    }

    event_handlers_[event] = handler;

    // **Connect to Qt signals if widget exists**
    if (widget_) {
        connectSignals();
    }

    return *this;
}

PropertyValue UIElement::getProperty(const QString &name) const {
    auto it = properties_.find(name);
    if (it == properties_.end()) {
        throw Exceptions::PropertyBindingException("Property not found: " +
                                                   name.toStdString());
    }
    return it->second;
}

void UIElement::cleanup() noexcept {
    try {
        event_handlers_.clear();
        bindings_.clear();
        properties_.clear();
        widget_.reset();
    } catch (...) {
        // **No-throw cleanup guarantee**
    }
}

bool UIElement::comparePropertyValues(const PropertyValue& a, const PropertyValue& b) const {
    // Handle std::function comparison (always consider them different for safety)
    if (std::holds_alternative<std::function<void()>>(a) ||
        std::holds_alternative<std::function<void()>>(b)) {
        return false; // Always update functions
    }

    // Check if variants hold the same type
    if (a.index() != b.index()) {
        return false;
    }

    // Use visitor pattern for safe comparison
    return std::visit([](const auto& val_a, const auto& val_b) -> bool {
        using T = std::decay_t<decltype(val_a)>;
        using U = std::decay_t<decltype(val_b)>;

        if constexpr (std::is_same_v<T, U>) {
            if constexpr (std::is_same_v<T, std::function<void()>>) {
                return false; // Functions are never equal
            } else if constexpr (std::is_same_v<T, QIcon>) {
                // QIcon doesn't have equality operator, compare by serialization
                return val_a.cacheKey() == val_b.cacheKey();
            } else if constexpr (std::is_same_v<T, QPixmap>) {
                // QPixmap doesn't have equality operator, compare by cache key
                return val_a.cacheKey() == val_b.cacheKey();
            } else {
                return val_a == val_b;
            }
        } else {
            return false;
        }
    }, a, b);
}

void UIElement::updateBoundProperties() {
    // Batch property updates for better performance
    std::vector<std::pair<QString, PropertyValue>> updates;
    updates.reserve(bindings_.size());

    // First pass: compute all new values
    for (const auto &[property, binding] : bindings_) {
        try {
            PropertyValue new_value = binding();

            // Only update if value actually changed
            auto current_it = properties_.find(property);
            bool should_update = (current_it == properties_.end());

            if (!should_update) {
                // Compare variants safely (handle std::function case)
                should_update = !comparePropertyValues(current_it->second, new_value);
            }

            if (should_update) {
                updates.emplace_back(property, new_value);
            }
        } catch (const std::exception &e) {
            qWarning() << "Property binding computation failed for" << property
                       << ":" << e.what();
        }
    }

    // Second pass: apply all updates at once
    if (!updates.empty() && widget_) {
        // Block signals during batch update to prevent cascading updates
        bool signals_blocked = widget_->signalsBlocked();
        widget_->blockSignals(true);

        for (const auto &[property, value] : updates) {
            properties_[property] = value;

            std::visit(
                [&](const auto &val) {
                    widget_->setProperty(property.toUtf8().constData(),
                                         QVariant::fromValue(val));
                },
                value);
        }

        // Restore signal state
        widget_->blockSignals(signals_blocked);

        // Emit update notifications for changed properties
        if (!signals_blocked) {
            for (const auto &[property, value] : updates) {
                emit propertyUpdated(property);
            }
        }
    }
}

void UIElement::connectSignals() {
    // **Dynamic signal connection using Qt's meta-object system**
    if (!widget_)
        return;

    const QMetaObject *metaObj = widget_->metaObject();

    for (const auto &[event, handler] : event_handlers_) {
        // **Find and connect signals dynamically**
        for (int i = 0; i < metaObj->methodCount(); ++i) {
            QMetaMethod method = metaObj->method(i);

            if (method.methodType() == QMetaMethod::Signal &&
                QString::fromUtf8(method.name()) == event) {
                QMetaObject::invokeMethod(
                    widget_.get(), method.name(), Qt::DirectConnection,
                    Q_ARG(std::function<void()>, handler));
                break;
            }
        }
    }
}

void UIElement::onPropertyChanged() { updateBoundProperties(); }

void UIElement::setWidget(QWidget *widget) {
    if (!widget) {
        throw Exceptions::UIException("Widget cannot be null");
    }

    widget_ = std::unique_ptr<QWidget>(widget);
    applyStoredProperties();
    connectSignals();
}

void UIElement::applyStoredProperties() {
    if (!widget_)
        return;

    for (const auto &[name, value] : properties_) {
        try {
            std::visit(
                [&](const auto &val) {
                    widget_->setProperty(name.toUtf8().constData(),
                                         QVariant::fromValue(val));
                },
                value);
        } catch (const std::exception &e) {
            qWarning() << "Failed to apply property" << name << ":" << e.what();
        }
    }
}

// **Implementation of missing virtual methods**
void UIElement::refresh() {
    if (!widget_)
        return;

    // Apply all stored properties
    applyStoredProperties();

    // Update the widget display
    widget_->update();

    // Emit refresh signal
    emit refreshed();
}

void UIElement::invalidate() {
    if (!widget_)
        return;

    // Mark widget as needing repaint
    widget_->repaint();

    // Emit invalidated signal
    emit invalidated();
}

bool UIElement::validate() const {
    // Run all validators
    for (const auto &validator : validators_) {
        if (!validator(this)) {
            return false;
        }
    }

    // Check if widget exists and is visible
    if (widget_ && widget_->isVisible()) {
        return true;
    }

    return widget_ != nullptr;
}

void UIElement::onWidgetResized() {
    if (!widget_)
        return;

    // Handle responsive breakpoints
    int width = widget_->width();
    for (const auto &[breakpoint, handler] : breakpoints_) {
        if (width >= breakpoint) {
            handler();
        }
    }

    // Emit resize signal
    emit widgetResized();
}

void UIElement::onAnimationFinished() {
    // Update performance metrics
    performance_metrics_.update_count++;

    // Emit animation finished signal with empty property name
    emit animationFinished(QString());
}

}  // namespace DeclarativeUI::Core