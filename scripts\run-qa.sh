#!/bin/bash
# Comprehensive Quality Assurance Script for DeclarativeUI Framework

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
BUILD_DIR="build-qa"
REPORT_DIR="qa-reports"
PARALLEL_JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# Parse command line arguments
ENABLE_STATIC_ANALYSIS=true
ENABLE_TESTS=true
ENABLE_COVERAGE=false
ENABLE_SANITIZERS=false
ENABLE_BENCHMARKS=false
CLEAN_BUILD=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-static-analysis)
            ENABLE_STATIC_ANALYSIS=false
            shift
            ;;
        --no-tests)
            ENABLE_TESTS=false
            shift
            ;;
        --coverage)
            ENABLE_COVERAGE=true
            shift
            ;;
        --sanitizers)
            ENABLE_SANITIZERS=true
            shift
            ;;
        --benchmarks)
            ENABLE_BENCHMARKS=true
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --no-static-analysis  Skip static analysis"
            echo "  --no-tests           Skip running tests"
            echo "  --coverage           Enable code coverage"
            echo "  --sanitizers         Enable runtime sanitizers"
            echo "  --benchmarks         Run benchmark tests"
            echo "  --clean              Clean build before running"
            echo "  --verbose            Verbose output"
            echo "  --help               Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}🔍 DeclarativeUI Framework Quality Assurance${NC}"
echo -e "${BLUE}=============================================${NC}"

# Create directories
mkdir -p "$REPORT_DIR"

# Clean build if requested
if [ "$CLEAN_BUILD" = true ]; then
    echo -e "${YELLOW}🧹 Cleaning build directory...${NC}"
    rm -rf "$BUILD_DIR"
fi

# =============================================================================
# Build Configuration
# =============================================================================

echo -e "${BLUE}🔧 Configuring build...${NC}"

CMAKE_ARGS=(
    "-B" "$BUILD_DIR"
    "-DCMAKE_BUILD_TYPE=Debug"
    "-DBUILD_TESTS=ON"
    "-DBUILD_EXAMPLES=ON"
    "-DDECLARATIVE_UI_DEBUG=ON"
)

if [ "$ENABLE_STATIC_ANALYSIS" = true ]; then
    CMAKE_ARGS+=("-DENABLE_CLANG_TIDY=ON")
    CMAKE_ARGS+=("-DENABLE_CPPCHECK=ON")
fi

if [ "$ENABLE_COVERAGE" = true ]; then
    CMAKE_ARGS+=("-DENABLE_TEST_COVERAGE=ON")
fi

if [ "$ENABLE_SANITIZERS" = true ]; then
    CMAKE_ARGS+=("-DENABLE_SANITIZERS=ON")
    CMAKE_ARGS+=("-DENABLE_ASAN=ON")
    CMAKE_ARGS+=("-DENABLE_UBSAN=ON")
fi

if [ "$ENABLE_BENCHMARKS" = true ]; then
    CMAKE_ARGS+=("-DENABLE_BENCHMARK_TESTS=ON")
fi

# Configure
cmake "${CMAKE_ARGS[@]}"

# =============================================================================
# Build
# =============================================================================

echo -e "${BLUE}🔨 Building project...${NC}"

BUILD_ARGS=(
    "--build" "$BUILD_DIR"
    "--config" "Debug"
    "--parallel" "$PARALLEL_JOBS"
)

if [ "$VERBOSE" = true ]; then
    BUILD_ARGS+=("--verbose")
fi

cmake "${BUILD_ARGS[@]}"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build successful${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# =============================================================================
# Static Analysis
# =============================================================================

if [ "$ENABLE_STATIC_ANALYSIS" = true ]; then
    echo -e "${BLUE}🔍 Running static analysis...${NC}"
    
    # Clang-Tidy
    if command -v clang-tidy >/dev/null 2>&1; then
        echo -e "${PURPLE}Running clang-tidy...${NC}"
        cd "$BUILD_DIR"
        make clang-tidy 2>&1 | tee "../$REPORT_DIR/clang-tidy-report.txt" || true
        cd ..
        echo -e "${GREEN}✅ Clang-tidy analysis complete${NC}"
    else
        echo -e "${YELLOW}⚠️  clang-tidy not found, skipping${NC}"
    fi
    
    # Cppcheck
    if command -v cppcheck >/dev/null 2>&1; then
        echo -e "${PURPLE}Running cppcheck...${NC}"
        cd "$BUILD_DIR"
        make cppcheck 2>&1 | tee "../$REPORT_DIR/cppcheck-report.txt" || true
        cd ..
        echo -e "${GREEN}✅ Cppcheck analysis complete${NC}"
    else
        echo -e "${YELLOW}⚠️  cppcheck not found, skipping${NC}"
    fi
    
    # Include What You Use
    if command -v include-what-you-use >/dev/null 2>&1; then
        echo -e "${PURPLE}Running include-what-you-use...${NC}"
        cd "$BUILD_DIR"
        make iwyu 2>&1 | tee "../$REPORT_DIR/iwyu-report.txt" || true
        cd ..
        echo -e "${GREEN}✅ IWYU analysis complete${NC}"
    else
        echo -e "${YELLOW}⚠️  include-what-you-use not found, skipping${NC}"
    fi
fi

# =============================================================================
# Testing
# =============================================================================

if [ "$ENABLE_TESTS" = true ]; then
    echo -e "${BLUE}🧪 Running tests...${NC}"
    
    cd "$BUILD_DIR"
    
    # Run unit tests
    echo -e "${PURPLE}Running unit tests...${NC}"
    ctest --label-regex "unit" --output-on-failure --parallel "$PARALLEL_JOBS" 2>&1 | tee "../$REPORT_DIR/unit-tests.txt"
    UNIT_TEST_RESULT=$?
    
    # Run integration tests
    echo -e "${PURPLE}Running integration tests...${NC}"
    ctest --label-regex "integration" --output-on-failure --parallel "$PARALLEL_JOBS" 2>&1 | tee "../$REPORT_DIR/integration-tests.txt"
    INTEGRATION_TEST_RESULT=$?
    
    # Run benchmark tests if enabled
    if [ "$ENABLE_BENCHMARKS" = true ]; then
        echo -e "${PURPLE}Running benchmark tests...${NC}"
        ctest --label-regex "benchmark" --output-on-failure 2>&1 | tee "../$REPORT_DIR/benchmark-tests.txt"
        BENCHMARK_TEST_RESULT=$?
    fi
    
    cd ..
    
    # Report test results
    if [ $UNIT_TEST_RESULT -eq 0 ]; then
        echo -e "${GREEN}✅ Unit tests passed${NC}"
    else
        echo -e "${RED}❌ Unit tests failed${NC}"
    fi
    
    if [ $INTEGRATION_TEST_RESULT -eq 0 ]; then
        echo -e "${GREEN}✅ Integration tests passed${NC}"
    else
        echo -e "${RED}❌ Integration tests failed${NC}"
    fi
    
    if [ "$ENABLE_BENCHMARKS" = true ]; then
        if [ $BENCHMARK_TEST_RESULT -eq 0 ]; then
            echo -e "${GREEN}✅ Benchmark tests passed${NC}"
        else
            echo -e "${RED}❌ Benchmark tests failed${NC}"
        fi
    fi
fi

# =============================================================================
# Code Coverage
# =============================================================================

if [ "$ENABLE_COVERAGE" = true ]; then
    echo -e "${BLUE}📊 Generating code coverage report...${NC}"
    
    cd "$BUILD_DIR"
    
    if command -v lcov >/dev/null 2>&1 && command -v genhtml >/dev/null 2>&1; then
        make coverage 2>&1 | tee "../$REPORT_DIR/coverage.txt"
        
        # Copy coverage report
        if [ -d "coverage-html" ]; then
            cp -r coverage-html "../$REPORT_DIR/"
            echo -e "${GREEN}✅ Coverage report generated at $REPORT_DIR/coverage-html/index.html${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  lcov/genhtml not found, skipping coverage${NC}"
    fi
    
    cd ..
fi

# =============================================================================
# Memory Testing
# =============================================================================

if command -v valgrind >/dev/null 2>&1 && [ "$ENABLE_SANITIZERS" = false ]; then
    echo -e "${BLUE}🧠 Running memory tests...${NC}"
    
    cd "$BUILD_DIR"
    ctest -T MemCheck --output-on-failure 2>&1 | tee "../$REPORT_DIR/memcheck.txt" || true
    cd ..
    
    echo -e "${GREEN}✅ Memory testing complete${NC}"
fi

# =============================================================================
# Documentation Generation
# =============================================================================

if command -v doxygen >/dev/null 2>&1; then
    echo -e "${BLUE}📚 Generating documentation...${NC}"
    
    doxygen Doxyfile 2>&1 | tee "$REPORT_DIR/doxygen.txt"
    
    if [ -d "docs/generated/html" ]; then
        echo -e "${GREEN}✅ Documentation generated at docs/generated/html/index.html${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Doxygen not found, skipping documentation${NC}"
fi

# =============================================================================
# Security Analysis
# =============================================================================

echo -e "${BLUE}🔒 Running security analysis...${NC}"

# Check for common security issues
echo -e "${PURPLE}Checking for security issues...${NC}"

# Look for potential security issues in source code
find src -name "*.cpp" -o -name "*.hpp" | xargs grep -n "strcpy\|strcat\|sprintf\|gets" > "$REPORT_DIR/security-issues.txt" 2>/dev/null || true

if [ -s "$REPORT_DIR/security-issues.txt" ]; then
    echo -e "${YELLOW}⚠️  Potential security issues found (see $REPORT_DIR/security-issues.txt)${NC}"
else
    echo -e "${GREEN}✅ No obvious security issues found${NC}"
fi

# =============================================================================
# Performance Analysis
# =============================================================================

if [ "$ENABLE_BENCHMARKS" = true ]; then
    echo -e "${BLUE}⚡ Running performance analysis...${NC}"
    
    # Run performance tests and collect metrics
    cd "$BUILD_DIR"
    
    # Look for performance test executables
    find tests -name "*performance*" -executable -type f | while read -r perf_test; do
        echo -e "${PURPLE}Running $perf_test...${NC}"
        ./"$perf_test" 2>&1 | tee "../$REPORT_DIR/$(basename "$perf_test")-results.txt" || true
    done
    
    cd ..
    
    echo -e "${GREEN}✅ Performance analysis complete${NC}"
fi

# =============================================================================
# Report Generation
# =============================================================================

echo -e "${BLUE}📋 Generating QA summary report...${NC}"

REPORT_FILE="$REPORT_DIR/qa-summary.md"

cat > "$REPORT_FILE" << EOF
# Quality Assurance Report

**Generated:** $(date)
**Project:** DeclarativeUI Framework
**Build Configuration:** Debug

## Summary

EOF

# Add test results
if [ "$ENABLE_TESTS" = true ]; then
    echo "### Test Results" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    if [ $UNIT_TEST_RESULT -eq 0 ]; then
        echo "- ✅ Unit Tests: PASSED" >> "$REPORT_FILE"
    else
        echo "- ❌ Unit Tests: FAILED" >> "$REPORT_FILE"
    fi
    
    if [ $INTEGRATION_TEST_RESULT -eq 0 ]; then
        echo "- ✅ Integration Tests: PASSED" >> "$REPORT_FILE"
    else
        echo "- ❌ Integration Tests: FAILED" >> "$REPORT_FILE"
    fi
    
    echo "" >> "$REPORT_FILE"
fi

# Add static analysis results
if [ "$ENABLE_STATIC_ANALYSIS" = true ]; then
    echo "### Static Analysis" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    if [ -f "$REPORT_DIR/clang-tidy-report.txt" ]; then
        TIDY_ISSUES=$(grep -c "warning:" "$REPORT_DIR/clang-tidy-report.txt" 2>/dev/null || echo "0")
        echo "- Clang-Tidy: $TIDY_ISSUES warnings found" >> "$REPORT_FILE"
    fi
    
    if [ -f "$REPORT_DIR/cppcheck-report.txt" ]; then
        CPPCHECK_ISSUES=$(grep -c "error\|warning" "$REPORT_DIR/cppcheck-report.txt" 2>/dev/null || echo "0")
        echo "- Cppcheck: $CPPCHECK_ISSUES issues found" >> "$REPORT_FILE"
    fi
    
    echo "" >> "$REPORT_FILE"
fi

# Add coverage information
if [ "$ENABLE_COVERAGE" = true ] && [ -f "$REPORT_DIR/coverage.txt" ]; then
    echo "### Code Coverage" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    COVERAGE_PERCENT=$(grep -o "lines......: [0-9.]*%" "$REPORT_DIR/coverage.txt" | tail -1 | grep -o "[0-9.]*%" || echo "N/A")
    echo "- Line Coverage: $COVERAGE_PERCENT" >> "$REPORT_FILE"
    echo "- [Detailed Report](coverage-html/index.html)" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
fi

echo "### Reports Generated" >> "$REPORT_FILE"
echo "" >> "$REPORT_FILE"
echo "All detailed reports are available in the \`$REPORT_DIR\` directory:" >> "$REPORT_FILE"
echo "" >> "$REPORT_FILE"

for report in "$REPORT_DIR"/*.txt; do
    if [ -f "$report" ]; then
        echo "- [$(basename "$report")]($(basename "$report"))" >> "$REPORT_FILE"
    fi
done

echo -e "${GREEN}✅ QA summary report generated: $REPORT_FILE${NC}"

# =============================================================================
# Final Summary
# =============================================================================

echo ""
echo -e "${BLUE}📊 Quality Assurance Complete${NC}"
echo -e "${BLUE}=============================${NC}"

OVERALL_STATUS="PASSED"

if [ "$ENABLE_TESTS" = true ]; then
    if [ $UNIT_TEST_RESULT -ne 0 ] || [ $INTEGRATION_TEST_RESULT -ne 0 ]; then
        OVERALL_STATUS="FAILED"
    fi
fi

if [ "$OVERALL_STATUS" = "PASSED" ]; then
    echo -e "${GREEN}🎉 Overall Status: PASSED${NC}"
    exit 0
else
    echo -e "${RED}💥 Overall Status: FAILED${NC}"
    exit 1
fi
