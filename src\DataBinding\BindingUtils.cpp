#include "DataBindingEngine.hpp"
#include <QDebug>
#include <QRegularExpression>

namespace DeclarativeUI {
namespace DataBinding {
namespace BindingUtils {

std::shared_ptr<FormData> createFormData(const std::unordered_map<QString, ValidationRule>& rules) {
    auto form_data = std::make_shared<FormData>();
    
    if (!rules.empty()) {
        auto validation_engine = std::make_shared<ValidationEngine>();
        
        for (const auto& [field_name, rule] : rules) {
            // Since ValidationRule is move-only, we need to create rules differently
            // For now, just create a basic rule - this would need to be enhanced
            // to properly copy the validators from the source rule
            validation_engine->addRule(field_name);
        }
        
        form_data->setValidationEngine(validation_engine);
    }
    
    return form_data;
}

void bindInput(QWidget* widget, const QString& state_key) {
    DataBindingEngine::instance().bindWidget(widget, state_key);
}

void bindForm(QWidget* form_widget, const QString& form_state_prefix) {
    auto form_data = createFormData();
    DataBindingEngine::instance().bindForm(form_widget, form_data);
}

// Data transformer implementations

DataTransformer createStringToIntTransformer() {
    return [](const QVariant& value) -> QVariant {
        bool ok;
        int result = value.toString().toInt(&ok);
        return ok ? QVariant(result) : QVariant(0);
    };
}

DataTransformer createIntToStringTransformer() {
    return [](const QVariant& value) -> QVariant {
        return QVariant(QString::number(value.toInt()));
    };
}

DataTransformer createStringToDoubleTransformer() {
    return [](const QVariant& value) -> QVariant {
        bool ok;
        double result = value.toString().toDouble(&ok);
        return ok ? QVariant(result) : QVariant(0.0);
    };
}

DataTransformer createDoubleToStringTransformer() {
    return [](const QVariant& value) -> QVariant {
        return QVariant(QString::number(value.toDouble()));
    };
}

DataTransformer createBoolToStringTransformer(const QString& true_value, const QString& false_value) {
    return [true_value, false_value](const QVariant& value) -> QVariant {
        return value.toBool() ? QVariant(true_value) : QVariant(false_value);
    };
}

DataTransformer createStringToBoolTransformer(const QString& true_value) {
    return [true_value](const QVariant& value) -> QVariant {
        return QVariant(value.toString().compare(true_value, Qt::CaseInsensitive) == 0);
    };
}

// Validation rule creators

ValidationRule createEmailValidation() {
    ValidationRule rule;
    rule.required().email();
    return rule;
}

ValidationRule createPasswordValidation(int min_length) {
    ValidationRule rule;
    rule.required()
        .minLength(min_length)
        .custom([](const QVariant& value, const QString& field_name) -> ValidationResult {
            QString password = value.toString();
            
            // Check for at least one uppercase letter
            if (!password.contains(QRegularExpression("[A-Z]"))) {
                return ValidationResult::error("Password must contain at least one uppercase letter", field_name);
            }
            
            // Check for at least one lowercase letter
            if (!password.contains(QRegularExpression("[a-z]"))) {
                return ValidationResult::error("Password must contain at least one lowercase letter", field_name);
            }
            
            // Check for at least one digit
            if (!password.contains(QRegularExpression("[0-9]"))) {
                return ValidationResult::error("Password must contain at least one digit", field_name);
            }
            
            // Check for at least one special character
            if (!password.contains(QRegularExpression("[!@#$%^&*(),.?\":{}|<>]"))) {
                return ValidationResult::error("Password must contain at least one special character", field_name);
            }
            
            return ValidationResult::success(value);
        }, "Password must contain uppercase, lowercase, digit, and special character", "password_strength");
    
    return rule;
}

ValidationRule createPhoneValidation() {
    ValidationRule rule;
    rule.required()
        .regex(R"(^[\+]?[1-9][\d]{0,15}$)", "Phone number must be in valid international format");
    return rule;
}

ValidationRule createRequiredTextValidation(int min_length, int max_length) {
    ValidationRule rule;
    rule.required();
    
    if (min_length > 0) {
        rule.minLength(min_length);
    }
    
    if (max_length > 0) {
        rule.maxLength(max_length);
    }
    
    return rule;
}

ValidationRule createNumericValidation(double min_value, double max_value) {
    ValidationRule rule;
    rule.required().range(min_value, max_value);
    return rule;
}

} // namespace BindingUtils
} // namespace DataBinding
} // namespace DeclarativeUI
