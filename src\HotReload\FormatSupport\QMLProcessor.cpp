#include "QMLProcessor.hpp"
#include <QDebug>
#include <QQmlError>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QElapsedTimer>
#include <QJsonArray>
#include <QFileInfo>
#include <QDir>
#include <QQmlComponent>
#include <QQmlContext>

namespace DeclarativeUI::HotReload::FormatSupport {

QMLProcessor::QMLProcessor(QObject* parent) : IFormatProcessor(parent) {
    setupQMLEngine();
    
    // Setup regex patterns for parsing
    import_regex_.setPattern(R"(^\s*import\s+([^\s]+)(?:\s+(\d+\.\d+))?(?:\s+as\s+(\w+))?\s*$)");
    component_regex_.setPattern(R"(^\s*(\w+)\s*\{)");
}

void QMLProcessor::setupQMLEngine() {
    qml_engine_ = std::make_unique<QQmlEngine>(this);

    // Add standard import paths
    QStringList standard_paths = qml_engine_->importPathList();
    for (const QString& path : standard_paths) {
        addImportPath(path);
    }
    
    // Add application-specific paths
    QString app_dir = QCoreApplication::applicationDirPath();
    addImportPath(app_dir + "/qml");
    addImportPath(app_dir + "/imports");
}

void QMLProcessor::addImportPath(const QString& path) {
    if (!import_paths_.contains(path)) {
        import_paths_.append(path);
        if (qml_engine_) {
            qml_engine_->addImportPath(path);
        }
    }
}

bool QMLProcessor::canProcess(const QString& file_path) const {
    QFileInfo file_info(file_path);
    QString extension = file_info.suffix().toLower();
    return getSupportedExtensions().contains(extension);
}

ProcessingResult QMLProcessor::processFile(const QString& file_path, const ProcessingConfig& config) {
    startPerformanceMeasurement();
    emit processingStarted(file_path);
    
    try {
        if (!QFileInfo::exists(file_path)) {
            return ProcessingResult::createError("QML file does not exist: " + file_path);
        }
        
        QString content = readFileContent(file_path);
        if (content.isEmpty()) {
            return ProcessingResult::createError("Failed to read QML file: " + file_path);
        }
        
        return processContent(content, file_path, config);
        
    } catch (const std::exception& e) {
        QString error = QString("QML processing error: %1").arg(e.what());
        emit processingError(file_path, error);
        return ProcessingResult::createError(error);
    }
}

ProcessingResult QMLProcessor::processContent(const QString& content, const QString& file_path, const ProcessingConfig& config) {
    Q_UNUSED(config)
    
    try {
        // Validate QML syntax
        QString error_message;
        if (!hasQMLSyntaxErrors(content, error_message)) {
            return ProcessingResult::createError("QML syntax error: " + error_message);
        }
        
        // Extract dependencies
        QStringList imports = extractImports(content);
        QStringList dependencies = extractDependencies(file_path);
        
        // Create metadata
        QJsonObject metadata = createQMLMetadata(file_path, content);
        metadata["imports"] = QJsonArray::fromStringList(imports);
        metadata["dependencies"] = QJsonArray::fromStringList(dependencies);
        
        // Create result
        ProcessingResult result = ProcessingResult::createSuccess(content, metadata);
        result.processing_time_ms = endPerformanceMeasurement();
        
        emit processingFinished(file_path, result);
        emit qmlImportsChanged(file_path, imports);
        
        return result;
        
    } catch (const std::exception& e) {
        QString error = QString("QML content processing error: %1").arg(e.what());
        emit processingError(file_path, error);
        return ProcessingResult::createError(error);
    }
}

bool QMLProcessor::validateFile(const QString& file_path) const {
    if (!QFileInfo::exists(file_path)) {
        return false;
    }
    
    QString content = readFileContent(file_path);
    return validateContent(content);
}

bool QMLProcessor::validateContent(const QString& content) const {
    QString error_message;
    return hasQMLSyntaxErrors(content, error_message);
}

ProcessingConfig QMLProcessor::getDefaultConfig() const {
    ProcessingConfig config;
    config.enable_live_injection = true;
    config.enable_caching = true;
    config.max_processing_time_ms = 3000;
    return config;
}

bool QMLProcessor::isAvailable() const {
    return qml_engine_ != nullptr;
}

ProcessingResult QMLProcessor::prepareLiveInjection(const QString& content, const QString& file_path) {
    try {
        // Create QML component for validation
        QQmlComponent* component = createComponent(content, file_path);
        if (!component) {
            return ProcessingResult::createError("Failed to create QML component");
        }
        
        if (component->isError()) {
            QStringList errors;
            for (const QQmlError& error : component->errors()) {
                errors.append(formatQMLError(error));
            }
            component->deleteLater();
            return ProcessingResult::createError("QML component errors: " + errors.join("; "));
        }
        
        emit qmlComponentReady(file_path, component);
        
        // Prepare for live injection
        QJsonObject metadata;
        metadata["component_ready"] = true;
        metadata["injection_method"] = "qml_component";
        
        ProcessingResult result = ProcessingResult::createSuccess(content, metadata);
        result.additional_data["qml_component"] = QVariant::fromValue(component);
        
        return result;
        
    } catch (const std::exception& e) {
        return ProcessingResult::createError("Live injection preparation failed: " + QString(e.what()));
    }
}

QStringList QMLProcessor::extractImports(const QString& content) const {
    QStringList imports;
    QStringList lines = content.split('\n');
    
    for (const QString& line : lines) {
        QRegularExpressionMatch match = import_regex_.match(line.trimmed());
        if (match.hasMatch()) {
            QString import_statement = match.captured(1);
            if (!import_statement.isEmpty()) {
                imports.append(import_statement);
            }
        }
    }
    
    return imports;
}

QStringList QMLProcessor::extractDependencies(const QString& file_path) const {
    QStringList dependencies;
    QString content = readFileContent(file_path);
    QStringList imports = extractImports(content);
    
    QFileInfo file_info(file_path);
    QString base_path = file_info.absolutePath();
    
    for (const QString& import : imports) {
        QString resolved_path = resolveImportPath(import, base_path);
        if (!resolved_path.isEmpty() && QFileInfo::exists(resolved_path)) {
            dependencies.append(resolved_path);
            // Note: Cannot emit signal from const method
        }
    }
    
    return dependencies;
}

QString QMLProcessor::resolveImportPath(const QString& import_statement, const QString& base_path) const {
    // Handle relative imports
    if (import_statement.startsWith("./") || import_statement.startsWith("../")) {
        QString relative_path = QDir(base_path).absoluteFilePath(import_statement);
        if (QFileInfo::exists(relative_path + ".qml")) {
            return relative_path + ".qml";
        }
    }
    
    // Handle module imports
    for (const QString& import_path : import_paths_) {
        QString import_copy = import_statement;
        QString module_path = import_path + "/" + import_copy.replace('.', '/');
        if (QFileInfo::exists(module_path + ".qml")) {
            return module_path + ".qml";
        }
        if (QFileInfo::exists(module_path + "/qmldir")) {
            return module_path + "/qmldir";
        }
    }
    
    return QString();
}

bool QMLProcessor::hasQMLSyntaxErrors(const QString& content, QString& error_message) const {
    if (!qml_engine_) {
        error_message = "QML engine not available";
        return false;
    }
    
    QQmlComponent component(qml_engine_.get());
    component.setData(content.toUtf8(), QUrl());
    
    if (component.isError()) {
        QStringList errors;
        for (const QQmlError& error : component.errors()) {
            errors.append(formatQMLError(error));
        }
        error_message = errors.join("; ");
        return false;
    }
    
    return true;
}

QQmlComponent* QMLProcessor::createComponent(const QString& content, const QString& file_path) {
    if (!qml_engine_) {
        return nullptr;
    }
    
    QQmlComponent* component = new QQmlComponent(qml_engine_.get(), this);
    QUrl file_url = QUrl::fromLocalFile(file_path);
    component->setData(content.toUtf8(), file_url);
    
    return component;
}

QString QMLProcessor::extractComponentName(const QString& file_path) const {
    QFileInfo file_info(file_path);
    return file_info.baseName();
}

QJsonObject QMLProcessor::createQMLMetadata(const QString& file_path, const QString& content) const {
    QJsonObject metadata = createMetadata(file_path);
    metadata["format"] = "QML";
    metadata["component_name"] = extractComponentName(file_path);
    metadata["line_count"] = content.split('\n').size();
    metadata["has_imports"] = !extractImports(content).isEmpty();
    return metadata;
}

bool QMLProcessor::isValidQMLIdentifier(const QString& identifier) const {
    QRegularExpression identifier_regex("^[a-zA-Z_][a-zA-Z0-9_]*$");
    return identifier_regex.match(identifier).hasMatch();
}

QString QMLProcessor::formatQMLError(const QQmlError& error) const {
    return QString("Line %1: %2").arg(error.line()).arg(error.description());
}

QStringList QMLProcessor::parseImportStatement(const QString& import_line) const {
    QRegularExpressionMatch match = import_regex_.match(import_line.trimmed());
    QStringList parts;
    
    if (match.hasMatch()) {
        parts.append(match.captured(1)); // Module name
        if (!match.captured(2).isEmpty()) {
            parts.append(match.captured(2)); // Version
        }
        if (!match.captured(3).isEmpty()) {
            parts.append(match.captured(3)); // Alias
        }
    }
    
    return parts;
}

} // namespace DeclarativeUI::HotReload::FormatSupport
