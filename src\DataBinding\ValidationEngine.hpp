#pragma once

#include <QObject>
#include <QString>
#include <QVariant>
#include <QRegularExpression>
#include <QDateTime>
#include <QUrl>
#include <functional>
#include <memory>
#include <unordered_map>
#include <vector>

namespace DeclarativeUI {
namespace DataBinding {

/**
 * @brief Validation result containing success status and error messages
 */
struct ValidationResult {
    bool is_valid = true;
    QString error_message;
    QString field_name;
    QVariant validated_value;
    QString validation_rule;
    
    ValidationResult() = default;
    ValidationResult(bool valid, const QString& message = QString(), const QString& field = QString())
        : is_valid(valid), error_message(message), field_name(field) {}
    
    static ValidationResult success(const QVariant& value = QVariant()) {
        ValidationResult result(true);
        result.validated_value = value;
        return result;
    }
    
    static ValidationResult error(const QString& message, const QString& field = QString()) {
        return ValidationResult(false, message, field);
    }
    
    operator bool() const { return is_valid; }
};

/**
 * @brief Base class for all validators
 */
class Validator {
public:
    virtual ~Validator() = default;
    virtual ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const = 0;
    virtual QString getDescription() const = 0;
    virtual QString getRuleName() const = 0;
};

/**
 * @brief Validator function type for lambda-based validators
 */
using ValidatorFunction = std::function<ValidationResult(const QVariant&, const QString&)>;

/**
 * @brief Function-based validator implementation
 */
class FunctionValidator : public Validator {
public:
    explicit FunctionValidator(ValidatorFunction func, const QString& description, const QString& rule_name)
        : validator_func_(std::move(func)), description_(description), rule_name_(rule_name) {}
    
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override {
        return validator_func_(value, field_name);
    }
    
    QString getDescription() const override { return description_; }
    QString getRuleName() const override { return rule_name_; }

private:
    ValidatorFunction validator_func_;
    QString description_;
    QString rule_name_;
};

/**
 * @brief Built-in validator implementations
 */
namespace BuiltinValidators {

/**
 * @brief Required field validator
 */
class RequiredValidator : public Validator {
public:
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override { return "Field is required"; }
    QString getRuleName() const override { return "required"; }
};

/**
 * @brief String length validator
 */
class LengthValidator : public Validator {
public:
    explicit LengthValidator(int min_length = 0, int max_length = -1)
        : min_length_(min_length), max_length_(max_length) {}
    
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override;
    QString getRuleName() const override { return "length"; }

private:
    int min_length_;
    int max_length_;
};

/**
 * @brief Regular expression validator
 */
class RegexValidator : public Validator {
public:
    explicit RegexValidator(const QString& pattern, const QString& description = QString())
        : regex_(pattern), description_(description.isEmpty() ? QString("Must match pattern: %1").arg(pattern) : description) {}
    
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override { return description_; }
    QString getRuleName() const override { return "regex"; }

private:
    QRegularExpression regex_;
    QString description_;
};

/**
 * @brief Email validator
 */
class EmailValidator : public Validator {
public:
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override { return "Must be a valid email address"; }
    QString getRuleName() const override { return "email"; }
};

/**
 * @brief URL validator
 */
class UrlValidator : public Validator {
public:
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override { return "Must be a valid URL"; }
    QString getRuleName() const override { return "url"; }
};

/**
 * @brief Numeric range validator
 */
class RangeValidator : public Validator {
public:
    explicit RangeValidator(double min_value, double max_value)
        : min_value_(min_value), max_value_(max_value) {}
    
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override;
    QString getRuleName() const override { return "range"; }

private:
    double min_value_;
    double max_value_;
};

/**
 * @brief Date validator
 */
class DateValidator : public Validator {
public:
    explicit DateValidator(const QDate& min_date = QDate(), const QDate& max_date = QDate())
        : min_date_(min_date), max_date_(max_date) {}
    
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override;
    QString getRuleName() const override { return "date"; }

private:
    QDate min_date_;
    QDate max_date_;
};

/**
 * @brief Custom comparison validator
 */
class ComparisonValidator : public Validator {
public:
    enum class ComparisonType {
        Equal,
        NotEqual,
        GreaterThan,
        GreaterThanOrEqual,
        LessThan,
        LessThanOrEqual
    };
    
    explicit ComparisonValidator(const QVariant& compare_value, ComparisonType type)
        : compare_value_(compare_value), comparison_type_(type) {}
    
    ValidationResult validate(const QVariant& value, const QString& field_name = QString()) const override;
    QString getDescription() const override;
    QString getRuleName() const override { return "comparison"; }

private:
    QVariant compare_value_;
    ComparisonType comparison_type_;
    QString getComparisonTypeString() const;
};

} // namespace BuiltinValidators

/**
 * @brief Validation rule that can contain multiple validators
 */
class ValidationRule {
public:
    explicit ValidationRule(const QString& field_name = QString()) : field_name_(field_name) {}

    // Make ValidationRule movable but not copyable due to unique_ptr
    ValidationRule(const ValidationRule&) = delete;
    ValidationRule& operator=(const ValidationRule&) = delete;
    ValidationRule(ValidationRule&&) = default;
    ValidationRule& operator=(ValidationRule&&) = default;
    
    // Add validators
    ValidationRule& required();
    ValidationRule& minLength(int length);
    ValidationRule& maxLength(int length);
    ValidationRule& length(int min_length, int max_length);
    ValidationRule& regex(const QString& pattern, const QString& description = QString());
    ValidationRule& email();
    ValidationRule& url();
    ValidationRule& range(double min_value, double max_value);
    ValidationRule& date(const QDate& min_date = QDate(), const QDate& max_date = QDate());
    ValidationRule& custom(ValidatorFunction func, const QString& description, const QString& rule_name = "custom");
    
    // Validation execution
    ValidationResult validate(const QVariant& value) const;
    std::vector<ValidationResult> validateAll(const QVariant& value) const;
    
    // Rule information
    QString getFieldName() const { return field_name_; }
    void setFieldName(const QString& name) { field_name_ = name; }
    std::vector<QString> getValidatorDescriptions() const;
    bool isEmpty() const { return validators_.empty(); }

private:
    QString field_name_;
    std::vector<std::unique_ptr<Validator>> validators_;
};

/**
 * @brief Central validation engine for managing validation rules
 */
class ValidationEngine : public QObject {
    Q_OBJECT

public:
    explicit ValidationEngine(QObject* parent = nullptr);
    ~ValidationEngine() override = default;

    // Rule management
    ValidationRule& addRule(const QString& field_name);
    ValidationRule& getRule(const QString& field_name);
    bool hasRule(const QString& field_name) const;
    void removeRule(const QString& field_name);
    void clearRules();
    
    // Validation execution
    ValidationResult validateField(const QString& field_name, const QVariant& value);
    std::unordered_map<QString, ValidationResult> validateAll(const std::unordered_map<QString, QVariant>& data);
    bool isValid(const std::unordered_map<QString, QVariant>& data);
    
    // Error management
    std::unordered_map<QString, QString> getErrors() const;
    QString getError(const QString& field_name) const;
    bool hasErrors() const;
    void clearErrors();
    
    // Configuration
    void setStopOnFirstError(bool stop) { stop_on_first_error_ = stop; }
    bool getStopOnFirstError() const { return stop_on_first_error_; }
    
    // Utility methods
    QStringList getFieldNames() const;
    int getRuleCount() const { return static_cast<int>(rules_.size()); }

signals:
    void validationCompleted(const QString& field_name, const ValidationResult& result);
    void allValidationCompleted(bool all_valid);
    void errorAdded(const QString& field_name, const QString& error_message);
    void errorCleared(const QString& field_name);

private:
    std::unordered_map<QString, ValidationRule> rules_;
    std::unordered_map<QString, QString> current_errors_;
    bool stop_on_first_error_ = false;
};

} // namespace DataBinding
} // namespace DeclarativeUI
