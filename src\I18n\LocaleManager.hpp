#pragma once

#include <QObject>
#include <QLocale>
#include <QString>
#include <QDateTime>
#include <QDate>
#include <QTime>
#include <QTimeZone>

#include <QHash>
#include <QMutex>

namespace DeclarativeUI {
namespace I18n {

/**
 * @brief Comprehensive locale management for formatting and cultural conventions
 * 
 * Features:
 * - Number formatting (decimals, thousands separators, currency)
 * - Date and time formatting
 * - Currency formatting with exchange rates
 * - Text direction (LTR/RTL) support
 * - Collation and sorting
 * - Cultural conventions (first day of week, etc.)
 */
class LocaleManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Text direction for UI layout
     */
    enum class TextDirection {
        LeftToRight,
        RightToLeft,
        Auto  // Determined by locale
    };

    /**
     * @brief Number formatting options
     */
    struct NumberFormatOptions {
        int decimal_places = -1;  // -1 = use locale default
        bool use_grouping = true;
        QString custom_decimal_point;
        QString custom_thousands_separator;
        bool show_positive_sign = false;
        
        NumberFormatOptions() = default;
        
        NumberFormatOptions& decimals(int places) {
            decimal_places = places;
            return *this;
        }
        
        NumberFormatOptions& grouping(bool use) {
            use_grouping = use;
            return *this;
        }
        
        NumberFormatOptions& positiveSign(bool show) {
            show_positive_sign = show;
            return *this;
        }
    };

    /**
     * @brief Currency formatting options
     */
    struct CurrencyFormatOptions {
        QString currency_code;
        QString currency_symbol;
        bool show_currency_code = false;
        bool show_currency_symbol = true;
        int decimal_places = 2;
        
        CurrencyFormatOptions() = default;
        
        CurrencyFormatOptions& currency(const QString& code) {
            currency_code = code;
            return *this;
        }
        
        CurrencyFormatOptions& symbol(const QString& sym) {
            currency_symbol = sym;
            return *this;
        }
        
        CurrencyFormatOptions& decimals(int places) {
            decimal_places = places;
            return *this;
        }
    };

    /**
     * @brief Date/time formatting options
     */
    struct DateTimeFormatOptions {
        QString custom_format;
        QLocale::FormatType format_type = QLocale::ShortFormat;
        bool use_relative_dates = false;  // "today", "yesterday", etc.
        bool use_12_hour_format = false;  // Override locale default
        
        DateTimeFormatOptions() = default;
        
        DateTimeFormatOptions& format(const QString& fmt) {
            custom_format = fmt;
            return *this;
        }
        
        DateTimeFormatOptions& formatType(QLocale::FormatType type) {
            format_type = type;
            return *this;
        }
        
        DateTimeFormatOptions& relative(bool use) {
            use_relative_dates = use;
            return *this;
        }
    };

    /**
     * @brief Get singleton instance
     */
    static LocaleManager& instance();

    /**
     * @brief Set current locale
     */
    void setLocale(const QLocale& locale);
    void setLocale(const QString& locale_name);

    /**
     * @brief Get current locale
     */
    QLocale getCurrentLocale() const { return current_locale_; }
    QString getCurrentLocaleName() const { return current_locale_.name(); }

    /**
     * @brief Text direction support
     */
    TextDirection getTextDirection() const;
    TextDirection getTextDirection(const QLocale& locale) const;
    bool isRightToLeft() const;
    bool isRightToLeft(const QLocale& locale) const;

    /**
     * @brief Number formatting
     */
    QString formatNumber(double number, const NumberFormatOptions& options = NumberFormatOptions()) const;
    QString formatInteger(int number, const NumberFormatOptions& options = NumberFormatOptions()) const;
    QString formatPercent(double value, const NumberFormatOptions& options = NumberFormatOptions()) const;

    /**
     * @brief Currency formatting
     */
    QString formatCurrency(double amount, const CurrencyFormatOptions& options = CurrencyFormatOptions()) const;
    QString formatCurrency(double amount, const QString& currency_code) const;

    /**
     * @brief Date and time formatting
     */
    QString formatDate(const QDate& date, const DateTimeFormatOptions& options = DateTimeFormatOptions()) const;
    QString formatTime(const QTime& time, const DateTimeFormatOptions& options = DateTimeFormatOptions()) const;
    QString formatDateTime(const QDateTime& datetime, const DateTimeFormatOptions& options = DateTimeFormatOptions()) const;
    
    /**
     * @brief Relative date formatting
     */
    QString formatRelativeDate(const QDate& date) const;
    QString formatRelativeDateTime(const QDateTime& datetime) const;

    /**
     * @brief Cultural conventions
     */
    Qt::DayOfWeek getFirstDayOfWeek() const;
    QList<Qt::DayOfWeek> getWeekendDays() const;
    QString getAMText() const;
    QString getPMText() const;

    /**
     * @brief Parsing methods
     */
    double parseNumber(const QString& text, bool* ok = nullptr) const;
    QDate parseDate(const QString& text, const QString& format = QString(), bool* ok = nullptr) const;
    QTime parseTime(const QString& text, const QString& format = QString(), bool* ok = nullptr) const;
    QDateTime parseDateTime(const QString& text, const QString& format = QString(), bool* ok = nullptr) const;

    /**
     * @brief Collation and sorting
     */
    int compareStrings(const QString& s1, const QString& s2) const;
    QStringList sortStrings(const QStringList& strings) const;

    /**
     * @brief Utility methods
     */
    QStringList getAvailableLocales() const;
    QString getLocaleName(const QLocale& locale) const;
    QString getNativeLocaleName(const QLocale& locale) const;

signals:
    /**
     * @brief Emitted when locale changes
     */
    void localeChanged(const QLocale& new_locale, const QLocale& old_locale);

private:
    explicit LocaleManager(QObject* parent = nullptr);
    ~LocaleManager() override = default;

    // Helper methods
    QString applyNumberFormatOptions(const QString& formatted, const NumberFormatOptions& options) const;
    QString getRelativeDateString(int days_diff) const;
    QString getRelativeTimeString(const QDateTime& datetime) const;

    // Current locale
    QLocale current_locale_;

    // Cached formatters and collators
    mutable QHash<QString, QString> format_cache_;
    mutable QMutex cache_mutex_;

    static LocaleManager* instance_;
    static QMutex instance_mutex_;
};

/**
 * @brief Convenience macros for locale formatting
 */
#define DUI_FORMAT_NUMBER(num) DeclarativeUI::I18n::LocaleManager::instance().formatNumber(num)
#define DUI_FORMAT_CURRENCY(amount, code) DeclarativeUI::I18n::LocaleManager::instance().formatCurrency(amount, code)
#define DUI_FORMAT_DATE(date) DeclarativeUI::I18n::LocaleManager::instance().formatDate(date)
#define DUI_FORMAT_TIME(time) DeclarativeUI::I18n::LocaleManager::instance().formatTime(time)
#define DUI_FORMAT_DATETIME(datetime) DeclarativeUI::I18n::LocaleManager::instance().formatDateTime(datetime)

} // namespace I18n
} // namespace DeclarativeUI
