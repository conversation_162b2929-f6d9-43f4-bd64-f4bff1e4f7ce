#pragma once

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QComboBox>
#include <QGroupBox>
#include <QProgressBar>
#include <QTextEdit>
#include <QTimer>
#include <QTextCursor>
#include <QDateTime>
#include <QSplitter>
#include <QFrame>
#include <memory>
#include <cmath>

#include "../src/Animation/AnimationEngine.hpp"

using namespace DeclarativeUI::Animation;

/**
 * @brief Advanced Animation Example demonstrating the enhanced animation system
 * 
 * This example showcases:
 * - Timeline-based animations with keyframes
 * - Physics-based animations (spring, damping, gravity)
 * - Complex transition effects
 * - Multi-property animations
 * - Animation sequences and parallel execution
 * - Real-time animation parameter adjustment
 */
class AdvancedAnimationExample : public QMainWindow {
    Q_OBJECT

public:
    explicit AdvancedAnimationExample(QWidget* parent = nullptr);
    ~AdvancedAnimationExample() override = default;

private slots:
    void onTimelineAnimationClicked();
    void onPhysicsAnimationClicked();
    void onTransitionAnimationClicked();
    void onMultiPropertyAnimationClicked();
    void onSequenceAnimationClicked();
    void onRippleEffectClicked();
    void onMorphAnimationClicked();
    
    void onPhysicsParameterChanged();
    void onAnimationSpeedChanged(int speed);
    void onEasingTypeChanged(const QString& easing);
    
    void updateAnimationStatus();

private:
    void setupUI();
    void setupAnimationControls();
    void setupPhysicsControls();
    void setupTransitionControls();
    void setupStatusDisplay();
    
    void createTimelineAnimation();
    void createPhysicsAnimation();
    void createTransitionAnimation();
    void createMultiPropertyAnimation();
    void createSequenceAnimation();
    void createRippleEffect();
    void createMorphAnimation();
    
    void logAnimationEvent(const QString& message);

    // UI Components
    QWidget* central_widget_;
    QVBoxLayout* main_layout_;
    QHBoxLayout* controls_layout_;
    QGridLayout* animation_grid_;
    
    // Animation Controls
    QGroupBox* basic_controls_;
    QGroupBox* physics_controls_;
    QGroupBox* transition_controls_;
    QGroupBox* status_group_;
    
    // Control Widgets
    QPushButton* timeline_btn_;
    QPushButton* physics_btn_;
    QPushButton* transition_btn_;
    QPushButton* multi_property_btn_;
    QPushButton* sequence_btn_;
    QPushButton* ripple_btn_;
    QPushButton* morph_btn_;
    
    // Physics Parameter Controls
    QSlider* stiffness_slider_;
    QSlider* damping_slider_;
    QSlider* mass_slider_;
    QSlider* gravity_slider_;
    QSlider* friction_slider_;
    
    QLabel* stiffness_label_;
    QLabel* damping_label_;
    QLabel* mass_label_;
    QLabel* gravity_label_;
    QLabel* friction_label_;
    
    // Animation Settings
    QSlider* speed_slider_;
    QComboBox* easing_combo_;
    QSpinBox* duration_spin_;
    
    // Status Display
    QTextEdit* status_text_;
    QProgressBar* animation_progress_;
    QLabel* active_animations_label_;
    
    // Animated Widgets
    QWidget* animated_widget1_;
    QWidget* animated_widget2_;
    QWidget* animated_widget3_;
    QWidget* animated_widget4_;
    
    // Animation System
    std::unique_ptr<AnimationEngine> animation_engine_;
    std::vector<std::shared_ptr<Animation>> active_animations_;
    std::vector<std::shared_ptr<PhysicsAnimation>> active_physics_animations_;
    std::vector<std::shared_ptr<TransitionAnimator>> active_transitions_;
    
    // Status Update Timer
    QTimer* status_timer_;
    
    // Animation Parameters
    double current_stiffness_ = 100.0;
    double current_damping_ = 10.0;
    double current_mass_ = 1.0;
    double current_gravity_ = 0.0;
    double current_friction_ = 0.0;
    int current_duration_ = 1000;
    EasingType current_easing_ = EasingType::QuartInOut;
};
