#!/bin/bash
# Check for memory management best practices in C++ files

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

errors=0
warnings=0

echo "🔍 Checking memory management patterns..."

for file in "$@"; do
    if [[ ! -f "$file" ]]; then
        continue
    fi
    
    echo "Checking: $file"
    
    # Check for raw pointer usage
    if grep -n "\*\s*[a-zA-Z_][a-zA-Z0-9_]*\s*=" "$file" >/dev/null 2>&1; then
        if ! grep -n "std::unique_ptr\|std::shared_ptr\|QPointer" "$file" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Warning: Raw pointer usage detected in $file${NC}"
            echo "   Consider using smart pointers for automatic memory management"
            ((warnings++))
        fi
    fi
    
    # Check for new/delete usage
    if grep -n "\bnew\s" "$file" >/dev/null 2>&1; then
        if ! grep -n "std::make_unique\|std::make_shared" "$file" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Warning: 'new' operator found in $file${NC}"
            echo "   Consider using std::make_unique or std::make_shared"
            ((warnings++))
        fi
    fi
    
    if grep -n "\bdelete\s" "$file" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Warning: 'delete' operator found in $file${NC}"
        echo "   Consider using RAII and smart pointers"
        ((warnings++))
    fi
    
    # Check for malloc/free usage
    if grep -n "\bmalloc\|free\|calloc\|realloc" "$file" >/dev/null 2>&1; then
        echo -e "${RED}❌ Error: C-style memory allocation found in $file${NC}"
        echo "   Use C++ RAII and smart pointers instead"
        ((errors++))
    fi
    
    # Check for proper Qt parent-child relationships
    if grep -n "QObject\|QWidget" "$file" >/dev/null 2>&1; then
        if grep -n "new.*QObject\|new.*QWidget" "$file" >/dev/null 2>&1; then
            if ! grep -n "parent" "$file" >/dev/null 2>&1; then
                echo -e "${YELLOW}⚠️  Warning: Qt object creation without parent in $file${NC}"
                echo "   Consider setting parent for automatic cleanup"
                ((warnings++))
            fi
        fi
    fi
    
    # Check for smart pointer usage (good practice)
    if grep -n "std::unique_ptr\|std::shared_ptr\|std::weak_ptr" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Using smart pointers in $file${NC}"
    fi
    
    # Check for RAII patterns
    if grep -n "class.*{" "$file" >/dev/null 2>&1; then
        # Check for destructor
        if grep -n "~[a-zA-Z_][a-zA-Z0-9_]*(" "$file" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Good: Destructor found in $file${NC}"
        fi
    fi
    
    # Check for move semantics
    if grep -n "std::move\|&&" "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Good: Using move semantics in $file${NC}"
    fi
    
    # Check for potential memory leaks in loops
    if grep -n "for\|while" "$file" >/dev/null 2>&1; then
        if grep -n "new\s" "$file" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Warning: Memory allocation in loop detected in $file${NC}"
            echo "   Verify proper cleanup to avoid memory leaks"
            ((warnings++))
        fi
    fi
    
    # Check for exception safety
    if grep -n "throw\|try\|catch" "$file" >/dev/null 2>&1; then
        if grep -n "new\s" "$file" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Warning: Raw allocation with exceptions in $file${NC}"
            echo "   Ensure exception safety with RAII"
            ((warnings++))
        fi
    fi
    
done

echo ""
echo "📊 Summary:"
echo "   Errors: $errors"
echo "   Warnings: $warnings"

if [ $errors -gt 0 ]; then
    echo -e "${RED}❌ Memory management check failed with $errors errors${NC}"
    exit 1
elif [ $warnings -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Memory management check completed with $warnings warnings${NC}"
    exit 0
else
    echo -e "${GREEN}✅ Memory management check passed${NC}"
    exit 0
fi
