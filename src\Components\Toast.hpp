#pragma once

#include "../Core/UIElement.hpp"
#include <QWidget>
#include <QLabel>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QIcon>
#include <QApplication>
#include <QScreen>
#include <functional>

namespace DeclarativeUI::Components {

/**
 * @brief Modern toast notification component
 * 
 * Provides non-intrusive notifications that appear temporarily and auto-dismiss.
 * Supports different types (info, success, warning, error), positioning, and animations.
 */
class Toast : public Core::UIElement {
    Q_OBJECT

public:
    enum class Type {
        Info,
        Success,
        Warning,
        Error
    };

    enum class Position {
        TopLeft,
        TopCenter,
        TopRight,
        BottomLeft,
        BottomCenter,
        BottomRight,
        Center
    };

    explicit Toast(QObject* parent = nullptr);
    ~Toast() override = default;

    // **UIElement interface implementation**
    void initialize() override;

    // **Fluent interface for toast configuration**
    Toast& message(const QString& text);
    Toast& type(Type toast_type);
    Toast& duration(int milliseconds);
    Toast& position(Position pos);
    Toast& closable(bool can_close = true);
    Toast& persistent(bool is_persistent = false);
    Toast& icon(const QIcon& toast_icon);
    Toast& action(const QString& text, std::function<void()> callback);
    Toast& onClosed(std::function<void()> callback);

    // **Static convenience methods**
    static Toast* info(const QString& message, int duration = 3000);
    static Toast* success(const QString& message, int duration = 3000);
    static Toast* warning(const QString& message, int duration = 4000);
    static Toast* error(const QString& message, int duration = 5000);

    // **Show/hide methods**
    void show();
    void hide();
    void close();

    // **Getters**
    QString getMessage() const;
    Type getType() const;
    int getDuration() const;
    Position getPosition() const;
    bool isClosable() const;
    bool isPersistent() const;

signals:
    void closed();
    void actionClicked();

private slots:
    void onAutoHideTimer();
    void onCloseButtonClicked();
    void onActionButtonClicked();

private:
    // **UI components**
    QWidget* toast_widget_;
    QLabel* icon_label_;
    QLabel* message_label_;
    QPushButton* close_button_;
    QPushButton* action_button_;
    QHBoxLayout* main_layout_;
    
    // **Animation and effects**
    QPropertyAnimation* fade_in_animation_;
    QPropertyAnimation* fade_out_animation_;
    QPropertyAnimation* slide_animation_;
    QGraphicsOpacityEffect* opacity_effect_;
    QTimer* auto_hide_timer_;

    // **Configuration**
    QString message_text_;
    Type toast_type_;
    int duration_ms_;
    Position position_;
    bool closable_;
    bool persistent_;
    QIcon toast_icon_;
    QString action_text_;
    std::function<void()> action_callback_;
    std::function<void()> closed_callback_;

    // **Helper methods**
    void setupUI();
    void setupAnimations();
    void applyTypeStyle();
    void calculatePosition();
    QPoint getPositionCoordinates() const;
    QString getTypeStyleSheet() const;
    QIcon getDefaultIcon() const;
    void connectSignals();

    // **Static toast management**
    static QList<Toast*> active_toasts_;
    static void addToActiveList(Toast* toast);
    static void removeFromActiveList(Toast* toast);
    static void repositionActiveToasts();
};

} // namespace DeclarativeUI::Components
