{"name": "ExamplePlugin", "version": "1.0.0", "description": "Example plugin demonstrating DeclarativeUI plugin capabilities", "author": "DeclarativeUI Team", "license": "MIT", "website": "https://github.com/DeclarativeUI/DeclarativeUI", "minFrameworkVersion": "1.0.0", "maxFrameworkVersion": "2.0.0", "capabilities": ["Components", "Commands", "Themes"], "dependencies": [], "tags": ["example", "demo", "ui"], "category": "Examples", "configuration": {"enableAdvancedFeatures": true, "defaultTheme": "ExampleDark", "componentPrefix": "Example"}, "components": [{"type": "ExampleButton", "description": "Enhanced button with custom styling", "category": "Controls", "tags": ["button", "control"]}, {"type": "ExampleCard", "description": "Card component with shadow and animations", "category": "Layout", "tags": ["card", "container"]}], "commands": [{"type": "ShowNotification", "description": "Display a notification message", "category": "UI", "tags": ["notification", "message"]}, {"type": "ToggleTheme", "description": "Toggle between light and dark themes", "category": "Appearance", "tags": ["theme", "toggle"]}], "themes": [{"name": "ExampleDark", "description": "Dark theme with blue accents", "isDark": true, "tags": ["dark", "blue"]}, {"name": "ExampleLight", "description": "Light theme with green accents", "isDark": false, "tags": ["light", "green"]}]}