#pragma once

#include "ValidationEngine.hpp"
#include "../Binding/StateManager.hpp"
#include <QObject>
#include <QWidget>
#include <QLineEdit>
#include <QTextEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QSlider>
#include <QDateEdit>
#include <QTimeEdit>
#include <QDateTimeEdit>
#include <QLabel>
#include <QProgressBar>
#include <QMutex>
#include <QMutexLocker>
#include <functional>
#include <memory>
#include <unordered_map>
#include <unordered_set>

namespace DeclarativeUI {
namespace DataBinding {

/**
 * @brief Data transformation function type
 */
using DataTransformer = std::function<QVariant(const QVariant&)>;

/**
 * @brief Binding configuration for a widget property
 */
struct BindingConfig {
    QString state_key;
    QString widget_property;
    DataTransformer to_widget_transformer;
    DataTransformer to_state_transformer;
    bool two_way = true;
    bool validate_on_change = true;
    bool immediate_validation = false;
    
    BindingConfig() = default;
    BindingConfig(const QString& key, const QString& property = "value")
        : state_key(key), widget_property(property) {}
    
    BindingConfig& oneWay() { two_way = false; return *this; }
    BindingConfig& noValidation() { validate_on_change = false; return *this; }
    BindingConfig& immediateValidation() { immediate_validation = true; return *this; }
    BindingConfig& transformToWidget(DataTransformer transformer) { to_widget_transformer = std::move(transformer); return *this; }
    BindingConfig& transformToState(DataTransformer transformer) { to_state_transformer = std::move(transformer); return *this; }
};

/**
 * @brief Information about a bound widget
 */
struct BoundWidget {
    QWidget* widget;
    BindingConfig config;
    QMetaObject::Connection state_connection;
    QMetaObject::Connection widget_connection;
    bool is_updating = false;  // Prevent circular updates
    
    BoundWidget() = default;
    BoundWidget(QWidget* w, const BindingConfig& cfg) : widget(w), config(cfg) {}
};

/**
 * @brief Form data container with validation support
 */
class FormData : public QObject {
    Q_OBJECT

public:
    explicit FormData(QObject* parent = nullptr);
    ~FormData() override = default;

    // Data management
    void setValue(const QString& field, const QVariant& value);
    QVariant getValue(const QString& field) const;
    bool hasField(const QString& field) const;
    void removeField(const QString& field);
    void clear();
    
    // Validation
    void setValidationEngine(std::shared_ptr<ValidationEngine> engine);
    ValidationResult validateField(const QString& field);
    bool validateAll();
    bool isValid() const;
    
    // Error handling
    QString getError(const QString& field) const;
    QStringList getErrors() const;
    bool hasErrors() const;
    void clearErrors();
    
    // Utility
    QStringList getFieldNames() const;
    std::unordered_map<QString, QVariant> getAllData() const;
    void setAllData(const std::unordered_map<QString, QVariant>& data);
    
    // Serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);

signals:
    void fieldChanged(const QString& field, const QVariant& value);
    void validationChanged(const QString& field, bool is_valid);
    void formValidationChanged(bool is_valid);

private:
    std::unordered_map<QString, QVariant> data_;
    std::shared_ptr<ValidationEngine> validation_engine_;
    mutable std::unordered_map<QString, QString> cached_errors_;
};

/**
 * @brief Central data binding engine
 */
class DataBindingEngine : public QObject {
    Q_OBJECT

public:
    explicit DataBindingEngine(QObject* parent = nullptr);
    ~DataBindingEngine() override;

    // Singleton access
    static DataBindingEngine& instance();

    // Widget binding
    void bindWidget(QWidget* widget, const BindingConfig& config);
    void bindWidget(QWidget* widget, const QString& state_key, const QString& property = QString());
    void unbindWidget(QWidget* widget);
    void unbindAll();
    
    // Form binding
    void bindForm(QWidget* form_widget, std::shared_ptr<FormData> form_data);
    void unbindForm(QWidget* form_widget);
    std::shared_ptr<FormData> getFormData(QWidget* form_widget);
    
    // State integration
    void setStateManager(std::shared_ptr<Binding::StateManager> state_manager);
    std::shared_ptr<Binding::StateManager> getStateManager() const;
    
    // Validation integration
    void setValidationEngine(std::shared_ptr<ValidationEngine> validation_engine);
    std::shared_ptr<ValidationEngine> getValidationEngine() const;
    
    // Manual synchronization
    void syncWidgetToState(QWidget* widget);
    void syncStateToWidget(QWidget* widget);
    void syncAll();
    
    // Configuration
    void setAutoSync(bool enabled) { auto_sync_enabled_ = enabled; }
    bool isAutoSyncEnabled() const { return auto_sync_enabled_; }
    
    void setValidationOnChange(bool enabled) { validation_on_change_enabled_ = enabled; }
    bool isValidationOnChangeEnabled() const { return validation_on_change_enabled_; }
    
    // Debugging and monitoring
    QStringList getBoundWidgets() const;
    BindingConfig getBindingConfig(QWidget* widget) const;
    bool isWidgetBound(QWidget* widget) const;

signals:
    void widgetBound(QWidget* widget, const QString& state_key);
    void widgetUnbound(QWidget* widget);
    void bindingError(QWidget* widget, const QString& error);
    void validationTriggered(QWidget* widget, const QString& field, bool is_valid);

private slots:
    void onStateChanged(const QString& key, const QVariant& value);
    void onWidgetValueChanged();
    void onWidgetDestroyed(QObject* widget);

private:
    // Helper methods
    void setupWidgetBinding(BoundWidget& bound_widget);
    void connectWidgetSignals(BoundWidget& bound_widget);
    void disconnectWidgetSignals(BoundWidget& bound_widget);
    QString getDefaultPropertyForWidget(QWidget* widget) const;
    QVariant getWidgetValue(QWidget* widget, const QString& property) const;
    void setWidgetValue(QWidget* widget, const QString& property, const QVariant& value);
    void updateWidgetFromState(BoundWidget& bound_widget);
    void updateStateFromWidget(BoundWidget& bound_widget);
    void performValidation(BoundWidget& bound_widget);
    
    // Form helpers
    void setupFormBinding(QWidget* form_widget, std::shared_ptr<FormData> form_data);
    void scanFormWidgets(QWidget* parent, std::shared_ptr<FormData> form_data);
    QString extractFieldNameFromWidget(QWidget* widget) const;
    
    // Data members
    std::unordered_map<QWidget*, BoundWidget> bound_widgets_;
    std::unordered_map<QWidget*, std::shared_ptr<FormData>> bound_forms_;
    std::shared_ptr<Binding::StateManager> state_manager_;
    std::shared_ptr<ValidationEngine> validation_engine_;
    
    bool auto_sync_enabled_ = true;
    bool validation_on_change_enabled_ = true;
    
    static DataBindingEngine* instance_;
    static QMutex instance_mutex_;
};

/**
 * @brief Utility functions for common binding scenarios
 */
namespace BindingUtils {

/**
 * @brief Create a form data object with validation rules
 */
std::shared_ptr<FormData> createFormData(const std::unordered_map<QString, ValidationRule>& rules = {});

/**
 * @brief Bind a simple input widget to state
 */
void bindInput(QWidget* widget, const QString& state_key);

/**
 * @brief Bind a form widget with automatic field discovery
 */
void bindForm(QWidget* form_widget, const QString& form_state_prefix = QString());

/**
 * @brief Create common data transformers
 */
DataTransformer createStringToIntTransformer();
DataTransformer createIntToStringTransformer();
DataTransformer createStringToDoubleTransformer();
DataTransformer createDoubleToStringTransformer();
DataTransformer createBoolToStringTransformer(const QString& true_value = "true", const QString& false_value = "false");
DataTransformer createStringToBoolTransformer(const QString& true_value = "true");

/**
 * @brief Create validation rules for common scenarios
 */
ValidationRule createEmailValidation();
ValidationRule createPasswordValidation(int min_length = 8);
ValidationRule createPhoneValidation();
ValidationRule createRequiredTextValidation(int min_length = 1, int max_length = -1);
ValidationRule createNumericValidation(double min_value, double max_value);

} // namespace BindingUtils

/**
 * @brief Convenience macros for data binding
 */
#define DUI_BIND_ENGINE DeclarativeUI::DataBinding::DataBindingEngine::instance()
#define DUI_BIND_WIDGET(widget, state_key) DUI_BIND_ENGINE.bindWidget(widget, state_key)
#define DUI_BIND_FORM(form, form_data) DUI_BIND_ENGINE.bindForm(form, form_data)
#define DUI_CREATE_FORM_DATA() DeclarativeUI::DataBinding::BindingUtils::createFormData()

} // namespace DataBinding
} // namespace DeclarativeUI
