# =============================================================================
# Git Attributes for Qt/C++ Project
# =============================================================================

# Set default behavior to automatically normalize line endings
* text=auto

# =============================================================================
# C++ Source Files
# =============================================================================

# C++ source and header files - always use LF
*.c text eol=lf
*.cc text eol=lf
*.cpp text eol=lf
*.cxx text eol=lf
*.c++ text eol=lf
*.h text eol=lf
*.hh text eol=lf
*.hpp text eol=lf
*.hxx text eol=lf
*.h++ text eol=lf
*.inl text eol=lf
*.ipp text eol=lf
*.tpp text eol=lf

# Template files
*.tcc text eol=lf
*.inc text eol=lf

# =============================================================================
# Qt Specific Files
# =============================================================================

# Qt project files
*.pro text eol=lf
*.pri text eol=lf
*.prf text eol=lf

# Qt UI files
*.ui text eol=lf

# Qt resource files
*.qrc text eol=lf

# Qt translation files
*.ts text eol=lf
*.qm binary

# Qt style sheets
*.qss text eol=lf

# =============================================================================
# Build and Configuration Files
# =============================================================================

# CMake files
CMakeLists.txt text eol=lf
*.cmake text eol=lf
*.cmake.in text eol=lf

# Makefiles
Makefile text eol=lf
makefile text eol=lf
*.mk text eol=lf
*.mak text eol=lf

# Configuration files
*.conf text eol=lf
*.config text eol=lf
*.cfg text eol=lf
*.ini text eol=lf

# =============================================================================
# Platform Specific Scripts
# =============================================================================

# Windows batch files and PowerShell scripts
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=lf

# Unix shell scripts
*.sh text eol=lf
*.bash text eol=lf
*.zsh text eol=lf
*.fish text eol=lf

# =============================================================================
# Documentation and Text Files
# =============================================================================

# Markdown and text files
*.md text eol=lf
*.txt text eol=lf
*.rst text eol=lf
*.adoc text eol=lf

# Documentation files
*.dox text eol=lf
*.doxygen text eol=lf

# =============================================================================
# Data and Configuration Formats
# =============================================================================

# JSON, XML, YAML
*.json text eol=lf
*.xml text eol=lf
*.yaml text eol=lf
*.yml text eol=lf

# Web technologies (if used in Qt WebEngine projects)
*.html text eol=lf
*.htm text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.sass text eol=lf
*.less text eol=lf
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf

# =============================================================================
# Binary Files - No Text Processing
# =============================================================================

# Compiled binaries
*.exe binary
*.dll binary
*.so binary
*.dylib binary
*.a binary
*.lib binary
*.obj binary
*.o binary
*.pdb binary
*.ilk binary
*.exp binary

# Qt compiled files
*.qm binary
*.rcc binary

# Images
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.bmp binary
*.ico binary
*.svg text eol=lf
*.webp binary
*.tiff binary
*.tif binary

# Audio and video
*.mp3 binary
*.mp4 binary
*.avi binary
*.mov binary
*.wav binary
*.ogg binary

# Archives and packages
*.zip binary
*.tar binary
*.gz binary
*.bz2 binary
*.xz binary
*.7z binary
*.rar binary

# Documents
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# Fonts
*.ttf binary
*.otf binary
*.woff binary
*.woff2 binary
*.eot binary

# =============================================================================
# Git LFS (Large File Storage) - Uncomment if using LFS
# =============================================================================

# Large binary files that should be tracked with Git LFS
# *.dll filter=lfs diff=lfs merge=lfs -text
# *.exe filter=lfs diff=lfs merge=lfs -text
# *.so filter=lfs diff=lfs merge=lfs -text
# *.dylib filter=lfs diff=lfs merge=lfs -text
# *.a filter=lfs diff=lfs merge=lfs -text
# *.lib filter=lfs diff=lfs merge=lfs -text

# =============================================================================
# Diff and Merge Settings
# =============================================================================

# Use built-in diff drivers for specific file types
*.cpp diff=cpp
*.h diff=cpp
*.hpp diff=cpp
*.c diff=cpp

# Custom diff patterns for better readability
*.json diff=json
*.xml diff=xml

# =============================================================================
# Export Settings
# =============================================================================

# Files to exclude from git archive exports
.gitattributes export-ignore
.gitignore export-ignore
.github/ export-ignore
docs/developer/ export-ignore