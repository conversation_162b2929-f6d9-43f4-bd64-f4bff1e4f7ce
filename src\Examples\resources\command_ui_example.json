{"type": "Container", "properties": {"layout": "VBox", "spacing": 20, "margins": 25, "windowTitle": "Command System JSON Example", "minWidth": 600, "minHeight": 500, "style": "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);"}, "children": [{"type": "Label", "properties": {"text": "🚀 Command System JSON Loading Demo", "style": "font-size: 24px; font-weight: bold; color: #2c3e50; text-align: center; padding: 15px; background-color: #3498db; color: white; border-radius: 8px; margin-bottom: 10px;"}}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 20}, "children": [{"type": "Container", "properties": {"layout": "VBox", "spacing": 15, "style": "background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; flex: 1;"}, "children": [{"type": "Label", "properties": {"text": "📝 User Input Section", "style": "font-size: 16px; font-weight: bold; color: #495057; margin-bottom: 10px;"}}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 10}, "children": [{"type": "Label", "properties": {"text": "Name:", "style": "font-weight: bold; min-width: 60px;"}}, {"type": "TextInput", "properties": {"placeholder": "Enter your name...", "style": "padding: 8px; border: 1px solid #ced4da; border-radius: 4px; flex: 1;"}, "bindings": {"text": "user.name"}}]}, {"type": "Label", "properties": {"text": "Welcome! Please enter your name above.", "style": "font-size: 14px; color: #6c757d; font-style: italic; padding: 10px; background-color: #f8f9fa; border-radius: 4px; margin-top: 10px;"}, "bindings": {"text": "app.message"}}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 10, "style": "margin-top: 15px;"}, "children": [{"type": "<PERSON><PERSON>", "properties": {"text": "👋 Greet (Command)", "style": "background-color: #28a745; color: white; padding: 10px 15px; border-radius: 4px; font-weight: bold; flex: 1;"}, "events": {"clicked": "greetUserCommand"}}, {"type": "<PERSON><PERSON>", "properties": {"text": "🧹 Clear (Command)", "style": "background-color: #6c757d; color: white; padding: 10px 15px; border-radius: 4px; font-weight: bold; flex: 1;"}, "events": {"clicked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}]}]}, {"type": "Container", "properties": {"layout": "VBox", "spacing": 15, "style": "background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; flex: 1;"}, "children": [{"type": "Label", "properties": {"text": "⚡ Command System Features", "style": "font-size: 16px; font-weight: bold; color: #495057; margin-bottom: 10px;"}}, {"type": "Container", "properties": {"layout": "VBox", "spacing": 8}, "children": [{"type": "Label", "properties": {"text": "✅ Declarative UI Construction", "style": "font-size: 14px; color: #28a745; padding: 5px;"}}, {"type": "Label", "properties": {"text": "✅ State Management Integration", "style": "font-size: 14px; color: #28a745; padding: 5px;"}}, {"type": "Label", "properties": {"text": "✅ Event Handling System", "style": "font-size: 14px; color: #28a745; padding: 5px;"}}, {"type": "Label", "properties": {"text": "✅ JSON Loading with Commands", "style": "font-size: 14px; color: #28a745; padding: 5px;"}}, {"type": "Label", "properties": {"text": "✅ Widget Mapping", "style": "font-size: 14px; color: #28a745; padding: 5px;"}}, {"type": "Label", "properties": {"text": "✅ Legacy Integration", "style": "font-size: 14px; color: #28a745; padding: 5px;"}}]}, {"type": "<PERSON><PERSON>", "properties": {"text": "ℹ️ Show Info (Command)", "style": "background-color: #17a2b8; color: white; padding: 12px; border-radius: 4px; font-weight: bold; margin-top: 15px;"}, "events": {"clicked": "showInfoCommand"}}]}]}, {"type": "Container", "properties": {"layout": "VBox", "spacing": 10, "style": "background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; margin-top: 10px;"}, "children": [{"type": "Label", "properties": {"text": "🔄 Interactive Counter (State Bound)", "style": "font-size: 16px; font-weight: bold; color: #495057; margin-bottom: 10px;"}}, {"type": "Label", "properties": {"text": "Count: 0", "style": "font-size: 24px; color: #007bff; text-align: center; font-weight: bold; padding: 10px; background-color: #f8f9fa; border-radius: 4px;"}, "bindings": {"text": "counter.display"}}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 10, "style": "margin-top: 10px;"}, "children": [{"type": "<PERSON><PERSON>", "properties": {"text": "➖ Decrement", "style": "background-color: #dc3545; color: white; padding: 8px 15px; border-radius: 4px; flex: 1;"}, "events": {"clicked": "decrementCounter"}}, {"type": "<PERSON><PERSON>", "properties": {"text": "🔄 Reset", "style": "background-color: #ffc107; color: #212529; padding: 8px 15px; border-radius: 4px; flex: 1;"}, "events": {"clicked": "resetCounter"}}, {"type": "<PERSON><PERSON>", "properties": {"text": "➕ Increment", "style": "background-color: #28a745; color: white; padding: 8px 15px; border-radius: 4px; flex: 1;"}, "events": {"clicked": "incrementCounter"}}]}]}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 10, "style": "background-color: #343a40; color: white; padding: 15px; border-radius: 4px; margin-top: 10px;"}, "children": [{"type": "Label", "properties": {"text": "🎯 This UI was loaded from JSON using the Command system", "style": "font-size: 12px; color: #adb5bd; flex: 1;"}}, {"type": "Label", "properties": {"text": "Status: Ready", "style": "font-size: 12px; color: #28a745; font-family: monospace;"}, "bindings": {"text": "system.status"}}]}]}