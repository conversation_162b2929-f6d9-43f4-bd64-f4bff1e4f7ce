#include "TestingFrameworkExample.hpp"
#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QFontDatabase>
#include <QScreen>
#include <QDebug>

using namespace DeclarativeUI::Testing::Examples;

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // **Application Information**
    app.setApplicationName("DeclarativeUI Testing Framework Example");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("DeclarativeUI");
    app.setOrganizationDomain("declarativeui.org");
    
    // **High DPI Support**
    app.setAttribute(Qt::AA_EnableHighDpiScaling);
    app.setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    // **Set Application Style**
    QStringList available_styles = QStyleFactory::keys();
    if (available_styles.contains("Fusion")) {
        app.setStyle("Fusion");
    }
    
    // **Load Custom Fonts (if available)**
    QString fonts_dir = QDir::currentPath() + "/Resources/fonts";
    if (QDir(fonts_dir).exists()) {
        QStringList font_filters;
        font_filters << "*.ttf" << "*.otf";
        
        QDir font_directory(fonts_dir);
        QStringList font_files = font_directory.entryList(font_filters, QDir::Files);
        
        for (const QString& font_file : font_files) {
            QString font_path = fonts_dir + "/" + font_file;
            int font_id = QFontDatabase::addApplicationFont(font_path);
            if (font_id != -1) {
                QStringList font_families = QFontDatabase::applicationFontFamilies(font_id);
                qDebug() << "Loaded font:" << font_families;
            }
        }
    }
    
    // **Create Application Directories**
    QString app_data_dir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(app_data_dir);
    QDir().mkpath(app_data_dir + "/test_results");
    QDir().mkpath(app_data_dir + "/test_baselines");
    QDir().mkpath(app_data_dir + "/benchmarks");
    QDir().mkpath(app_data_dir + "/logs");
    
    // **Apply Application Stylesheet**
    QString stylesheet = R"(
        /* Modern Application Styling */
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }
        
        QTabBar::tab {
            background-color: #e0e0e0;
            border: 1px solid #c0c0c0;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }
        
        QTabBar::tab:hover {
            background-color: #f0f0f0;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #c0c0c0;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #e1e1e1;
            border: 1px solid #adadad;
            border-radius: 3px;
            padding: 6px 12px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #d4d4d4;
        }
        
        QPushButton:pressed {
            background-color: #c0c0c0;
        }
        
        QPushButton:disabled {
            background-color: #f0f0f0;
            color: #a0a0a0;
        }
        
        QTreeWidget {
            alternate-background-color: #f9f9f9;
            selection-background-color: #3daee9;
            selection-color: white;
        }
        
        QTreeWidget::item {
            padding: 4px;
        }
        
        QTreeWidget::item:hover {
            background-color: #e8f4fd;
        }
        
        QProgressBar {
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            text-align: center;
            background-color: #f0f0f0;
        }
        
        QProgressBar::chunk {
            background-color: #3daee9;
            border-radius: 2px;
        }
        
        QTextEdit {
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            background-color: white;
            selection-background-color: #3daee9;
        }
        
        QLineEdit {
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            padding: 4px;
            background-color: white;
        }
        
        QLineEdit:focus {
            border-color: #3daee9;
        }
        
        QComboBox {
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            padding: 4px;
            background-color: white;
        }
        
        QComboBox::drop-down {
            border: none;
        }
        
        QComboBox::down-arrow {
            image: url(:/icons/down-arrow.png);
            width: 12px;
            height: 12px;
        }
        
        QSpinBox {
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            padding: 4px;
            background-color: white;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
        }
        
        QCheckBox::indicator:unchecked {
            border: 1px solid #c0c0c0;
            background-color: white;
        }
        
        QCheckBox::indicator:checked {
            border: 1px solid #3daee9;
            background-color: #3daee9;
            image: url(:/icons/checkmark.png);
        }
        
        QStatusBar {
            background-color: #e0e0e0;
            border-top: 1px solid #c0c0c0;
        }
        
        QMenuBar {
            background-color: #f0f0f0;
            border-bottom: 1px solid #c0c0c0;
        }
        
        QMenuBar::item {
            padding: 4px 8px;
        }
        
        QMenuBar::item:selected {
            background-color: #3daee9;
            color: white;
        }
        
        QToolBar {
            background-color: #f0f0f0;
            border: 1px solid #c0c0c0;
            spacing: 2px;
        }
        
        QToolButton {
            border: 1px solid transparent;
            border-radius: 3px;
            padding: 4px;
        }
        
        QToolButton:hover {
            border-color: #c0c0c0;
            background-color: #e0e0e0;
        }
        
        QToolButton:pressed {
            background-color: #d0d0d0;
        }
        
        QSplitter::handle {
            background-color: #c0c0c0;
        }
        
        QSplitter::handle:horizontal {
            width: 3px;
        }
        
        QSplitter::handle:vertical {
            height: 3px;
        }
    )";
    
    app.setStyleSheet(stylesheet);
    
    // **Create and Show Main Window**
    TestingFrameworkExample window;
    
    // **Center window on screen**
    QScreen* screen = QApplication::primaryScreen();
    if (screen) {
        QRect screen_geometry = screen->availableGeometry();
        int x = (screen_geometry.width() - window.width()) / 2;
        int y = (screen_geometry.height() - window.height()) / 2;
        window.move(x, y);
    }
    
    window.show();
    
    qDebug() << "Testing Framework Example started successfully";
    qDebug() << "Application data directory:" << app_data_dir;
    qDebug() << "Available styles:" << QStyleFactory::keys();
    qDebug() << "Current style:" << app.style()->objectName();
    
    return app.exec();
}
