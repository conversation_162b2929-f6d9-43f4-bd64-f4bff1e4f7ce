#include <QtTest/QtTest>
#include <QApplication>
#include <QElapsedTimer>
#include <QDebug>
#include <memory>
#include <vector>
#include <chrono>

#include "../../src/Core/MemoryManager.hpp"
#include "../../src/Core/UIElement.hpp"
#include "../../src/Binding/StateManager.hpp"

using namespace DeclarativeUI::Core;
using namespace DeclarativeUI::Binding;

class TestMemoryOptimization : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // **Memory pool performance tests**
    void testObjectPoolPerformance();
    void testObjectPoolVsDirectAllocation();
    void testObjectPoolMemoryUsage();
    void testObjectPoolConcurrency();

    // **Garbage collection performance tests**
    void testGarbageCollectionPerformance();
    void testIncrementalGCPerformance();
    void testGenerationalGCPerformance();
    void testGCMemoryReclamation();

    // **Property binding performance tests**
    void testPropertyBindingBatchUpdates();
    void testPropertyBindingDependencyResolution();
    void testPropertyBindingMemoryUsage();
    void testPropertyBindingCascadeUpdates();

    // **State management performance tests**
    void testStateManagerBatchOperations();
    void testStateManagerDependencyTracking();
    void testStateManagerMemoryLeaks();

    // **Integration performance tests**
    void testUIElementCreationPerformance();
    void testUIElementPropertyUpdatePerformance();
    void testUIElementMemoryFootprint();

    // **Regression tests**
    void testMemoryLeakRegression();
    void testPerformanceRegression();

private:
    QApplication* app_;
    std::unique_ptr<MemoryManager> memory_manager_;
    std::unique_ptr<StateManager> state_manager_;

    // **Performance measurement helpers**
    struct PerformanceMetrics {
        qint64 execution_time_ms;
        size_t memory_used_bytes;
        size_t peak_memory_bytes;
        size_t allocations_count;
        size_t deallocations_count;
    };

    PerformanceMetrics measurePerformance(std::function<void()> operation);
    void logPerformanceMetrics(const QString& test_name, const PerformanceMetrics& metrics);
    size_t getCurrentMemoryUsage();
    void forceGarbageCollection();
};

void TestMemoryOptimization::initTestCase() {
    if (!QApplication::instance()) {
        int argc = 0;
        char** argv = nullptr;
        app_ = new QApplication(argc, argv);
    }
}

void TestMemoryOptimization::cleanupTestCase() {
    // Cleanup handled by QApplication destructor
}

void TestMemoryOptimization::init() {
    memory_manager_ = std::make_unique<MemoryManager>();
    state_manager_ = std::make_unique<StateManager>();
}

void TestMemoryOptimization::cleanup() {
    state_manager_.reset();
    memory_manager_.reset();
    forceGarbageCollection();
}

void TestMemoryOptimization::testObjectPoolPerformance() {
    const int num_allocations = 10000;
    
    // Test optimized object pool
    auto metrics = measurePerformance([this, num_allocations]() {
        std::vector<std::unique_ptr<UIElement>> elements;
        elements.reserve(num_allocations);
        
        for (int i = 0; i < num_allocations; ++i) {
            auto element = memory_manager_->acquire<UIElement>();
            elements.push_back(std::move(element));
        }
        
        // Release all elements
        for (auto& element : elements) {
            memory_manager_->release(std::move(element));
        }
    });
    
    logPerformanceMetrics("ObjectPool Performance", metrics);
    
    // Verify performance criteria
    QVERIFY(metrics.execution_time_ms < 1000); // Should complete in under 1 second
    
    // Check pool efficiency
    auto stats = memory_manager_->getStatistics();
    double hit_ratio = static_cast<double>(stats.pool_hits) / 
                      (stats.pool_hits + stats.pool_misses);
    QVERIFY(hit_ratio > 0.8); // At least 80% hit ratio
}

void TestMemoryOptimization::testObjectPoolVsDirectAllocation() {
    const int num_allocations = 5000;
    
    // Test direct allocation
    auto direct_metrics = measurePerformance([num_allocations]() {
        std::vector<std::unique_ptr<UIElement>> elements;
        elements.reserve(num_allocations);
        
        for (int i = 0; i < num_allocations; ++i) {
            elements.push_back(std::make_unique<UIElement>());
        }
        // Elements automatically destroyed when vector goes out of scope
    });
    
    // Test pool allocation
    auto pool_metrics = measurePerformance([this, num_allocations]() {
        std::vector<std::unique_ptr<UIElement>> elements;
        elements.reserve(num_allocations);
        
        for (int i = 0; i < num_allocations; ++i) {
            auto element = memory_manager_->acquire<UIElement>();
            elements.push_back(std::move(element));
        }
        
        for (auto& element : elements) {
            memory_manager_->release(std::move(element));
        }
    });
    
    logPerformanceMetrics("Direct Allocation", direct_metrics);
    logPerformanceMetrics("Pool Allocation", pool_metrics);
    
    // Pool should be faster for repeated allocations
    QVERIFY(pool_metrics.execution_time_ms <= direct_metrics.execution_time_ms * 1.2);
    
    qDebug() << "Pool allocation speedup:" 
             << (static_cast<double>(direct_metrics.execution_time_ms) / pool_metrics.execution_time_ms)
             << "x";
}

void TestMemoryOptimization::testGarbageCollectionPerformance() {
    // Create memory pressure
    std::vector<std::unique_ptr<UIElement>> elements;
    for (int i = 0; i < 1000; ++i) {
        elements.push_back(memory_manager_->acquire<UIElement>());
    }
    
    // Release half of them to create fragmentation
    for (int i = 0; i < 500; i += 2) {
        memory_manager_->release(std::move(elements[i]));
    }
    
    // Measure GC performance
    auto gc_metrics = measurePerformance([this]() {
        memory_manager_->performGarbageCollection();
    });
    
    logPerformanceMetrics("Garbage Collection", gc_metrics);
    
    // GC should complete quickly
    QVERIFY(gc_metrics.execution_time_ms < 100); // Under 100ms
    
    // Verify memory was reclaimed
    auto stats = memory_manager_->getStatistics();
    QVERIFY(stats.total_freed_bytes > 0);
}

void TestMemoryOptimization::testPropertyBindingBatchUpdates() {
    const int num_elements = 1000;
    const int num_updates = 100;
    
    // Create elements with property bindings
    std::vector<std::unique_ptr<UIElement>> elements;
    for (int i = 0; i < num_elements; ++i) {
        auto element = std::make_unique<UIElement>();
        element->bindProperty("test_prop", [i]() { return i; });
        elements.push_back(std::move(element));
    }
    
    // Test batch updates
    auto batch_metrics = measurePerformance([&elements, num_updates]() {
        for (int update = 0; update < num_updates; ++update) {
            // Simulate batch property updates
            for (auto& element : elements) {
                element->updateBoundProperties();
            }
        }
    });
    
    logPerformanceMetrics("Property Binding Batch Updates", batch_metrics);
    
    // Should handle batch updates efficiently
    QVERIFY(batch_metrics.execution_time_ms < 2000); // Under 2 seconds
    
    qDebug() << "Updates per second:" 
             << (num_elements * num_updates * 1000.0 / batch_metrics.execution_time_ms);
}

void TestMemoryOptimization::testStateManagerBatchOperations() {
    const int num_states = 1000;
    const int num_operations = 100;
    
    // Test batch state operations
    auto batch_metrics = measurePerformance([this, num_states, num_operations]() {
        state_manager_->beginBatch();
        
        for (int op = 0; op < num_operations; ++op) {
            for (int i = 0; i < num_states; ++i) {
                QString key = QString("state_%1").arg(i);
                state_manager_->setState(key, QVariant(op * i));
            }
        }
        
        state_manager_->endBatch();
    });
    
    logPerformanceMetrics("StateManager Batch Operations", batch_metrics);
    
    // Batch operations should be efficient
    QVERIFY(batch_metrics.execution_time_ms < 1000); // Under 1 second
    
    qDebug() << "State operations per second:" 
             << (num_states * num_operations * 1000.0 / batch_metrics.execution_time_ms);
}

void TestMemoryOptimization::testMemoryLeakRegression() {
    size_t initial_memory = getCurrentMemoryUsage();
    
    // Perform operations that previously caused memory leaks
    for (int iteration = 0; iteration < 10; ++iteration) {
        // Create and destroy elements
        std::vector<std::unique_ptr<UIElement>> elements;
        for (int i = 0; i < 100; ++i) {
            auto element = memory_manager_->acquire<UIElement>();
            element->bindProperty("test", [i]() { return i; });
            elements.push_back(std::move(element));
        }
        
        // Update properties
        for (auto& element : elements) {
            element->updateBoundProperties();
        }
        
        // Release elements
        for (auto& element : elements) {
            memory_manager_->release(std::move(element));
        }
        
        // Force garbage collection
        memory_manager_->performGarbageCollection();
    }
    
    forceGarbageCollection();
    size_t final_memory = getCurrentMemoryUsage();
    
    // Memory usage should not grow significantly
    size_t memory_growth = final_memory - initial_memory;
    size_t acceptable_growth = initial_memory * 0.1; // 10% growth is acceptable
    
    QVERIFY2(memory_growth < acceptable_growth, 
             QString("Memory leak detected: grew by %1 bytes").arg(memory_growth).toUtf8());
    
    qDebug() << "Memory growth:" << memory_growth << "bytes";
}

// **Helper method implementations**
TestMemoryOptimization::PerformanceMetrics 
TestMemoryOptimization::measurePerformance(std::function<void()> operation) {
    PerformanceMetrics metrics{};
    
    size_t initial_memory = getCurrentMemoryUsage();
    auto initial_stats = memory_manager_->getStatistics();
    
    QElapsedTimer timer;
    timer.start();
    
    operation();
    
    metrics.execution_time_ms = timer.elapsed();
    
    auto final_stats = memory_manager_->getStatistics();
    size_t final_memory = getCurrentMemoryUsage();
    
    metrics.memory_used_bytes = final_memory - initial_memory;
    metrics.peak_memory_bytes = final_stats.peak_allocated_bytes;
    metrics.allocations_count = final_stats.allocation_count - initial_stats.allocation_count;
    metrics.deallocations_count = final_stats.deallocation_count - initial_stats.deallocation_count;
    
    return metrics;
}

void TestMemoryOptimization::logPerformanceMetrics(const QString& test_name, 
                                                   const PerformanceMetrics& metrics) {
    qDebug() << "=== Performance Metrics for" << test_name << "===";
    qDebug() << "Execution time:" << metrics.execution_time_ms << "ms";
    qDebug() << "Memory used:" << metrics.memory_used_bytes << "bytes";
    qDebug() << "Peak memory:" << metrics.peak_memory_bytes << "bytes";
    qDebug() << "Allocations:" << metrics.allocations_count;
    qDebug() << "Deallocations:" << metrics.deallocations_count;
    qDebug() << "================================================";
}

size_t TestMemoryOptimization::getCurrentMemoryUsage() {
    return memory_manager_->getStatistics().current_allocated_bytes;
}

void TestMemoryOptimization::forceGarbageCollection() {
    memory_manager_->performGarbageCollection();
    QTest::qWait(100); // Allow GC to complete
}

QTEST_MAIN(TestMemoryOptimization)
#include "test_memory_optimization.moc"
