# =============================================================================
# Qt/C++ Project .gitignore
# =============================================================================

# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# =============================================================================
# Build Systems and Artifacts
# =============================================================================

# CMake
build/
build-*/
cmake-build-*/
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# QMake
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
Makefile*
*build-*
*.Debug
*.Release

# Other build systems
.xmake/
.cache/
out/
bin/
lib/

# =============================================================================
# Qt Specific
# =============================================================================

# Qt MOC files
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h

# Qt Creator
*.autosave
*.user
*.user.*

# Qt Linguist
*.qm
*.ts

# Qt Assistant
*.qhc
*.qch

# =============================================================================
# IDE and Editor Specific
# =============================================================================

# Visual Studio / Visual Studio Code
.vs/
.vscode/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# JetBrains IDEs (IntelliJ, CLion, etc.)
.idea/
*.iws
*.iml
*.ipr
cmake-build-*/

# Code::Blocks
*.cbp
*.layout
*.depend

# Dev-C++
*.dev

# =============================================================================
# Platform Specific
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Development Tools and Temporary Files
# =============================================================================

# Debugging and profiling
*.pdb
*.ilk
*.map
*.exp
*.stackdump
core
vgcore.*
*.dmp

# Package managers
node_modules/
.npm/
.yarn/
yarn-error.log

# Python (if used for build scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.venv/
.env
.mypy_cache/
.pytest_cache/

# =============================================================================
# Documentation and Reports
# =============================================================================

# Generated documentation
docs/html/
docs/latex/
docs/man/
docs/rtf/
docs/xml/
doxygen_warnings.txt

# Test reports and coverage
*.gcov
*.gcda
*.gcno
lcov.info
coverage.xml
*.coverage
htmlcov/
.coverage.*
.cache
.pytest_cache/

# Benchmark results
benchmark_results/
*.benchmark

# =============================================================================
# Logs and Temporary Files
# =============================================================================

# Log files
*.log
*.log.*
logs/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
.#*
\#*#

# Backup files
*.bak
*.backup
*.orig

# =============================================================================
# Project Specific
# =============================================================================

# Test artifacts
test_output/
test_results/
Testing/Temporary/

# Configuration files (keep templates, ignore local configs)
config.local.*
settings.local.*
*.local.json

# Resource compilation artifacts
*.qrc.depends
*.rcc

# Hot reload temporary files
.hotreload/
hotreload_cache/