name: 🐛 Bug Report
description: Report a bug or unexpected behavior in DeclarativeUI Framework
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for reporting a bug! Please fill out the information below to help us understand and reproduce the issue.

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened.
      placeholder: What actually happened?
    validations:
      required: true

  - type: dropdown
    id: component
    attributes:
      label: Component
      description: Which component is affected?
      options:
        - Core Framework
        - Components Library
        - Command System
        - State Management
        - Hot Reload
        - JSON Support
        - Build System
        - Documentation
        - Examples
        - Tests
        - Other
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: DeclarativeUI Version
      description: What version of DeclarativeUI are you using?
      placeholder: e.g., 1.0.0, main branch commit hash
    validations:
      required: true

  - type: dropdown
    id: qt-version
    attributes:
      label: Qt Version
      description: What version of Qt are you using?
      options:
        - Qt 6.8
        - Qt 6.7
        - Qt 6.6
        - Qt 6.5
        - Qt 6.4
        - Qt 6.3
        - Qt 6.2
        - Other (specify in additional context)
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - Windows 11
        - Windows 10
        - Ubuntu 22.04
        - Ubuntu 20.04
        - macOS Ventura (13.x)
        - macOS Monterey (12.x)
        - macOS Big Sur (11.x)
        - Other Linux Distribution
        - Other (specify in additional context)
    validations:
      required: true

  - type: dropdown
    id: compiler
    attributes:
      label: Compiler
      description: What compiler are you using?
      options:
        - MSVC 2022
        - MSVC 2019
        - GCC 11
        - GCC 10
        - GCC 9
        - Clang 15
        - Clang 14
        - Clang 13
        - Clang 12
        - MinGW
        - Other (specify in additional context)
    validations:
      required: true

  - type: dropdown
    id: build-type
    attributes:
      label: Build Configuration
      description: What build configuration were you using?
      options:
        - Debug
        - Release
        - RelWithDebInfo
        - MinSizeRel
    validations:
      required: true

  - type: textarea
    id: code-sample
    attributes:
      label: Minimal Code Sample
      description: If applicable, provide a minimal code sample that reproduces the issue
      render: cpp
      placeholder: |
        #include "DeclarativeUI.hpp"
        
        int main() {
            // Minimal code that reproduces the issue
            return 0;
        }

  - type: textarea
    id: logs
    attributes:
      label: Error Messages/Logs
      description: If applicable, add any error messages, stack traces, or log output
      render: text
      placeholder: Paste error messages or logs here...

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here, such as screenshots, related issues, or workarounds you've tried.
      placeholder: Any additional information that might help...

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided all the requested information above
          required: true
        - label: I have tested this with the latest version of DeclarativeUI
          required: false
        - label: I am willing to submit a pull request to fix this issue
          required: false
