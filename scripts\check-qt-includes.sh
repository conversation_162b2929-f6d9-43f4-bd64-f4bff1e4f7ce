#!/bin/bash
# Check for proper Qt includes and common issues

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

errors=0
warnings=0

echo "🔍 Checking Qt includes and usage patterns..."

for file in "$@"; do
    if [[ ! -f "$file" ]]; then
        continue
    fi
    
    echo "Checking: $file"
    
    # Check for deprecated Qt includes
    if grep -n "#include <QtGui>" "$file" >/dev/null 2>&1; then
        echo -e "${RED}❌ Error: Deprecated QtGui include found in $file${NC}"
        echo "   Use specific includes like #include <QWidget> instead"
        ((errors++))
    fi
    
    # Check for missing Q_OBJECT macro in classes with signals/slots
    if grep -n "signals:\|slots:\|Q_SIGNAL\|Q_SLOT" "$file" >/dev/null 2>&1; then
        if ! grep -n "Q_OBJECT" "$file" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Warning: Class with signals/slots missing Q_OBJECT macro in $file${NC}"
            ((warnings++))
        fi
    fi
    
    # Check for proper Qt forward declarations
    if grep -n "class Q" "$file" >/dev/null 2>&1; then
        if ! grep -n "#include.*Q" "$file" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Good: Using Qt forward declarations in $file${NC}"
        fi
    fi
    
    # Check for Qt6 compatibility
    if grep -n "#include <QtWidgets>" "$file" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Warning: Consider using specific Qt6 includes instead of QtWidgets in $file${NC}"
        ((warnings++))
    fi
    
    # Check for proper namespace usage
    if grep -n "using namespace Qt;" "$file" >/dev/null 2>&1; then
        echo -e "${RED}❌ Error: Avoid 'using namespace Qt;' in headers in $file${NC}"
        ((errors++))
    fi
    
    # Check for Qt6 specific patterns
    if grep -n "QStringRef" "$file" >/dev/null 2>&1; then
        echo -e "${RED}❌ Error: QStringRef is deprecated in Qt6, use QStringView in $file${NC}"
        ((errors++))
    fi
    
done

echo ""
echo "📊 Summary:"
echo "   Errors: $errors"
echo "   Warnings: $warnings"

if [ $errors -gt 0 ]; then
    echo -e "${RED}❌ Qt include check failed with $errors errors${NC}"
    exit 1
elif [ $warnings -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Qt include check completed with $warnings warnings${NC}"
    exit 0
else
    echo -e "${GREEN}✅ Qt include check passed${NC}"
    exit 0
fi
