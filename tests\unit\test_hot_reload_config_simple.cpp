#include <QtTest/QtTest>
#include <QObject>
#include <QTemporaryDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <memory>

#include "../../src/HotReload/HotReloadConfig.hpp"

using namespace DeclarativeUI::HotReload;

/**
 * @brief Simple test class for HotReloadConfig
 */
class TestHotReloadConfigSimple : public QObject {
    Q_OBJECT

private slots:
    void init();
    void cleanup();
    
    // Basic functionality tests
    void testConfigCreation();
    void testFileFilterConfig();
    void testPerformanceConfig();
    void testReloadConfig();
    void testConfigProfile();
    void testJsonSerialization();

private:
    std::unique_ptr<HotReloadConfig> config_;
};

void TestHotReloadConfigSimple::init() {
    config_ = std::make_unique<HotReloadConfig>();
}

void TestHotReloadConfigSimple::cleanup() {
    config_.reset();
}

void TestHotReloadConfigSimple::testConfigCreation() {
    // Test that config object is created successfully
    QVERIFY(config_ != nullptr);

    // Test default values
    QVERIFY(!config_->isConfigurationWatchingEnabled());
}

void TestHotReloadConfigSimple::testFileFilterConfig() {
    // Test file filter configuration
    FileFilterConfig filter_config;
    
    // Test default values
    QVERIFY(filter_config.include_patterns.contains("*.qml"));
    QVERIFY(filter_config.include_patterns.contains("*.css"));
    QVERIFY(filter_config.exclude_patterns.contains("*.tmp"));
    QVERIFY(filter_config.exclude_directories.contains(".git"));
    
    // Test setting custom values
    filter_config.include_patterns = {"*.js", "*.ts"};
    filter_config.exclude_patterns = {"*.log"};
    filter_config.max_file_size_bytes = 10 * 1024 * 1024; // 10MB

    QCOMPARE(filter_config.include_patterns.size(), 2);
    QVERIFY(filter_config.include_patterns.contains("*.js"));
    QVERIFY(filter_config.include_patterns.contains("*.ts"));
    QCOMPARE(filter_config.exclude_patterns.size(), 1);
    QVERIFY(filter_config.exclude_patterns.contains("*.log"));
    QCOMPARE(filter_config.max_file_size_bytes, 10 * 1024 * 1024);
}

void TestHotReloadConfigSimple::testPerformanceConfig() {
    // Test performance configuration
    PerformanceConfig perf_config;
    
    // Test default values
    QVERIFY(perf_config.enable_monitoring);
    QVERIFY(perf_config.enable_real_time_analytics);
    QVERIFY(perf_config.enable_bottleneck_detection);
    QVERIFY(!perf_config.enable_memory_profiling);
    
    // Test setting custom values
    perf_config.enable_memory_profiling = true;
    perf_config.cpu_error_threshold_percent = 90.0;
    perf_config.memory_error_threshold_mb = 2048;

    QVERIFY(perf_config.enable_memory_profiling);
    QCOMPARE(perf_config.cpu_error_threshold_percent, 90.0);
    QCOMPARE(perf_config.memory_error_threshold_mb, 2048);
}

void TestHotReloadConfigSimple::testReloadConfig() {
    // Test reload configuration
    ReloadConfig reload_config;
    
    // Test default values
    QCOMPARE(reload_config.strategy, ReloadConfig::Strategy::Smart);
    QCOMPARE(reload_config.reload_delay_ms, 100);
    QVERIFY(reload_config.enable_incremental_reloading);

    // Test setting custom values
    reload_config.strategy = ReloadConfig::Strategy::Batched;
    reload_config.reload_delay_ms = 1000;
    reload_config.batch_timeout_ms = 10000;

    QCOMPARE(reload_config.strategy, ReloadConfig::Strategy::Batched);
    QCOMPARE(reload_config.reload_delay_ms, 1000);
    QCOMPARE(reload_config.batch_timeout_ms, 10000);
}

void TestHotReloadConfigSimple::testConfigProfile() {
    // Test configuration profile
    ConfigProfile profile;
    profile.name = "test_profile";
    profile.description = "Test configuration profile";
    
    // Test basic properties
    QCOMPARE(profile.name, "test_profile");
    QCOMPARE(profile.description, "Test configuration profile");
    QVERIFY(!profile.is_active);
    
    // Test setting profile as active
    profile.is_active = true;
    QVERIFY(profile.is_active);
}

void TestHotReloadConfigSimple::testJsonSerialization() {
    // Test JSON serialization of ConfigProfile
    ConfigProfile profile;
    profile.name = "json_test";
    profile.description = "JSON serialization test";
    profile.is_active = true;
    
    // Customize some settings
    profile.file_filter.include_patterns = {"*.json", "*.xml"};
    profile.file_filter.max_file_size_bytes = 5 * 1024 * 1024; // 5MB
    profile.performance.enable_monitoring = false;
    profile.reload.reload_delay_ms = 750;
    
    // Convert to JSON
    QJsonObject json = profile.toJson();
    
    // Verify JSON structure
    QVERIFY(json.contains("name"));
    QVERIFY(json.contains("description"));
    QVERIFY(json.contains("is_active"));
    QVERIFY(json.contains("file_filter"));
    QVERIFY(json.contains("performance"));
    QVERIFY(json.contains("reload"));
    
    QCOMPARE(json["name"].toString(), "json_test");
    QCOMPARE(json["description"].toString(), "JSON serialization test");
    QCOMPARE(json["is_active"].toBool(), true);
    
    // Test deserialization
    ConfigProfile restored = ConfigProfile::fromJson(json);
    QCOMPARE(restored.name, profile.name);
    QCOMPARE(restored.description, profile.description);
    QCOMPARE(restored.is_active, profile.is_active);
    QCOMPARE(restored.file_filter.max_file_size_bytes, profile.file_filter.max_file_size_bytes);
    QCOMPARE(restored.performance.enable_monitoring, profile.performance.enable_monitoring);
    QCOMPARE(restored.reload.reload_delay_ms, profile.reload.reload_delay_ms);
}

QTEST_MAIN(TestHotReloadConfigSimple)
#include "test_hot_reload_config_simple.moc"
