#pragma once

#include "PluginInterface.hpp"
#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QVersionNumber>

namespace DeclarativeUI::Plugins {

/**
 * @brief Plugin validation result
 */
struct ValidationResult {
    bool isValid = false;
    QString error;
    QStringList warnings;
    QStringList suggestions;
    
    explicit operator bool() const { return isValid; }
};

/**
 * @brief Security validation levels
 */
enum class SecurityLevel {
    None,       // No security validation
    Basic,      // Basic file and signature checks
    Standard,   // Standard security validation
    Strict      // Strict security validation
};

/**
 * @brief Plugin validator for security and compatibility checks
 */
class PluginValidator : public QObject {
    Q_OBJECT
    
public:
    explicit PluginValidator(QObject* parent = nullptr);
    
    // Main validation methods
    ValidationResult validatePlugin(const QString& pluginPath);
    ValidationResult validatePluginManifest(const QString& manifestPath) const;
    ValidationResult validatePluginLibrary(const QString& libraryPath) const;
    ValidationResult validatePluginMetadata(const PluginMetadata& metadata) const;

    // Security validation
    bool validateSecurity(const QString& pluginPath);
    bool checkFileIntegrity(const QString& filePath) const;
    bool verifySignature(const QString& filePath) const;
    bool checkPermissions(const QString& filePath) const;
    
    // Compatibility validation
    bool checkFrameworkCompatibility(const PluginMetadata& metadata) const;
    bool checkDependencyCompatibility(const QStringList& dependencies) const;
    bool checkVersionCompatibility(const QVersionNumber& pluginVersion,
                                  const QVersionNumber& minVersion,
                                  const QVersionNumber& maxVersion) const;
    
    // Configuration
    void setSecurityLevel(SecurityLevel level);
    SecurityLevel getSecurityLevel() const { return security_level_; }
    
    void setFrameworkVersion(const QVersionNumber& version);
    QVersionNumber getFrameworkVersion() const { return framework_version_; }
    
    void setMaxPluginSize(qint64 maxSize);
    qint64 getMaxPluginSize() const { return max_plugin_size_; }
    
    void setAllowedExtensions(const QStringList& extensions);
    QStringList getAllowedExtensions() const { return allowed_extensions_; }
    
    void setRequireSignature(bool require);
    bool isSignatureRequired() const { return require_signature_; }
    
    void setTrustedAuthors(const QStringList& authors);
    QStringList getTrustedAuthors() const { return trusted_authors_; }
    
    void setBlacklistedPlugins(const QStringList& plugins);
    QStringList getBlacklistedPlugins() const { return blacklisted_plugins_; }
    
    // Validation rules
    void addCustomValidationRule(const QString& name, 
                                std::function<ValidationResult(const QString&)> rule);
    void removeCustomValidationRule(const QString& name);
    QStringList getCustomValidationRules() const;
    
    // Error handling
    QString getLastError() const { return last_error_; }
    QStringList getValidationWarnings() const { return validation_warnings_; }
    void clearWarnings();
    
    // Statistics
    int getValidationCount() const { return validation_count_; }
    int getSuccessfulValidations() const { return successful_validations_; }
    int getFailedValidations() const { return failed_validations_; }
    void resetStatistics();
    
signals:
    void validationStarted(const QString& pluginPath);
    void validationCompleted(const QString& pluginPath, bool success);
    void validationWarning(const QString& pluginPath, const QString& warning);
    void securityViolation(const QString& pluginPath, const QString& violation);
    
private:
    // Internal validation methods
    ValidationResult validateFileSystem(const QString& pluginPath) const;
    ValidationResult validateManifestContent(const QJsonObject& manifest) const;
    ValidationResult validateLibraryContent(const QString& libraryPath) const;
    ValidationResult validateSecurityPolicy(const QString& pluginPath);
    
    // Security helpers
    bool checkFileSize(const QString& filePath) const;
    bool checkFileExtension(const QString& filePath) const;
    bool checkFileLocation(const QString& filePath) const;
    bool checkAuthorTrust(const QString& author) const;
    bool isPluginBlacklisted(const QString& pluginPath) const;
    
    // Validation helpers
    QString calculateFileHash(const QString& filePath) const;
    bool validateJsonSchema(const QJsonObject& json, const QString& schemaType) const;
    QStringList extractDependencies(const QJsonObject& manifest) const;
    
    void setError(const QString& error) const;
    void addWarning(const QString& warning) const;
    
private:
    SecurityLevel security_level_ = SecurityLevel::Standard;
    QVersionNumber framework_version_;
    qint64 max_plugin_size_ = 50 * 1024 * 1024; // 50MB
    QStringList allowed_extensions_;
    bool require_signature_ = false;
    QStringList trusted_authors_;
    QStringList blacklisted_plugins_;
    
    // Custom validation rules
    QHash<QString, std::function<ValidationResult(const QString&)>> custom_rules_;
    
    // Statistics
    mutable int validation_count_ = 0;
    mutable int successful_validations_ = 0;
    mutable int failed_validations_ = 0;
    
    mutable QString last_error_;
    mutable QStringList validation_warnings_;
};

} // namespace DeclarativeUI::Plugins
