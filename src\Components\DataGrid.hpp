#pragma once

#include "../Core/UIElement.hpp"
#include <QTableWidget>
#include <QHeaderView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLineEdit>
#include <QLabel>
#include <QComboBox>
#include <QCheckBox>
#include <QMenu>
#include <QJsonObject>
#include <QJsonArray>
#include <functional>

namespace DeclarativeUI::Components {

/**
 * @brief Modern data grid component with sorting, filtering, and pagination
 * 
 * Provides advanced data grid functionality including:
 * - Column sorting and filtering
 * - Row selection (single/multiple)
 * - Pagination support
 * - Custom cell renderers
 * - Export capabilities
 * - Virtual scrolling for large datasets
 */
class DataGrid : public Core::UIElement {
    Q_OBJECT

public:
    enum class SelectionMode {
        None,
        Single,
        Multiple
    };

    enum class SortOrder {
        Ascending,
        Descending,
        None
    };

    struct Column {
        QString key;
        QString title;
        int width = 100;
        bool sortable = true;
        bool filterable = true;
        bool resizable = true;
        bool visible = true;
        Qt::Alignment alignment = Qt::AlignLeft | Qt::AlignVCenter;
        std::function<QString(const QJsonObject&)> formatter;
        std::function<QWidget*(const QJsonObject&, int, int)> renderer;
    };

    struct FilterCriteria {
        QString column;
        QString operator_; // "equals", "contains", "startsWith", "endsWith", "greaterThan", "lessThan"
        QVariant value;
    };

    explicit DataGrid(QObject* parent = nullptr);
    ~DataGrid() override = default;
    
    // **UIElement interface implementation**
    void initialize() override;

    // **Fluent interface for grid configuration**
    DataGrid& columns(const QList<Column>& columns);
    DataGrid& addColumn(const Column& column);
    DataGrid& addColumn(const QString& key, const QString& title, int width = 100);
    DataGrid& removeColumn(const QString& key);
    DataGrid& hideColumn(const QString& key);
    DataGrid& showColumn(const QString& key);

    // **Data management**
    DataGrid& data(const QJsonArray& data);
    DataGrid& addRow(const QJsonObject& row);
    DataGrid& updateRow(int index, const QJsonObject& row);
    DataGrid& removeRow(int index);
    DataGrid& clearData();

    // **Selection and interaction**
    DataGrid& selectionMode(SelectionMode mode);
    DataGrid& sortable(bool enable = true);
    DataGrid& filterable(bool enable = true);
    DataGrid& paginated(bool enable = true);
    DataGrid& pageSize(int size);
    DataGrid& striped(bool enable = true);
    DataGrid& bordered(bool enable = true);
    DataGrid& hoverable(bool enable = true);

    // **Event handlers**
    DataGrid& onRowClicked(std::function<void(int, const QJsonObject&)> callback);
    DataGrid& onRowDoubleClicked(std::function<void(int, const QJsonObject&)> callback);
    DataGrid& onSelectionChanged(std::function<void(const QList<int>&)> callback);
    DataGrid& onSortChanged(std::function<void(const QString&, SortOrder)> callback);
    DataGrid& onFilterChanged(std::function<void(const QList<FilterCriteria>&)> callback);
    DataGrid& onPageChanged(std::function<void(int)> callback);

    // **Filtering and sorting**
    DataGrid& addFilter(const FilterCriteria& filter);
    DataGrid& removeFilter(const QString& column);
    DataGrid& clearFilters();
    DataGrid& sortBy(const QString& column, SortOrder order = SortOrder::Ascending);
    DataGrid& clearSort();

    // **Pagination**
    DataGrid& goToPage(int page);
    DataGrid& nextPage();
    DataGrid& previousPage();
    DataGrid& firstPage();
    DataGrid& lastPage();

    // **Export functionality**
    DataGrid& exportToCsv(const QString& filename);
    DataGrid& exportToJson(const QString& filename);

    // **Getters**
    QJsonArray getData() const;
    QJsonArray getFilteredData() const;
    QList<int> getSelectedRows() const;
    QJsonObject getRow(int index) const;
    int getCurrentPage() const;
    int getTotalPages() const;
    int getTotalRows() const;
    QList<Column> getColumns() const;

    // **Static convenience methods**
    static DataGrid* simple(const QJsonArray& data, const QStringList& columns);
    static DataGrid* sortable(const QJsonArray& data, const QStringList& columns);
    static DataGrid* paginated(const QJsonArray& data, const QStringList& columns, int pageSize = 25);

signals:
    void rowClicked(int row, const QJsonObject& data);
    void rowDoubleClicked(int row, const QJsonObject& data);
    void selectionChanged(const QList<int>& selectedRows);
    void sortChanged(const QString& column, SortOrder order);
    void filterChanged();
    void pageChanged(int page);

private slots:
    void onHeaderClicked(int logicalIndex);
    void onCellClicked(int row, int column);
    void onCellDoubleClicked(int row, int column);
    void onSelectionChangedInternal();
    void onFilterTextChanged();
    void onPageSizeChanged();
    void onPreviousPageClicked();
    void onNextPageClicked();
    void onFirstPageClicked();
    void onLastPageClicked();

private:
    // **UI components**
    QVBoxLayout* main_layout_;
    QHBoxLayout* toolbar_layout_;
    QLineEdit* search_edit_;
    QComboBox* page_size_combo_;
    QLabel* info_label_;
    QTableWidget* table_;
    QHBoxLayout* pagination_layout_;
    QPushButton* first_page_btn_;
    QPushButton* prev_page_btn_;
    QLabel* page_info_label_;
    QPushButton* next_page_btn_;
    QPushButton* last_page_btn_;

    // **Configuration**
    QList<Column> columns_;
    SelectionMode selection_mode_;
    bool sortable_;
    bool filterable_;
    bool paginated_;
    int page_size_;
    bool striped_;
    bool bordered_;
    bool hoverable_;

    // **Data**
    QJsonArray original_data_;
    QJsonArray filtered_data_;
    QJsonArray current_page_data_;
    QList<FilterCriteria> filters_;
    QString sort_column_;
    SortOrder sort_order_;
    int current_page_;
    int total_pages_;

    // **Callbacks**
    std::function<void(int, const QJsonObject&)> row_click_callback_;
    std::function<void(int, const QJsonObject&)> row_double_click_callback_;
    std::function<void(const QList<int>&)> selection_callback_;
    std::function<void(const QString&, SortOrder)> sort_callback_;
    std::function<void(const QList<FilterCriteria>&)> filter_callback_;
    std::function<void(int)> page_callback_;

    // **Helper methods**
    void setupUI();
    void connectSignals();
    void updateTable();
    void applyFilters();
    void applySorting();
    void updatePagination();
    void updatePageData();
    void updateInfoLabel();
    void updatePaginationControls();
    void applyStyles();
    QWidget* createCellWidget(const QJsonObject& rowData, const Column& column, int row, int col);
    QString formatCellValue(const QJsonObject& rowData, const Column& column);
    bool matchesFilter(const QJsonObject& row, const FilterCriteria& filter);
    int compareValues(const QVariant& a, const QVariant& b);
};

} // namespace DeclarativeUI::Components
