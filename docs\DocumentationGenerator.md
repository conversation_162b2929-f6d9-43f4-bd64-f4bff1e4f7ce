# Documentation Generator

The **Documentation Generator** is a comprehensive automated documentation system for the DeclarativeUI framework that creates beautiful, interactive documentation from your source code annotations and comments.

## 🌟 Features

### **Automated API Documentation**
- **Source Code Analysis**: Automatically parses C++ source files and extracts classes, methods, properties, and documentation comments
- **Doxygen Integration**: Full support for Doxygen-style comments with parameter descriptions, return values, and examples
- **Cross-Reference Generation**: Automatic linking between related classes, methods, and components
- **Multiple Output Formats**: Generate HTML, Markdown, JSON, and PDF documentation

### **Interactive Component Gallery**
- **Live Component Demos**: Interactive showcases of UI components with real-time customization
- **Property Panels**: Dynamic property editors that allow users to experiment with component settings
- **Code Examples**: Automatically generated code snippets showing how to use each component
- **Visual Previews**: Screenshots and live previews of components in different states

### **Example Documentation**
- **Interactive Tutorials**: Step-by-step guides with executable code examples
- **Code Playground**: In-browser code editor with live preview capabilities
- **Best Practices**: Curated examples demonstrating recommended usage patterns
- **Integration Guides**: Comprehensive guides for integrating with other frameworks

### **Advanced Features**
- **Live Documentation Server**: Built-in web server with hot reload for development
- **Search and Indexing**: Full-text search across all documentation content
- **Quality Validation**: Automated checks for documentation completeness and quality
- **Incremental Updates**: Smart regeneration that only updates changed content
- **Responsive Design**: Mobile-friendly documentation that works on all devices
- **Theme Support**: Multiple built-in themes with customization options

## 🚀 Quick Start

### **Basic Usage**

```cpp
#include "Documentation/DocumentationGenerator.hpp"

using namespace DeclarativeUI::Documentation;

// Create and configure the generator
DocumentationGenerator generator;

DocumentationGenerator::GeneratorConfig config;
config.project_name = "My Project";
config.project_version = "1.0.0";
config.output_directory = "docs/generated";
config.source_directories = {"src/", "include/"};
config.generate_api_reference = true;
config.generate_component_gallery = true;
config.generate_examples = true;

generator.setConfiguration(config);

// Generate documentation
if (generator.generateDocumentation()) {
    qDebug() << "Documentation generated successfully!";
} else {
    qDebug() << "Documentation generation failed";
}
```

### **Interactive Example Application**

Run the comprehensive example application to explore all features:

```bash
# Build and run the example
mkdir build && cd build
cmake .. -DBUILD_DOCUMENTATION_GENERATOR=ON
make DocumentationGeneratorExample
./DocumentationGeneratorExample
```

The example application provides:
- **Interactive Configuration**: GUI for setting up documentation generation
- **Real-time Progress**: Live progress monitoring with detailed status updates
- **Preview and Validation**: Built-in preview and quality validation tools
- **Live Server**: Integrated web server for testing generated documentation

## 📖 Configuration

### **Generator Configuration**

```cpp
struct GeneratorConfig {
    // **Project Information**
    QString project_name = "DeclarativeUI Project";
    QString project_version = "1.0.0";
    QString project_description = "Modern declarative UI framework";
    QString project_author = "";
    QString project_url = "";
    
    // **Input/Output**
    QString output_directory = "docs/generated";
    QStringList source_directories = {"src/"};
    QStringList example_directories = {"examples/"};
    QStringList exclude_patterns = {"*.tmp", "*.bak"};
    
    // **Generation Options**
    bool generate_api_reference = true;
    bool generate_component_gallery = true;
    bool generate_examples = true;
    bool generate_search_index = true;
    bool enable_live_examples = true;
    bool parse_doxygen_comments = true;
    
    // **Appearance**
    QString theme = "default";  // "default", "dark", "light", "modern"
    QString custom_css_file = "";
    QString logo_path = "";
    
    // **Advanced Options**
    bool enable_incremental_updates = false;
    bool generate_pdf_output = false;
    bool include_private_members = false;
    int max_concurrent_threads = 4;
};
```

### **Loading Configuration from File**

```cpp
// Load from JSON configuration file
generator.loadConfigurationFromFile("documentation_config.json");

// Save current configuration
generator.saveConfigurationToFile("documentation_config.json");
```

Example JSON configuration:

```json
{
    "project_name": "DeclarativeUI Framework",
    "project_version": "1.0.0",
    "project_description": "Modern declarative UI framework for Qt",
    "output_directory": "docs/generated",
    "source_directories": ["src/", "include/"],
    "example_directories": ["examples/"],
    "theme": "modern",
    "generate_api_reference": true,
    "generate_component_gallery": true,
    "generate_examples": true,
    "parse_doxygen_comments": true
}
```

## 🎨 Documentation Comments

### **Doxygen-Style Comments**

The Documentation Generator supports comprehensive Doxygen-style comments:

```cpp
/**
 * @brief A modern button component with advanced styling
 * 
 * The ModernButton provides a highly customizable button widget
 * with support for themes, animations, and accessibility features.
 * 
 * @example Basic Usage
 * ```cpp
 * ModernButton* button = new ModernButton("Click Me");
 * button->setTheme(ModernButton::Theme::Primary);
 * button->setIcon("icons/save.png");
 * ```
 * 
 * @see ButtonGroup, IconButton
 * @since 1.0.0
 * <AUTHOR> Team
 */
class ModernButton : public QPushButton {
    Q_OBJECT
    
    /**
     * @brief The visual theme of the button
     * 
     * Controls the overall appearance and color scheme.
     * Available themes include Primary, Secondary, Success, Warning, and Danger.
     * 
     * @default Theme::Primary
     */
    Q_PROPERTY(Theme theme READ theme WRITE setTheme NOTIFY themeChanged)
    
public:
    /**
     * @brief Available button themes
     */
    enum class Theme {
        Primary,    ///< Blue primary theme
        Secondary,  ///< Gray secondary theme
        Success,    ///< Green success theme
        Warning,    ///< Orange warning theme
        Danger      ///< Red danger theme
    };
    
    /**
     * @brief Constructs a new ModernButton
     * 
     * @param text The button text to display
     * @param parent The parent widget (optional)
     * 
     * @example Creating a button
     * ```cpp
     * auto button = new ModernButton("Save Document", this);
     * ```
     */
    explicit ModernButton(const QString& text, QWidget* parent = nullptr);
    
    /**
     * @brief Sets the button theme
     * 
     * @param theme The new theme to apply
     * 
     * @note Theme changes are animated automatically
     * @see Theme, themeChanged()
     */
    void setTheme(Theme theme);
    
    /**
     * @brief Gets the current theme
     * 
     * @return The currently active theme
     */
    Theme theme() const { return current_theme_; }

signals:
    /**
     * @brief Emitted when the theme changes
     * 
     * @param theme The new theme that was applied
     */
    void themeChanged(Theme theme);
    
private:
    Theme current_theme_ = Theme::Primary;
};
```

### **Component Documentation**

For UI components, add special component documentation:

```cpp
/**
 * @component ModernButton
 * @category Buttons
 * @description A modern, themeable button component
 * 
 * @property text string "Button text"
 * @property theme enum "Visual theme" Primary|Secondary|Success|Warning|Danger
 * @property icon string "Icon path (optional)"
 * @property enabled bool "Whether the button is enabled"
 * 
 * @demo
 * ```qml
 * ModernButton {
 *     text: "Click Me"
 *     theme: ModernButton.Primary
 *     onClicked: console.log("Button clicked!")
 * }
 * ```
 * 
 * @demo Advanced Usage
 * ```cpp
 * auto button = new ModernButton("Save", this);
 * button->setTheme(ModernButton::Theme::Success);
 * button->setIcon(":/icons/save.svg");
 * connect(button, &ModernButton::clicked, this, &MainWindow::saveDocument);
 * ```
 */
```

## 🔧 Advanced Usage

### **Custom Templates**

Create custom HTML templates for documentation output:

```cpp
// Set custom template directory
config.template_directory = "templates/custom";

// Templates should include:
// - index.html.template
// - api_class.html.template  
// - component.html.template
// - example.html.template
```

### **Live Documentation Server**

Start a live documentation server for development:

```cpp
// Enable live server
generator.startLiveDocumentation(8080);

// Enable hot reload for source changes
generator.enableIncrementalUpdates(true);

// Server will automatically regenerate documentation when files change
```

### **Quality Validation**

Validate documentation quality and completeness:

```cpp
auto validation_results = generator.validateDocumentation();

qDebug() << "Documentation coverage:" << validation_results.coverage_percentage << "%";
qDebug() << "Missing documentation:" << validation_results.missing_items.size() << "items";
qDebug() << "Broken links:" << validation_results.broken_links.size();

// Get detailed recommendations
for (const auto& recommendation : validation_results.recommendations) {
    qDebug() << "Recommendation:" << recommendation;
}
```

### **Performance Optimization**

Optimize documentation generation for large codebases:

```cpp
// Enable parallel processing
config.max_concurrent_threads = QThread::idealThreadCount();

// Use incremental updates
config.enable_incremental_updates = true;

// Exclude unnecessary files
config.exclude_patterns = {"*.tmp", "*.bak", "build/*", "*.o"};

// Monitor generation performance
connect(&generator, &DocumentationGenerator::generationProgress,
        [](int percentage, const QString& task) {
    qDebug() << "Progress:" << percentage << "% -" << task;
});
```

## 📊 Output Structure

The generated documentation follows this structure:

```
docs/generated/
├── index.html                 # Main documentation index
├── api/                       # API reference documentation
│   ├── classes/              # Individual class documentation
│   ├── namespaces/           # Namespace documentation
│   └── files/                # File-based documentation
├── components/               # Component gallery
│   ├── gallery.html          # Component overview
│   └── [component-name]/     # Individual component pages
├── examples/                 # Example documentation
│   ├── tutorials/            # Step-by-step tutorials
│   └── code-samples/         # Code example collection
├── search/                   # Search index and interface
│   ├── search.js             # Search functionality
│   └── index.json            # Search index data
├── assets/                   # Static assets
│   ├── css/                  # Stylesheets
│   ├── js/                   # JavaScript files
│   └── images/               # Images and icons
└── data/                     # Generated data files
    ├── api.json              # API reference data
    ├── components.json       # Component metadata
    └── examples.json         # Example metadata
```

## 🎯 Best Practices

### **Writing Good Documentation Comments**

1. **Be Descriptive**: Write clear, concise descriptions that explain what the code does
2. **Include Examples**: Provide practical usage examples for complex APIs
3. **Document Parameters**: Always document function parameters and return values
4. **Use Cross-References**: Link to related classes and methods using `@see`
5. **Keep It Updated**: Ensure documentation stays in sync with code changes

### **Organizing Components**

1. **Use Categories**: Group related components using `@category` tags
2. **Provide Demos**: Include interactive demos for visual components
3. **Show Variations**: Document different states and configurations
4. **Include Best Practices**: Show recommended usage patterns

### **Performance Considerations**

1. **Use Incremental Updates**: Enable incremental updates for large projects
2. **Exclude Build Artifacts**: Use exclude patterns to skip temporary files
3. **Optimize Images**: Compress images used in documentation
4. **Enable Parallel Processing**: Use multiple threads for faster generation

## 🔍 Troubleshooting

### **Common Issues**

**Documentation not generating:**
- Check that source directories exist and contain valid C++ files
- Verify that Doxygen comments are properly formatted
- Enable debug output to see detailed error messages

**Missing components in gallery:**
- Ensure components have `@component` tags in their documentation
- Check that component files are in the specified source directories
- Verify that components inherit from appropriate base classes

**Broken links in output:**
- Run validation to identify broken cross-references
- Check that referenced classes and methods exist
- Ensure proper spelling in `@see` tags

**Performance issues:**
- Enable incremental updates for faster regeneration
- Increase the number of concurrent threads
- Use exclude patterns to skip unnecessary files

### **Debug Output**

Enable detailed debug output for troubleshooting:

```cpp
// Enable debug logging
qputenv("QT_LOGGING_RULES", "declarativeui.documentation.debug=true");

// Monitor generation progress
connect(&generator, &DocumentationGenerator::fileProcessed,
        [](const QString& file) {
    qDebug() << "Processed:" << file;
});

connect(&generator, &DocumentationGenerator::errorOccurred,
        [](const QString& error) {
    qCritical() << "Error:" << error;
});
```

## 📚 API Reference

For complete API documentation, see the generated documentation or explore the source code in `src/Documentation/DocumentationGenerator.hpp`.

### **Key Classes**

- **`DocumentationGenerator`**: Main documentation generation engine
- **`ComponentGalleryGenerator`**: Specialized generator for component galleries  
- **`ExampleDocumentationGenerator`**: Generator for example documentation
- **`LiveDocumentationServer`**: Built-in web server for live documentation
- **`DocumentationQualityAnalyzer`**: Quality validation and metrics

### **Signals and Events**

The Documentation Generator emits detailed progress and status signals:

- `generationStarted()`: Documentation generation begins
- `generationProgress(int, QString)`: Progress updates with current task
- `generationFinished(bool)`: Generation completed (success/failure)
- `fileProcessed(QString)`: Individual file processed
- `componentExtracted(ComponentInfo)`: Component discovered
- `exampleFound(ExampleInfo)`: Example discovered
- `errorOccurred(QString)`: Error during generation
- `warningIssued(QString)`: Warning during generation

## 🎉 Conclusion

The Documentation Generator provides a comprehensive solution for creating beautiful, interactive documentation for your DeclarativeUI projects. With its powerful automation, flexible configuration, and rich output formats, it makes maintaining high-quality documentation effortless and enjoyable.

Start generating amazing documentation today! 🚀
