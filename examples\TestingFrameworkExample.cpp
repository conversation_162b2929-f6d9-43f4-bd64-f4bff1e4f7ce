#include "TestingFrameworkExample.hpp"
#include <QApplication>
#include <QScreen>
#include <QSplitter>
#include <QTabWidget>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QHeaderView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QSpacerItem>
#include <QSizePolicy>
#include <QFont>
#include <QFontMetrics>
#include <QPalette>
#include <QStyle>
#include <QStyleOption>
#include <QPainter>
#include <QPixmap>
#include <QIcon>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTextStream>
#include <QDebug>

namespace DeclarativeUI::Testing::Examples {

// **TestingFrameworkExample Implementation**

TestingFrameworkExample::TestingFrameworkExample(QWidget* parent)
    : QMainWindow(parent)
{
    setWindowTitle("DeclarativeUI Testing Framework - Comprehensive Example");
    setMinimumSize(1200, 800);
    resize(1400, 1000);

    // Initialize testing framework components
    test_runner_ = std::make_unique<TestRunner>(this);
    ui_automation_ = std::make_unique<UIAutomation>(this);
    visual_testing_ = std::make_unique<VisualTesting>(this);
    component_tester_ = std::make_unique<ComponentTester>(this);
    performance_benchmark_ = std::make_unique<PerformanceBenchmark>(this);
    accessibility_tester_ = std::make_unique<AccessibilityTester>(this);
    memory_profiler_ = std::make_unique<MemoryProfiler>(this);

    // Setup UI
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();

    // Connect test runner signals
    connect(test_runner_.get(), &TestRunner::testStarted, this, &TestingFrameworkExample::onTestStarted);
    connect(test_runner_.get(), &TestRunner::testFinished, this, &TestingFrameworkExample::onTestFinished);
    connect(test_runner_.get(), &TestRunner::executionFinished, this, &TestingFrameworkExample::onExecutionFinished);
    connect(test_runner_.get(), &TestRunner::progressUpdated, this, &TestingFrameworkExample::updateTestProgress);

    // Create sample tests
    createSampleTests();
    populateTestList();

    // Load default configuration
    test_config_ = TestConfigurationManager::getDefaultConfiguration();
    test_runner_->setConfiguration(test_config_);

    qDebug() << "Testing Framework Example initialized successfully";
}

TestingFrameworkExample::~TestingFrameworkExample() {
    if (tests_running_) {
        test_runner_->stopExecution();
    }
}

void TestingFrameworkExample::setupUI() {
    central_widget_ = new QWidget(this);
    setCentralWidget(central_widget_);

    // Create main splitter
    main_splitter_ = new QSplitter(Qt::Horizontal, central_widget_);
    
    QVBoxLayout* main_layout = new QVBoxLayout(central_widget_);
    main_layout->addWidget(main_splitter_);
    main_layout->setContentsMargins(5, 5, 5, 5);

    // Setup panels
    setupTestControlPanel();
    setupTestResultsPanel();
    setupDemoPanel();
    setupConfigurationPanel();

    // Create main tabs
    main_tabs_ = new QTabWidget();
    main_tabs_->addTab(test_control_panel_, "Test Control");
    main_tabs_->addTab(test_results_panel_, "Test Results");
    main_tabs_->addTab(demo_panel_, "Live Demo");
    main_tabs_->addTab(config_panel_, "Configuration");

    main_splitter_->addWidget(main_tabs_);
    main_splitter_->setSizes({800, 600});
}

void TestingFrameworkExample::setupMenuBar() {
    QMenuBar* menu_bar = menuBar();

    // **File Menu**
    QMenu* file_menu = menu_bar->addMenu("&File");
    
    QAction* load_tests_action = file_menu->addAction("&Load Tests...");
    connect(load_tests_action, &QAction::triggered, this, &TestingFrameworkExample::loadTestsFromFile);
    
    QAction* save_config_action = file_menu->addAction("&Save Configuration...");
    connect(save_config_action, &QAction::triggered, this, &TestingFrameworkExample::saveTestConfiguration);
    
    file_menu->addSeparator();
    
    QAction* export_results_action = file_menu->addAction("&Export Results...");
    connect(export_results_action, &QAction::triggered, this, &TestingFrameworkExample::exportTestResults);
    
    file_menu->addSeparator();
    
    QAction* exit_action = file_menu->addAction("E&xit");
    connect(exit_action, &QAction::triggered, this, &QWidget::close);

    // **Tests Menu**
    QMenu* tests_menu = menu_bar->addMenu("&Tests");
    
    QAction* run_all_action = tests_menu->addAction("Run &All Tests");
    connect(run_all_action, &QAction::triggered, this, &TestingFrameworkExample::runAllTests);
    
    QAction* run_selected_action = tests_menu->addAction("Run &Selected Tests");
    connect(run_selected_action, &QAction::triggered, this, &TestingFrameworkExample::runSelectedTests);
    
    tests_menu->addSeparator();
    
    QAction* run_performance_action = tests_menu->addAction("Run &Performance Tests");
    connect(run_performance_action, &QAction::triggered, this, &TestingFrameworkExample::runPerformanceTests);
    
    QAction* run_visual_action = tests_menu->addAction("Run &Visual Tests");
    connect(run_visual_action, &QAction::triggered, this, &TestingFrameworkExample::runVisualTests);
    
    QAction* run_accessibility_action = tests_menu->addAction("Run &Accessibility Tests");
    connect(run_accessibility_action, &QAction::triggered, this, &TestingFrameworkExample::runAccessibilityTests);
    
    tests_menu->addSeparator();
    
    QAction* stop_action = tests_menu->addAction("&Stop Execution");
    connect(stop_action, &QAction::triggered, this, &TestingFrameworkExample::stopTestExecution);
    
    QAction* clear_action = tests_menu->addAction("&Clear Results");
    connect(clear_action, &QAction::triggered, this, &TestingFrameworkExample::clearTestResults);

    // **Demo Menu**
    QMenu* demo_menu = menu_bar->addMenu("&Demo");
    
    QAction* ui_automation_action = demo_menu->addAction("&UI Automation");
    connect(ui_automation_action, &QAction::triggered, this, &TestingFrameworkExample::demonstrateUIAutomation);
    
    QAction* visual_testing_action = demo_menu->addAction("&Visual Testing");
    connect(visual_testing_action, &QAction::triggered, this, &TestingFrameworkExample::demonstrateVisualTesting);
    
    QAction* component_testing_action = demo_menu->addAction("&Component Testing");
    connect(component_testing_action, &QAction::triggered, this, &TestingFrameworkExample::demonstrateComponentTesting);
    
    QAction* performance_testing_action = demo_menu->addAction("&Performance Testing");
    connect(performance_testing_action, &QAction::triggered, this, &TestingFrameworkExample::demonstratePerformanceTesting);

    // **Reports Menu**
    QMenu* reports_menu = menu_bar->addMenu("&Reports");
    
    QAction* html_report_action = reports_menu->addAction("Generate &HTML Report");
    connect(html_report_action, &QAction::triggered, this, &TestingFrameworkExample::generateHTMLReport);
    
    QAction* xml_report_action = reports_menu->addAction("Generate &JUnit XML Report");
    connect(xml_report_action, &QAction::triggered, this, &TestingFrameworkExample::generateJUnitXMLReport);
    
    QAction* json_report_action = reports_menu->addAction("Generate &JSON Report");
    connect(json_report_action, &QAction::triggered, this, &TestingFrameworkExample::generateJSONReport);

    // **Help Menu**
    QMenu* help_menu = menu_bar->addMenu("&Help");
    
    QAction* about_action = help_menu->addAction("&About");
    connect(about_action, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About Testing Framework Example",
                          "DeclarativeUI Testing Framework Example\n\n"
                          "This application demonstrates the comprehensive testing capabilities "
                          "of the DeclarativeUI framework including:\n"
                          "• UI automation and interaction simulation\n"
                          "• Visual regression testing\n"
                          "• Component testing with mocks\n"
                          "• Performance benchmarking\n"
                          "• Accessibility testing\n"
                          "• Memory profiling\n"
                          "• Advanced test reporting\n\n"
                          "Version 1.0\n"
                          "Built with Qt6 and C++20");
    });
}

void TestingFrameworkExample::setupToolBar() {
    QToolBar* tool_bar = addToolBar("Main");
    tool_bar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    // **Test Execution Actions**
    QAction* run_all_action = tool_bar->addAction("Run All");
    run_all_action->setIcon(style()->standardIcon(QStyle::SP_MediaPlay));
    connect(run_all_action, &QAction::triggered, this, &TestingFrameworkExample::runAllTests);

    QAction* stop_action = tool_bar->addAction("Stop");
    stop_action->setIcon(style()->standardIcon(QStyle::SP_MediaStop));
    connect(stop_action, &QAction::triggered, this, &TestingFrameworkExample::stopTestExecution);

    tool_bar->addSeparator();

    // **Demo Actions**
    QAction* ui_demo_action = tool_bar->addAction("UI Demo");
    ui_demo_action->setIcon(style()->standardIcon(QStyle::SP_ComputerIcon));
    connect(ui_demo_action, &QAction::triggered, this, &TestingFrameworkExample::demonstrateUIAutomation);

    QAction* visual_demo_action = tool_bar->addAction("Visual Demo");
    visual_demo_action->setIcon(style()->standardIcon(QStyle::SP_FileDialogDetailedView));
    connect(visual_demo_action, &QAction::triggered, this, &TestingFrameworkExample::demonstrateVisualTesting);

    tool_bar->addSeparator();

    // **Report Actions**
    QAction* html_report_action = tool_bar->addAction("HTML Report");
    html_report_action->setIcon(style()->standardIcon(QStyle::SP_FileIcon));
    connect(html_report_action, &QAction::triggered, this, &TestingFrameworkExample::generateHTMLReport);
}

void TestingFrameworkExample::setupStatusBar() {
    QStatusBar* status_bar = statusBar();
    
    test_status_label_ = new QLabel("Ready");
    status_bar->addWidget(test_status_label_);
    
    status_bar->addPermanentWidget(new QLabel("Tests: 0 | Passed: 0 | Failed: 0"));
}

void TestingFrameworkExample::setupTestControlPanel() {
    test_control_panel_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(test_control_panel_);

    // **Test Execution Controls**
    QGroupBox* execution_group = new QGroupBox("Test Execution");
    QGridLayout* execution_layout = new QGridLayout(execution_group);

    run_all_button_ = new QPushButton("Run All Tests");
    run_all_button_->setIcon(style()->standardIcon(QStyle::SP_MediaPlay));
    connect(run_all_button_, &QPushButton::clicked, this, &TestingFrameworkExample::runAllTests);

    run_selected_button_ = new QPushButton("Run Selected");
    run_selected_button_->setIcon(style()->standardIcon(QStyle::SP_MediaSeekForward));
    connect(run_selected_button_, &QPushButton::clicked, this, &TestingFrameworkExample::runSelectedTests);

    run_performance_button_ = new QPushButton("Performance Tests");
    connect(run_performance_button_, &QPushButton::clicked, this, &TestingFrameworkExample::runPerformanceTests);

    run_visual_button_ = new QPushButton("Visual Tests");
    connect(run_visual_button_, &QPushButton::clicked, this, &TestingFrameworkExample::runVisualTests);

    run_accessibility_button_ = new QPushButton("Accessibility Tests");
    connect(run_accessibility_button_, &QPushButton::clicked, this, &TestingFrameworkExample::runAccessibilityTests);

    stop_button_ = new QPushButton("Stop");
    stop_button_->setIcon(style()->standardIcon(QStyle::SP_MediaStop));
    stop_button_->setEnabled(false);
    connect(stop_button_, &QPushButton::clicked, this, &TestingFrameworkExample::stopTestExecution);

    clear_results_button_ = new QPushButton("Clear Results");
    clear_results_button_->setIcon(style()->standardIcon(QStyle::SP_DialogResetButton));
    connect(clear_results_button_, &QPushButton::clicked, this, &TestingFrameworkExample::clearTestResults);

    execution_layout->addWidget(run_all_button_, 0, 0);
    execution_layout->addWidget(run_selected_button_, 0, 1);
    execution_layout->addWidget(run_performance_button_, 1, 0);
    execution_layout->addWidget(run_visual_button_, 1, 1);
    execution_layout->addWidget(run_accessibility_button_, 2, 0);
    execution_layout->addWidget(stop_button_, 2, 1);
    execution_layout->addWidget(clear_results_button_, 3, 0, 1, 2);

    // **Progress Tracking**
    QGroupBox* progress_group = new QGroupBox("Progress");
    QVBoxLayout* progress_layout = new QVBoxLayout(progress_group);

    test_progress_ = new QProgressBar();
    test_progress_->setRange(0, 100);
    test_progress_->setValue(0);

    statistics_label_ = new QLabel("No tests executed");
    statistics_label_->setAlignment(Qt::AlignCenter);

    progress_layout->addWidget(test_progress_);
    progress_layout->addWidget(statistics_label_);

    // **Test List**
    QGroupBox* test_list_group = new QGroupBox("Available Tests");
    QVBoxLayout* test_list_layout = new QVBoxLayout(test_list_group);

    test_list_ = new QTreeWidget();
    test_list_->setHeaderLabels({"Test Name", "Type", "Status", "Duration"});
    test_list_->setSelectionMode(QAbstractItemView::ExtendedSelection);
    test_list_->setAlternatingRowColors(true);
    test_list_->header()->setStretchLastSection(false);
    test_list_->header()->setSectionResizeMode(0, QHeaderView::Stretch);

    connect(test_list_, &QTreeWidget::itemSelectionChanged, this, &TestingFrameworkExample::refreshTestResults);

    test_list_layout->addWidget(test_list_);

    // **Add to main layout**
    layout->addWidget(execution_group);
    layout->addWidget(progress_group);
    layout->addWidget(test_list_group);
    layout->addStretch();
}

void TestingFrameworkExample::setupTestResultsPanel() {
    test_results_panel_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(test_results_panel_);

    // **Results Display**
    test_details_ = new QTextEdit();
    test_details_->setReadOnly(true);
    test_details_->setFont(QFont("Consolas", 10));
    test_details_->setPlainText("Select a test from the list to view detailed results...");

    layout->addWidget(new QLabel("Test Results Details:"));
    layout->addWidget(test_details_);
}

void TestingFrameworkExample::setupDemoPanel() {
    demo_panel_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(demo_panel_);

    demo_tabs_ = new QTabWidget();

    // **UI Automation Demo**
    setupUIAutomationDemo();
    demo_tabs_->addTab(ui_automation_demo_, "UI Automation");

    // **Visual Testing Demo**
    setupVisualTestingDemo();
    demo_tabs_->addTab(visual_testing_demo_, "Visual Testing");

    // **Component Testing Demo**
    setupComponentTestingDemo();
    demo_tabs_->addTab(component_testing_demo_, "Component Testing");

    // **Performance Testing Demo**
    setupPerformanceTestingDemo();
    demo_tabs_->addTab(performance_testing_demo_, "Performance Testing");

    layout->addWidget(demo_tabs_);
}

void TestingFrameworkExample::setupConfigurationPanel() {
    config_panel_ = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(config_panel_);

    // **Execution Settings**
    QGroupBox* execution_settings = new QGroupBox("Execution Settings");
    QFormLayout* execution_form = new QFormLayout(execution_settings);

    parallel_execution_checkbox_ = new QCheckBox();
    parallel_execution_checkbox_->setChecked(false);

    max_concurrent_spinbox_ = new QSpinBox();
    max_concurrent_spinbox_->setRange(1, QThread::idealThreadCount());
    max_concurrent_spinbox_->setValue(QThread::idealThreadCount());

    timeout_spinbox_ = new QSpinBox();
    timeout_spinbox_->setRange(1, 300);
    timeout_spinbox_->setValue(30);
    timeout_spinbox_->setSuffix(" seconds");

    fail_fast_checkbox_ = new QCheckBox();
    fail_fast_checkbox_->setChecked(false);

    execution_form->addRow("Parallel Execution:", parallel_execution_checkbox_);
    execution_form->addRow("Max Concurrent Tests:", max_concurrent_spinbox_);
    execution_form->addRow("Test Timeout:", timeout_spinbox_);
    execution_form->addRow("Fail Fast:", fail_fast_checkbox_);

    // **Report Settings**
    QGroupBox* report_settings = new QGroupBox("Report Settings");
    QFormLayout* report_form = new QFormLayout(report_settings);

    generate_html_checkbox_ = new QCheckBox();
    generate_html_checkbox_->setChecked(true);

    generate_xml_checkbox_ = new QCheckBox();
    generate_xml_checkbox_->setChecked(false);

    output_directory_edit_ = new QLineEdit();
    output_directory_edit_->setText(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/test_results");

    QPushButton* browse_button = new QPushButton("Browse...");
    connect(browse_button, &QPushButton::clicked, [this]() {
        QString dir = QFileDialog::getExistingDirectory(this, "Select Output Directory", output_directory_edit_->text());
        if (!dir.isEmpty()) {
            output_directory_edit_->setText(dir);
        }
    });

    QHBoxLayout* output_layout = new QHBoxLayout();
    output_layout->addWidget(output_directory_edit_);
    output_layout->addWidget(browse_button);

    report_form->addRow("Generate HTML Report:", generate_html_checkbox_);
    report_form->addRow("Generate JUnit XML:", generate_xml_checkbox_);
    report_form->addRow("Output Directory:", output_layout);

    // **Apply Settings Button**
    QPushButton* apply_button = new QPushButton("Apply Settings");
    connect(apply_button, &QPushButton::clicked, [this]() {
        test_config_.enable_parallel_testing = parallel_execution_checkbox_->isChecked();
        test_config_.timeout_seconds = timeout_spinbox_->value();
        test_config_.generate_html_report = generate_html_checkbox_->isChecked();
        test_config_.generate_junit_xml = generate_xml_checkbox_->isChecked();
        test_config_.output_directory = output_directory_edit_->text();

        test_runner_->setConfiguration(test_config_);
        test_runner_->setParallelExecution(parallel_execution_checkbox_->isChecked());
        test_runner_->setMaxConcurrentTests(max_concurrent_spinbox_->value());
        test_runner_->setTimeout(timeout_spinbox_->value());
        test_runner_->setFailFast(fail_fast_checkbox_->isChecked());

        QMessageBox::information(this, "Settings Applied", "Test configuration has been updated successfully.");
    });

    layout->addWidget(execution_settings);
    layout->addWidget(report_settings);
    layout->addWidget(apply_button);
    layout->addStretch();
}

} // namespace DeclarativeUI::Testing::Examples

#include "TestingFrameworkExample.moc"
