{"type": "QWidget", "properties": {"windowTitle": "Counter Application", "minimumSize": [300, 200], "styleSheet": "QWidget { background-color: #f5f5f5; font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif; }"}, "layout": {"type": "VBoxLayout", "spacing": 20, "margins": [30, 30, 30, 30]}, "children": [{"type": "QLabel", "id": "title_label", "properties": {"text": "🔢 Counter Application", "alignment": 4, "styleSheet": "QLabel { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }"}}, {"type": "QLabel", "id": "counter_display", "bindings": {"text": "counter_text"}, "properties": {"alignment": 4, "styleSheet": "QLabel { font-size: 48px; font-weight: bold; color: #3498db; background-color: white; border: 2px solid #3498db; border-radius: 10px; padding: 20px; }"}}, {"type": "QWidget", "layout": {"type": "HBoxLayout", "spacing": 15}, "children": [{"type": "QPushButton", "properties": {"text": "➖ Decrement", "minimumHeight": 50, "styleSheet": "QPushButton { background-color: #e74c3c; color: white; font-size: 16px; font-weight: bold; border: none; border-radius: 8px; } QPushButton:hover { background-color: #c0392b; } QPushButton:pressed { background-color: #a93226; }"}, "events": {"clicked": "decrementCounter"}}, {"type": "QPushButton", "properties": {"text": "🔄 Reset", "minimumHeight": 50, "styleSheet": "QPushButton { background-color: #95a5a6; color: white; font-size: 16px; font-weight: bold; border: none; border-radius: 8px; } QPushButton:hover { background-color: #7f8c8d; } QPushButton:pressed { background-color: #6c7b7d; }"}, "events": {"clicked": "resetCounter"}}, {"type": "QPushButton", "properties": {"text": "➕ Increment", "minimumHeight": 50, "styleSheet": "QPushButton { background-color: #27ae60; color: white; font-size: 16px; font-weight: bold; border: none; border-radius: 8px; } QPushButton:hover { background-color: #229954; } QPushButton:pressed { background-color: #1e8449; }"}, "events": {"clicked": "incrementCounter"}}]}, {"type": "QWidget", "layout": {"type": "HBoxLayout", "spacing": 15}, "children": [{"type": "QPushButton", "properties": {"text": "↶ Undo", "minimumHeight": 40, "styleSheet": "QPushButton { background-color: #f39c12; color: white; font-size: 14px; font-weight: bold; border: none; border-radius: 6px; } QPushButton:hover { background-color: #e67e22; } QPushButton:pressed { background-color: #d35400; } QPushButton:disabled { background-color: #bdc3c7; }"}, "events": {"clicked": "undoCounter"}}, {"type": "QPushButton", "properties": {"text": "↷ Redo", "minimumHeight": 40, "styleSheet": "QPushButton { background-color: #9b59b6; color: white; font-size: 14px; font-weight: bold; border: none; border-radius: 6px; } QPushButton:hover { background-color: #8e44ad; } QPushButton:pressed { background-color: #7d3c98; } QPushButton:disabled { background-color: #bdc3c7; }"}, "events": {"clicked": "redoCounter"}}]}, {"type": "QLabel", "properties": {"text": "Use the buttons above to modify the counter.\nUndo/Redo functionality is available!", "alignment": 4, "styleSheet": "QLabel { font-size: 12px; color: #7f8c8d; margin-top: 10px; }"}}]}