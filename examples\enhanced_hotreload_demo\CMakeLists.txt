cmake_minimum_required(VERSION 3.16)

project(EnhancedHotReloadDemo VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/src)

# Create executable
add_executable(EnhancedHotReloadDemo
    main.cpp
)

# Link libraries
target_link_libraries(EnhancedHotReloadDemo
    Qt6::Core
    Qt6::Widgets
    DeclarativeUI  # Main library with enhanced hot-reload features
)

# Set target properties
set_target_properties(EnhancedHotReloadDemo PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Copy demo configuration files
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/demo_config.json
    ${CMAKE_CURRENT_BINARY_DIR}/demo_config.json
    COPYONLY
)

# Install target
install(TARGETS EnhancedHotReloadDemo
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
