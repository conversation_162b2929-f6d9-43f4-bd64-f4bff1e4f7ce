# Pre-commit hooks configuration for DeclarativeUI Framework
# See https://pre-commit.com for more information

repos:
  # =============================================================================
  # General Code Quality
  # =============================================================================
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # File formatting
      - id: trailing-whitespace
        exclude: '\.md$'  # Preserve trailing spaces in markdown for line breaks
      - id: end-of-file-fixer
        exclude: '\.(qm|rcc|moc_.*|ui_.*|qrc_.*)$'  # Exclude generated files
      - id: check-yaml
        args: ['--unsafe']  # Allow custom YAML tags
      - id: check-json
      - id: check-xml
      - id: check-toml
      
      # File integrity
      - id: check-added-large-files
        args: ['--maxkb=1024']  # Limit files to 1MB
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: destroyed-symlinks
      
      # Code quality
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: fix-byte-order-marker
      
      # Prevent commits to main
      - id: no-commit-to-branch
        args: ['--branch', 'main', '--branch', 'master']

  # =============================================================================
  # C++ Code Formatting
  # =============================================================================
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v17.0.6
    hooks:
      - id: clang-format
        files: '\.(c|cc|cpp|cxx|c\+\+|h|hh|hpp|hxx|h\+\+|inl|ipp|tpp)$'
        args: ['-i']  # In-place formatting

  # =============================================================================
  # CMake Formatting
  # =============================================================================
  - repo: https://github.com/cheshirekow/cmake-format-precommit
    rev: v0.6.13
    hooks:
      - id: cmake-format
        files: '(CMakeLists\.txt|\.cmake)$'
        args: ['-i']

  # =============================================================================
  # Documentation and Markdown
  # =============================================================================
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.38.0
    hooks:
      - id: markdownlint
        args: ['--fix']
        exclude: 'CHANGELOG\.md$'  # Exclude changelog from strict formatting

  # =============================================================================
  # Git Hooks
  # =============================================================================
  - repo: https://github.com/jorisroovers/gitlint
    rev: v0.19.1
    hooks:
      - id: gitlint
        args: ['--contrib=contrib-title-conventional-commits']

  # =============================================================================
  # Security and Secrets
  # =============================================================================
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: '\.lock$|package-lock\.json$'

  # =============================================================================
  # File Permissions and Line Endings
  # =============================================================================
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.4
    hooks:
      # Ensure shell scripts are executable
      - id: insert-license
        files: '\.(sh|bash)$'
        args: ['--license-filepath', 'scripts/license-header.txt']
        exclude: '^\.git/'
      
      # Remove tabs (except in Makefiles)
      - id: remove-tabs
        exclude: '(Makefile|\.mk|\.mak)$'

  # =============================================================================
  # Language-Specific Hooks
  # =============================================================================
  
  # Python (for build scripts)
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        files: '\.py$'
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        files: '\.py$'
        args: ['--profile', 'black']

  # Shell scripts
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        files: '\.(sh|bash)$'

  # =============================================================================
  # Custom Local Hooks
  # =============================================================================
  - repo: local
    hooks:
      # Check for Qt-specific issues
      - id: qt-includes-check
        name: Check Qt includes
        entry: scripts/check-qt-includes.sh
        language: script
        files: '\.(cpp|hpp|h)$'
        pass_filenames: true
      
      # Validate CMake structure
      - id: cmake-structure-check
        name: Validate CMake structure
        entry: scripts/check-cmake-structure.sh
        language: script
        files: '(CMakeLists\.txt|\.cmake)$'
        pass_filenames: false
      
      # Check for proper namespace usage
      - id: namespace-check
        name: Check namespace usage
        entry: scripts/check-namespaces.sh
        language: script
        files: '\.(cpp|hpp|h)$'
        pass_filenames: true
      
      # Validate JSON UI definitions
      - id: json-ui-validation
        name: Validate JSON UI files
        entry: scripts/validate-json-ui.py
        language: python3
        files: '\.json$'
        exclude: '^(package|tsconfig|\.vscode)'
      
      # Check for memory management best practices
      - id: memory-management-check
        name: Check memory management
        entry: scripts/check-memory-management.sh
        language: script
        files: '\.(cpp|hpp|h)$'
        pass_filenames: true
      
      # Validate example code
      - id: example-validation
        name: Validate example code
        entry: scripts/validate-examples.sh
        language: script
        files: '^examples/.*\.(cpp|hpp|h)$'
        pass_filenames: true

# =============================================================================
# Configuration
# =============================================================================
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: '3.0.0'

# Exclude patterns
exclude: |
  (?x)^(
    build/.*|
    \.cache/.*|
    \.git/.*|
    Testing/Temporary/.*|
    .*\.qm|
    .*\.rcc|
    moc_.*\.cpp|
    ui_.*\.h|
    qrc_.*\.cpp
  )$
