#include "ThemeManager.hpp"
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QRegularExpression>
#include <cmath>

namespace DeclarativeUI::Theming {

/**
 * @brief Comprehensive theme validation system
 */
class ThemeValidator {
public:
    enum class ValidationLevel {
        Basic,      // Basic structure validation
        Standard,   // Standard compliance (WCAG A)
        Strict,     // Strict compliance (WCAG AA)
        Premium     // Premium compliance (WCAG AAA)
    };
    
    enum class ValidationCategory {
        Structure,
        Colors,
        Typography,
        Spacing,
        Accessibility,
        Performance,
        Consistency
    };
    
    struct ValidationIssue {
        ValidationCategory category;
        QString severity;       // "error", "warning", "info"
        QString message;
        QString suggestion;
        QString property;       // The property that has the issue
        QVariant currentValue;
        QVariant suggestedValue;
    };
    
    struct ValidationReport {
        bool isValid = true;
        QList<ValidationIssue> issues;
        double accessibilityScore = 0.0;
        double consistencyScore = 0.0;
        double overallScore = 0.0;
        
        int errorCount() const {
            return std::count_if(issues.begin(), issues.end(), 
                [](const ValidationIssue& issue) { return issue.severity == "error"; });
        }
        
        int warningCount() const {
            return std::count_if(issues.begin(), issues.end(), 
                [](const ValidationIssue& issue) { return issue.severity == "warning"; });
        }
        
        int infoCount() const {
            return std::count_if(issues.begin(), issues.end(), 
                [](const ValidationIssue& issue) { return issue.severity == "info"; });
        }
    };
    
    /**
     * @brief Validate a theme with specified validation level
     */
    static ValidationReport validateTheme(const ThemeManager::Theme& theme, 
                                        ValidationLevel level = ValidationLevel::Standard) {
        ValidationReport report;
        
        // Structure validation
        validateStructure(theme, report);
        
        // Color validation
        validateColors(theme, report, level);
        
        // Typography validation
        validateTypography(theme, report, level);
        
        // Spacing validation
        validateSpacing(theme, report);
        
        // Accessibility validation
        validateAccessibility(theme, report, level);
        
        // Performance validation
        validatePerformance(theme, report);
        
        // Consistency validation
        validateConsistency(theme, report);
        
        // Calculate scores
        calculateScores(report);
        
        // Determine overall validity
        report.isValid = (report.errorCount() == 0);
        
        return report;
    }
    
private:
    static void validateStructure(const ThemeManager::Theme& theme, ValidationReport& report) {
        // Check required fields
        if (theme.name.isEmpty()) {
            report.issues.append({
                ValidationCategory::Structure,
                "error",
                "Theme name is required",
                "Provide a descriptive name for the theme",
                "name",
                theme.name,
                "My Theme"
            });
        }
        
        if (theme.version.isEmpty()) {
            report.issues.append({
                ValidationCategory::Structure,
                "warning",
                "Theme version is recommended",
                "Add a version number for better theme management",
                "version",
                theme.version,
                "1.0"
            });
        }
        
        // Validate name format
        QRegularExpression nameRegex("^[A-Za-z0-9\\s\\-_]+$");
        if (!nameRegex.match(theme.name).hasMatch()) {
            report.issues.append({
                ValidationCategory::Structure,
                "warning",
                "Theme name contains invalid characters",
                "Use only letters, numbers, spaces, hyphens, and underscores",
                "name",
                theme.name,
                QVariant()
            });
        }
    }
    
    static void validateColors(const ThemeManager::Theme& theme, ValidationReport& report, 
                             ValidationLevel level) {
        const auto& colors = theme.colors;
        
        // Check if required colors are valid
        QList<QPair<QString, QColor>> requiredColors = {
            {"primary", colors.primary},
            {"background", colors.background},
            {"text_primary", colors.text_primary}
        };
        
        for (const auto& [name, color] : requiredColors) {
            if (!color.isValid()) {
                report.issues.append({
                    ValidationCategory::Colors,
                    "error",
                    QString("Required color '%1' is invalid").arg(name),
                    "Provide a valid color value",
                    name,
                    color.name(),
                    "#000000"
                });
            }
        }
        
        // Check contrast ratios based on validation level
        double minContrastRatio = 3.0; // WCAG A
        if (level == ValidationLevel::Strict) {
            minContrastRatio = 4.5; // WCAG AA
        } else if (level == ValidationLevel::Premium) {
            minContrastRatio = 7.0; // WCAG AAA
        }
        
        // Primary text contrast
        double primaryTextContrast = calculateContrastRatio(colors.text_primary, colors.background);
        if (primaryTextContrast < minContrastRatio) {
            report.issues.append({
                ValidationCategory::Accessibility,
                "error",
                QString("Primary text contrast ratio (%1) is below minimum (%2)")
                    .arg(primaryTextContrast, 0, 'f', 2).arg(minContrastRatio),
                "Increase contrast between text and background colors",
                "text_primary",
                colors.text_primary.name(),
                QVariant()
            });
        }
        
        // Secondary text contrast
        double secondaryTextContrast = calculateContrastRatio(colors.text_secondary, colors.background);
        if (secondaryTextContrast < (minContrastRatio * 0.75)) {
            report.issues.append({
                ValidationCategory::Accessibility,
                "warning",
                QString("Secondary text contrast ratio (%1) may be too low")
                    .arg(secondaryTextContrast, 0, 'f', 2),
                "Consider increasing contrast for better readability",
                "text_secondary",
                colors.text_secondary.name(),
                QVariant()
            });
        }
        
        // Check for color blindness accessibility
        validateColorBlindnessAccessibility(colors, report);
    }
    
    static void validateTypography(const ThemeManager::Theme& theme, ValidationReport& report, 
                                 ValidationLevel level) {
        const auto& typography = theme.typography;
        
        // Check if fonts are properly defined
        QList<QPair<QString, QFont>> fonts = {
            {"heading1", typography.heading1},
            {"body1", typography.body1},
            {"button", typography.button}
        };
        
        for (const auto& [name, font] : fonts) {
            if (font.family().isEmpty()) {
                report.issues.append({
                    ValidationCategory::Typography,
                    "error",
                    QString("Font family for '%1' is not specified").arg(name),
                    "Specify a font family for better typography control",
                    name + "_family",
                    font.family(),
                    "Arial"
                });
            }
            
            // Check font size
            if (font.pointSize() < 8) {
                report.issues.append({
                    ValidationCategory::Typography,
                    "warning",
                    QString("Font size for '%1' (%2pt) may be too small").arg(name).arg(font.pointSize()),
                    "Consider using a larger font size for better readability",
                    name + "_size",
                    font.pointSize(),
                    12
                });
            }
            
            if (font.pointSize() > 72) {
                report.issues.append({
                    ValidationCategory::Typography,
                    "warning",
                    QString("Font size for '%1' (%2pt) may be too large").arg(name).arg(font.pointSize()),
                    "Consider using a smaller font size",
                    name + "_size",
                    font.pointSize(),
                    24
                });
            }
        }
        
        // Check font hierarchy
        if (typography.heading1.pointSize() <= typography.body1.pointSize()) {
            report.issues.append({
                ValidationCategory::Typography,
                "warning",
                "Heading font size should be larger than body font size",
                "Establish a clear typographic hierarchy",
                "heading1_size",
                typography.heading1.pointSize(),
                typography.body1.pointSize() * 1.5
            });
        }
    }
    
    static void validateSpacing(const ThemeManager::Theme& theme, ValidationReport& report) {
        const auto& spacing = theme.spacing;
        
        // Check if spacing values are logical
        QList<int> spacingValues = {spacing.xs, spacing.sm, spacing.md, spacing.lg, spacing.xl, spacing.xxl};
        
        for (int i = 1; i < spacingValues.size(); ++i) {
            if (spacingValues[i] <= spacingValues[i-1]) {
                report.issues.append({
                    ValidationCategory::Spacing,
                    "warning",
                    "Spacing values should increase progressively",
                    "Ensure each spacing size is larger than the previous one",
                    "spacing",
                    QVariant(),
                    QVariant()
                });
                break;
            }
        }
        
        // Check for reasonable spacing values
        if (spacing.xs < 2 || spacing.xs > 8) {
            report.issues.append({
                ValidationCategory::Spacing,
                "info",
                QString("Extra small spacing (%1px) is outside typical range (2-8px)").arg(spacing.xs),
                "Consider using a value between 2-8px for extra small spacing",
                "spacing_xs",
                spacing.xs,
                4
            });
        }
    }
    
    static void validateAccessibility(const ThemeManager::Theme& theme, ValidationReport& report, 
                                    ValidationLevel level) {
        // Focus indicator validation
        if (theme.colors.focus == theme.colors.background) {
            report.issues.append({
                ValidationCategory::Accessibility,
                "error",
                "Focus indicator color is the same as background",
                "Use a contrasting color for focus indicators",
                "focus_color",
                theme.colors.focus.name(),
                theme.colors.primary.name()
            });
        }
        
        // Error color validation
        double errorContrast = calculateContrastRatio(theme.colors.error, theme.colors.background);
        if (errorContrast < 3.0) {
            report.issues.append({
                ValidationCategory::Accessibility,
                "error",
                "Error color has insufficient contrast with background",
                "Use a more contrasting error color",
                "error_color",
                theme.colors.error.name(),
                QVariant()
            });
        }
    }
    
    static void validatePerformance(const ThemeManager::Theme& theme, ValidationReport& report) {
        // Check for overly complex CSS
        if (theme.css_overrides.length() > 10000) {
            report.issues.append({
                ValidationCategory::Performance,
                "warning",
                "CSS overrides are very large and may impact performance",
                "Consider optimizing or splitting CSS overrides",
                "css_overrides",
                theme.css_overrides.length(),
                QVariant()
            });
        }
        
        // Check for too many custom properties
        if (theme.custom_properties.size() > 50) {
            report.issues.append({
                ValidationCategory::Performance,
                "info",
                "Large number of custom properties may impact performance",
                "Consider consolidating custom properties",
                "custom_properties",
                theme.custom_properties.size(),
                QVariant()
            });
        }
    }
    
    static void validateConsistency(const ThemeManager::Theme& theme, ValidationReport& report) {
        // Check color consistency
        QList<QColor> primaryColors = {
            theme.colors.primary,
            theme.colors.primary_light,
            theme.colors.primary_dark
        };
        
        // Verify that light/dark variants are actually lighter/darker
        if (theme.colors.primary_light.value() <= theme.colors.primary.value()) {
            report.issues.append({
                ValidationCategory::Consistency,
                "warning",
                "Primary light color is not lighter than primary color",
                "Ensure color variants follow their naming convention",
                "primary_light",
                theme.colors.primary_light.name(),
                QVariant()
            });
        }
        
        if (theme.colors.primary_dark.value() >= theme.colors.primary.value()) {
            report.issues.append({
                ValidationCategory::Consistency,
                "warning",
                "Primary dark color is not darker than primary color",
                "Ensure color variants follow their naming convention",
                "primary_dark",
                theme.colors.primary_dark.name(),
                QVariant()
            });
        }
    }
    
    static void validateColorBlindnessAccessibility(const ThemeManager::ColorPalette& colors, 
                                                  ValidationReport& report) {
        // Check if important information relies solely on color
        // This is a simplified check - in practice, you'd need more sophisticated analysis
        
        if (colors.success.hue() == colors.error.hue()) {
            report.issues.append({
                ValidationCategory::Accessibility,
                "warning",
                "Success and error colors have similar hues",
                "Use different hues to help color-blind users distinguish between states",
                "success_error_colors",
                QVariant(),
                QVariant()
            });
        }
    }
    
    static double calculateContrastRatio(const QColor& color1, const QColor& color2) {
        auto luminance = [](const QColor& color) {
            auto toLinear = [](double c) {
                return (c <= 0.03928) ? c / 12.92 : std::pow((c + 0.055) / 1.055, 2.4);
            };
            
            double r = toLinear(color.redF());
            double g = toLinear(color.greenF());
            double b = toLinear(color.blueF());
            
            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        };
        
        double lum1 = luminance(color1);
        double lum2 = luminance(color2);
        
        double lighter = std::max(lum1, lum2);
        double darker = std::min(lum1, lum2);
        
        return (lighter + 0.05) / (darker + 0.05);
    }
    
    static void calculateScores(ValidationReport& report) {
        int totalIssues = report.issues.size();
        int errorCount = report.errorCount();
        int warningCount = report.warningCount();
        
        // Calculate accessibility score (0-100)
        int accessibilityIssues = std::count_if(report.issues.begin(), report.issues.end(),
            [](const ValidationIssue& issue) { 
                return issue.category == ValidationCategory::Accessibility; 
            });
        report.accessibilityScore = std::max(0.0, 100.0 - (accessibilityIssues * 20.0));
        
        // Calculate consistency score (0-100)
        int consistencyIssues = std::count_if(report.issues.begin(), report.issues.end(),
            [](const ValidationIssue& issue) { 
                return issue.category == ValidationCategory::Consistency; 
            });
        report.consistencyScore = std::max(0.0, 100.0 - (consistencyIssues * 15.0));
        
        // Calculate overall score (0-100)
        double errorPenalty = errorCount * 25.0;
        double warningPenalty = warningCount * 10.0;
        report.overallScore = std::max(0.0, 100.0 - errorPenalty - warningPenalty);
    }
};

} // namespace DeclarativeUI::Theming
