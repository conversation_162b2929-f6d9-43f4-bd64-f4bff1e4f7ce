# **Command System Tests Configuration**
# Tests specific to the command system functionality

# **Core Command System Tests**
add_executable(CommandSystemTest test_command_system.cpp)
target_link_libraries(CommandSystemTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(BuiltinCommandsTest test_builtin_commands.cpp)
target_link_libraries(BuiltinCommandsTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# add_executable(CommandIntegrationTest test_command_integration.cpp)
# target_link_libraries(CommandIntegrationTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
# )

# **Command System Specific Tests** (temporarily disabled due to API mismatches)
# add_executable(CommandBindingTest test_command_binding.cpp)
# target_link_libraries(CommandBindingTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
# )

# add_executable(CommandBuilderTest test_command_builder.cpp)
# target_link_libraries(CommandBuilderTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
# )

# add_executable(CommandCoreTest test_command_core.cpp)
# target_link_libraries(CommandCoreTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
# )

# add_executable(CommandEventsTest test_command_events.cpp)
# target_link_libraries(CommandEventsTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
# )

# add_executable(CommandStateTest test_command_state.cpp)
# target_link_libraries(CommandStateTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
# )

add_executable(WidgetMapperTest test_widget_mapper.cpp)
target_link_libraries(WidgetMapperTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Adapter Tests**
add_executable(ComponentSystemAdapterTest test_component_system_adapter.cpp)
target_link_libraries(ComponentSystemAdapterTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(IntegrationManagerTest test_integration_manager.cpp)
target_link_libraries(IntegrationManagerTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(JSONCommandLoaderTest test_json_command_loader.cpp)
target_link_libraries(JSONCommandLoaderTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(MVCIntegrationTest test_mvc_integration.cpp)
target_link_libraries(MVCIntegrationTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(StateManagerAdapterTest test_state_manager_adapter.cpp)
target_link_libraries(StateManagerAdapterTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(UIElementAdapterTest test_uielement_adapter.cpp)
target_link_libraries(UIElementAdapterTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Command Performance Tests** (temporarily disabled due to API mismatches)
# add_executable(CommandPerformanceTest test_command_performance.cpp)
# target_link_libraries(CommandPerformanceTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
#     Qt6::Concurrent
# )

# **Set output directory for command tests**
set_target_properties(
    CommandSystemTest
    BuiltinCommandsTest
    # CommandIntegrationTest
    # CommandBindingTest
    # CommandBuilderTest
    # CommandCoreTest
    # CommandEventsTest
    # CommandStateTest
    WidgetMapperTest
    ComponentSystemAdapterTest
    IntegrationManagerTest
    JSONCommandLoaderTest
    MVCIntegrationTest
    StateManagerAdapterTest
    UIElementAdapterTest
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/command
)

# **Make tests depend on resources**
add_dependencies(CommandSystemTest CopyTestResources)
add_dependencies(BuiltinCommandsTest CopyTestResources)
# add_dependencies(CommandIntegrationTest CopyTestResources)
# add_dependencies(CommandBindingTest CopyTestResources)
# add_dependencies(CommandBuilderTest CopyTestResources)
# add_dependencies(CommandCoreTest CopyTestResources)
# add_dependencies(CommandEventsTest CopyTestResources)
# add_dependencies(CommandStateTest CopyTestResources)
add_dependencies(WidgetMapperTest CopyTestResources)
add_dependencies(ComponentSystemAdapterTest CopyTestResources)
add_dependencies(IntegrationManagerTest CopyTestResources)
add_dependencies(JSONCommandLoaderTest CopyTestResources)
add_dependencies(MVCIntegrationTest CopyTestResources)
add_dependencies(StateManagerAdapterTest CopyTestResources)
add_dependencies(UIElementAdapterTest CopyTestResources)

# **Register tests with CTest**
add_test(NAME CommandSystemTest COMMAND CommandSystemTest)
add_test(NAME BuiltinCommandsTest COMMAND BuiltinCommandsTest)
# add_test(NAME CommandIntegrationTest COMMAND CommandIntegrationTest)
# add_test(NAME CommandBindingTest COMMAND CommandBindingTest)
# add_test(NAME CommandBuilderTest COMMAND CommandBuilderTest)
# add_test(NAME CommandCoreTest COMMAND CommandCoreTest)
# add_test(NAME CommandEventsTest COMMAND CommandEventsTest)
# add_test(NAME CommandStateTest COMMAND CommandStateTest)
add_test(NAME WidgetMapperTest COMMAND WidgetMapperTest)
add_test(NAME ComponentSystemAdapterTest COMMAND ComponentSystemAdapterTest)
add_test(NAME IntegrationManagerTest COMMAND IntegrationManagerTest)
add_test(NAME JSONCommandLoaderTest COMMAND JSONCommandLoaderTest)
add_test(NAME MVCIntegrationTest COMMAND MVCIntegrationTest)
add_test(NAME StateManagerAdapterTest COMMAND StateManagerAdapterTest)
add_test(NAME UIElementAdapterTest COMMAND UIElementAdapterTest)
