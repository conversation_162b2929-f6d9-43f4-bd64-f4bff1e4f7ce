#include "DocumentationGenerator.hpp"
#include <QApplication>
#include <QDebug>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QDirIterator>
#include <QCryptographicHash>
#include <QUuid>
#include <QMimeDatabase>
#include <QMimeType>
#include <algorithm>
#include <numeric>
#include <chrono>

namespace DeclarativeUI::Documentation {

// **DocumentationGenerator Implementation**

DocumentationGenerator::DocumentationGenerator(QObject* parent)
    : QObject(parent)
{
    initializeGenerator();
}

DocumentationGenerator::~DocumentationGenerator() {
    stopLiveDocumentation();
}

void DocumentationGenerator::initializeGenerator() {
    // Initialize regular expressions for parsing
    class_regex_.setPattern(R"(class\s+(\w+)(?:\s*:\s*(?:public|private|protected)\s+[\w:]+)?\s*\{)");
    function_regex_.setPattern(R"((?:virtual\s+)?(?:static\s+)?(?:inline\s+)?(\w+(?:\s*\*)*)\s+(\w+)\s*\([^)]*\)(?:\s*const)?(?:\s*override)?(?:\s*=\s*0)?;?)");
    enum_regex_.setPattern(R"(enum\s+(?:class\s+)?(\w+)(?:\s*:\s*\w+)?\s*\{)");
    comment_regex_.setPattern(R"(/\*\*(.*?)\*/)");
    doxygen_regex_.setPattern(R"(@(\w+)\s+([^\n@]*))");
    include_regex_.setPattern(R"(#include\s*[<"]([^>"]+)[>"])");
    namespace_regex_.setPattern(R"(namespace\s+([\w:]+)\s*\{)");
    
    // Set default configuration
    config_.project_name = "DeclarativeUI Framework";
    config_.output_directory = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/DeclarativeUI/docs";
    
    // Initialize file watcher
    file_watcher_ = std::make_unique<QFileSystemWatcher>(this);
    connect(file_watcher_.get(), &QFileSystemWatcher::fileChanged,
            this, &DocumentationGenerator::onSourceFileChanged);
    
    // Initialize update timer
    update_timer_ = std::make_unique<QTimer>(this);
    update_timer_->setSingleShot(true);
    update_timer_->setInterval(1000); // 1 second delay for batching updates
    connect(update_timer_.get(), &QTimer::timeout,
            this, &DocumentationGenerator::updateIncrementalDocumentation);
    
    qDebug() << "Documentation Generator initialized";
}

void DocumentationGenerator::setConfiguration(const GeneratorConfig& config) {
    config_ = config;
    emit configurationChanged();
    qDebug() << "Documentation Generator configuration updated";
}

void DocumentationGenerator::loadConfigurationFromFile(const QString& config_file) {
    QFile file(config_file);
    if (!file.open(QIODevice::ReadOnly)) {
        emit errorOccurred("Failed to open configuration file: " + config_file);
        return;
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        emit errorOccurred("Failed to parse configuration file: " + error.errorString());
        return;
    }
    
    QJsonObject config_obj = doc.object();
    
    // Load configuration from JSON
    config_.project_name = config_obj["project_name"].toString(config_.project_name);
    config_.project_version = config_obj["project_version"].toString(config_.project_version);
    config_.project_description = config_obj["project_description"].toString(config_.project_description);
    config_.output_directory = config_obj["output_directory"].toString(config_.output_directory);
    
    // Load arrays
    if (config_obj.contains("source_directories")) {
        config_.source_directories.clear();
        for (const auto& value : config_obj["source_directories"].toArray()) {
            config_.source_directories.append(value.toString());
        }
    }
    
    if (config_obj.contains("output_formats")) {
        config_.output_formats.clear();
        for (const auto& value : config_obj["output_formats"].toArray()) {
            config_.output_formats.append(value.toString());
        }
    }
    
    // Load boolean options
    config_.generate_component_gallery = config_obj["generate_component_gallery"].toBool(config_.generate_component_gallery);
    config_.generate_api_reference = config_obj["generate_api_reference"].toBool(config_.generate_api_reference);
    config_.parse_doxygen_comments = config_obj["parse_doxygen_comments"].toBool(config_.parse_doxygen_comments);
    
    emit configurationChanged();
    qDebug() << "Configuration loaded from:" << config_file;
}

void DocumentationGenerator::saveConfigurationToFile(const QString& config_file) const {
    QJsonObject config_obj;
    
    config_obj["project_name"] = config_.project_name;
    config_obj["project_version"] = config_.project_version;
    config_obj["project_description"] = config_.project_description;
    config_obj["output_directory"] = config_.output_directory;
    
    // Save arrays
    QJsonArray source_dirs;
    for (const QString& dir : config_.source_directories) {
        source_dirs.append(dir);
    }
    config_obj["source_directories"] = source_dirs;
    
    QJsonArray output_formats;
    for (const QString& format : config_.output_formats) {
        output_formats.append(format);
    }
    config_obj["output_formats"] = output_formats;
    
    // Save boolean options
    config_obj["generate_component_gallery"] = config_.generate_component_gallery;
    config_obj["generate_api_reference"] = config_.generate_api_reference;
    config_obj["parse_doxygen_comments"] = config_.parse_doxygen_comments;
    
    QJsonDocument doc(config_obj);
    
    QFile file(config_file);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        qDebug() << "Configuration saved to:" << config_file;
    } else {
        qWarning() << "Failed to save configuration to:" << config_file;
    }
}

bool DocumentationGenerator::generateDocumentation() {
    if (generation_in_progress_.load()) {
        emit warningIssued("Documentation generation already in progress");
        return false;
    }
    
    generation_in_progress_.store(true);
    current_progress_.store(0);
    auto start_time = std::chrono::high_resolution_clock::now();
    
    emit generationStarted();
    qDebug() << "Starting documentation generation...";
    
    try {
        // Setup output directories
        current_task_ = "Setting up output directories";
        emit generationProgress(5, current_task_);
        setupOutputDirectories();
        
        // Parse source files
        current_task_ = "Parsing source files";
        emit generationProgress(10, current_task_);
        documentation_items_ = parseSourceFiles();
        stats_.total_files_processed = static_cast<int>(documentation_items_.size());
        
        // Extract components
        current_task_ = "Extracting components";
        emit generationProgress(25, current_task_);
        components_ = extractComponents();
        stats_.components_extracted = static_cast<int>(components_.size());
        
        // Extract examples
        current_task_ = "Extracting examples";
        emit generationProgress(40, current_task_);
        examples_ = extractExamples();
        stats_.examples_found = static_cast<int>(examples_.size());
        
        // Generate API reference
        if (config_.generate_api_reference) {
            current_task_ = "Generating API reference";
            emit generationProgress(55, current_task_);
            generateAPIReference();
        }
        
        // Generate component gallery
        if (config_.generate_component_gallery) {
            current_task_ = "Generating component gallery";
            emit generationProgress(70, current_task_);
            generateComponentGallery();
        }
        
        // Generate examples documentation
        current_task_ = "Generating examples documentation";
        emit generationProgress(80, current_task_);
        generateExamples();
        
        // Generate search index
        if (config_.generate_search_index) {
            current_task_ = "Building search index";
            emit generationProgress(90, current_task_);
            buildSearchIndex();
        }
        
        // Generate cross-references
        if (config_.generate_cross_references) {
            current_task_ = "Generating cross-references";
            emit generationProgress(95, current_task_);
            generateCrossReferences();
        }
        
        // Copy static assets
        current_task_ = "Copying static assets";
        emit generationProgress(98, current_task_);
        copyStaticAssets();
        
        // Finalize
        current_task_ = "Finalizing documentation";
        emit generationProgress(100, current_task_);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        stats_.generation_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
        stats_.last_generation = QDateTime::currentDateTime();
        
        generation_in_progress_.store(false);
        emit generationFinished(true);
        
        qDebug() << "Documentation generation completed successfully in" << stats_.generation_time_ms << "ms";
        qDebug() << "Generated documentation for:" << stats_.classes_documented << "classes," 
                 << stats_.functions_documented << "functions," << stats_.components_extracted << "components";
        
        return true;
        
    } catch (const std::exception& e) {
        generation_in_progress_.store(false);
        QString error_msg = QString("Documentation generation failed: %1").arg(e.what());
        emit errorOccurred(error_msg);
        emit generationFinished(false);
        qCritical() << error_msg;
        return false;
    }
}

void DocumentationGenerator::setupOutputDirectories() {
    QDir output_dir(config_.output_directory);
    if (!output_dir.exists()) {
        if (!output_dir.mkpath(".")) {
            throw std::runtime_error("Failed to create output directory: " + config_.output_directory.toStdString());
        }
    }
    
    // Create subdirectories
    QStringList subdirs = {"api", "components", "examples", "assets", "search"};
    for (const QString& subdir : subdirs) {
        QDir sub_dir(output_dir.absoluteFilePath(subdir));
        if (!sub_dir.exists()) {
            sub_dir.mkpath(".");
        }
    }
    
    qDebug() << "Output directories created at:" << config_.output_directory;
}

void DocumentationGenerator::copyStaticAssets() {
    QString assets_dir = config_.output_directory + "/assets";
    QDir assets(assets_dir);
    
    // Create CSS file
    QString css_content = R"(
/* DeclarativeUI Documentation Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 0;
}

.nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: #495057;
    font-weight: 500;
}

.nav a:hover {
    color: #007bff;
}

.content {
    padding: 2rem 0;
}

.api-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.api-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.api-body {
    padding: 1rem;
}

.code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.component-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.component-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.component-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.component-preview {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.component-info {
    padding: 1rem;
}

.search-box {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

.footer {
    background: #343a40;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
}
)";
    
    QFile css_file(assets_dir + "/style.css");
    if (css_file.open(QIODevice::WriteOnly)) {
        css_file.write(css_content.toUtf8());
    }
    
    // Create JavaScript file
    QString js_content = R"(
// DeclarativeUI Documentation JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize search functionality
    const searchBox = document.getElementById('search-box');
    if (searchBox) {
        searchBox.addEventListener('input', function(e) {
            performSearch(e.target.value);
        });
    }
    
    // Initialize component demos
    initializeComponentDemos();
    
    // Initialize syntax highlighting
    initializeSyntaxHighlighting();
});

function performSearch(query) {
    if (query.length < 2) {
        clearSearchResults();
        return;
    }
    
    // Perform search (this would connect to the search index)
    fetch(`/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(results => displaySearchResults(results))
        .catch(error => console.error('Search error:', error));
}

function displaySearchResults(results) {
    const resultsContainer = document.getElementById('search-results');
    if (!resultsContainer) return;
    
    resultsContainer.innerHTML = '';
    
    results.forEach(result => {
        const resultElement = document.createElement('div');
        resultElement.className = 'search-result';
        resultElement.innerHTML = `
            <h4><a href="${result.url}">${result.title}</a></h4>
            <p>${result.description}</p>
            <small>${result.type} - ${result.file}</small>
        `;
        resultsContainer.appendChild(resultElement);
    });
}

function clearSearchResults() {
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
}

function initializeComponentDemos() {
    const demoContainers = document.querySelectorAll('.component-demo');
    demoContainers.forEach(container => {
        const componentType = container.dataset.component;
        if (componentType) {
            loadComponentDemo(container, componentType);
        }
    });
}

function loadComponentDemo(container, componentType) {
    // This would load and render live component demos
    container.innerHTML = `<div class="demo-placeholder">Live demo for ${componentType}</div>`;
}

function initializeSyntaxHighlighting() {
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
        // Basic syntax highlighting (in a real implementation, use a library like Prism.js)
        highlightCppSyntax(block);
    });
}

function highlightCppSyntax(block) {
    let content = block.innerHTML;
    
    // Highlight keywords
    const keywords = ['class', 'struct', 'enum', 'namespace', 'public', 'private', 'protected', 
                     'virtual', 'override', 'const', 'static', 'inline', 'template', 'typename'];
    
    keywords.forEach(keyword => {
        const regex = new RegExp('\\\\b' + keyword + '\\\\b', 'g');
        content = content.replace(regex, '<span class="keyword">' + keyword + '</span>');
    });

    // Highlight strings
    content = content.replace(/"([^"]*)"/g, '<span class="string">"$1"</span>');

    // Highlight comments
    content = content.replace(/\\/\\/([^\\n]*)/g, '<span class="comment">//$1</span>');
    content = content.replace(/\\/\\*([^*]|\\*(?!\\/))*\\*\\//g, '<span class="comment">$&</span>');

    block.innerHTML = content;
}
)";
    
    QFile js_file(assets_dir + "/script.js");
    if (js_file.open(QIODevice::WriteOnly)) {
        js_file.write(js_content.toUtf8());
    }
    
    qDebug() << "Static assets copied to:" << assets_dir;
}

std::vector<DocumentationGenerator::DocumentationItem> DocumentationGenerator::parseSourceFiles() {
    std::vector<DocumentationItem> items;
    QStringList source_files = findSourceFiles();
    
    int processed_count = 0;
    for (const QString& file_path : source_files) {
        emit fileProcessed(file_path);
        
        QString content = readFileContent(file_path);
        if (content.isEmpty()) {
            continue;
        }
        
        // Parse classes
        QRegularExpressionMatchIterator class_matches = class_regex_.globalMatch(content);
        while (class_matches.hasNext()) {
            QRegularExpressionMatch match = class_matches.next();
            DocumentationItem item = parseClass(file_path, match.captured(0));
            if (!item.name.isEmpty()) {
                items.push_back(item);
                stats_.classes_documented++;
            }
        }
        
        // Parse functions
        QRegularExpressionMatchIterator function_matches = function_regex_.globalMatch(content);
        while (function_matches.hasNext()) {
            QRegularExpressionMatch match = function_matches.next();
            DocumentationItem item = parseFunction(file_path, match.captured(0));
            if (!item.name.isEmpty()) {
                items.push_back(item);
                stats_.functions_documented++;
            }
        }
        
        // Parse enums
        QRegularExpressionMatchIterator enum_matches = enum_regex_.globalMatch(content);
        while (enum_matches.hasNext()) {
            QRegularExpressionMatch match = enum_matches.next();
            DocumentationItem item = parseEnum(file_path, match.captured(0));
            if (!item.name.isEmpty()) {
                items.push_back(item);
            }
        }
        
        processed_count++;
        int progress = 10 + (processed_count * 15 / source_files.size()); // 10-25% range
        emit generationProgress(progress, QString("Processed %1/%2 files").arg(processed_count).arg(source_files.size()));
    }
    
    qDebug() << "Parsed" << items.size() << "documentation items from" << source_files.size() << "source files";
    return items;
}

QStringList DocumentationGenerator::findSourceFiles() {
    QStringList files;
    
    for (const QString& source_dir : config_.source_directories) {
        QDirIterator iterator(source_dir, config_.include_patterns, QDir::Files, QDirIterator::Subdirectories);
        
        while (iterator.hasNext()) {
            QString file_path = iterator.next();
            
            // Check exclude patterns
            bool excluded = false;
            for (const QString& exclude_pattern : config_.exclude_patterns) {
                QRegularExpression exclude_regex(QRegularExpression::wildcardToRegularExpression(exclude_pattern));
                if (exclude_regex.match(file_path).hasMatch()) {
                    excluded = true;
                    break;
                }
            }
            
            if (!excluded) {
                files.append(file_path);
            }
        }
    }
    
    qDebug() << "Found" << files.size() << "source files to process";
    return files;
}

QString DocumentationGenerator::readFileContent(const QString& file_path) {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emit warningIssued("Failed to read file: " + file_path);
        return QString();
    }
    
    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    return stream.readAll();
}

bool DocumentationGenerator::writeFileContent(const QString& file_path, const QString& content) {
    QFileInfo file_info(file_path);
    QDir dir = file_info.dir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    QFile file(file_path);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit errorOccurred("Failed to write file: " + file_path);
        return false;
    }
    
    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    stream << content;
    
    return true;
}

void DocumentationGenerator::onSourceFileChanged(const QString& file_path) {
    if (!incremental_updates_enabled_.load()) {
        return;
    }
    
    qDebug() << "Source file changed:" << file_path;
    
    // Restart the update timer to batch multiple changes
    update_timer_->start();
}

void DocumentationGenerator::onConfigurationChanged() {
    qDebug() << "Documentation generator configuration changed";
}

void DocumentationGenerator::updateIncrementalDocumentation() {
    if (generation_in_progress_.load()) {
        // Reschedule if generation is in progress
        update_timer_->start();
        return;
    }
    
    qDebug() << "Performing incremental documentation update";
    
    // For now, regenerate everything (in a real implementation, this would be more selective)
    generateDocumentation();
}

DocumentationGenerator::DocumentationItem DocumentationGenerator::parseClass(const QString& file_path, const QString& class_content) {
    DocumentationItem item;
    item.type = DocumentationItem::CLASS;
    item.file_path = file_path;

    // Extract class name
    QRegularExpressionMatch match = class_regex_.match(class_content);
    if (match.hasMatch()) {
        item.name = match.captured(1);
        item.qualified_name = item.name; // TODO: Add namespace resolution
    }

    // Look for documentation comment before the class
    QString file_content = readFileContent(file_path);
    int class_pos = file_content.indexOf(class_content);
    if (class_pos > 0) {
        QString before_class = file_content.left(class_pos);
        QRegularExpressionMatch doc_match = comment_regex_.match(before_class);
        if (doc_match.hasMatch()) {
            QString doc_comment = doc_match.captured(1);
            item.detailed_description = parseDoxygenComment(doc_comment);

            // Extract brief description (first sentence)
            QStringList sentences = item.detailed_description.split(QRegularExpression(R"([.!?]\s+)"));
            if (!sentences.isEmpty()) {
                item.brief_description = sentences.first().trimmed();
            }
        }
    }

    // Extract metadata
    item.metadata = createMetadata(file_path);
    item.line_number = file_content.left(class_pos).count('\n') + 1;

    return item;
}

DocumentationGenerator::DocumentationItem DocumentationGenerator::parseFunction(const QString& file_path, const QString& function_content) {
    DocumentationItem item;
    item.type = DocumentationItem::FUNCTION;
    item.file_path = file_path;

    // Extract function details
    QRegularExpressionMatch match = function_regex_.match(function_content);
    if (match.hasMatch()) {
        item.return_type = match.captured(1);
        item.name = match.captured(2);
        item.qualified_name = item.name; // TODO: Add namespace/class resolution
    }

    // Extract parameters from function signature
    int param_start = function_content.indexOf('(');
    int param_end = function_content.lastIndexOf(')');
    if (param_start >= 0 && param_end > param_start) {
        QString params_str = function_content.mid(param_start + 1, param_end - param_start - 1);
        if (!params_str.trimmed().isEmpty()) {
            item.parameters = params_str.split(',');
            for (QString& param : item.parameters) {
                param = param.trimmed();
            }
        }
    }

    // Look for documentation comment
    QString file_content = readFileContent(file_path);
    int func_pos = file_content.indexOf(function_content);
    if (func_pos > 0) {
        QString before_func = file_content.left(func_pos);
        QRegularExpressionMatch doc_match = comment_regex_.match(before_func);
        if (doc_match.hasMatch()) {
            QString doc_comment = doc_match.captured(1);
            item.detailed_description = parseDoxygenComment(doc_comment);

            // Extract brief description
            QStringList sentences = item.detailed_description.split(QRegularExpression(R"([.!?]\s+)"));
            if (!sentences.isEmpty()) {
                item.brief_description = sentences.first().trimmed();
            }
        }
    }

    item.metadata = createMetadata(file_path);
    item.line_number = file_content.left(func_pos).count('\n') + 1;

    return item;
}

DocumentationGenerator::DocumentationItem DocumentationGenerator::parseEnum(const QString& file_path, const QString& enum_content) {
    DocumentationItem item;
    item.type = DocumentationItem::ENUM;
    item.file_path = file_path;

    // Extract enum name
    QRegularExpressionMatch match = enum_regex_.match(enum_content);
    if (match.hasMatch()) {
        item.name = match.captured(1);
        item.qualified_name = item.name; // TODO: Add namespace resolution
    }

    // Look for documentation comment
    QString file_content = readFileContent(file_path);
    int enum_pos = file_content.indexOf(enum_content);
    if (enum_pos > 0) {
        QString before_enum = file_content.left(enum_pos);
        QRegularExpressionMatch doc_match = comment_regex_.match(before_enum);
        if (doc_match.hasMatch()) {
            QString doc_comment = doc_match.captured(1);
            item.detailed_description = parseDoxygenComment(doc_comment);

            // Extract brief description
            QStringList sentences = item.detailed_description.split(QRegularExpression(R"([.!?]\s+)"));
            if (!sentences.isEmpty()) {
                item.brief_description = sentences.first().trimmed();
            }
        }
    }

    item.metadata = createMetadata(file_path);
    item.line_number = file_content.left(enum_pos).count('\n') + 1;

    return item;
}

QString DocumentationGenerator::parseDoxygenComment(const QString& comment) {
    QString processed_comment = comment;

    // Remove leading asterisks and whitespace
    QStringList lines = processed_comment.split('\n');
    QStringList cleaned_lines;

    for (QString line : lines) {
        line = line.trimmed();
        if (line.startsWith("*")) {
            line = line.mid(1).trimmed();
        }
        if (line.startsWith("/**") || line.startsWith("*/")) {
            continue;
        }
        cleaned_lines.append(line);
    }

    processed_comment = cleaned_lines.join('\n').trimmed();

    // Process Doxygen tags
    QRegularExpressionMatchIterator matches = doxygen_regex_.globalMatch(processed_comment);
    while (matches.hasNext()) {
        QRegularExpressionMatch match = matches.next();
        QString tag = match.captured(1);
        QString content = match.captured(2);

        // Convert common Doxygen tags to readable format
        if (tag == "brief") {
            processed_comment.replace(match.captured(0), content);
        } else if (tag == "param") {
            processed_comment.replace(match.captured(0), QString("Parameter: %1").arg(content));
        } else if (tag == "return" || tag == "returns") {
            processed_comment.replace(match.captured(0), QString("Returns: %1").arg(content));
        } else if (tag == "see") {
            processed_comment.replace(match.captured(0), QString("See also: %1").arg(content));
        } else if (tag == "since") {
            processed_comment.replace(match.captured(0), QString("Since: %1").arg(content));
        } else if (tag == "deprecated") {
            processed_comment.replace(match.captured(0), QString("Deprecated: %1").arg(content));
        }
    }

    return processed_comment;
}

QJsonObject DocumentationGenerator::createMetadata(const QString& file_path) {
    QJsonObject metadata;
    QFileInfo file_info(file_path);

    metadata["file_name"] = file_info.fileName();
    metadata["file_path"] = file_path;
    metadata["file_size"] = file_info.size();
    metadata["last_modified"] = file_info.lastModified().toString(Qt::ISODate);
    metadata["generated_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return metadata;
}

std::vector<DocumentationGenerator::ComponentInfo> DocumentationGenerator::extractComponents() {
    std::vector<ComponentInfo> components;

    // Look for component classes in the Components namespace
    for (const auto& item : documentation_items_) {
        if (item.type == DocumentationItem::CLASS &&
            (item.qualified_name.contains("Components::") || item.file_path.contains("/Components/"))) {

            ComponentInfo component;
            component.name = item.name;
            component.class_name = item.qualified_name;
            component.description = item.brief_description;
            component.header_file = item.file_path;

            // Determine category based on file path or class name
            if (item.file_path.contains("/Widgets/")) {
                component.category = "Widgets";
            } else if (item.file_path.contains("/Layout/")) {
                component.category = "Layout";
            } else if (item.file_path.contains("/Input/")) {
                component.category = "Input";
            } else if (item.file_path.contains("/Display/")) {
                component.category = "Display";
            } else {
                component.category = "General";
            }

            // TODO: Extract properties, signals, slots from Qt meta-object system
            // This would require runtime analysis or parsing of Q_PROPERTY macros

            components.push_back(component);
            emit componentExtracted(component);
        }
    }

    qDebug() << "Extracted" << components.size() << "components";
    return components;
}

std::vector<DocumentationGenerator::ExampleInfo> DocumentationGenerator::extractExamples() {
    std::vector<ExampleInfo> examples;

    for (const QString& example_dir : config_.example_directories) {
        QDirIterator iterator(example_dir, {"*.cpp", "*.hpp"}, QDir::Files, QDirIterator::Subdirectories);

        while (iterator.hasNext()) {
            QString file_path = iterator.next();
            QFileInfo file_info(file_path);

            // Skip if not a main file or example file
            if (!file_info.baseName().contains("example", Qt::CaseInsensitive) &&
                !file_info.baseName().contains("demo", Qt::CaseInsensitive) &&
                !file_path.contains("main.cpp")) {
                continue;
            }

            ExampleInfo example;
            example.file_path = file_path;
            example.main_file = file_path;
            example.name = file_info.baseName();

            // Extract title and description from file content
            QString content = readFileContent(file_path);
            example.description = extractExampleDescription(file_path);

            // Determine category from directory structure
            QDir example_base_dir(example_dir);
            QString relative_path = example_base_dir.relativeFilePath(file_path);
            QStringList path_parts = relative_path.split('/');
            if (path_parts.size() > 1) {
                example.category = path_parts.first();
            } else {
                example.category = "General";
            }

            // Check if it's an interactive example
            example.is_interactive = content.contains("QApplication") || content.contains("QWidget");
            example.is_live_demo = content.contains("HotReload") || content.contains("LiveDemo");

            examples.push_back(example);
            emit exampleFound(example);
        }
    }

    qDebug() << "Extracted" << examples.size() << "examples";
    return examples;
}

QString DocumentationGenerator::extractExampleDescription(const QString& file_path) {
    QString content = readFileContent(file_path);

    // Look for description in comments at the top of the file
    QRegularExpression desc_regex(R"(/\*\*(.*?)\*/)");
    QRegularExpressionMatch match = desc_regex.match(content);

    if (match.hasMatch()) {
        QString description = match.captured(1);
        description = description.replace(QRegularExpression(R"(\s*\*\s*)"), " ");
        description = description.trimmed();

        // Take first paragraph as description
        QStringList paragraphs = description.split(QRegularExpression(R"(\n\s*\n)"));
        if (!paragraphs.isEmpty()) {
            return paragraphs.first().trimmed();
        }
    }

    // Fallback: look for single-line comment with description
    QRegularExpression single_line_regex(R"(//\s*(.+))");
    QRegularExpressionMatchIterator matches = single_line_regex.globalMatch(content);

    QStringList comment_lines;
    while (matches.hasNext() && comment_lines.size() < 3) {
        QRegularExpressionMatch single_match = matches.next();
        QString line = single_match.captured(1).trimmed();
        if (!line.isEmpty() && !line.startsWith("#") && !line.startsWith("TODO")) {
            comment_lines.append(line);
        }
    }

    if (!comment_lines.isEmpty()) {
        return comment_lines.join(" ");
    }

    return QString("Example demonstrating %1").arg(QFileInfo(file_path).baseName());
}

bool DocumentationGenerator::generateAPIReference() {
    QString api_dir = config_.output_directory + "/api";

    // Generate main API index
    QString index_html = generateAPIIndexHTML();
    writeFileContent(api_dir + "/index.html", index_html);

    // Generate individual API pages
    for (const auto& item : documentation_items_) {
        QString item_html = generateAPIHTML(item);
        QString filename = sanitizeFileName(item.qualified_name) + ".html";
        writeFileContent(api_dir + "/" + filename, item_html);
    }

    // Generate API by category
    QMap<QString, std::vector<DocumentationItem>> categories;
    for (const auto& item : documentation_items_) {
        QString category = "General";
        if (item.file_path.contains("/Core/")) category = "Core";
        else if (item.file_path.contains("/Components/")) category = "Components";
        else if (item.file_path.contains("/Layout/")) category = "Layout";
        else if (item.file_path.contains("/Binding/")) category = "Binding";
        else if (item.file_path.contains("/State/")) category = "State";
        else if (item.file_path.contains("/Command/")) category = "Command";
        else if (item.file_path.contains("/HotReload/")) category = "HotReload";

        categories[category].push_back(item);
    }

    for (auto it = categories.begin(); it != categories.end(); ++it) {
        QString category_html = generateCategoryHTML(it.key(), it.value());
        writeFileContent(api_dir + "/" + sanitizeFileName(it.key()) + ".html", category_html);
    }

    qDebug() << "Generated API reference with" << documentation_items_.size() << "items";
    return true;
}

bool DocumentationGenerator::generateComponentGallery() {
    ComponentGalleryGenerator gallery_generator(this);

    ComponentGalleryGenerator::GalleryConfig gallery_config;
    gallery_config.output_directory = config_.output_directory + "/components";
    gallery_config.generate_live_demos = config_.enable_live_examples;
    gallery_config.theme = config_.theme;

    gallery_generator.setConfiguration(gallery_config);
    return gallery_generator.generateGallery(components_);
}

bool DocumentationGenerator::generateExamples() {
    ExampleDocumentationGenerator example_generator(this);
    return example_generator.generateExampleDocumentation(examples_);
}

QString DocumentationGenerator::generateAPIIndexHTML() {
    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" + config_.project_name + R"( - API Reference</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>)" + config_.project_name + R"( API Reference</h1>
            <p>)" + config_.project_description + R"(</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="index.html">API Reference</a></li>
                <li><a href="../components/index.html">Components</a></li>
                <li><a href="../examples/index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="search-section">
            <input type="text" id="search-box" class="search-box" placeholder="Search API...">
            <div id="search-results"></div>
        </div>

        <h2>API Categories</h2>
        <div class="api-categories">)";

    // Add categories
    QStringList categories = {"Core", "Components", "Layout", "Binding", "State", "Command", "HotReload"};
    for (const QString& category : categories) {
        int count = std::count_if(documentation_items_.begin(), documentation_items_.end(),
                                  [&category](const DocumentationItem& item) {
                                      return item.file_path.contains("/" + category + "/");
                                  });

        if (count > 0) {
            html += QString(R"(
            <div class="category-card">
                <h3><a href="%1.html">%2</a></h3>
                <p>%3 items</p>
            </div>)").arg(sanitizeFileName(category), category, QString::number(count));
        }
    }

    html += R"(
        </div>

        <h2>All API Items</h2>
        <div class="api-list">)";

    // Add all items
    for (const auto& item : documentation_items_) {
        QString type_name;
        switch (item.type) {
            case DocumentationGenerator::DocumentationItem::CLASS: type_name = "Class"; break;
            case DocumentationGenerator::DocumentationItem::STRUCT: type_name = "Struct"; break;
            case DocumentationGenerator::DocumentationItem::ENUM: type_name = "Enum"; break;
            case DocumentationGenerator::DocumentationItem::FUNCTION: type_name = "Function"; break;
            case DocumentationGenerator::DocumentationItem::METHOD: type_name = "Method"; break;
            default: type_name = "Item"; break;
        }

        html += QString(R"(
            <div class="api-item">
                <div class="api-header">
                    <h4><a href="%1.html">%2</a></h4>
                    <span class="api-type">%3</span>
                </div>
                <div class="api-body">
                    <p>%4</p>
                    <small>%5</small>
                </div>
            </div>)").arg(sanitizeFileName(item.qualified_name),
                         item.qualified_name,
                         type_name,
                         item.brief_description.isEmpty() ? "No description available" : item.brief_description,
                         QFileInfo(item.file_path).fileName());
    }

    html += R"(
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 )" + config_.project_name + R"(. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::DocumentationGenerator::generateAPIHTML(const DocumentationGenerator::DocumentationItem& item) {
    QString type_name;
    switch (item.type) {
        case DocumentationGenerator::DocumentationItem::CLASS: type_name = "Class"; break;
        case DocumentationGenerator::DocumentationItem::STRUCT: type_name = "Struct"; break;
        case DocumentationGenerator::DocumentationItem::ENUM: type_name = "Enum"; break;
        case DocumentationGenerator::DocumentationItem::FUNCTION: type_name = "Function"; break;
        case DocumentationGenerator::DocumentationItem::METHOD: type_name = "Method"; break;
        default: type_name = "Item"; break;
    }

    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" + item.qualified_name + " - " + config_.project_name + R"(</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>)" + item.qualified_name + R"(</h1>
            <p>)" + type_name + R"(</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="index.html">API Reference</a></li>
                <li><a href="../components/index.html">Components</a></li>
                <li><a href="../examples/index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="breadcrumbs">
            <a href="index.html">API Reference</a> &gt; )" + item.qualified_name + R"(
        </div>

        <div class="api-details">
            <h2>Description</h2>
            <p>)" + (item.brief_description.isEmpty() ? "No description available" : escapeHtml(item.brief_description)) + R"(</p>

            )" + (item.detailed_description.isEmpty() ? "" :
                  "<h3>Detailed Description</h3><div class=\"detailed-description\">" +
                  escapeHtml(item.detailed_description).replace("\n", "<br>") + "</div>") + R"(

            <h3>Declaration</h3>
            <div class="code-block">)";

    // Add declaration based on type
    if (item.type == DocumentationGenerator::DocumentationItem::CLASS) {
        html += "class " + escapeHtml(item.name);
        if (!item.base_classes.isEmpty()) {
            html += " : " + escapeHtml(item.base_classes.join(", "));
        }
    } else if (item.type == DocumentationGenerator::DocumentationItem::FUNCTION) {
        html += escapeHtml(item.return_type) + " " + escapeHtml(item.name) + "(";
        if (!item.parameters.isEmpty()) {
            html += escapeHtml(item.parameters.join(", "));
        }
        html += ")";
    } else if (item.type == DocumentationGenerator::DocumentationItem::ENUM) {
        html += "enum " + escapeHtml(item.name);
    }

    html += R"(</div>

            <h3>File Information</h3>
            <table class="info-table">
                <tr><td>Header File:</td><td>)" + escapeHtml(QFileInfo(item.file_path).fileName()) + R"(</td></tr>
                <tr><td>Line Number:</td><td>)" + QString::number(item.line_number) + R"(</td></tr>
                <tr><td>Namespace:</td><td>)" + escapeHtml(item.namespace_name) + R"(</td></tr>
            </table>)";

    if (!item.parameters.isEmpty()) {
        html += R"(
            <h3>Parameters</h3>
            <ul class="parameter-list">)";
        for (const QString& param : item.parameters) {
            html += "<li>" + escapeHtml(param) + "</li>";
        }
        html += "</ul>";
    }

    if (!item.see_also.isEmpty()) {
        html += R"(
            <h3>See Also</h3>
            <ul class="see-also-list">)";
        for (const QString& see : item.see_also) {
            html += "<li>" + escapeHtml(see) + "</li>";
        }
        html += "</ul>";
    }

    html += R"(
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 )" + config_.project_name + R"(. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::DocumentationGenerator::generateCategoryHTML(const QString& category, const std::vector<DocumentationGenerator::DocumentationItem>& items) {
    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" + category + " - " + config_.project_name + R"(</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>)" + category + R"( API</h1>
            <p>)" + QString::number(items.size()) + R"( items in this category</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="index.html">API Reference</a></li>
                <li><a href="../components/index.html">Components</a></li>
                <li><a href="../examples/index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="breadcrumbs">
            <a href="index.html">API Reference</a> &gt; )" + category + R"(
        </div>

        <div class="category-items">)";

    for (const auto& item : items) {
        QString type_name;
        switch (item.type) {
            case DocumentationGenerator::DocumentationItem::CLASS: type_name = "Class"; break;
            case DocumentationGenerator::DocumentationItem::STRUCT: type_name = "Struct"; break;
            case DocumentationGenerator::DocumentationItem::ENUM: type_name = "Enum"; break;
            case DocumentationGenerator::DocumentationItem::FUNCTION: type_name = "Function"; break;
            case DocumentationGenerator::DocumentationItem::METHOD: type_name = "Method"; break;
            default: type_name = "Item"; break;
        }

        html += QString(R"(
            <div class="api-item">
                <div class="api-header">
                    <h3><a href="%1.html">%2</a></h3>
                    <span class="api-type">%3</span>
                </div>
                <div class="api-body">
                    <p>%4</p>
                    <small>%5:%6</small>
                </div>
            </div>)").arg(sanitizeFileName(item.qualified_name),
                         item.qualified_name,
                         type_name,
                         item.brief_description.isEmpty() ? "No description available" : escapeHtml(item.brief_description),
                         QFileInfo(item.file_path).fileName(),
                         QString::number(item.line_number));
    }

    html += R"(
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 )" + config_.project_name + R"(. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::DocumentationGenerator::sanitizeFileName(const QString& name) {
    QString sanitized = name;
    sanitized.replace("::", "_");
    sanitized.replace("<", "_");
    sanitized.replace(">", "_");
    sanitized.replace(" ", "_");
    sanitized.replace("/", "_");
    sanitized.replace("\\", "_");
    return sanitized;
}

QString DeclarativeUI::Documentation::DocumentationGenerator::escapeHtml(const QString& text) {
    QString escaped = text;
    escaped.replace("&", "&amp;");
    escaped.replace("<", "&lt;");
    escaped.replace(">", "&gt;");
    escaped.replace("\"", "&quot;");
    escaped.replace("'", "&#39;");
    return escaped;
}

// **ComponentGalleryGenerator Implementation**

DeclarativeUI::Documentation::ComponentGalleryGenerator::ComponentGalleryGenerator(QObject* parent)
    : QObject(parent)
{
}

void DeclarativeUI::Documentation::ComponentGalleryGenerator::setConfiguration(const ComponentGalleryGenerator::GalleryConfig& config) {
    config_ = config;
}

bool DeclarativeUI::Documentation::ComponentGalleryGenerator::generateGallery(const std::vector<DocumentationGenerator::ComponentInfo>& components) {
    QDir output_dir(config_.output_directory);
    if (!output_dir.exists()) {
        output_dir.mkpath(".");
    }

    // Generate main gallery index
    QString index_html = generateGalleryIndexHTML(components);
    QFile index_file(output_dir.absoluteFilePath("index.html"));
    if (index_file.open(QIODevice::WriteOnly)) {
        index_file.write(index_html.toUtf8());
    }

    // Generate individual component pages
    for (const auto& component : components) {
        QString component_html = generateComponentHTML(component);
        QString filename = component.name.toLower().replace(" ", "-") + ".html";

        QFile component_file(output_dir.absoluteFilePath(filename));
        if (component_file.open(QIODevice::WriteOnly)) {
            component_file.write(component_html.toUtf8());
        }

        // Generate live demo if enabled
        if (config_.generate_live_demos) {
            QString demo_html = generateInteractiveDemo(component);
            QString demo_filename = component.name.toLower().replace(" ", "-") + "-demo.html";

            QFile demo_file(output_dir.absoluteFilePath(demo_filename));
            if (demo_file.open(QIODevice::WriteOnly)) {
                demo_file.write(demo_html.toUtf8());
            }
        }
    }

    qDebug() << "Generated component gallery with" << components.size() << "components";
    return true;
}

QString DeclarativeUI::Documentation::ComponentGalleryGenerator::generateGalleryIndexHTML(const std::vector<DocumentationGenerator::ComponentInfo>& components) {
    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" + config_.title + R"(</title>
    <link rel="stylesheet" href="../assets/style.css">
    <style>
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .component-card {
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
        }
        .component-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .component-preview {
            height: 200px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .component-info {
            padding: 1.5rem;
        }
        .component-category {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 0.75rem;
        }
        .component-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>)" + config_.title + R"(</h1>
            <p>)" + config_.description + R"(</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="../api/index.html">API Reference</a></li>
                <li><a href="index.html">Components</a></li>
                <li><a href="../examples/index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="search-section">
            <input type="text" id="component-search" class="search-box" placeholder="Search components...">
        </div>

        <div class="component-grid" id="component-grid">)";

    // Group components by category
    QMap<QString, std::vector<ComponentInfo>> categories;
    for (const auto& component : components) {
        categories[component.category].push_back(component);
    }

    // Generate component cards
    for (auto it = categories.begin(); it != categories.end(); ++it) {
        for (const auto& component : it.value()) {
            QString component_filename = component.name.toLower().replace(" ", "-") + ".html";
            QString demo_filename = component.name.toLower().replace(" ", "-") + "-demo.html";

            html += QString(R"(
            <div class="component-card" data-category="%1" data-name="%2">
                <div class="component-preview">
                    <div class="preview-placeholder">%3</div>
                </div>
                <div class="component-info">
                    <div class="component-category">%4</div>
                    <h3>%5</h3>
                    <p>%6</p>
                    <div class="component-actions">
                        <a href="%7" class="btn btn-primary">View Details</a>)").arg(
                component.category,
                component.name,
                component.name,
                component.category,
                component.name,
                component.description.isEmpty() ? "No description available" : component.description,
                component_filename);

            if (config_.generate_live_demos) {
                html += QString(R"(
                        <a href="%1" class="btn btn-secondary">Live Demo</a>)").arg(demo_filename);
            }

            html += R"(
                    </div>
                </div>
            </div>)";
        }
    }

    html += R"(
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 DeclarativeUI Framework. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
    <script>
        // Component search functionality
        document.getElementById('component-search').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            const cards = document.querySelectorAll('.component-card');

            cards.forEach(card => {
                const name = card.dataset.name.toLowerCase();
                const category = card.dataset.category.toLowerCase();
                const description = card.querySelector('p').textContent.toLowerCase();

                if (name.includes(query) || category.includes(query) || description.includes(query)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::ComponentGalleryGenerator::generateComponentHTML(const DocumentationGenerator::ComponentInfo& component) {
    return generateDemoHTML(component);
}

QString DeclarativeUI::Documentation::ComponentGalleryGenerator::generateDemoHTML(const DocumentationGenerator::ComponentInfo& component) {
    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" + component.name + R"( - Component Gallery</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>)" + component.name + R"(</h1>
            <p>)" + component.category + R"( Component</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="../api/index.html">API Reference</a></li>
                <li><a href="index.html">Components</a></li>
                <li><a href="../examples/index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="breadcrumbs">
            <a href="index.html">Components</a> &gt; )" + component.name + R"(
        </div>

        <div class="component-details">
            <h2>Description</h2>
            <p>)" + (component.description.isEmpty() ? "No description available" : component.description) + R"(</p>

            <h2>Usage Example</h2>
            <div class="code-block">)" + (component.usage_example.isEmpty() ?
                "auto " + component.name.toLower() + " = Components::" + component.name + "();" :
                component.usage_example) + R"(</div>

            <h2>Properties</h2>
            <ul class="property-list">)";

    if (component.properties.isEmpty()) {
        html += "<li>No properties documented</li>";
    } else {
        for (const QString& property : component.properties) {
            html += "<li>" + property + "</li>";
        }
    }

    html += R"(</ul>

            <h2>Signals</h2>
            <ul class="signal-list">)";

    if (component.signal_list.isEmpty()) {
        html += "<li>No signals documented</li>";
    } else {
        for (const QString& signal : component.signal_list) {
            html += "<li>" + signal + "</li>";
        }
    }

    html += R"(</ul>

            <h2>Header File</h2>
            <div class="code-block">#include ")" + component.header_file + R"("</div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 DeclarativeUI Framework. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::ComponentGalleryGenerator::generateInteractiveDemo(const DocumentationGenerator::ComponentInfo& component) {
    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" + component.name + R"( - Live Demo</title>
    <link rel="stylesheet" href="../assets/style.css">
    <style>
        .demo-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 2rem;
            margin: 2rem 0;
        }
        .demo-preview {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 2rem;
            background: white;
            min-height: 400px;
        }
        .demo-controls {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            background: #f8f9fa;
        }
        .control-group {
            margin-bottom: 1rem;
        }
        .control-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .control-group input,
        .control-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>)" + component.name + R"( - Live Demo</h1>
            <p>Interactive demonstration</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="../api/index.html">API Reference</a></li>
                <li><a href="index.html">Components</a></li>
                <li><a href="../examples/index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="breadcrumbs">
            <a href="index.html">Components</a> &gt;
            <a href=")" + component.name.toLower().replace(" ", "-") + R"(.html">)" + component.name + R"(</a> &gt;
            Live Demo
        </div>

        <div class="demo-container">
            <div class="demo-preview" id="demo-preview">
                <div class="demo-placeholder">
                    <h3>)" + component.name + R"( Demo</h3>
                    <p>This would show a live, interactive version of the )" + component.name + R"( component.</p>
                    <p>In a full implementation, this would be rendered using WebAssembly or a similar technology.</p>
                </div>
            </div>

            <div class="demo-controls">
                <h3>Customize</h3>
                )" + generateCustomizationPanel(component) + R"(
            </div>
        </div>

        <div class="demo-code">
            <h3>Generated Code</h3>
            <div class="code-block" id="generated-code">
auto )" + component.name.toLower() + R"( = Components::)" + component.name + R"(();
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 DeclarativeUI Framework. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
    <script>
        // Demo customization functionality
        function updateDemo() {
            const controls = document.querySelectorAll('.demo-controls input, .demo-controls select');
            let code = 'auto )" + component.name.toLower() + R"( = Components::)" + component.name + R"(()';

            controls.forEach(control => {
                if (control.value && control.value !== control.defaultValue) {
                    code += '\n    .' + control.name + '("' + control.value + '")';
                }
            });

            code += ';';
            document.getElementById('generated-code').textContent = code;
        }

        // Add event listeners to all controls
        document.addEventListener('DOMContentLoaded', function() {
            const controls = document.querySelectorAll('.demo-controls input, .demo-controls select');
            controls.forEach(control => {
                control.addEventListener('input', updateDemo);
                control.addEventListener('change', updateDemo);
            });
        });
    </script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::ComponentGalleryGenerator::generateCustomizationPanel(const DocumentationGenerator::ComponentInfo& component) {
    QString html;

    // Generate common controls based on component type
    if (component.name.contains("Button", Qt::CaseInsensitive)) {
        html += R"(
            <div class="control-group">
                <label for="text">Text:</label>
                <input type="text" id="text" name="text" value="Click Me" placeholder="Button text">
            </div>
            <div class="control-group">
                <label for="enabled">Enabled:</label>
                <select id="enabled" name="enabled">
                    <option value="true">True</option>
                    <option value="false">False</option>
                </select>
            </div>)";
    } else if (component.name.contains("Label", Qt::CaseInsensitive)) {
        html += R"(
            <div class="control-group">
                <label for="text">Text:</label>
                <input type="text" id="text" name="text" value="Sample Text" placeholder="Label text">
            </div>
            <div class="control-group">
                <label for="alignment">Alignment:</label>
                <select id="alignment" name="alignment">
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                </select>
            </div>)";
    } else if (component.name.contains("Input", Qt::CaseInsensitive) ||
               component.name.contains("LineEdit", Qt::CaseInsensitive)) {
        html += R"(
            <div class="control-group">
                <label for="placeholder">Placeholder:</label>
                <input type="text" id="placeholder" name="placeholder" value="Enter text..." placeholder="Placeholder text">
            </div>
            <div class="control-group">
                <label for="readonly">Read Only:</label>
                <select id="readonly" name="readonly">
                    <option value="false">False</option>
                    <option value="true">True</option>
                </select>
            </div>)";
    }

    // Add common styling controls
    html += R"(
        <div class="control-group">
            <label for="width">Width:</label>
            <input type="number" id="width" name="width" placeholder="Auto" min="50" max="800">
        </div>
        <div class="control-group">
            <label for="height">Height:</label>
            <input type="number" id="height" name="height" placeholder="Auto" min="20" max="600">
        </div>
        <div class="control-group">
            <label for="margin">Margin:</label>
            <input type="number" id="margin" name="margin" placeholder="0" min="0" max="50">
        </div>)";

    return html;
}

// **ExampleDocumentationGenerator Implementation**

DeclarativeUI::Documentation::ExampleDocumentationGenerator::ExampleDocumentationGenerator(QObject* parent)
    : QObject(parent)
{
}

bool DeclarativeUI::Documentation::ExampleDocumentationGenerator::generateExampleDocumentation(const std::vector<DocumentationGenerator::ExampleInfo>& examples) {
    QString examples_dir = "docs/generated/examples";
    QDir output_dir(examples_dir);
    if (!output_dir.exists()) {
        output_dir.mkpath(".");
    }

    // Generate examples index
    QString index_html = generateExampleIndex(examples);
    QFile index_file(output_dir.absoluteFilePath("index.html"));
    if (index_file.open(QIODevice::WriteOnly)) {
        index_file.write(index_html.toUtf8());
    }

    // Generate individual example pages
    for (const auto& example : examples) {
        QString example_html = generateExamplePage(example);
        QString filename = example.name.toLower().replace(" ", "-") + ".html";

        QFile example_file(output_dir.absoluteFilePath(filename));
        if (example_file.open(QIODevice::WriteOnly)) {
            example_file.write(example_html.toUtf8());
        }
    }

    qDebug() << "Generated example documentation for" << examples.size() << "examples";
    return true;
}

QString DeclarativeUI::Documentation::ExampleDocumentationGenerator::generateExampleIndex(const std::vector<DocumentationGenerator::ExampleInfo>& examples) {
    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examples - DeclarativeUI Framework</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>DeclarativeUI Examples</h1>
            <p>Learn by example with our comprehensive collection</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="../api/index.html">API Reference</a></li>
                <li><a href="../components/index.html">Components</a></li>
                <li><a href="index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="examples-grid">)";

    // Group examples by category
    QMap<QString, std::vector<DocumentationGenerator::ExampleInfo>> categories;
    for (const auto& example : examples) {
        categories[example.category].push_back(example);
    }

    for (auto it = categories.begin(); it != categories.end(); ++it) {
        html += QString(R"(
            <div class="category-section">
                <h2>%1</h2>
                <div class="example-cards">)").arg(it.key());

        for (const auto& example : it.value()) {
            QString filename = example.name.toLower().replace(" ", "-") + ".html";

            html += QString(R"(
                <div class="example-card">
                    <h3><a href="%1">%2</a></h3>
                    <p>%3</p>
                    <div class="example-meta">
                        <span class="example-type">%4</span>
                        %5
                    </div>
                </div>)").arg(
                filename,
                example.name,
                example.description,
                example.is_interactive ? "Interactive" : "Static",
                example.is_live_demo ? "<span class=\"live-demo\">Live Demo</span>" : "");
        }

        html += R"(
                </div>
            </div>)";
    }

    html += R"(
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 DeclarativeUI Framework. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::ExampleDocumentationGenerator::generateExamplePage(const DocumentationGenerator::ExampleInfo& example) {
    QString html = R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" + example.name + R"( - DeclarativeUI Examples</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>)" + example.name + R"(</h1>
            <p>)" + example.category + R"( Example</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="../api/index.html">API Reference</a></li>
                <li><a href="../components/index.html">Components</a></li>
                <li><a href="index.html">Examples</a></li>
            </ul>
        </div>
    </nav>

    <div class="container content">
        <div class="breadcrumbs">
            <a href="index.html">Examples</a> &gt; )" + example.name + R"(
        </div>

        <div class="example-details">
            <h2>Description</h2>
            <p>)" + example.description + R"(</p>

            <h2>Source Code</h2>
            <div class="code-block">)" + generateRunInstructions(example) + R"(</div>

            <h2>Files</h2>
            <ul class="file-list">
                <li><strong>Main File:</strong> )" + example.main_file + R"(</li>)";

    for (const QString& file : example.source_files) {
        html += "<li>" + file + "</li>";
    }

    html += R"(</ul>

            <h2>Dependencies</h2>
            <ul class="dependency-list">)";

    if (example.dependencies.isEmpty()) {
        html += "<li>No additional dependencies</li>";
    } else {
        for (const QString& dep : example.dependencies) {
            html += "<li>" + dep + "</li>";
        }
    }

    html += R"(</ul>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>&copy; 2024 DeclarativeUI Framework. Generated by DeclarativeUI Documentation Generator.</p>
        </div>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>)";

    return html;
}

QString DeclarativeUI::Documentation::ExampleDocumentationGenerator::generateRunInstructions(const DocumentationGenerator::ExampleInfo& example) {
    return QString(R"(// To run this example:
// 1. Navigate to the example directory
// 2. Build the project:
//    mkdir build && cd build
//    cmake ..
//    cmake --build .
// 3. Run the executable:
//    ./%1

// Main file: %2)").arg(
        QFileInfo(example.main_file).baseName(),
        example.main_file);
}

} // namespace DeclarativeUI::Documentation
