#include "ParallelProcessor.hpp"

#include <QDebug>
#include <QJsonArray>
#include <QUuid>
#include <QCoreApplication>
#include <algorithm>

namespace DeclarativeUI::Core {

// **ThreadPool implementation**
ThreadPool::ThreadPool(size_t thread_count) {
    workers_.reserve(thread_count);
    
    for (size_t i = 0; i < thread_count; ++i) {
        workers_.emplace_back(&ThreadPool::worker_thread, this);
    }
    
    qDebug() << "🔥 ThreadPool initialized with" << thread_count << "threads";
}

ThreadPool::~ThreadPool() {
    shutdown();
}



void ThreadPool::shutdown() {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        running_.store(false);
    }
    
    condition_.notify_all();
    
    for (std::thread& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    workers_.clear();
    qDebug() << "🔥 ThreadPool shutdown completed";
}

void ThreadPool::pause() {
    paused_.store(true);
    qDebug() << "🔥 ThreadPool paused";
}

void ThreadPool::resume() {
    paused_.store(false);
    condition_.notify_all();
    qDebug() << "🔥 ThreadPool resumed";
}

size_t ThreadPool::queued_tasks() const {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    return task_queue_.size();
}

void ThreadPool::worker_thread() {
    // **Set thread affinity for better cache locality**
    static std::atomic<int> thread_counter{0};
    int thread_id = thread_counter.fetch_add(1);

    // **Optimize thread for performance**
    std::this_thread::yield(); // Allow other threads to start

    while (true) {
        TaskWrapper task;
        bool task_acquired = false;

        // **Try lock-free fast path first**
        {
            std::unique_lock<std::mutex> lock(queue_mutex_, std::try_to_lock);
            if (lock.owns_lock() && !task_queue_.empty() && !paused_.load() && running_.load()) {
                task = task_queue_.top();
                task_queue_.pop();
                task_acquired = true;
            }
        }

        // **Fallback to blocking wait if no task acquired**
        if (!task_acquired) {
            std::unique_lock<std::mutex> lock(queue_mutex_);

            condition_.wait(lock, [this] {
                return !running_.load() || (!task_queue_.empty() && !paused_.load());
            });

            if (!running_.load()) {
                break;
            }

            if (task_queue_.empty() || paused_.load()) {
                continue;
            }

            task = task_queue_.top();
            task_queue_.pop();
            task_acquired = true;
        }

        if (!task_acquired) {
            continue;
        }

        active_threads_.fetch_add(1, std::memory_order_relaxed);

        // **Execute task with performance monitoring**
        auto start_time = std::chrono::steady_clock::now();

        try {
            task.task();
        } catch (const std::exception& e) {
            qWarning() << "🔥 Task execution failed:" << e.what();
        } catch (...) {
            qWarning() << "🔥 Task execution failed with unknown error";
        }

        // **Update performance metrics**
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

        // **Track task execution time for adaptive scheduling**
        if (duration.count() > 10000) { // Tasks longer than 10ms
            // Could implement adaptive priority adjustment here
        }

        active_threads_.fetch_sub(1, std::memory_order_relaxed);

        // **Yield to other threads periodically for better fairness**
        if (thread_id % 4 == 0) {
            std::this_thread::yield();
        }
    }
}

// **ParallelProcessor implementation**
ParallelProcessor::ParallelProcessor(QObject* parent) : QObject(parent) {
    thread_pool_ = std::make_unique<ThreadPool>();
    
    // Setup timeout timer
    timeout_timer_ = std::make_unique<QTimer>(this);
    timeout_timer_->setInterval(1000);  // Check every second
    connect(timeout_timer_.get(), &QTimer::timeout, this, &ParallelProcessor::onTaskTimeout);
    timeout_timer_->start();
    
    // Setup performance monitoring timer
    performance_timer_ = std::make_unique<QTimer>(this);
    performance_timer_->setInterval(10000);  // Check every 10 seconds
    connect(performance_timer_.get(), &QTimer::timeout, this, &ParallelProcessor::onPerformanceCheck);
    performance_timer_->start();
    
    qDebug() << "🔥 ParallelProcessor initialized";
}

ParallelProcessor::~ParallelProcessor() {
    if (thread_pool_) {
        thread_pool_->shutdown();
    }
    qDebug() << "🔥 ParallelProcessor destroyed";
}







void ParallelProcessor::setThreadPoolSize(size_t size) {
    if (thread_pool_) {
        thread_pool_->shutdown();
    }
    thread_pool_ = std::make_unique<ThreadPool>(size);
    qDebug() << "🔥 Thread pool resized to" << size << "threads";
}

void ParallelProcessor::pauseProcessing() {
    if (thread_pool_) {
        thread_pool_->pause();
    }
}

void ParallelProcessor::resumeProcessing() {
    if (thread_pool_) {
        thread_pool_->resume();
    }
}

void ParallelProcessor::setMaxQueueSize(size_t max_size) {
    max_queue_size_.store(max_size);
}

QJsonObject ParallelProcessor::getPerformanceMetrics() const {
    QJsonObject metrics;
    
    metrics["total_tasks_executed"] = static_cast<qint64>(total_tasks_executed_.load());
    metrics["total_tasks_failed"] = static_cast<qint64>(total_tasks_failed_.load());
    metrics["active_task_count"] = static_cast<qint64>(getActiveTaskCount());
    metrics["queued_task_count"] = static_cast<qint64>(getQueuedTaskCount());
    metrics["average_execution_time"] = getAverageExecutionTime();
    metrics["peak_queue_size"] = static_cast<qint64>(peak_queue_size_.load());
    
    if (thread_pool_) {
        metrics["active_threads"] = static_cast<qint64>(thread_pool_->active_threads());
        metrics["thread_pool_running"] = thread_pool_->is_running();
    }
    
    double success_rate = total_tasks_executed_.load() > 0 ? 
        (1.0 - static_cast<double>(total_tasks_failed_.load()) / total_tasks_executed_.load()) * 100.0 : 100.0;
    metrics["success_rate"] = success_rate;
    
    return metrics;
}

size_t ParallelProcessor::getActiveTaskCount() const {
    std::shared_lock<std::shared_mutex> lock(tasks_mutex_);
    return active_tasks_.size();
}

size_t ParallelProcessor::getQueuedTaskCount() const {
    return thread_pool_ ? thread_pool_->queued_tasks() : 0;
}

double ParallelProcessor::getAverageExecutionTime() const {
    size_t executed = total_tasks_executed_.load();
    return executed > 0 ? total_execution_time_.load() / executed : 0.0;
}

void ParallelProcessor::enableTaskProfiling(bool enabled) {
    task_profiling_enabled_.store(enabled);
}

void ParallelProcessor::setTaskTimeout(std::chrono::milliseconds timeout) {
    task_timeout_ = timeout;
}

void ParallelProcessor::enableLoadBalancing(bool enabled) {
    load_balancing_enabled_.store(enabled);
}

void ParallelProcessor::onTaskTimeout() {
    // Check for timed out tasks and handle them
    cleanupCompletedTasks();
}

void ParallelProcessor::onPerformanceCheck() {
    auto metrics = getPerformanceMetrics();
    
    // Check for performance alerts
    double success_rate = metrics["success_rate"].toDouble();
    if (success_rate < 90.0) {
        emit performanceAlert("success_rate", success_rate);
    }
    
    double avg_execution_time = metrics["average_execution_time"].toDouble();
    if (avg_execution_time > 5000.0) {  // 5 seconds
        emit performanceAlert("average_execution_time", avg_execution_time);
    }
    
    size_t queue_size = getQueuedTaskCount();
    if (queue_size > max_queue_size_.load() * 0.8) {
        emit performanceAlert("queue_size", static_cast<double>(queue_size));
    }
}

QString ParallelProcessor::generateTaskId() const {
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

void ParallelProcessor::updatePerformanceMetrics(const QString& task_id, 
                                                std::chrono::milliseconds execution_time, bool success) {
    total_tasks_executed_.fetch_add(1);
    
    if (!success) {
        total_tasks_failed_.fetch_add(1);
    }
    
    total_execution_time_.fetch_add(execution_time.count());
    
    // Update peak queue size
    size_t current_queue_size = getQueuedTaskCount();
    size_t current_peak = peak_queue_size_.load();
    while (current_queue_size > current_peak && 
           !peak_queue_size_.compare_exchange_weak(current_peak, current_queue_size)) {
        // Retry if another thread updated peak_queue_size_
    }
}

void ParallelProcessor::checkQueueOverflow() {
    size_t queue_size = getQueuedTaskCount();
    if (queue_size > max_queue_size_.load()) {
        emit queueOverflow(queue_size);
    }
}

void ParallelProcessor::balanceLoad() {
    if (!load_balancing_enabled_.load()) return;

    // **Advanced load balancing with adaptive thread pool sizing**
    size_t queue_size = getQueuedTaskCount();
    size_t active_threads = thread_pool_ ? thread_pool_->active_threads() : 0;
    size_t max_threads = std::thread::hardware_concurrency();

    // **Calculate optimal thread count based on queue pressure**
    double queue_pressure = static_cast<double>(queue_size) / (active_threads + 1);

    // **Adaptive scaling based on system load**
    if (queue_pressure > 3.0 && active_threads < max_threads) {
        // High queue pressure - consider scaling up
        size_t new_thread_count = std::min(active_threads + 2, max_threads);

        // **Check if we should actually scale up based on recent performance**
        double avg_execution_time = getAverageExecutionTime();
        if (avg_execution_time < 1000.0) { // Only scale up if tasks are completing quickly
            qDebug() << "🔥 Load balancing: Scaling up thread pool to" << new_thread_count;
            setThreadPoolSize(new_thread_count);
        }
    } else if (queue_pressure < 0.5 && active_threads > 2) {
        // Low queue pressure - consider scaling down to save resources
        size_t new_thread_count = std::max(active_threads - 1, size_t(2));

        // **Only scale down if we've been underutilized for a while**
        low_pressure_count_++;

        if (low_pressure_count_ > 10) { // 10 performance checks = ~100 seconds
            qDebug() << "🔥 Load balancing: Scaling down thread pool to" << new_thread_count;
            setThreadPoolSize(new_thread_count);
            low_pressure_count_ = 0;
        }
    } else {
        // Reset low pressure counter if we're in normal range
        low_pressure_count_ = 0;
    }

    // **Monitor for thread starvation**
    if (queue_size > 0 && active_threads == 0) {
        qWarning() << "🔥 Thread starvation detected - no active threads with queued tasks";
        // Force wake up threads
        if (thread_pool_) {
            thread_pool_->resume(); // This will notify waiting threads
        }
    }
}

void ParallelProcessor::cleanupCompletedTasks() {
    std::unique_lock<std::shared_mutex> lock(tasks_mutex_);
    
    // Remove completed tasks from active_tasks_
    for (auto it = active_tasks_.begin(); it != active_tasks_.end();) {
        // In a real implementation, we would check if the task is completed
        // For now, we'll keep all tasks
        ++it;
    }
}





// **Missing class constructors**
ParallelFileProcessor::ParallelFileProcessor(QObject* parent) 
    : QObject(parent) {
    qDebug() << "ParallelFileProcessor created";
}

ParallelUICompiler::ParallelUICompiler(QObject* parent) 
    : QObject(parent) {
    qDebug() << "ParallelUICompiler created";
}

ParallelPropertyBinder::ParallelPropertyBinder(QObject* parent) 
    : QObject(parent) {
    qDebug() << "ParallelPropertyBinder created";
}

}  // namespace DeclarativeUI::Core
