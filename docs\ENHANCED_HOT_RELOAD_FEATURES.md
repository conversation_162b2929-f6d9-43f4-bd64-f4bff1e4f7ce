# Enhanced Hot-Reload Features Documentation

## Overview

This document describes the comprehensive enhancements made to the DeclarativeUI Framework's hot-reload system. These enhancements provide advanced configuration management, sophisticated file filtering, real-time monitoring, selective reload capabilities, and robust error recovery mechanisms.

## Table of Contents

1. [Enhanced Configuration System](#enhanced-configuration-system)
2. [Advanced File Filtering](#advanced-file-filtering)
3. [Hot-Reload Dashboard Widget](#hot-reload-dashboard-widget)
4. [Selective Reload Mechanisms](#selective-reload-mechanisms)
5. [Enhanced Error Recovery System](#enhanced-error-recovery-system)
6. [Integration Guide](#integration-guide)
7. [API Reference](#api-reference)

## Enhanced Configuration System

### Overview

The Enhanced Configuration System provides a unified, profile-based approach to managing hot-reload settings with JSON serialization, environment-specific profiles, and runtime configuration updates.

### Key Features

- **Profile-Based Configuration**: Support for multiple configuration profiles (development, testing, production)
- **JSON Serialization**: Easy configuration persistence and sharing
- **Runtime Updates**: Dynamic configuration changes without restart
- **Environment Variables**: Integration with system environment variables
- **Validation**: Comprehensive configuration validation and error reporting

### Basic Usage

```cpp
#include "HotReload/HotReloadConfig.hpp"

using namespace DeclarativeUI::HotReload;

// Create and configure the system
HotReloadConfig config;

// Create a development profile
ConfigProfile dev_profile;
dev_profile.name = "development";
dev_profile.watch_interval = 100;  // Fast updates for development
dev_profile.enable_debug_logging = true;
dev_profile.auto_reload = true;

// Add the profile
config.addProfile(dev_profile);
config.setActiveProfile("development");

// Save configuration
config.saveToFile("hotreload_config.json");
```

### Configuration Profiles

Each profile supports the following settings:

- `name`: Profile identifier
- `watch_interval`: File system polling interval (milliseconds)
- `enable_debug_logging`: Enable detailed logging
- `auto_reload`: Automatic reload on file changes
- `debounce_delay`: Delay before processing file changes
- `max_reload_attempts`: Maximum retry attempts for failed reloads
- `backup_enabled`: Enable automatic backups before reload

### Environment Integration

```cpp
// Load configuration with environment variable support
config.loadFromFile("config.json");
config.applyEnvironmentOverrides();

// Environment variables like HOTRELOAD_WATCH_INTERVAL will override config values
```

## Advanced File Filtering

### Overview

The Advanced File Filtering system provides sophisticated rule-based filtering with multiple match types, performance optimization, caching, and detailed statistics tracking.

### Key Features

- **Multiple Match Types**: Glob patterns, regex, file size, modification time, content-based filtering
- **Rule Priority System**: Ordered rule evaluation with priority levels
- **Performance Optimization**: Intelligent caching and batch processing
- **Statistics Tracking**: Detailed filtering performance metrics
- **Preset Configurations**: Pre-defined filter sets for common scenarios

### Basic Usage

```cpp
#include "HotReload/AdvancedFileFilter.hpp"

using namespace DeclarativeUI::HotReload;

AdvancedFileFilter filter;

// Add a glob pattern rule to exclude build files
FilterRule build_rule;
build_rule.pattern = "build/**/*";
build_rule.match_type = FilterRule::MatchType::Glob;
build_rule.type = FilterRule::Type::Exclude;
build_rule.priority = 100;

filter.addRule(build_rule);

// Add a size-based rule to exclude large files
FilterRule size_rule;
size_rule.match_type = FilterRule::MatchType::Size;
size_rule.type = FilterRule::Type::Exclude;
size_rule.min_size_bytes = 0;
size_rule.max_size_bytes = 10 * 1024 * 1024;  // 10MB limit
size_rule.priority = 50;

filter.addRule(size_rule);

// Test file filtering
QStringList files = {"src/main.cpp", "build/main.o", "large_file.bin"};
QStringList filtered = filter.filterFiles(files);
```

### Filter Rule Types

1. **Glob Patterns**: Standard shell-style wildcards
   - `*.cpp` - All C++ files
   - `src/**/*` - All files in src directory and subdirectories
   - `test_*.{cpp,hpp}` - Test files with specific extensions

2. **Regular Expressions**: Full regex support
   - `^test_.*\.cpp$` - Test files ending in .cpp
   - `\.(tmp|bak)$` - Temporary and backup files

3. **File Size**: Size-based filtering
   - Minimum and maximum size limits
   - Supports bytes, KB, MB, GB units

4. **Modification Time**: Time-based filtering
   - Files modified within specific time ranges
   - Relative time specifications (e.g., "last 24 hours")

5. **Content-Based**: Filter based on file content
   - Text pattern matching within files
   - Binary file detection and filtering

### Performance Features

- **Intelligent Caching**: Results cached based on file modification time
- **Batch Processing**: Efficient handling of large file sets
- **Parallel Evaluation**: Multi-threaded rule processing for large directories
- **Statistics Tracking**: Performance metrics and bottleneck identification

## Hot-Reload Dashboard Widget

### Overview

The Hot-Reload Dashboard Widget provides a comprehensive real-time monitoring interface with performance analytics, file watch status, reload history, and interactive controls.

### Key Features

- **Real-Time Monitoring**: Live updates of file system activity and reload operations
- **Performance Analytics**: CPU usage, memory consumption, and reload timing metrics
- **Interactive Controls**: Start/stop/pause monitoring with configuration adjustments
- **Visual Feedback**: Charts, graphs, and status indicators
- **Export Capabilities**: Save metrics and reports to files

### Integration

```cpp
#include "HotReload/HotReloadDashboard.hpp"

using namespace DeclarativeUI::HotReload;

// Create dashboard widget
HotReloadDashboard* dashboard = new HotReloadDashboard(parent);

// Connect to hot-reload system components
dashboard->setHotReloadManager(hot_reload_manager);
dashboard->setPerformanceMonitor(performance_monitor);
dashboard->setFileWatcher(file_watcher);
dashboard->setConfiguration(config);

// Configure dashboard settings
dashboard->setUpdateInterval(1000);  // Update every second
dashboard->enableRealTimeUpdates(true);
dashboard->setMaxHistorySize(1000);

// Start monitoring
dashboard->startMonitoring();
```

### Dashboard Components

1. **Status Panel**: Current monitoring state and system health
2. **Performance Metrics**: Real-time CPU, memory, and timing data
3. **File Watch Status**: Active watches and recent file changes
4. **Reload History**: Timeline of reload operations with success/failure status
5. **Configuration Panel**: Runtime configuration adjustments
6. **Control Buttons**: Start, stop, pause, and reset operations

### Customization

```cpp
// Set custom themes
dashboard->setTheme("dark");  // or "light", "high-contrast"

// Configure performance thresholds
dashboard->setPerformanceThresholds(
    0.8,        // CPU threshold (80%)
    100 * 1024, // Memory threshold (100KB)
    5000        // Reload time threshold (5 seconds)
);

// Enable specific features
dashboard->enableRealTimeUpdates(true);
dashboard->setMaxHistorySize(500);
```

## Selective Reload Mechanisms

### Overview

The Selective Reload system enables targeted reloading of specific UI components, stylesheets, or resources without requiring full application restart, providing faster development cycles and better user experience.

### Key Features

- **Component-Level Granularity**: Reload individual UI components
- **CSS Live Injection**: Real-time stylesheet updates
- **Resource Hot-Swapping**: Dynamic replacement of images, icons, and other assets
- **Dependency Tracking**: Automatic cascading updates for dependent components
- **Rollback Mechanisms**: Safe fallback on reload failures

### Component Selective Reload

```cpp
#include "HotReload/SelectiveReloadManager.hpp"

using namespace DeclarativeUI::HotReload;

SelectiveReloadManager reload_manager;

// Register components for selective reload
reload_manager.registerComponent("main_window", main_window_widget);
reload_manager.registerComponent("toolbar", toolbar_widget);
reload_manager.registerComponent("status_bar", status_bar_widget);

// Reload specific component
ReloadResult result = reload_manager.reloadComponent("toolbar");
if (result.success) {
    qDebug() << "Toolbar reloaded successfully";
} else {
    qDebug() << "Reload failed:" << result.error_message;
}

// Reload multiple components
QStringList components = {"toolbar", "status_bar"};
BatchReloadResult batch_result = reload_manager.reloadComponents(components);
```

### CSS Live Injection

```cpp
// Enable CSS live injection
reload_manager.enableCSSLiveInjection(true);

// Register stylesheets for hot-swapping
reload_manager.registerStylesheet("main.css", main_widget);
reload_manager.registerStylesheet("toolbar.css", toolbar_widget);

// CSS files will be automatically reloaded when changed
// No manual intervention required
```

### Resource Hot-Swapping

```cpp
// Register resources for hot-swapping
reload_manager.registerResource("icons/save.png", ResourceType::Image);
reload_manager.registerResource("fonts/custom.ttf", ResourceType::Font);

// Resources will be automatically updated when files change
// All widgets using these resources will be refreshed
```

### Dependency Management

```cpp
// Define component dependencies
reload_manager.addDependency("main_window", "toolbar");
reload_manager.addDependency("main_window", "status_bar");

// When main_window is reloaded, toolbar and status_bar will also be updated
// Dependency graph ensures correct reload order
```

## Enhanced Error Recovery System

### Overview

The Enhanced Error Recovery System provides comprehensive error handling with automatic recovery strategies, user notifications, graceful degradation mechanisms, and intelligent error categorization.

### Key Features

- **Automatic Recovery**: Intelligent recovery strategies based on error type
- **User Notifications**: Clear, actionable error messages and status updates
- **Graceful Degradation**: Fallback mechanisms to maintain application stability
- **Error Categorization**: Classification of errors by severity and type
- **Recovery History**: Tracking of recovery attempts and success rates

### Basic Usage

```cpp
#include "HotReload/ErrorRecoveryManager.hpp"

using namespace DeclarativeUI::HotReload;

ErrorRecoveryManager recovery_manager;

// Configure recovery strategies
recovery_manager.enableAutoRecovery(true);
recovery_manager.setMaxRecoveryAttempts(3);
recovery_manager.setRecoveryDelay(1000);  // 1 second between attempts

// Register error handlers
recovery_manager.registerErrorHandler(ErrorType::FileNotFound, 
    [](const ErrorContext& context) -> RecoveryResult {
        // Custom recovery logic for missing files
        return RecoveryResult::success("File restored from backup");
    });

// Handle errors
ErrorContext error_context;
error_context.error_type = ErrorType::CompilationError;
error_context.file_path = "src/main.cpp";
error_context.error_message = "Syntax error on line 42";

RecoveryResult result = recovery_manager.handleError(error_context);
```

### Error Categories

1. **File System Errors**: Missing files, permission issues, disk space
2. **Compilation Errors**: Syntax errors, missing dependencies, build failures
3. **Runtime Errors**: Crashes, exceptions, memory issues
4. **Configuration Errors**: Invalid settings, missing required values
5. **Network Errors**: Connection failures, timeout issues

### Recovery Strategies

1. **Automatic Retry**: Simple retry with exponential backoff
2. **Fallback Resources**: Use backup or default resources
3. **Safe Mode**: Disable problematic features temporarily
4. **User Intervention**: Prompt user for manual resolution
5. **System Restart**: Last resort full system restart

### Notification System

```cpp
// Configure notification preferences
recovery_manager.setNotificationLevel(NotificationLevel::Warning);
recovery_manager.enablePopupNotifications(true);
recovery_manager.enableStatusBarNotifications(true);

// Custom notification handlers
recovery_manager.setNotificationHandler([](const Notification& notification) {
    // Custom notification display logic
    showCustomNotification(notification.title, notification.message);
});
```

## Integration Guide

### Complete System Setup

```cpp
#include "HotReload/HotReloadConfig.hpp"
#include "HotReload/AdvancedFileFilter.hpp"
#include "HotReload/HotReloadDashboard.hpp"
#include "HotReload/SelectiveReloadManager.hpp"
#include "HotReload/ErrorRecoveryManager.hpp"

class EnhancedHotReloadSystem {
private:
    HotReloadConfig config_;
    AdvancedFileFilter filter_;
    HotReloadDashboard* dashboard_;
    SelectiveReloadManager reload_manager_;
    ErrorRecoveryManager recovery_manager_;

public:
    void initialize() {
        // Load configuration
        config_.loadFromFile("hotreload_config.json");
        
        // Setup file filtering
        filter_.loadPreset("development");
        
        // Create dashboard
        dashboard_ = new HotReloadDashboard();
        dashboard_->setConfiguration(&config_);
        
        // Configure selective reload
        reload_manager_.setConfiguration(&config_);
        reload_manager_.setFileFilter(&filter_);
        
        // Setup error recovery
        recovery_manager_.enableAutoRecovery(true);
        
        // Connect components
        connectComponents();
    }
    
    void connectComponents() {
        // Connect dashboard to other components
        dashboard_->setSelectiveReloadManager(&reload_manager_);
        dashboard_->setErrorRecoveryManager(&recovery_manager_);
        
        // Connect error recovery to reload manager
        reload_manager_.setErrorRecoveryManager(&recovery_manager_);
    }
};
```

### Best Practices

1. **Configuration Management**
   - Use environment-specific profiles
   - Validate configuration on startup
   - Provide sensible defaults

2. **File Filtering**
   - Start with broad exclusions (build directories, temporary files)
   - Add specific inclusions for watched files
   - Monitor filter performance and adjust as needed

3. **Dashboard Usage**
   - Position dashboard for easy access during development
   - Configure appropriate update intervals
   - Use themes that match your development environment

4. **Selective Reload**
   - Register components with meaningful names
   - Define clear dependency relationships
   - Test reload scenarios thoroughly

5. **Error Recovery**
   - Implement custom recovery strategies for common errors
   - Provide clear user feedback
   - Log recovery attempts for debugging

## API Reference

### Core Classes

- `HotReloadConfig`: Configuration management and persistence
- `ConfigProfile`: Individual configuration profile
- `AdvancedFileFilter`: Sophisticated file filtering system
- `FilterRule`: Individual filtering rule definition
- `HotReloadDashboard`: Real-time monitoring widget
- `SelectiveReloadManager`: Component-level reload management
- `ErrorRecoveryManager`: Comprehensive error handling

### Key Enumerations

- `FilterRule::MatchType`: Glob, Regex, Size, Time, Content
- `FilterRule::Type`: Include, Exclude
- `ErrorType`: FileNotFound, CompilationError, RuntimeError, etc.
- `RecoveryStrategy`: Retry, Fallback, SafeMode, UserIntervention

### Signal/Slot Connections

All components provide comprehensive signal/slot interfaces for integration with Qt applications and custom event handling.

For detailed API documentation, see the individual header files in the `src/HotReload/` directory.
