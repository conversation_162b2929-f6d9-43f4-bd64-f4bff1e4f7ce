#include "PerformanceDashboard.hpp"
#include "PerformanceProfiler.hpp"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTabWidget>
#include <QLabel>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QPushButton>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QSplitter>
#include <QGroupBox>
#include <QProgressBar>
#include <QTimer>
#include <QFileDialog>
#include <QMessageBox>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

namespace DeclarativeUI::Profiling {

PerformanceDashboard::PerformanceDashboard(QWidget* parent)
    : QMainWindow(parent)
    , profiler_(nullptr)
    , update_timer_(std::make_unique<QTimer>(this))
    , monitoring_active_(false)
    , monitoring_paused_(false)
{
    setupUI();

    // Setup update timer
    update_timer_->setSingleShot(false);
    connect(update_timer_.get(), &QTimer::timeout, this, &PerformanceDashboard::updateDashboard);
}

PerformanceDashboard::~PerformanceDashboard() = default;

void PerformanceDashboard::setProfiler(std::shared_ptr<PerformanceProfiler> profiler) {
    if (profiler_) {
        // Disconnect old profiler
        disconnect(profiler_.get(), nullptr, this, nullptr);
    }
    
    profiler_ = profiler;
    
    if (profiler_) {
        // Connect to profiler signals
        connect(profiler_.get(), &PerformanceProfiler::snapshotTaken,
                this, &PerformanceDashboard::onSnapshotTaken);
        connect(profiler_.get(), &PerformanceProfiler::bottleneckDetected,
                this, &PerformanceDashboard::onBottleneckDetected);
        connect(profiler_.get(), &PerformanceProfiler::performanceWarning,
                this, &PerformanceDashboard::onPerformanceWarning);
        connect(profiler_.get(), &PerformanceProfiler::optimizationRecommendation,
                this, &PerformanceDashboard::onOptimizationRecommendation);
    }
}

void PerformanceDashboard::startMonitoring() {
    if (!profiler_) return;
    
    monitoring_active_ = true;
    monitoring_paused_ = false;
    
    profiler_->startProfiling();
    update_timer_->start(update_interval_ms_);
}

void PerformanceDashboard::stopMonitoring() {
    monitoring_active_ = false;
    monitoring_paused_ = false;

    update_timer_->stop();

    if (profiler_) {
        profiler_->stopProfiling();
    }
}

void PerformanceDashboard::pauseMonitoring() {
    if (!monitoring_active_) return;

    monitoring_paused_ = true;
    update_timer_->stop();

    if (profiler_) {
        profiler_->pauseProfiling();
    }
}

void PerformanceDashboard::resumeMonitoring() {
    if (!monitoring_active_ || !monitoring_paused_) return;

    monitoring_paused_ = false;
    update_timer_->start(update_interval_ms_);

    if (profiler_) {
        profiler_->resumeProfiling();
    }
}

void PerformanceDashboard::loadSession(const QString& session_file) {
    // Implementation for loading session data
    Q_UNUSED(session_file)
    // TODO: Implement session loading
}

void PerformanceDashboard::saveSession(const QString& session_file) {
    // Implementation for saving session data
    Q_UNUSED(session_file)
    // TODO: Implement session saving
}

void PerformanceDashboard::exportReport(const QString& file_path, const QString& format) {
    // Implementation for exporting reports
    Q_UNUSED(file_path)
    Q_UNUSED(format)
    // TODO: Implement report export
}

void PerformanceDashboard::clearData() {
    // Clear chart data
    time_axis_.clear();
    cpu_data_.clear();
    memory_data_.clear();
    fps_data_.clear();
    io_read_data_.clear();
    io_write_data_.clear();

    updateDashboard();
}

void PerformanceDashboard::setUpdateInterval(int interval_ms) {
    update_interval_ms_ = interval_ms;
    if (update_timer_->isActive()) {
        update_timer_->setInterval(interval_ms);
    }
}

void PerformanceDashboard::setHistorySize(int max_samples) {
    max_history_size_ = max_samples;

    // Trim existing data if necessary
    while (time_axis_.size() > max_history_size_) {
        time_axis_.removeFirst();
        cpu_data_.removeFirst();
        memory_data_.removeFirst();
        fps_data_.removeFirst();
        io_read_data_.removeFirst();
        io_write_data_.removeFirst();
    }
}

void PerformanceDashboard::setThresholds(double cpu_threshold, qint64 memory_threshold_mb, double fps_threshold) {
    cpu_threshold_ = cpu_threshold;
    memory_threshold_mb_ = memory_threshold_mb;
    fps_threshold_ = fps_threshold;
}

// Slot implementations
void PerformanceDashboard::onSnapshotTaken(const PerformanceProfiler::PerformanceSnapshot& snapshot) {
    // Add data to chart vectors
    time_axis_.append(snapshot.timestamp.toMSecsSinceEpoch());
    cpu_data_.append(snapshot.cpu.usage_percentage);
    memory_data_.append(snapshot.memory.used_memory_kb / 1024.0); // Convert to MB
    fps_data_.append(snapshot.render.frame_rate);
    io_read_data_.append(snapshot.io.read_bytes);
    io_write_data_.append(snapshot.io.write_bytes);

    // Maintain history size
    while (time_axis_.size() > max_history_size_) {
        time_axis_.removeFirst();
        cpu_data_.removeFirst();
        memory_data_.removeFirst();
        fps_data_.removeFirst();
        io_read_data_.removeFirst();
        io_write_data_.removeFirst();
    }

    // Update charts and metrics
    updateDashboard();
}

void PerformanceDashboard::onBottleneckDetected(const PerformanceProfiler::BottleneckInfo& bottleneck) {
    Q_UNUSED(bottleneck)
    updateBottlenecksList();
}

void PerformanceDashboard::onPerformanceWarning(const QString& message, const QJsonObject& details) {
    Q_UNUSED(message)
    Q_UNUSED(details)
    // TODO: Handle performance warnings
}

void PerformanceDashboard::onOptimizationRecommendation(const PerformanceProfiler::OptimizationRecommendation& recommendation) {
    Q_UNUSED(recommendation)
    updateRecommendationsList();
}

void PerformanceDashboard::updateDashboard() {
    refreshCharts();
    updateStatistics();
    updateBottlenecksList();
    updateRecommendationsList();
}

void PerformanceDashboard::refreshCharts() {
    // TODO: Update chart widgets with latest data
}

void PerformanceDashboard::updateStatistics() {
    // TODO: Update statistics displays
}

void PerformanceDashboard::updateBottlenecksList() {
    // TODO: Update bottlenecks list widget
}

void PerformanceDashboard::updateRecommendationsList() {
    // TODO: Update recommendations list widget
}

// Control action slots
void PerformanceDashboard::onStartMonitoring() {
    startMonitoring();
}

void PerformanceDashboard::onStopMonitoring() {
    stopMonitoring();
}

void PerformanceDashboard::onPauseMonitoring() {
    if (monitoring_paused_) {
        resumeMonitoring();
    } else {
        pauseMonitoring();
    }
}

void PerformanceDashboard::onClearData() {
    clearData();
}

void PerformanceDashboard::onExportReport() {
    QString fileName = QFileDialog::getSaveFileName(this, 
        tr("Export Performance Report"), 
        QString(), 
        tr("HTML Files (*.html);;JSON Files (*.json)"));
    
    if (!fileName.isEmpty()) {
        QString format = fileName.endsWith(".json") ? "json" : "html";
        exportReport(fileName, format);
    }
}

void PerformanceDashboard::onLoadSession() {
    QString fileName = QFileDialog::getOpenFileName(this,
        tr("Load Performance Session"),
        QString(),
        tr("JSON Files (*.json)"));
    
    if (!fileName.isEmpty()) {
        loadSession(fileName);
    }
}

void PerformanceDashboard::onSaveSession() {
    QString fileName = QFileDialog::getSaveFileName(this,
        tr("Save Performance Session"),
        QString(),
        tr("JSON Files (*.json)"));
    
    if (!fileName.isEmpty()) {
        saveSession(fileName);
    }
}

void PerformanceDashboard::onConfigureThresholds() {
    // TODO: Open threshold configuration dialog
}

// Chart event handlers
void PerformanceDashboard::onCPUChartClicked(QMouseEvent* event) {
    Q_UNUSED(event)
    // TODO: Handle CPU chart click
}

void PerformanceDashboard::onMemoryChartClicked(QMouseEvent* event) {
    Q_UNUSED(event)
    // TODO: Handle memory chart click
}

void PerformanceDashboard::onRenderChartClicked(QMouseEvent* event) {
    Q_UNUSED(event)
    // TODO: Handle render chart click
}

void PerformanceDashboard::onTimeRangeChanged(double start_time, double end_time) {
    Q_UNUSED(start_time)
    Q_UNUSED(end_time)
    // TODO: Handle time range change
}

void PerformanceDashboard::setupUI() {
    // Create central widget
    auto* central_widget = new QWidget(this);
    setCentralWidget(central_widget);
    
    // Create main layout
    auto* main_layout = new QVBoxLayout(central_widget);
    
    // Create tab widget for different views
    auto* tab_widget = new QTabWidget(this);
    main_layout->addWidget(tab_widget);
    
    // Setup basic tabs (detailed implementation would be added later)
    setupOverviewTab();
    setupCPUTab();
    setupMemoryTab();
    setupRenderTab();
    setupIOTab();
    setupBottlenecksTab();
    setupRecommendationsTab();
    setupConfigurationTab();
    
    // Setup menu bar, toolbar, and status bar
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
}

// UI setup methods (basic implementations)
void PerformanceDashboard::setupMenuBar() {
    auto* file_menu = menuBar()->addMenu(tr("&File"));
    file_menu->addAction(tr("&Load Session..."), this, &PerformanceDashboard::onLoadSession);
    file_menu->addAction(tr("&Save Session..."), this, &PerformanceDashboard::onSaveSession);
    file_menu->addSeparator();
    file_menu->addAction(tr("&Export Report..."), this, &PerformanceDashboard::onExportReport);
}

void PerformanceDashboard::setupToolBar() {
    auto* toolbar = addToolBar(tr("Controls"));
    toolbar->addAction(tr("Start"), this, &PerformanceDashboard::onStartMonitoring);
    toolbar->addAction(tr("Stop"), this, &PerformanceDashboard::onStopMonitoring);
    toolbar->addAction(tr("Pause"), this, &PerformanceDashboard::onPauseMonitoring);
    toolbar->addSeparator();
    toolbar->addAction(tr("Clear"), this, &PerformanceDashboard::onClearData);
}

void PerformanceDashboard::setupStatusBar() {
    statusBar()->showMessage(tr("Ready"));
}

// Tab setup methods (basic implementations)
void PerformanceDashboard::setupOverviewTab() {
    auto* overview_widget = new QWidget();
    auto* layout = new QVBoxLayout(overview_widget);
    layout->addWidget(new QLabel(tr("Performance Overview")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(overview_widget, tr("Overview"));
    }
}

void PerformanceDashboard::setupCPUTab() {
    auto* cpu_widget = new QWidget();
    auto* layout = new QVBoxLayout(cpu_widget);
    layout->addWidget(new QLabel(tr("CPU Performance")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(cpu_widget, tr("CPU"));
    }
}

void PerformanceDashboard::setupMemoryTab() {
    auto* memory_widget = new QWidget();
    auto* layout = new QVBoxLayout(memory_widget);
    layout->addWidget(new QLabel(tr("Memory Usage")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(memory_widget, tr("Memory"));
    }
}

void PerformanceDashboard::setupRenderTab() {
    auto* render_widget = new QWidget();
    auto* layout = new QVBoxLayout(render_widget);
    layout->addWidget(new QLabel(tr("Rendering Performance")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(render_widget, tr("Rendering"));
    }
}

void PerformanceDashboard::setupIOTab() {
    auto* io_widget = new QWidget();
    auto* layout = new QVBoxLayout(io_widget);
    layout->addWidget(new QLabel(tr("I/O Performance")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(io_widget, tr("I/O"));
    }
}

void PerformanceDashboard::setupBottlenecksTab() {
    auto* bottlenecks_widget = new QWidget();
    auto* layout = new QVBoxLayout(bottlenecks_widget);
    layout->addWidget(new QLabel(tr("Performance Bottlenecks")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(bottlenecks_widget, tr("Bottlenecks"));
    }
}

void PerformanceDashboard::setupRecommendationsTab() {
    auto* recommendations_widget = new QWidget();
    auto* layout = new QVBoxLayout(recommendations_widget);
    layout->addWidget(new QLabel(tr("Optimization Recommendations")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(recommendations_widget, tr("Recommendations"));
    }
}

void PerformanceDashboard::setupConfigurationTab() {
    auto* config_widget = new QWidget();
    auto* layout = new QVBoxLayout(config_widget);
    layout->addWidget(new QLabel(tr("Configuration")));
    
    if (auto* tab_widget = findChild<QTabWidget*>()) {
        tab_widget->addTab(config_widget, tr("Configuration"));
    }
}

// PerformanceAlertSystem implementation
PerformanceAlertSystem::PerformanceAlertSystem(QObject* parent)
    : QObject(parent)
{
    qDebug() << "🚨 PerformanceAlertSystem initialized";
}

void PerformanceAlertSystem::addAlert(AlertLevel level, const QString& category, const QString& message, const QJsonObject& details) {
    QMutexLocker locker(&alerts_mutex_);

    Alert alert;
    alert.level = level;
    alert.category = category;
    alert.message = message;
    alert.timestamp = QDateTime::currentDateTime();
    alert.details = details;
    alert.acknowledged = false;

    alerts_.push_back(alert);

    emit alertTriggered(alert);
    if (level == CRITICAL) {
        emit criticalAlertTriggered(alert);
    }

    if (notifications_enabled_) {
        showNotification(alert);
    }

    qDebug() << "🚨 Alert added:" << category << "-" << message;
}

void PerformanceAlertSystem::acknowledgeAlert(int alert_id) {
    QMutexLocker locker(&alerts_mutex_);

    if (alert_id >= 0 && alert_id < static_cast<int>(alerts_.size())) {
        alerts_[alert_id].acknowledged = true;
        qDebug() << "🚨 Alert acknowledged:" << alert_id;
    }
}

void PerformanceAlertSystem::clearAlerts() {
    QMutexLocker locker(&alerts_mutex_);
    alerts_.clear();
    qDebug() << "🚨 All alerts cleared";
}

std::vector<PerformanceAlertSystem::Alert> PerformanceAlertSystem::getAlerts() const {
    QMutexLocker locker(&alerts_mutex_);
    return alerts_;
}

std::vector<PerformanceAlertSystem::Alert> PerformanceAlertSystem::getUnacknowledgedAlerts() const {
    QMutexLocker locker(&alerts_mutex_);

    std::vector<Alert> unacknowledged;
    for (const auto& alert : alerts_) {
        if (!alert.acknowledged) {
            unacknowledged.push_back(alert);
        }
    }

    return unacknowledged;
}

void PerformanceAlertSystem::setThresholds(double cpu_threshold, qint64 memory_threshold_mb, double fps_threshold) {
    cpu_threshold_ = cpu_threshold;
    memory_threshold_mb_ = memory_threshold_mb;
    fps_threshold_ = fps_threshold;

    qDebug() << "🚨 Thresholds updated - CPU:" << cpu_threshold << "%, Memory:" << memory_threshold_mb << "MB, FPS:" << fps_threshold;
}

void PerformanceAlertSystem::enableNotifications(bool enabled) {
    notifications_enabled_ = enabled;
    qDebug() << "🚨 Notifications" << (enabled ? "enabled" : "disabled");
}

void PerformanceAlertSystem::setNotificationSound(const QString& sound_file) {
    notification_sound_file_ = sound_file;
    qDebug() << "🚨 Notification sound set to:" << sound_file;
}

void PerformanceAlertSystem::checkSnapshot(const PerformanceProfiler::PerformanceSnapshot& snapshot) {
    // Check CPU usage
    if (snapshot.cpu.usage_percentage > cpu_threshold_) {
        QJsonObject details;
        details["cpu_usage"] = snapshot.cpu.usage_percentage;
        details["threshold"] = cpu_threshold_;
        addAlert(WARNING, "CPU", QString("High CPU usage: %1%").arg(snapshot.cpu.usage_percentage), details);
    }

    // Check memory usage
    qint64 memory_mb = snapshot.memory.used_memory_kb / 1024;
    if (memory_mb > memory_threshold_mb_) {
        QJsonObject details;
        details["memory_usage_mb"] = memory_mb;
        details["threshold_mb"] = memory_threshold_mb_;
        addAlert(WARNING, "Memory", QString("High memory usage: %1 MB").arg(memory_mb), details);
    }

    // Check FPS
    if (snapshot.render.frame_rate < fps_threshold_ && snapshot.render.frame_rate > 0) {
        QJsonObject details;
        details["fps"] = snapshot.render.frame_rate;
        details["threshold"] = fps_threshold_;
        addAlert(WARNING, "Performance", QString("Low FPS: %1").arg(snapshot.render.frame_rate), details);
    }
}

void PerformanceAlertSystem::checkBottleneck(const PerformanceProfiler::BottleneckInfo& bottleneck) {
    QJsonObject details;
    details["component"] = bottleneck.component;
    details["severity"] = static_cast<int>(bottleneck.severity);
    details["severity"] = bottleneck.severity;

    AlertLevel level = (bottleneck.severity > 0.8) ? CRITICAL : WARNING;
    addAlert(level, "Bottleneck", QString("Bottleneck detected in %1").arg(bottleneck.component), details);
}

void PerformanceAlertSystem::showNotification(const Alert& alert) {
    // Stub implementation - would show system notification
    qDebug() << "🚨 Notification:" << alert.category << "-" << alert.message;

    if (!notification_sound_file_.isEmpty()) {
        playNotificationSound();
    }
}

void PerformanceAlertSystem::playNotificationSound() {
    // Stub implementation - would play notification sound
    qDebug() << "🔊 Playing notification sound:" << notification_sound_file_;
}

} // namespace DeclarativeUI::Profiling
