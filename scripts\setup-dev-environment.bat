@echo off
REM Setup development environment for DeclarativeUI Framework

echo 🚀 Setting up DeclarativeUI development environment...

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to PATH
    exit /b 1
)

REM Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip is not available
    echo Please ensure pip is installed with Python
    exit /b 1
)

echo ✅ Python and pip are available

REM Install pre-commit if not already installed
echo 📦 Installing pre-commit...
pip install pre-commit

REM Install pre-commit hooks
echo 🔧 Installing pre-commit hooks...
pre-commit install

REM Install additional development tools
echo 📦 Installing additional development tools...
pip install black isort flake8 mypy

REM Check if clang-format is available
clang-format --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  clang-format is not available
    echo Please install LLVM/Clang tools for code formatting
    echo Download from: https://releases.llvm.org/download.html
) else (
    echo ✅ clang-format is available
)

REM Check if CMake is available
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ CMake is not installed or not in PATH
    echo Please install CMake 3.20+ and add it to PATH
    echo Download from: https://cmake.org/download/
    exit /b 1
) else (
    echo ✅ CMake is available
)

REM Check if Qt6 is available
echo 🔍 Checking for Qt6...
if exist "C:\Qt\6.*" (
    echo ✅ Qt6 installation found
) else (
    echo ⚠️  Qt6 not found in standard location
    echo Please ensure Qt6 is installed and CMAKE_PREFIX_PATH is set
    echo Download from: https://www.qt.io/download
)

REM Create .secrets.baseline for detect-secrets
echo 📝 Creating secrets baseline...
if not exist ".secrets.baseline" (
    echo {} > .secrets.baseline
)

REM Run initial pre-commit check
echo 🧪 Running initial pre-commit check...
pre-commit run --all-files

echo ""
echo 🎉 Development environment setup complete!
echo ""
echo "📋 Next steps:"
echo "   1. Configure your IDE to use .editorconfig settings"
echo "   2. Set up Qt6 environment variables if needed"
echo "   3. Run 'build.bat' to build the project"
echo "   4. Run tests with 'ctest --output-on-failure'"
echo ""
echo "🔧 Available development commands:"
echo "   - pre-commit run --all-files  # Run all checks"
echo "   - pre-commit run <hook-name>  # Run specific check"
echo "   - scripts\check-qt-includes.sh <files>  # Check Qt includes"
echo "   - scripts\validate-json-ui.py <files>   # Validate JSON UI"
echo ""
echo "📚 Documentation:"
echo "   - README.md - Project overview and quick start"
echo "   - CONTRIBUTING.md - Contributing guidelines"
echo "   - docs/ - Comprehensive documentation"
echo ""

pause
