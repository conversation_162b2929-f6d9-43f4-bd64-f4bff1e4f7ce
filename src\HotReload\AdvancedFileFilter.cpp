#include "AdvancedFileFilter.hpp"
#include <QFile>
#include <QTextStream>
#include <QCryptographicHash>
#include <QJsonDocument>
#include <QDebug>
#include <QElapsedTimer>
#include <QStandardPaths>
#include <QCoreApplication>

#include <algorithm>
#include <chrono>

namespace DeclarativeUI::HotReload {

// **FilterRule Implementation**
QJsonObject FilterRule::toJson() const {
    QJsonObject json;
    json["type"] = (type == Type::Include) ? "include" : "exclude";
    json["match_type"] = static_cast<int>(match_type);
    json["pattern"] = pattern;
    json["description"] = description;
    json["min_size_bytes"] = static_cast<qint64>(min_size_bytes);
    json["max_size_bytes"] = static_cast<qint64>(max_size_bytes);
    json["max_age_seconds"] = static_cast<qint64>(max_age.count());
    json["min_age_seconds"] = static_cast<qint64>(min_age.count());
    json["priority"] = priority;
    return json;
}

FilterRule FilterRule::fromJson(const QJsonObject& json) {
    FilterRule rule;
    rule.type = (json["type"].toString() == "include") ? Type::Include : Type::Exclude;
    rule.match_type = static_cast<MatchType>(json["match_type"].toInt());
    rule.pattern = json["pattern"].toString();
    rule.description = json["description"].toString();
    rule.min_size_bytes = json["min_size_bytes"].toVariant().toLongLong();
    rule.max_size_bytes = json["max_size_bytes"].toVariant().toLongLong();
    rule.max_age = std::chrono::seconds(json["max_age_seconds"].toVariant().toLongLong());
    rule.min_age = std::chrono::seconds(json["min_age_seconds"].toVariant().toLongLong());
    rule.priority = json["priority"].toInt();
    return rule;
}

bool FilterRule::matches(const QString& file_path, const QFileInfo& file_info) const {
    switch (match_type) {
        case MatchType::Extension: {
            QString suffix = file_info.suffix().toLower();
            QString pattern_lower = pattern.toLower();
            if (pattern_lower.startsWith("*.")) {
                pattern_lower = pattern_lower.mid(2);
            }
            return suffix == pattern_lower;
        }
        
        case MatchType::Glob: {
            compileRegex();
            return compiled_regex.match(file_info.fileName()).hasMatch();
        }
        
        case MatchType::Regex: {
            compileRegex();
            return compiled_regex.match(file_path).hasMatch();
        }
        
        case MatchType::Size: {
            qint64 size = file_info.size();
            if (min_size_bytes > 0 && size < min_size_bytes) return false;
            if (max_size_bytes > 0 && size > max_size_bytes) return false;
            return true;
        }
        
        case MatchType::Directory: {
            QString dir_path = file_info.absolutePath();
            return dir_path.contains(pattern, Qt::CaseInsensitive);
        }
        
        case MatchType::Age: {
            auto file_time = std::chrono::system_clock::from_time_t(file_info.lastModified().toSecsSinceEpoch());
            auto now = std::chrono::system_clock::now();
            auto age = std::chrono::duration_cast<std::chrono::seconds>(now - file_time);
            
            if (min_age.count() > 0 && age < min_age) return false;
            if (max_age.count() > 0 && age > max_age) return false;
            return true;
        }
        
        case MatchType::Custom: {
            if (custom_filter) {
                return custom_filter(file_path, file_info);
            }
            return true;
        }
        
        case MatchType::MimeType:
        case MatchType::Content:
        default:
            return true; // These require additional context
    }
}

bool FilterRule::matchesContent(const QString& content) const {
    if (match_type != MatchType::Content) {
        return true;
    }
    
    compileRegex();
    return compiled_regex.match(content).hasMatch();
}

void FilterRule::compileRegex() const {
    if (regex_compiled) return;
    
    QString regex_pattern;
    switch (match_type) {
        case MatchType::Glob:
            regex_pattern = QRegularExpression::wildcardToRegularExpression(pattern);
            break;
        case MatchType::Regex:
        case MatchType::Content:
            regex_pattern = pattern;
            break;
        default:
            regex_pattern = QRegularExpression::escape(pattern);
            break;
    }
    
    compiled_regex = QRegularExpression(regex_pattern, QRegularExpression::CaseInsensitiveOption);
    regex_compiled = true;
}

// **FilterStats is now a simple struct - no methods needed**

// **AdvancedFileFilter Implementation**
AdvancedFileFilter::AdvancedFileFilter(QObject* parent) 
    : QObject(parent)
    , filter_cache_(1000) { // Default cache size
    
    // Add some basic default rules
    addCommonQtRules();
    addCommonBuildExclusions();
}

void AdvancedFileFilter::addRule(const FilterRule& rule) {
    QWriteLocker locker(&rules_lock_);
    rules_.push_back(rule);
    sortRulesByPriority();
    emit ruleAdded(rule.pattern);
}

void AdvancedFileFilter::removeRule(const QString& pattern) {
    QWriteLocker locker(&rules_lock_);
    auto it = std::remove_if(rules_.begin(), rules_.end(),
        [&pattern](const FilterRule& rule) { return rule.pattern == pattern; });
    
    if (it != rules_.end()) {
        rules_.erase(it, rules_.end());
        emit ruleRemoved(pattern);
    }
}

void AdvancedFileFilter::clearRules() {
    QWriteLocker locker(&rules_lock_);
    rules_.clear();
}

void AdvancedFileFilter::setRules(const std::vector<FilterRule>& rules) {
    QWriteLocker locker(&rules_lock_);
    rules_ = rules;
    sortRulesByPriority();
}

std::vector<FilterRule> AdvancedFileFilter::getRules() const {
    QReadLocker locker(&rules_lock_);
    return rules_;
}

// **Helper function to create filter rules**
static FilterRule createRule(FilterRule::Type type, FilterRule::MatchType match_type,
                           const QString& pattern, const QString& description, int priority = 0) {
    FilterRule rule;
    rule.type = type;
    rule.match_type = match_type;
    rule.pattern = pattern;
    rule.description = description;
    rule.priority = priority;
    return rule;
}

void AdvancedFileFilter::addCommonQtRules() {
    // Qt-specific file types
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "qml", "QML files", 100));
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "ui", "Qt UI files", 100));
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "qrc", "Qt Resource files", 90));
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "qss", "Qt Style Sheets", 90));

    // Common web assets used in Qt apps
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "js", "JavaScript files", 80));
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "css", "CSS files", 80));
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "json", "JSON files", 70));

    // Image files commonly used in Qt
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "png", "PNG images", 60));
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "svg", "SVG images", 60));
    addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "ico", "Icon files", 50));
}

void AdvancedFileFilter::addCommonBuildExclusions() {
    // Build directories
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, "build", "Build directories", 200));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, "debug", "Debug directories", 200));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, "release", "Release directories", 200));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, "dist", "Distribution directories", 200));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, "target", "Target directories", 200));

    // Version control
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, ".git", "Git directories", 250));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, ".svn", "SVN directories", 250));

    // Package managers
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, "node_modules", "Node.js modules", 220));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, ".npm", "NPM cache", 220));

    // Temporary files
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Glob, "*.tmp", "Temporary files", 180));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Glob, "*.bak", "Backup files", 180));
    addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Glob, "*~", "Editor backup files", 180));
}

bool AdvancedFileFilter::shouldIncludeFile(const QString& file_path) const {
    QFileInfo file_info(file_path);
    return shouldIncludeFile(file_path, file_info);
}

bool AdvancedFileFilter::shouldIncludeFile(const QString& file_path, const QFileInfo& file_info) const {
    QElapsedTimer timer;
    timer.start();
    
    // Check cache first
    if (caching_enabled_.load()) {
        QReadLocker cache_locker(&cache_lock_);
        if (auto* cached = filter_cache_.object(file_path)) {
            if (isCacheEntryValid(*cached, file_info)) {
                cache_hits_.fetch_add(1);
                updateStatistics(cached->should_include, timer.nsecsElapsed());
                return cached->should_include;
            }
        }
        cache_misses_.fetch_add(1);
    }
    
    // Evaluate rules
    bool should_include = evaluateRules(file_path, file_info);
    
    // Update cache
    if (caching_enabled_.load()) {
        QWriteLocker cache_locker(&cache_lock_);
        auto* entry = new CacheEntry{
            should_include,
            QDateTime::currentDateTime(),
            file_info.size(),
            fast_mode_enabled_.load() ? QString() : calculateFileHash(file_path)
        };
        filter_cache_.insert(file_path, entry);
    }
    
    updateStatistics(should_include, timer.nsecsElapsed());
    
    if (!should_include) {
        addToRecentlyExcluded(file_path);
    }
    
    return should_include;
}

bool AdvancedFileFilter::evaluateRules(const QString& file_path, const QFileInfo& file_info) const {
    QReadLocker locker(&rules_lock_);
    
    // Default to exclude if no rules match
    bool should_include = false;
    
    // Process rules in priority order (higher priority first)
    for (const auto& rule : rules_) {
        if (rule.matches(file_path, file_info)) {
            // Update rule usage statistics
            {
                QMutexLocker stats_locker(&rule_stats_mutex_);
                rule_usage_count_[rule.pattern]++;
            }
            
            if (rule.type == FilterRule::Type::Include) {
                should_include = true;
            } else {
                should_include = false;
                break; // Exclude rules are final
            }
        }
    }
    
    return should_include;
}

void AdvancedFileFilter::sortRulesByPriority() {
    std::sort(rules_.begin(), rules_.end(),
        [](const FilterRule& a, const FilterRule& b) {
            return a.priority > b.priority; // Higher priority first
        });
}

void AdvancedFileFilter::updateStatistics(bool included, qint64 processing_time_ns) const {
    total_files_processed_.fetch_add(1);
    if (included) {
        files_included_.fetch_add(1);
    } else {
        files_excluded_.fetch_add(1);
    }

    total_filter_time_ms_.fetch_add(processing_time_ns / 1000000);

    // Update rolling average
    qint64 total_processed = total_files_processed_.load();
    qint64 current_avg = average_filter_time_ns_.load();
    qint64 new_avg = (current_avg * (total_processed - 1) + processing_time_ns) / total_processed;
    average_filter_time_ns_.store(new_avg);
}

void AdvancedFileFilter::addToRecentlyExcluded(const QString& file_path) const {
    QMutexLocker locker(&excluded_files_mutex_);
    recently_excluded_files_.prepend(file_path);
    if (recently_excluded_files_.size() > 100) {
        recently_excluded_files_.removeLast();
    }
}

QString AdvancedFileFilter::calculateFileHash(const QString& file_path) const {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }
    
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(file.read(1024)); // Only hash first 1KB for performance
    return hash.result().toHex();
}

bool AdvancedFileFilter::isCacheEntryValid(const CacheEntry& entry, const QFileInfo& file_info) const {
    // Check if file has been modified since cache entry
    if (file_info.lastModified() > entry.timestamp) {
        return false;
    }
    
    // Check if file size has changed
    if (file_info.size() != entry.file_size) {
        return false;
    }
    
    // In fast mode, we don't check file hash
    if (fast_mode_enabled_.load() || entry.file_hash.isEmpty()) {
        return true;
    }
    
    // Check file hash for content changes
    QString current_hash = calculateFileHash(file_info.absoluteFilePath());
    return current_hash == entry.file_hash;
}

FilterStats AdvancedFileFilter::getStatistics() const {
    FilterStats stats;
    stats.total_files_processed = total_files_processed_.load();
    stats.files_included = files_included_.load();
    stats.files_excluded = files_excluded_.load();
    stats.cache_hits = cache_hits_.load();
    stats.cache_misses = cache_misses_.load();
    stats.total_filter_time_ms = total_filter_time_ms_.load();
    stats.average_filter_time_ns = average_filter_time_ns_.load();

    // Copy rule usage statistics
    QMutexLocker locker(&rule_stats_mutex_);
    stats.rule_usage_count = rule_usage_count_;

    return stats;
}

void AdvancedFileFilter::resetStatistics() {
    total_files_processed_.store(0);
    files_included_.store(0);
    files_excluded_.store(0);
    cache_hits_.store(0);
    cache_misses_.store(0);
    total_filter_time_ms_.store(0);
    average_filter_time_ns_.store(0);

    QMutexLocker locker(&rule_stats_mutex_);
    rule_usage_count_.clear();

    emit statisticsUpdated();
}

// **Additional AdvancedFileFilter methods**
void AdvancedFileFilter::enableCaching(bool enabled) {
    caching_enabled_.store(enabled);
}

void AdvancedFileFilter::setCacheSize(int max_entries) {
    QWriteLocker locker(&cache_lock_);
    filter_cache_.setMaxCost(max_entries);
}

void AdvancedFileFilter::enableContentFiltering(bool enabled) {
    content_filtering_enabled_.store(enabled);
}

void AdvancedFileFilter::enableMimeTypeDetection(bool enabled) {
    mime_type_detection_enabled_.store(enabled);
}

void AdvancedFileFilter::setMaxFileSize(qint64 max_size_bytes) {
    max_file_size_bytes_.store(max_size_bytes);
}

void AdvancedFileFilter::setMinFileSize(qint64 min_size_bytes) {
    min_file_size_bytes_.store(min_size_bytes);
}

void AdvancedFileFilter::enableFastMode(bool enabled) {
    fast_mode_enabled_.store(enabled);
}

void AdvancedFileFilter::setMaxContentScanSize(qint64 max_size_bytes) {
    max_content_scan_size_.store(max_size_bytes);
}

QStringList AdvancedFileFilter::filterFiles(const QStringList& file_paths) const {
    QStringList result;
    result.reserve(file_paths.size());

    for (const QString& file_path : file_paths) {
        if (shouldIncludeFile(file_path)) {
            result.append(file_path);
        }
    }

    return result;
}

QStringList AdvancedFileFilter::filterDirectory(const QString& directory_path, bool recursive) const {
    QStringList result;
    QDir dir(directory_path);

    if (!dir.exists()) {
        return result;
    }

    // Get all files in directory
    QFileInfoList files = dir.entryInfoList(QDir::Files | QDir::NoDotAndDotDot);
    for (const QFileInfo& file_info : files) {
        if (shouldIncludeFile(file_info.absoluteFilePath(), file_info)) {
            result.append(file_info.absoluteFilePath());
        }
    }

    // Recursively process subdirectories
    if (recursive) {
        QFileInfoList subdirs = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
        for (const QFileInfo& subdir_info : subdirs) {
            QStringList subdir_files = filterDirectory(subdir_info.absoluteFilePath(), true);
            result.append(subdir_files);
        }
    }

    return result;
}

bool AdvancedFileFilter::shouldIncludeContent(const QString& file_path, const QString& content) const {
    if (!content_filtering_enabled_.load()) {
        return true;
    }

    QReadLocker locker(&rules_lock_);

    for (const auto& rule : rules_) {
        if (rule.match_type == FilterRule::MatchType::Content) {
            if (rule.matchesContent(content)) {
                return rule.type == FilterRule::Type::Include;
            }
        }
    }

    return true; // Default to include if no content rules match
}

QJsonObject AdvancedFileFilter::toJson() const {
    QJsonObject json;

    // Configuration
    json["caching_enabled"] = caching_enabled_.load();
    json["content_filtering_enabled"] = content_filtering_enabled_.load();
    json["mime_type_detection_enabled"] = mime_type_detection_enabled_.load();
    json["fast_mode_enabled"] = fast_mode_enabled_.load();
    json["max_file_size_bytes"] = static_cast<qint64>(max_file_size_bytes_.load());
    json["min_file_size_bytes"] = static_cast<qint64>(min_file_size_bytes_.load());
    json["max_content_scan_size"] = static_cast<qint64>(max_content_scan_size_.load());

    // Rules
    QJsonArray rules_array;
    QReadLocker locker(&rules_lock_);
    for (const auto& rule : rules_) {
        rules_array.append(rule.toJson());
    }
    json["rules"] = rules_array;

    // Statistics
    QJsonObject stats_json;
    stats_json["total_files_processed"] = static_cast<qint64>(total_files_processed_.load());
    stats_json["files_included"] = static_cast<qint64>(files_included_.load());
    stats_json["files_excluded"] = static_cast<qint64>(files_excluded_.load());
    stats_json["cache_hits"] = static_cast<qint64>(cache_hits_.load());
    stats_json["cache_misses"] = static_cast<qint64>(cache_misses_.load());
    stats_json["total_filter_time_ms"] = static_cast<qint64>(total_filter_time_ms_.load());
    stats_json["average_filter_time_ns"] = static_cast<qint64>(average_filter_time_ns_.load());

    // Calculate derived statistics
    qint64 total_files = total_files_processed_.load();
    qint64 total_cache_ops = cache_hits_.load() + cache_misses_.load();
    stats_json["inclusion_rate"] = total_files > 0 ? static_cast<double>(files_included_.load()) / total_files : 0.0;
    stats_json["cache_hit_rate"] = total_cache_ops > 0 ? static_cast<double>(cache_hits_.load()) / total_cache_ops : 0.0;

    json["statistics"] = stats_json;

    return json;
}

bool AdvancedFileFilter::loadFromJson(const QJsonObject& json) {
    try {
        // Load configuration
        enableCaching(json["caching_enabled"].toBool(true));
        enableContentFiltering(json["content_filtering_enabled"].toBool(false));
        enableMimeTypeDetection(json["mime_type_detection_enabled"].toBool(false));
        enableFastMode(json["fast_mode_enabled"].toBool(false));
        setMaxFileSize(json["max_file_size_bytes"].toVariant().toLongLong());
        setMinFileSize(json["min_file_size_bytes"].toVariant().toLongLong());
        setMaxContentScanSize(json["max_content_scan_size"].toVariant().toLongLong());

        // Load rules
        std::vector<FilterRule> rules;
        QJsonArray rules_array = json["rules"].toArray();
        for (const auto& rule_value : rules_array) {
            FilterRule rule = FilterRule::fromJson(rule_value.toObject());
            rules.push_back(rule);
        }
        setRules(rules);

        return true;
    } catch (const std::exception& e) {
        emit filteringError(QString("Failed to load configuration: %1").arg(e.what()));
        return false;
    }
}

bool AdvancedFileFilter::saveToFile(const QString& file_path) const {
    QFile file(file_path);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    QJsonDocument doc(toJson());
    file.write(doc.toJson());
    return true;
}

bool AdvancedFileFilter::loadFromFile(const QString& file_path) {
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QByteArray data = file.readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (doc.isNull()) {
        emit filteringError(QString("JSON parse error: %1").arg(error.errorString()));
        return false;
    }

    return loadFromJson(doc.object());
}

QString AdvancedFileFilter::explainDecision(const QString& file_path) const {
    QFileInfo file_info(file_path);
    QStringList explanations;

    QReadLocker locker(&rules_lock_);

    explanations.append(QString("Evaluating file: %1").arg(file_path));
    explanations.append(QString("File size: %1 bytes").arg(file_info.size()));
    explanations.append(QString("Last modified: %1").arg(file_info.lastModified().toString()));

    bool final_decision = false;
    bool any_rule_matched = false;

    for (const auto& rule : rules_) {
        if (rule.matches(file_path, file_info)) {
            any_rule_matched = true;
            QString action = (rule.type == FilterRule::Type::Include) ? "INCLUDE" : "EXCLUDE";
            explanations.append(QString("✓ Rule matched [%1]: %2 (%3)")
                .arg(action)
                .arg(rule.pattern)
                .arg(rule.description));

            final_decision = (rule.type == FilterRule::Type::Include);

            if (rule.type == FilterRule::Type::Exclude) {
                break; // Exclude rules are final
            }
        }
    }

    if (!any_rule_matched) {
        explanations.append("✗ No rules matched - defaulting to EXCLUDE");
    }

    QString final_action = final_decision ? "INCLUDE" : "EXCLUDE";
    explanations.append(QString("Final decision: %1").arg(final_action));

    return explanations.join("\n");
}

QStringList AdvancedFileFilter::getRecentlyExcludedFiles(int max_count) const {
    QMutexLocker locker(&excluded_files_mutex_);
    return recently_excluded_files_.mid(0, qMin(max_count, recently_excluded_files_.size()));
}

// **FilterPresets Implementation**
std::unique_ptr<AdvancedFileFilter> FilterPresets::createWebDevelopmentFilter() {
    auto filter = std::make_unique<AdvancedFileFilter>();

    // Clear default rules and add web-specific ones
    filter->clearRules();

    // Web development files
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "html", "HTML files", 100));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "css", "CSS files", 100));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "scss", "SCSS files", 100));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "js", "JavaScript files", 100));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "ts", "TypeScript files", 100));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "json", "JSON files", 90));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "xml", "XML files", 80));

    // Add common exclusions
    filter->addCommonBuildExclusions();

    return filter;
}

std::unique_ptr<AdvancedFileFilter> FilterPresets::createQtDevelopmentFilter() {
    auto filter = std::make_unique<AdvancedFileFilter>();

    // Qt development files are already added by default constructor
    // Just add some additional ones
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "cpp", "C++ source files", 70));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "h", "C++ header files", 70));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "hpp", "C++ header files", 70));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "pro", "Qt project files", 60));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "pri", "Qt include files", 60));

    return filter;
}

std::unique_ptr<AdvancedFileFilter> FilterPresets::createMinimalFilter() {
    auto filter = std::make_unique<AdvancedFileFilter>();
    filter->clearRules();

    // Only the most essential files
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "json", "JSON files", 100));
    filter->addRule(createRule(FilterRule::Type::Include, FilterRule::MatchType::Extension, "qml", "QML files", 100));

    // Essential exclusions
    filter->addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, ".git", "Git directories", 200));
    filter->addRule(createRule(FilterRule::Type::Exclude, FilterRule::MatchType::Directory, "build", "Build directories", 200));

    return filter;
}

} // namespace DeclarativeUI::HotReload
