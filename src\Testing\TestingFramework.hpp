#pragma once

#include <QObject>
#include <QTest>
#include <QApplication>
#include <QWidget>
#include <QTimer>
#include <QSignalSpy>
#include <QElapsedTimer>
#include <QTemporaryDir>
#include <QTemporaryFile>
#include <QScreen>
#include <QPixmap>
#include <QBuffer>
#include <QCryptographicHash>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QDir>
#include <QFileInfo>
#include <QProcess>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QAtomicInt>
#include <QRandomGenerator>

#include <memory>
#include <functional>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <thread>
#include <future>
#include <regex>

/**
 * @brief Comprehensive Testing Framework for DeclarativeUI
 * 
 * This framework provides advanced testing capabilities including:
 * - UI automation and interaction simulation
 * - Visual regression testing with screenshot comparison
 * - Component testing helpers with mock objects
 * - Performance benchmarking and profiling
 * - Integration with popular testing frameworks
 * - Accessibility testing utilities
 * - Memory leak detection
 * - Concurrent testing support
 */
namespace DeclarativeUI::Testing {

// **Forward Declarations**
class TestRunner;
class UIAutomation;
class VisualTesting;
class ComponentTester;
class PerformanceBenchmark;
class AccessibilityTester;
class MemoryProfiler;
class TestReporter;

/**
 * @brief Test execution result
 */
struct TestResult {
    enum class Status {
        Passed,
        Failed,
        Skipped,
        Error
    };
    
    Status status = Status::Failed;
    QString test_name;
    QString description;
    QString error_message;
    qint64 execution_time_ms = 0;
    QJsonObject metadata;
    
    bool isPassed() const { return status == Status::Passed; }
    bool isFailed() const { return status == Status::Failed; }
    bool isSkipped() const { return status == Status::Skipped; }
    bool isError() const { return status == Status::Error; }
};

/**
 * @brief Test suite configuration
 */
struct TestSuiteConfig {
    QString name;
    QString description;
    QString output_directory;
    bool enable_visual_testing = true;
    bool enable_performance_testing = true;
    bool enable_accessibility_testing = false;
    bool enable_memory_profiling = false;
    bool enable_concurrent_testing = false;
    bool generate_html_report = true;
    bool generate_junit_xml = false;
    int timeout_seconds = 30;
    int retry_count = 0;
    QStringList tags;
    QJsonObject custom_config;
};

/**
 * @brief Base class for all test cases
 */
class TestCase : public QObject {
    Q_OBJECT

public:
    explicit TestCase(const QString& name, QObject* parent = nullptr);
    virtual ~TestCase() = default;

    // **Test Lifecycle**
    virtual void setUp() {}
    virtual void tearDown() {}
    virtual void run() = 0;

    // **Test Information**
    QString getName() const { return name_; }
    QString getDescription() const { return description_; }
    QStringList getTags() const { return tags_; }
    TestResult getResult() const { return result_; }

    // **Test Configuration**
    TestCase& setDescription(const QString& description);
    TestCase& addTag(const QString& tag);
    TestCase& setTimeout(int seconds);
    TestCase& setRetryCount(int count);
    TestCase& setMetadata(const QString& key, const QVariant& value);

    // **Test Assertions**
    void assertTrue(bool condition, const QString& message = "");
    void assertFalse(bool condition, const QString& message = "");
    void assertEqual(const QVariant& expected, const QVariant& actual, const QString& message = "");
    void assertNotEqual(const QVariant& expected, const QVariant& actual, const QString& message = "");
    void assertNull(const QVariant& value, const QString& message = "");
    void assertNotNull(const QVariant& value, const QString& message = "");
    void assertThrows(std::function<void()> func, const QString& message = "");
    void assertNoThrow(std::function<void()> func, const QString& message = "");

    // **Widget Assertions**
    void assertWidgetVisible(QWidget* widget, const QString& message = "");
    void assertWidgetHidden(QWidget* widget, const QString& message = "");
    void assertWidgetEnabled(QWidget* widget, const QString& message = "");
    void assertWidgetDisabled(QWidget* widget, const QString& message = "");
    void assertWidgetText(QWidget* widget, const QString& expected_text, const QString& message = "");
    void assertWidgetGeometry(QWidget* widget, const QRect& expected_geometry, const QString& message = "");

    // **Signal/Slot Testing**
    void assertSignalEmitted(QObject* sender, const char* signal, int timeout_ms = 5000);
    void assertSignalNotEmitted(QObject* sender, const char* signal, int timeout_ms = 1000);
    int getSignalEmissionCount(QObject* sender, const char* signal);

    // **Performance Assertions**
    void assertExecutionTime(std::function<void()> func, qint64 max_time_ms, const QString& message = "");
    void assertMemoryUsage(std::function<void()> func, qint64 max_memory_kb, const QString& message = "");

protected:
    void setResult(TestResult::Status status, const QString& error_message = "");
    void addResultMetadata(const QString& key, const QVariant& value);

signals:
    void testStarted(const QString& test_name);
    void testFinished(const TestResult& result);
    void assertionFailed(const QString& message);

private:
    QString name_;
    QString description_;
    QStringList tags_;
    TestResult result_;
    int timeout_seconds_ = 30;
    int retry_count_ = 0;
    QJsonObject metadata_;
    std::vector<std::unique_ptr<QSignalSpy>> signal_spies_;
};

/**
 * @brief UI automation utilities for simulating user interactions
 */
class UIAutomation : public QObject {
    Q_OBJECT

public:
    explicit UIAutomation(QObject* parent = nullptr);

    // **Mouse Interactions**
    void clickWidget(QWidget* widget, Qt::MouseButton button = Qt::LeftButton);
    void doubleClickWidget(QWidget* widget, Qt::MouseButton button = Qt::LeftButton);
    void rightClickWidget(QWidget* widget);
    void dragWidget(QWidget* from_widget, QWidget* to_widget);
    void hoverWidget(QWidget* widget, int duration_ms = 500);

    // **Keyboard Interactions**
    void typeText(QWidget* widget, const QString& text);
    void pressKey(QWidget* widget, Qt::Key key, Qt::KeyboardModifiers modifiers = Qt::NoModifier);
    void pressKeySequence(QWidget* widget, const QKeySequence& sequence);

    // **Widget Finding**
    QWidget* findWidget(QWidget* parent, const QString& object_name);
    QWidget* findWidgetByText(QWidget* parent, const QString& text);
    QWidget* findWidgetByType(QWidget* parent, const QString& type_name);
    std::vector<QWidget*> findAllWidgets(QWidget* parent, const QString& object_name);

    // **Wait Operations**
    bool waitForWidget(QWidget* parent, const QString& object_name, int timeout_ms = 5000);
    bool waitForWidgetVisible(QWidget* widget, int timeout_ms = 5000);
    bool waitForWidgetEnabled(QWidget* widget, int timeout_ms = 5000);
    bool waitForCondition(std::function<bool()> condition, int timeout_ms = 5000);

    // **Simulation Settings**
    void setSimulationSpeed(double speed_factor);  // 1.0 = normal, 0.5 = half speed, 2.0 = double speed
    void enableUserDelay(bool enable);
    void setUserDelayRange(int min_ms, int max_ms);

private:
    void simulateUserDelay();
    QPoint getWidgetCenter(QWidget* widget);

    double simulation_speed_ = 1.0;
    bool user_delay_enabled_ = true;
    int user_delay_min_ms_ = 50;
    int user_delay_max_ms_ = 200;
};

/**
 * @brief Visual regression testing utilities
 */
class VisualTesting : public QObject {
    Q_OBJECT

public:
    explicit VisualTesting(QObject* parent = nullptr);

    // **Screenshot Operations**
    QPixmap captureWidget(QWidget* widget);
    QPixmap captureScreen();
    QPixmap captureRegion(const QRect& region);

    // **Visual Comparison**
    struct ComparisonResult {
        bool matches = false;
        double similarity_percentage = 0.0;
        QPixmap difference_image;
        QString error_message;
        QJsonObject metadata;
    };

    ComparisonResult compareImages(const QPixmap& expected, const QPixmap& actual, double tolerance = 0.95);
    ComparisonResult compareWithBaseline(QWidget* widget, const QString& baseline_name, double tolerance = 0.95);

    // **Baseline Management**
    void saveBaseline(QWidget* widget, const QString& baseline_name);
    void updateBaseline(QWidget* widget, const QString& baseline_name);
    bool hasBaseline(const QString& baseline_name);
    void deleteBaseline(const QString& baseline_name);
    QStringList listBaselines();

    // **Configuration**
    void setBaselineDirectory(const QString& directory);
    void setOutputDirectory(const QString& directory);
    void setImageFormat(const QString& format);  // PNG, JPEG, etc.
    void setCompressionQuality(int quality);     // 0-100

    // **Advanced Features**
    void maskRegion(const QRect& region);  // Ignore specific regions during comparison
    void clearMasks();
    void setPixelTolerance(int tolerance);  // Tolerance for individual pixel differences

signals:
    void baselineSaved(const QString& baseline_name);
    void comparisonCompleted(const ComparisonResult& result);

private:
    QString generateImageHash(const QPixmap& image);
    QPixmap createDifferenceImage(const QPixmap& expected, const QPixmap& actual);
    double calculateSimilarity(const QPixmap& expected, const QPixmap& actual);

    QString baseline_directory_;
    QString output_directory_;
    QString image_format_ = "PNG";
    int compression_quality_ = 90;
    int pixel_tolerance_ = 5;
    std::vector<QRect> masked_regions_;
};

/**
 * @brief Component testing utilities with mock support
 */
class ComponentTester : public QObject {
    Q_OBJECT

public:
    explicit ComponentTester(QObject* parent = nullptr);

    // **Component Creation**
    template<typename T>
    std::unique_ptr<T> createComponent(const QJsonObject& config = {});

    template<typename T>
    std::unique_ptr<T> createMockComponent(const QJsonObject& mock_config = {});

    // **Component Validation**
    bool validateComponent(QWidget* component, const QJsonObject& expected_properties);
    bool validateComponentHierarchy(QWidget* root, const QJsonObject& expected_structure);
    bool validateComponentBehavior(QWidget* component, const QJsonObject& behavior_tests);

    // **Mock Objects**
    class MockWidget;
    class MockSignalEmitter;
    class MockEventFilter;

    std::unique_ptr<MockWidget> createMockWidget(const QString& type_name);
    std::unique_ptr<MockSignalEmitter> createMockSignalEmitter();
    std::unique_ptr<MockEventFilter> createMockEventFilter();

    // **Component Lifecycle Testing**
    void testComponentCreation(const QString& component_type, const QJsonObject& config);
    void testComponentDestruction(QWidget* component);
    void testComponentProperties(QWidget* component, const QJsonObject& properties);
    void testComponentEvents(QWidget* component, const QJsonArray& events);

    // **Integration Testing**
    void testComponentInteraction(QWidget* component1, QWidget* component2, const QJsonObject& interaction);
    void testComponentDataFlow(const std::vector<QWidget*>& components, const QJsonObject& data_flow);

signals:
    void componentCreated(QWidget* component);
    void componentDestroyed(QWidget* component);
    void validationCompleted(bool success, const QString& message);

private:
    std::unordered_map<QString, std::function<QWidget*()>> component_factories_;
    std::vector<std::unique_ptr<QWidget>> created_components_;
};

/**
 * @brief Performance benchmarking and profiling utilities
 */
class PerformanceBenchmark : public QObject {
    Q_OBJECT

public:
    explicit PerformanceBenchmark(QObject* parent = nullptr);

    // **Timing Measurements**
    struct TimingResult {
        qint64 total_time_ms = 0;
        qint64 average_time_ms = 0;
        qint64 min_time_ms = 0;
        qint64 max_time_ms = 0;
        qint64 median_time_ms = 0;
        double standard_deviation = 0.0;
        int iteration_count = 0;
        QJsonObject metadata;
    };

    TimingResult measureFunction(std::function<void()> func, int iterations = 100);
    TimingResult measureWidgetCreation(const QString& widget_type, int iterations = 100);
    TimingResult measureLayoutUpdate(QWidget* widget, int iterations = 100);
    TimingResult measurePaintEvent(QWidget* widget, int iterations = 100);

    // **Memory Measurements**
    struct MemoryResult {
        qint64 initial_memory_kb = 0;
        qint64 peak_memory_kb = 0;
        qint64 final_memory_kb = 0;
        qint64 memory_delta_kb = 0;
        qint64 leaked_memory_kb = 0;
        int allocation_count = 0;
        int deallocation_count = 0;
        QJsonObject metadata;
    };

    MemoryResult measureMemoryUsage(std::function<void()> func);
    MemoryResult measureWidgetMemoryUsage(QWidget* widget);

    // **CPU Profiling**
    struct CPUResult {
        double cpu_usage_percentage = 0.0;
        qint64 user_time_ms = 0;
        qint64 system_time_ms = 0;
        qint64 total_time_ms = 0;
        int context_switches = 0;
        QJsonObject metadata;
    };

    CPUResult measureCPUUsage(std::function<void()> func);

    // **Benchmark Comparison**
    bool compareWithBaseline(const QString& benchmark_name, const TimingResult& result, double tolerance = 0.1);
    void saveBaseline(const QString& benchmark_name, const TimingResult& result);
    void generatePerformanceReport(const QString& output_file);

signals:
    void benchmarkStarted(const QString& benchmark_name);
    void benchmarkCompleted(const QString& benchmark_name, const TimingResult& result);

private:
    qint64 getCurrentMemoryUsage();
    double getCurrentCPUUsage();

    std::unordered_map<QString, TimingResult> baselines_;
    std::vector<std::pair<QString, TimingResult>> benchmark_results_;
};

} // namespace DeclarativeUI::Testing
