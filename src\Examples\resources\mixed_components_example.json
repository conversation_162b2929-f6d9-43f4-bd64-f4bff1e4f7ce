{"type": "Container", "properties": {"layout": "VBox", "spacing": 15, "margins": 20, "windowTitle": "Mixed Components Example", "minWidth": 700, "minHeight": 600, "style": "background-color: #f8f9fa;"}, "children": [{"type": "Label", "properties": {"text": "🔗 Mixed Legacy & Command Components", "style": "font-size: 22px; font-weight: bold; color: white; background-color: #6f42c1; padding: 15px; border-radius: 8px; text-align: center;"}}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 20}, "children": [{"type": "Container", "properties": {"layout": "VBox", "spacing": 12, "style": "background-color: white; padding: 18px; border-radius: 8px; border: 2px solid #e74c3c; flex: 1;"}, "children": [{"type": "Label", "properties": {"text": "📦 Legacy Components", "style": "font-size: 16px; font-weight: bold; color: #e74c3c; margin-bottom: 8px;"}}, {"type": "LegacyButton", "properties": {"text": "Legacy Button", "style": "background-color: #e74c3c; color: white; padding: 10px; border-radius: 4px; font-weight: bold;"}, "events": {"clicked": "onLegacyButtonClicked"}}, {"type": "LegacyTextInput", "properties": {"placeholder": "Legacy input field...", "style": "padding: 8px; border: 2px solid #e74c3c; border-radius: 4px;"}, "events": {"textChanged": "onLegacyTextChanged"}}, {"type": "Label", "properties": {"text": "These components use the traditional DeclarativeUI system", "style": "font-size: 12px; color: #7f8c8d; font-style: italic; margin-top: 8px;"}}]}, {"type": "Container", "properties": {"layout": "VBox", "spacing": 12, "style": "background-color: white; padding: 18px; border-radius: 8px; border: 2px solid #3498db; flex: 1;"}, "children": [{"type": "Label", "properties": {"text": "⚡ Command Components", "style": "font-size: 16px; font-weight: bold; color: #3498db; margin-bottom: 8px;"}}, {"type": "<PERSON><PERSON>", "properties": {"text": "Command Button", "style": "background-color: #3498db; color: white; padding: 10px; border-radius: 4px; font-weight: bold;"}, "events": {"clicked": "onCommandButtonClicked"}}, {"type": "TextInput", "properties": {"placeholder": "Command input field...", "style": "padding: 8px; border: 2px solid #3498db; border-radius: 4px;"}, "bindings": {"text": "command.input.value"}, "events": {"textChanged": "onCommandTextChanged"}}, {"type": "Label", "properties": {"text": "These components use the new Command-based system", "style": "font-size: 12px; color: #7f8c8d; font-style: italic; margin-top: 8px;"}}]}]}, {"type": "Container", "properties": {"layout": "VBox", "spacing": 12, "style": "background-color: white; padding: 18px; border-radius: 8px; border: 2px solid #9b59b6;"}, "children": [{"type": "Label", "properties": {"text": "🔄 Hybrid Integration Section", "style": "font-size: 16px; font-weight: bold; color: #9b59b6; margin-bottom: 8px;"}}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 10}, "children": [{"type": "AdaptedLegacyButton", "properties": {"text": "Adapted Legacy", "style": "background-color: #9b59b6; color: white; padding: 8px 12px; border-radius: 4px; flex: 1;"}, "events": {"clicked": "onAdaptedLegacyClicked"}}, {"type": "<PERSON><PERSON>", "properties": {"text": "Native Command", "style": "background-color: #8e44ad; color: white; padding: 8px 12px; border-radius: 4px; flex: 1;"}, "events": {"clicked": "onNativeCommandClicked"}}, {"type": "HybridComponent", "properties": {"text": "Hybrid Component", "style": "background-color: #6f42c1; color: white; padding: 8px 12px; border-radius: 4px; flex: 1;"}, "events": {"clicked": "onHybridComponentClicked"}}]}, {"type": "Label", "properties": {"text": "This section demonstrates seamless integration between legacy and command components", "style": "font-size: 12px; color: #7f8c8d; font-style: italic; margin-top: 8px;"}}]}, {"type": "Container", "properties": {"layout": "VBox", "spacing": 10, "style": "background-color: white; padding: 18px; border-radius: 8px; border: 2px solid #28a745;"}, "children": [{"type": "Label", "properties": {"text": "📊 State Synchronization Demo", "style": "font-size: 16px; font-weight: bold; color: #28a745; margin-bottom: 8px;"}}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 15}, "children": [{"type": "Container", "properties": {"layout": "VBox", "spacing": 8, "style": "flex: 1;"}, "children": [{"type": "Label", "properties": {"text": "Shared Counter:", "style": "font-weight: bold; color: #495057;"}}, {"type": "Label", "properties": {"text": "0", "style": "font-size: 20px; color: #28a745; font-weight: bold; text-align: center; padding: 8px; background-color: #f8f9fa; border-radius: 4px;"}, "bindings": {"text": "shared.counter"}}]}, {"type": "Container", "properties": {"layout": "VBox", "spacing": 5, "style": "flex: 2;"}, "children": [{"type": "<PERSON><PERSON>", "properties": {"text": "Legacy +1", "style": "background-color: #e74c3c; color: white; padding: 6px; border-radius: 3px; margin-bottom: 3px;"}, "events": {"clicked": "incrementLegacyCounter"}}, {"type": "<PERSON><PERSON>", "properties": {"text": "Command +5", "style": "background-color: #3498db; color: white; padding: 6px; border-radius: 3px; margin-bottom: 3px;"}, "events": {"clicked": "incrementCommandCounter"}}, {"type": "<PERSON><PERSON>", "properties": {"text": "Reset All", "style": "background-color: #6c757d; color: white; padding: 6px; border-radius: 3px;"}, "events": {"clicked": "resetSharedCounter"}}]}]}, {"type": "Label", "properties": {"text": "Both legacy and command components can modify the same shared state", "style": "font-size: 12px; color: #7f8c8d; font-style: italic; margin-top: 8px;"}}]}, {"type": "Container", "properties": {"layout": "HBox", "spacing": 10, "style": "background-color: #343a40; color: white; padding: 12px; border-radius: 4px; margin-top: 10px;"}, "children": [{"type": "Label", "properties": {"text": "🎯 Mixed Components Demo - Legacy & Command Integration", "style": "font-size: 12px; color: #adb5bd; flex: 1;"}}, {"type": "Label", "properties": {"text": "Integration: Active", "style": "font-size: 12px; color: #28a745; font-family: monospace;"}, "bindings": {"text": "integration.status"}}]}]}