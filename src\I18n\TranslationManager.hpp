#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QLocale>
#include <QTranslator>
#include <QJsonObject>
#include <QJsonDocument>
#include <QHash>
#include <QMutex>
#include <QDir>
#include <QCoreApplication>
#include <QDebug>
#include <memory>
#include <functional>

namespace DeclarativeUI {
namespace I18n {

/**
 * @brief Comprehensive translation and internationalization manager
 * 
 * Features:
 * - Dynamic language switching
 * - Hierarchical translation keys
 * - Pluralization support
 * - Context-aware translations
 * - Fallback language support
 * - Hot reload of translation files
 * - Performance optimized caching
 */
class TranslationManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Translation context for scoped translations
     */
    struct TranslationContext {
        QString namespace_prefix;
        QString component_name;
        QJsonObject metadata;
        
        TranslationContext(const QString& ns = QString(), 
                          const QString& component = QString())
            : namespace_prefix(ns), component_name(component) {}
    };

    /**
     * @brief Pluralization rules for different languages
     */
    enum class PluralForm {
        Zero,    // 0 items
        One,     // 1 item
        Two,     // 2 items (for languages with dual)
        Few,     // few items (2-4 in some languages)
        Many,    // many items (5+ in some languages)
        Other    // default/other
    };

    /**
     * @brief Translation parameters for dynamic content
     */
    struct TranslationParams {
        QHash<QString, QVariant> named_params;
        QVariantList positional_params;
        int count = -1; // For pluralization
        
        TranslationParams& param(const QString& key, const QVariant& value) {
            named_params[key] = value;
            return *this;
        }
        
        TranslationParams& count(int n) {
            this->count = n;
            return *this;
        }
    };

    /**
     * @brief Get singleton instance
     */
    static TranslationManager& instance();

    /**
     * @brief Initialize translation system
     */
    bool initialize(const QString& translations_dir = "translations");

    /**
     * @brief Set current language
     */
    bool setLanguage(const QString& language_code);
    bool setLanguage(const QLocale& locale);

    /**
     * @brief Get current language
     */
    QString getCurrentLanguage() const { return current_language_; }
    QLocale getCurrentLocale() const { return current_locale_; }

    /**
     * @brief Get available languages
     */
    QStringList getAvailableLanguages() const { return available_languages_; }

    /**
     * @brief Add fallback language
     */
    void addFallbackLanguage(const QString& language_code);
    void setFallbackLanguages(const QStringList& languages);

    /**
     * @brief Load translation file
     */
    bool loadTranslationFile(const QString& file_path, const QString& language_code = QString());

    /**
     * @brief Core translation methods
     */
    QString translate(const QString& key, const TranslationParams& params = TranslationParams()) const;
    QString translate(const QString& key, const QString& default_text, const TranslationParams& params = TranslationParams()) const;
    QString translateWithContext(const TranslationContext& context, const QString& key, const TranslationParams& params = TranslationParams()) const;

    /**
     * @brief Convenience translation methods
     */
    QString tr(const QString& key) const { return translate(key); }
    QString tr(const QString& key, const QString& default_text) const { return translate(key, default_text); }
    QString tr(const QString& key, int count) const { return translate(key, TranslationParams().count(count)); }

    /**
     * @brief Pluralization support
     */
    QString translatePlural(const QString& key, int count, const TranslationParams& params = TranslationParams()) const;

    /**
     * @brief Context management
     */
    TranslationContext createContext(const QString& namespace_prefix, const QString& component_name = QString()) const;
    void pushContext(const TranslationContext& context);
    void popContext();
    TranslationContext getCurrentContext() const;

    /**
     * @brief Translation validation and debugging
     */
    bool hasTranslation(const QString& key) const;
    QStringList getMissingTranslations() const { return missing_translations_; }
    void clearMissingTranslations() { missing_translations_.clear(); }

    /**
     * @brief Hot reload support
     */
    void enableHotReload(bool enable = true);
    void reloadTranslations();

    /**
     * @brief Performance and caching
     */
    void clearCache();
    void preloadTranslations(const QStringList& keys);

signals:
    /**
     * @brief Emitted when language changes
     */
    void languageChanged(const QString& new_language, const QString& old_language);

    /**
     * @brief Emitted when translations are reloaded
     */
    void translationsReloaded();

    /**
     * @brief Emitted when a translation is missing
     */
    void translationMissing(const QString& key, const QString& language);

private:
    explicit TranslationManager(QObject* parent = nullptr);
    ~TranslationManager() override = default;

    // Core translation logic
    QString resolveTranslation(const QString& full_key, const TranslationParams& params) const;
    QString lookupTranslation(const QString& language, const QString& key) const;
    QString applyParameters(const QString& text, const TranslationParams& params) const;
    PluralForm getPluralForm(int count, const QString& language) const;
    QString buildFullKey(const QString& key) const;

    // File management
    bool loadJsonTranslationFile(const QString& file_path, const QString& language_code);
    bool loadQtTranslationFile(const QString& file_path, const QString& language_code);
    void scanForTranslationFiles();

    // Caching and performance
    mutable QHash<QString, QString> translation_cache_;
    mutable QMutex cache_mutex_;

    // Translation data
    QHash<QString, QJsonObject> translations_; // language -> translations
    QStringList available_languages_;
    QStringList fallback_languages_;
    QString current_language_;
    QLocale current_locale_;

    // Context management
    QList<TranslationContext> context_stack_;

    // Configuration
    QString translations_directory_;
    bool hot_reload_enabled_;
    bool cache_enabled_;

    // Debugging and validation
    mutable QStringList missing_translations_;
    bool track_missing_translations_;

    // Qt translators for .qm files
    QHash<QString, std::unique_ptr<QTranslator>> qt_translators_;

    static TranslationManager* instance_;
    static QMutex instance_mutex_;
};

/**
 * @brief Convenience macros for translation
 */
#define DUI_TR(key) DeclarativeUI::I18n::TranslationManager::instance().tr(key)
#define DUI_TR_DEFAULT(key, default_text) DeclarativeUI::I18n::TranslationManager::instance().tr(key, default_text)
#define DUI_TR_PLURAL(key, count) DeclarativeUI::I18n::TranslationManager::instance().translatePlural(key, count)
#define DUI_TR_CTX(context, key) DeclarativeUI::I18n::TranslationManager::instance().translateWithContext(context, key)

} // namespace I18n
} // namespace DeclarativeUI
