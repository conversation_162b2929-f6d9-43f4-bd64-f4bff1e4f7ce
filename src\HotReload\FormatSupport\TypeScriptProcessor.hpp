#pragma once

#include "IFormatProcessor.hpp"
#include <QProcess>
#include <QTemporaryDir>
#include <QJsonDocument>
#include <QJsonArray>
#include <QStandardPaths>
#include <memory>

namespace DeclarativeUI::HotReload::FormatSupport {

/**
 * @brief TypeScript and JavaScript processor with transpilation support
 */
class TypeScriptProcessor : public IFormatProcessor {
    Q_OBJECT

public:
    explicit TypeScriptProcessor(QObject* parent = nullptr);
    ~TypeScriptProcessor() override = default;

    // IFormatProcessor interface
    QString getFormatName() const override { return "TypeScript"; }
    QStringList getSupportedExtensions() const override { return {"ts", "tsx", "js", "jsx", "mjs"}; }
    bool canProcess(const QString& file_path) const override;
    
    ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    
    bool validateFile(const QString& file_path) const override;
    bool validateContent(const QString& content) const override;
    
    ProcessingConfig getDefaultConfig() const override;
    QStringList getRequiredDependencies() const override;
    bool isAvailable() const override;
    
    // Hot reload specific methods
    bool supportsLiveInjection() const override { return false; } // JS/TS typically doesn't support live injection like CSS
    ProcessingResult prepareLiveInjection(const QString& content, const QString& file_path) override;

    // TypeScript-specific configuration
    void setTypeScriptCompilerPath(const QString& path);
    void setCompilerOptions(const QJsonObject& options);
    void enableTypeChecking(bool enabled);
    void setTargetECMAScript(const QString& target); // "ES5", "ES2015", "ES2017", etc.
    void setModuleSystem(const QString& module); // "commonjs", "amd", "es6", "umd", etc.
    
    // Transpilation options
    void enableBabelTranspilation(bool enabled);
    void setBabelPresets(const QStringList& presets);
    void setBabelPlugins(const QStringList& plugins);
    
    // Validation and linting
    void enableESLint(bool enabled);
    void setESLintConfig(const QString& config_path);
    void enablePrettier(bool enabled);
    void setPrettierConfig(const QString& config_path);
    
    // Source maps
    void enableSourceMaps(bool enabled);
    void setSourceMapType(const QString& type); // "inline", "external", "both"

signals:
    void transpilationStarted(const QString& file_path);
    void transpilationFinished(const QString& file_path, bool success);
    void typeCheckingCompleted(const QString& file_path, const QStringList& errors);
    void lintingCompleted(const QString& file_path, const QStringList& warnings);

private:
    struct CompilerOptions {
        QString target = "ES2017";
        QString module = "commonjs";
        bool strict = true;
        bool declaration = false;
        bool sourceMap = false;
        QString outDir;
        QStringList lib = {"ES2017", "DOM"};
        QJsonObject custom_options;
    };
    
    struct BabelOptions {
        QStringList presets = {"@babel/preset-env"};
        QStringList plugins;
        QJsonObject custom_options;
    };
    
    struct LintingOptions {
        bool eslint_enabled = false;
        QString eslint_config;
        bool prettier_enabled = false;
        QString prettier_config;
    };
    
    // Processing methods
    ProcessingResult transpileTypeScript(const QString& content, const QString& file_path, const ProcessingConfig& config);
    ProcessingResult transpileBabel(const QString& content, const QString& file_path, const ProcessingConfig& config);
    ProcessingResult processJavaScript(const QString& content, const QString& file_path, const ProcessingConfig& config);
    
    // Validation methods
    QStringList performTypeChecking(const QString& content, const QString& file_path);
    QStringList performESLinting(const QString& content, const QString& file_path);
    QStringList performPrettierCheck(const QString& content, const QString& file_path);
    
    // Utility methods
    bool isTypeScriptFile(const QString& file_path) const;
    bool isJavaScriptFile(const QString& file_path) const;
    bool isReactFile(const QString& file_path) const;
    
    QString createTempFile(const QString& content, const QString& extension);
    QString runCompilerProcess(const QString& command, const QStringList& arguments, const QString& input = QString());
    QJsonObject createTSConfig(const CompilerOptions& options);
    QJsonObject createBabelConfig(const BabelOptions& options);
    
    // Dependency checking
    bool checkTypeScriptCompiler() const;
    bool checkBabelCompiler() const;
    bool checkESLint() const;
    bool checkPrettier() const;
    
    QString findExecutable(const QString& name) const;
    QString getNodeModulesPath() const;
    
    // Configuration
    QString typescript_compiler_path_;
    CompilerOptions compiler_options_;
    BabelOptions babel_options_;
    LintingOptions linting_options_;
    
    bool type_checking_enabled_ = true;
    bool babel_transpilation_enabled_ = false;
    bool source_maps_enabled_ = false;
    QString source_map_type_ = "external";
    
    // Temporary directory for compilation
    std::unique_ptr<QTemporaryDir> temp_dir_;
    
    // Cached availability checks
    mutable bool availability_checked_ = false;
    mutable bool typescript_available_ = false;
    mutable bool babel_available_ = false;
    mutable bool eslint_available_ = false;
    mutable bool prettier_available_ = false;
};

/**
 * @brief JavaScript-only processor (simpler version without TypeScript features)
 */
class JavaScriptProcessor : public IFormatProcessor {
    Q_OBJECT

public:
    explicit JavaScriptProcessor(QObject* parent = nullptr);
    ~JavaScriptProcessor() override = default;

    // IFormatProcessor interface
    QString getFormatName() const override { return "JavaScript"; }
    QStringList getSupportedExtensions() const override { return {"js", "jsx", "mjs", "cjs"}; }
    bool canProcess(const QString& file_path) const override;
    
    ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    
    bool validateFile(const QString& file_path) const override;
    bool validateContent(const QString& content) const override;
    
    ProcessingConfig getDefaultConfig() const override;
    QStringList getRequiredDependencies() const override { return QStringList(); }
    bool isAvailable() const override { return true; }
    
    // Hot reload specific methods
    bool supportsLiveInjection() const override { return false; }
    ProcessingResult prepareLiveInjection(const QString& content, const QString& file_path) override;

private:
    // Basic JavaScript validation and processing
    bool validateJavaScriptSyntax(const QString& content) const;
    QStringList findJavaScriptErrors(const QString& content) const;
    QString formatJavaScript(const QString& content) const;
    QString minifyJavaScript(const QString& content) const;
};

} // namespace DeclarativeUI::HotReload::FormatSupport
