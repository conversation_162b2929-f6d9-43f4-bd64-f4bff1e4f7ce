#include "I18nManager.hpp"
#include <QDebug>
#include <QDir>
#include <QCoreApplication>

namespace DeclarativeUI {
namespace I18n {

I18nManager* I18nManager::instance_ = nullptr;
QMutex I18nManager::instance_mutex_;

I18nManager& I18nManager::instance() {
    QMutexLocker locker(&instance_mutex_);
    if (!instance_) {
        instance_ = new I18nManager();
    }
    return *instance_;
}

I18nManager::I18nManager(QObject* parent)
    : QObject(parent)
    , initialized_(false)
    , translation_manager_(TranslationManager::instance())
    , locale_manager_(LocaleManager::instance())
    , rtl_support_(RTLSupport::instance())
{
    qDebug() << "🌍 I18nManager created";
}

bool I18nManager::initialize(const I18nConfiguration& config) {
    if (initialized_) {
        qWarning() << "I18nManager already initialized";
        return true;
    }
    
    config_ = config;
    
    // Initialize translation manager
    if (!translation_manager_.initialize(config.translations_directory)) {
        qWarning() << "Failed to initialize TranslationManager";
        return false;
    }
    
    // Set fallback languages
    translation_manager_.setFallbackLanguages(config.fallback_languages);
    
    // Enable hot reload if requested
    if (config.enable_hot_reload) {
        translation_manager_.enableHotReload(true);
    }
    
    // Initialize RTL support
    if (config.enable_rtl_support) {
        rtl_support_.enableGlobalRTLSupport(config.enable_global_rtl);
    }
    
    // Set default language
    if (!config.default_language.isEmpty()) {
        setLanguage(config.default_language);
    }
    
    // Connect signals
    connectSignals();
    
    // Update language information
    updateLanguageInfo();
    
    initialized_ = true;
    
    qDebug() << "🌍 I18nManager initialized successfully";
    qDebug() << "   Translations directory:" << config.translations_directory;
    qDebug() << "   Default language:" << config.default_language;
    qDebug() << "   Available languages:" << getAvailableLanguages();
    
    return true;
}

bool I18nManager::setLanguage(const QString& language_code) {
    if (!initialized_) {
        qWarning() << "I18nManager not initialized";
        return false;
    }
    
    QString old_language = getCurrentLanguage();
    
    // Set language in translation manager
    if (!translation_manager_.setLanguage(language_code)) {
        return false;
    }
    
    // Set corresponding locale
    QLocale locale(language_code);
    locale_manager_.setLocale(locale);
    
    // Update RTL support
    if (config_.enable_rtl_support) {
        rtl_support_.setLayoutDirection(locale);
    }
    
    // Sync all components
    syncComponents();
    
    qDebug() << "🌍 Language changed from" << old_language << "to" << language_code;
    emit languageChanged(language_code, old_language);
    
    return true;
}

bool I18nManager::setLanguage(const QLocale& locale) {
    return setLanguage(locale.name().left(2));
}

QString I18nManager::getCurrentLanguage() const {
    return translation_manager_.getCurrentLanguage();
}

QLocale I18nManager::getCurrentLocale() const {
    return locale_manager_.getCurrentLocale();
}

QStringList I18nManager::getAvailableLanguages() const {
    return translation_manager_.getAvailableLanguages();
}

QList<I18nManager::LanguageInfo> I18nManager::getLanguageInfoList() const {
    QList<LanguageInfo> info_list;
    for (auto it = language_info_.begin(); it != language_info_.end(); ++it) {
        info_list.append(it.value());
    }
    return info_list;
}

QString I18nManager::translate(const QString& key, const TranslationManager::TranslationParams& params) const {
    return translation_manager_.translate(key, params);
}

QString I18nManager::translate(const QString& key, const QString& default_text, const TranslationManager::TranslationParams& params) const {
    return translation_manager_.translate(key, default_text, params);
}

QString I18nManager::translatePlural(const QString& key, int count, const TranslationManager::TranslationParams& params) const {
    return translation_manager_.translatePlural(key, count, params);
}

QString I18nManager::formatNumber(double number, const LocaleManager::NumberFormatOptions& options) const {
    return locale_manager_.formatNumber(number, options);
}

QString I18nManager::formatCurrency(double amount, const LocaleManager::CurrencyFormatOptions& options) const {
    return locale_manager_.formatCurrency(amount, options);
}

QString I18nManager::formatDate(const QDate& date, const LocaleManager::DateTimeFormatOptions& options) const {
    return locale_manager_.formatDate(date, options);
}

QString I18nManager::formatTime(const QTime& time, const LocaleManager::DateTimeFormatOptions& options) const {
    return locale_manager_.formatTime(time, options);
}

QString I18nManager::formatDateTime(const QDateTime& datetime, const LocaleManager::DateTimeFormatOptions& options) const {
    return locale_manager_.formatDateTime(datetime, options);
}

void I18nManager::applyRTLSupport(QWidget* widget, const RTLSupport::RTLConfiguration& config) const {
    if (config_.enable_rtl_support) {
        rtl_support_.applyRTLSupport(widget, config);
    }
}

void I18nManager::applyRTLSupportRecursive(QWidget* widget, const RTLSupport::RTLConfiguration& config) const {
    if (config_.enable_rtl_support) {
        rtl_support_.applyRTLSupportRecursive(widget, config);
    }
}

bool I18nManager::isRightToLeft() const {
    return config_.enable_rtl_support && rtl_support_.isRightToLeft();
}

Qt::Alignment I18nManager::convertAlignment(Qt::Alignment alignment) const {
    if (config_.enable_rtl_support) {
        return rtl_support_.convertAlignment(alignment);
    }
    return alignment;
}

void I18nManager::internationalizeWidget(QWidget* widget) {
    if (!widget) {
        return;
    }
    
    // Apply RTL support
    if (config_.enable_rtl_support) {
        rtl_support_.applyRTLSupport(widget);
    }
    
    // Additional widget internationalization could be added here
    // such as automatic text translation, date/time formatting, etc.
}

void I18nManager::internationalizeWidgetRecursive(QWidget* widget) {
    if (!widget) {
        return;
    }
    
    // Apply RTL support recursively
    if (config_.enable_rtl_support) {
        rtl_support_.applyRTLSupportRecursive(widget);
    }
    
    // Additional recursive internationalization could be added here
}

void I18nManager::updateConfiguration(const I18nConfiguration& config) {
    config_ = config;
    
    // Update components with new configuration
    translation_manager_.setFallbackLanguages(config.fallback_languages);
    translation_manager_.enableHotReload(config.enable_hot_reload);
    
    if (config.enable_rtl_support) {
        rtl_support_.enableGlobalRTLSupport(config.enable_global_rtl);
    }
    
    qDebug() << "🌍 I18nManager configuration updated";
}

QStringList I18nManager::getMissingTranslations() const {
    return translation_manager_.getMissingTranslations();
}

void I18nManager::clearMissingTranslations() {
    translation_manager_.clearMissingTranslations();
}

bool I18nManager::validateTranslations() const {
    // Basic validation - check if we have translations for current language
    QStringList available = getAvailableLanguages();
    QString current = getCurrentLanguage();
    
    return available.contains(current);
}

void I18nManager::generateTranslationReport() const {
    qDebug() << "🌍 Translation Report:";
    qDebug() << "   Current language:" << getCurrentLanguage();
    qDebug() << "   Available languages:" << getAvailableLanguages();
    qDebug() << "   Missing translations:" << getMissingTranslations().size();
    qDebug() << "   RTL enabled:" << config_.enable_rtl_support;
    qDebug() << "   Is RTL:" << isRightToLeft();
}

void I18nManager::enableHotReload(bool enable) {
    translation_manager_.enableHotReload(enable);
    config_.enable_hot_reload = enable;
}

void I18nManager::reloadTranslations() {
    translation_manager_.reloadTranslations();
    updateLanguageInfo();
    emit translationsReloaded();
}

bool I18nManager::isLanguageAvailable(const QString& language_code) const {
    return getAvailableLanguages().contains(language_code);
}

I18nManager::LanguageInfo I18nManager::getLanguageInfo(const QString& language_code) const {
    return language_info_.value(language_code, LanguageInfo());
}

QString I18nManager::detectSystemLanguage() const {
    QLocale system_locale = QLocale::system();
    QString system_language = system_locale.name().left(2);
    
    if (isLanguageAvailable(system_language)) {
        return system_language;
    }
    
    // Fallback to default language
    return config_.default_language;
}

void I18nManager::setSystemLanguage() {
    QString system_language = detectSystemLanguage();
    setLanguage(system_language);
}

// Private helper methods

void I18nManager::connectSignals() {
    // Connect translation manager signals
    connect(&translation_manager_, &TranslationManager::languageChanged,
            this, [this](const QString& new_lang, const QString& old_lang) {
                emit languageChanged(new_lang, old_lang);
            });
    connect(&translation_manager_, &TranslationManager::translationsReloaded,
            this, [this]() {
                emit translationsReloaded();
            });
    connect(&translation_manager_, &TranslationManager::translationMissing,
            this, [this](const QString& key, const QString& language) {
                emit translationMissing(key, language);
            });

    // Note: Locale and RTL signals will be connected when moc is properly generated
    qDebug() << "🌍 I18nManager signals connected";
}

void I18nManager::updateLanguageInfo() {
    language_info_.clear();

    QStringList available = getAvailableLanguages();
    for (const QString& lang_code : available) {
        QLocale locale(lang_code);
        LanguageInfo info(lang_code, locale);
        info.is_available = true;
        language_info_[lang_code] = info;
    }

    qDebug() << "🌍 Updated language information for" << language_info_.size() << "languages";
}

void I18nManager::syncComponents() {
    // Ensure all components are using the same locale
    QLocale current_locale = getCurrentLocale();

    // Sync locale manager
    locale_manager_.setLocale(current_locale);

    // Sync RTL support
    if (config_.enable_rtl_support) {
        rtl_support_.setLayoutDirection(current_locale);
    }
}

} // namespace I18n
} // namespace DeclarativeUI
