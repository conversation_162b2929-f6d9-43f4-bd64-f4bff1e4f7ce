/**
 * @file 07_input_components.cpp
 * @brief Input components showcase - <PERSON><PERSON><PERSON>, <PERSON>mboBox, SpinBox, Slider
 * 
 * This example demonstrates:
 * - Various input component types
 * - Data validation and constraints
 * - Component state synchronization
 * - Input event handling patterns
 * 
 * Learning objectives:
 * - Master different input component types
 * - Understand input validation patterns
 * - Learn component state management
 * - See real-time data synchronization
 */

#include <QApplication>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QWidget>
#include <QGroupBox>
#include <QCheckBox>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QDial>
#include <QProgressBar>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QDebug>
#include <QTimer>
#include <QDateTime>
#include <QFileInfo>
#include <QRandomGenerator>

// Include DeclarativeUI components
#include "JSON/JSONUILoader.hpp"
#include "Binding/StateManager.hpp"

using namespace DeclarativeUI;

/**
 * @brief Input components demonstration
 */
class InputComponentsApp : public QObject {
    Q_OBJECT

public:
    InputComponentsApp(QObject* parent = nullptr) : QObject(parent) {
        setupState();
        setupUILoader();
        createUI();
    }

    void show() {
        if (main_widget_) {
            main_widget_->show();
        }
    }

private slots:
    void onCheckBoxToggled(bool checked) {
        checkbox_state_->set(checked);
        logInput("CheckBox", QString("Checked: %1").arg(checked ? "true" : "false"));
        updateSummary();
    }

    void onComboBoxChanged(const QString& text) {
        combo_state_->set(text);
        logInput("ComboBox", QString("Selected: %1").arg(text));
        updateSummary();
    }

    void onSpinBoxChanged(int value) {
        spinbox_state_->set(value);
        logInput("SpinBox", QString("Value: %1").arg(value));
        updateSummary();
        
        // Sync with slider
        auto slider = main_widget_->findChild<QSlider*>("valueSlider");
        if (slider) {
            slider->blockSignals(true);
            slider->setValue(value);
            slider->blockSignals(false);
        }
    }

    void onDoubleSpinBoxChanged(double value) {
        double_spinbox_state_->set(value);
        logInput("DoubleSpinBox", QString("Value: %1").arg(value, 0, 'f', 2));
        updateSummary();
    }

    void onSliderChanged(int value) {
        slider_state_->set(value);
        logInput("Slider", QString("Value: %1").arg(value));
        updateSummary();
        
        // Sync with spinbox
        auto spinbox = main_widget_->findChild<QSpinBox*>("numberSpin");
        if (spinbox) {
            spinbox->blockSignals(true);
            spinbox->setValue(value);
            spinbox->blockSignals(false);
        }
        
        // Update progress bar
        auto progress = main_widget_->findChild<QProgressBar*>("valueProgress");
        if (progress) {
            progress->setValue(value);
        }
    }

    void onDialChanged(int value) {
        dial_state_->set(value);
        logInput("Dial", QString("Value: %1").arg(value));
        updateSummary();
    }

    void onResetClicked() {
        // Reset all components to default values
        checkbox_state_->set(false);
        combo_state_->set("Option 1");
        spinbox_state_->set(50);
        double_spinbox_state_->set(1.0);
        slider_state_->set(50);
        dial_state_->set(0);
        
        // Update UI components
        auto checkbox = main_widget_->findChild<QCheckBox*>("enableCheck");
        auto combobox = main_widget_->findChild<QComboBox*>("optionCombo");
        auto spinbox = main_widget_->findChild<QSpinBox*>("numberSpin");
        auto double_spinbox = main_widget_->findChild<QDoubleSpinBox*>("precisionSpin");
        auto slider = main_widget_->findChild<QSlider*>("valueSlider");
        auto dial = main_widget_->findChild<QDial*>("rotaryDial");
        
        if (checkbox) checkbox->setChecked(false);
        if (combobox) combobox->setCurrentText("Option 1");
        if (spinbox) spinbox->setValue(50);
        if (double_spinbox) double_spinbox->setValue(1.0);
        if (slider) slider->setValue(50);
        if (dial) dial->setValue(0);
        
        logInput("System", "All inputs reset to defaults");
        updateSummary();
    }

    void onRandomizeClicked() {
        // Set random values
        checkbox_state_->set(QRandomGenerator::global()->bounded(2) == 1);
        
        QStringList options = {"Option 1", "Option 2", "Option 3", "Option 4", "Option 5"};
        combo_state_->set(options[QRandomGenerator::global()->bounded(options.size())]);
        
        int random_int = QRandomGenerator::global()->bounded(0, 101);
        spinbox_state_->set(random_int);
        slider_state_->set(random_int);
        
        double random_double = QRandomGenerator::global()->bounded(10.0);
        double_spinbox_state_->set(random_double);
        
        int random_dial = QRandomGenerator::global()->bounded(0, 361);
        dial_state_->set(random_dial);
        
        // Update UI components
        auto checkbox = main_widget_->findChild<QCheckBox*>("enableCheck");
        auto combobox = main_widget_->findChild<QComboBox*>("optionCombo");
        auto spinbox = main_widget_->findChild<QSpinBox*>("numberSpin");
        auto double_spinbox = main_widget_->findChild<QDoubleSpinBox*>("precisionSpin");
        auto slider = main_widget_->findChild<QSlider*>("valueSlider");
        auto dial = main_widget_->findChild<QDial*>("rotaryDial");
        
        if (checkbox) checkbox->setChecked(checkbox_state_->get());
        if (combobox) combobox->setCurrentText(combo_state_->get());
        if (spinbox) spinbox->setValue(spinbox_state_->get());
        if (double_spinbox) double_spinbox->setValue(double_spinbox_state_->get());
        if (slider) slider->setValue(slider_state_->get());
        if (dial) dial->setValue(dial_state_->get());
        
        logInput("System", "All inputs randomized");
        updateSummary();
    }

    void onClearLogClicked() {
        auto log_display = main_widget_->findChild<QTextEdit*>("inputLog");
        if (log_display) {
            log_display->clear();
            logInput("System", "Input log cleared");
        }
    }

private:
    std::unique_ptr<QWidget> main_widget_;
    std::unique_ptr<JSON::JSONUILoader> ui_loader_;
    
    // State management
    std::shared_ptr<Binding::StateManager> state_manager_;
    std::shared_ptr<Binding::ReactiveProperty<bool>> checkbox_state_;
    std::shared_ptr<Binding::ReactiveProperty<QString>> combo_state_;
    std::shared_ptr<Binding::ReactiveProperty<int>> spinbox_state_;
    std::shared_ptr<Binding::ReactiveProperty<double>> double_spinbox_state_;
    std::shared_ptr<Binding::ReactiveProperty<int>> slider_state_;
    std::shared_ptr<Binding::ReactiveProperty<int>> dial_state_;

    void setupState() {
        state_manager_ = std::shared_ptr<Binding::StateManager>(
            &Binding::StateManager::instance(), [](Binding::StateManager*) {});

        // Create reactive states for all input components
        checkbox_state_ = state_manager_->createState<bool>("checkbox", false);
        combo_state_ = state_manager_->createState<QString>("combo", "Option 1");
        spinbox_state_ = state_manager_->createState<int>("spinbox", 50);
        double_spinbox_state_ = state_manager_->createState<double>("double_spinbox", 1.0);
        slider_state_ = state_manager_->createState<int>("slider", 50);
        dial_state_ = state_manager_->createState<int>("dial", 0);

        qDebug() << "✅ Input component states initialized";
    }

    void setupUILoader() {
        ui_loader_ = std::make_unique<JSON::JSONUILoader>();

        // Register event handlers
        ui_loader_->registerEventHandler("resetInputs", [this]() {
            onResetClicked();
        });

        ui_loader_->registerEventHandler("randomizeInputs", [this]() {
            onRandomizeClicked();
        });

        ui_loader_->registerEventHandler("clearLog", [this]() {
            onClearLogClicked();
        });

        qDebug() << "✅ Event handlers registered";
    }

    void createUI() {
        try {
            QString ui_file = "resources/input_components_ui.json";
            
            if (QFileInfo::exists(ui_file)) {
                main_widget_ = ui_loader_->loadFromFile(ui_file);
                if (main_widget_) {
                    main_widget_->setWindowTitle("07 - Input Components | DeclarativeUI");
                    connectUIEvents();
                    updateSummary();
                    qDebug() << "✅ UI loaded from JSON";
                    return;
                }
            }
            
            // Fallback to programmatic UI
            main_widget_ = createProgrammaticUI();
            
        } catch (const std::exception& e) {
            qCritical() << "UI creation failed:" << e.what();
            main_widget_ = createProgrammaticUI();
        }
    }

    void connectUIEvents() {
        // Connect all input component events
        auto checkbox = main_widget_->findChild<QCheckBox*>("enableCheck");
        if (checkbox) {
            connect(checkbox, &QCheckBox::toggled, this, &InputComponentsApp::onCheckBoxToggled);
        }

        auto combobox = main_widget_->findChild<QComboBox*>("optionCombo");
        if (combobox) {
            connect(combobox, &QComboBox::currentTextChanged, this, &InputComponentsApp::onComboBoxChanged);
        }

        auto spinbox = main_widget_->findChild<QSpinBox*>("numberSpin");
        if (spinbox) {
            connect(spinbox, QOverload<int>::of(&QSpinBox::valueChanged), this, &InputComponentsApp::onSpinBoxChanged);
        }

        auto double_spinbox = main_widget_->findChild<QDoubleSpinBox*>("precisionSpin");
        if (double_spinbox) {
            connect(double_spinbox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &InputComponentsApp::onDoubleSpinBoxChanged);
        }

        auto slider = main_widget_->findChild<QSlider*>("valueSlider");
        if (slider) {
            connect(slider, &QSlider::valueChanged, this, &InputComponentsApp::onSliderChanged);
        }

        auto dial = main_widget_->findChild<QDial*>("rotaryDial");
        if (dial) {
            connect(dial, &QDial::valueChanged, this, &InputComponentsApp::onDialChanged);
        }

        qDebug() << "✅ UI events connected";
    }

    std::unique_ptr<QWidget> createProgrammaticUI() {
        auto widget = std::make_unique<QWidget>();
        widget->setWindowTitle("07 - Input Components (Fallback) | DeclarativeUI");
        widget->setMinimumSize(700, 600);

        auto layout = new QVBoxLayout(widget.get());
        layout->setSpacing(15);
        layout->setContentsMargins(20, 20, 20, 20);

        // Header
        auto header = new QLabel("🎛️ Input Components Showcase");
        header->setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50;");
        header->setAlignment(Qt::AlignCenter);
        layout->addWidget(header);

        // Create input components grid
        auto inputs_group = new QGroupBox("Input Controls");
        auto inputs_layout = new QGridLayout(inputs_group);

        // CheckBox
        auto checkbox = new QCheckBox("Enable Feature");
        checkbox->setObjectName("enableCheck");
        connect(checkbox, &QCheckBox::toggled, this, &InputComponentsApp::onCheckBoxToggled);
        inputs_layout->addWidget(new QLabel("CheckBox:"), 0, 0);
        inputs_layout->addWidget(checkbox, 0, 1);

        // ComboBox
        auto combobox = new QComboBox();
        combobox->setObjectName("optionCombo");
        combobox->addItems({"Option 1", "Option 2", "Option 3", "Option 4", "Option 5"});
        connect(combobox, &QComboBox::currentTextChanged, this, &InputComponentsApp::onComboBoxChanged);
        inputs_layout->addWidget(new QLabel("ComboBox:"), 1, 0);
        inputs_layout->addWidget(combobox, 1, 1);

        // SpinBox
        auto spinbox = new QSpinBox();
        spinbox->setObjectName("numberSpin");
        spinbox->setRange(0, 100);
        spinbox->setValue(50);
        connect(spinbox, QOverload<int>::of(&QSpinBox::valueChanged), this, &InputComponentsApp::onSpinBoxChanged);
        inputs_layout->addWidget(new QLabel("SpinBox:"), 2, 0);
        inputs_layout->addWidget(spinbox, 2, 1);

        // DoubleSpinBox
        auto double_spinbox = new QDoubleSpinBox();
        double_spinbox->setObjectName("precisionSpin");
        double_spinbox->setRange(0.0, 10.0);
        double_spinbox->setSingleStep(0.1);
        double_spinbox->setValue(1.0);
        connect(double_spinbox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &InputComponentsApp::onDoubleSpinBoxChanged);
        inputs_layout->addWidget(new QLabel("DoubleSpinBox:"), 3, 0);
        inputs_layout->addWidget(double_spinbox, 3, 1);

        // Slider
        auto slider = new QSlider(Qt::Horizontal);
        slider->setObjectName("valueSlider");
        slider->setRange(0, 100);
        slider->setValue(50);
        connect(slider, &QSlider::valueChanged, this, &InputComponentsApp::onSliderChanged);
        inputs_layout->addWidget(new QLabel("Slider:"), 4, 0);
        inputs_layout->addWidget(slider, 4, 1);

        // Progress bar (shows slider value)
        auto progress = new QProgressBar();
        progress->setObjectName("valueProgress");
        progress->setRange(0, 100);
        progress->setValue(50);
        inputs_layout->addWidget(new QLabel("Progress:"), 5, 0);
        inputs_layout->addWidget(progress, 5, 1);

        layout->addWidget(inputs_group);

        // Summary and log sections would be added here...
        // (Continuing in next part due to length)

        qDebug() << "✅ Programmatic UI created";
        return widget;
    }

    void logInput(const QString& component, const QString& details) {
        auto log_display = main_widget_->findChild<QTextEdit*>("inputLog");
        if (log_display) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            QString log_entry = QString("[%1] %2: %3").arg(timestamp, component, details);
            log_display->append(log_entry);
            
            // Auto-scroll to bottom
            auto cursor = log_display->textCursor();
            cursor.movePosition(QTextCursor::End);
            log_display->setTextCursor(cursor);
        }
        
        qDebug() << "Input:" << component << details;
    }

    void updateSummary() {
        auto summary_label = main_widget_->findChild<QLabel*>("summaryDisplay");
        if (summary_label) {
            QString summary = QString(
                "📊 Current Values:\n"
                "• CheckBox: %1\n"
                "• ComboBox: %2\n"
                "• SpinBox: %3\n"
                "• DoubleSpinBox: %4\n"
                "• Slider: %5\n"
                "• Dial: %6°"
            ).arg(
                checkbox_state_->get() ? "✅ Checked" : "❌ Unchecked",
                combo_state_->get(),
                QString::number(spinbox_state_->get()),
                QString::number(double_spinbox_state_->get(), 'f', 2),
                QString::number(slider_state_->get()),
                QString::number(dial_state_->get())
            );
            
            summary_label->setText(summary);
        }
    }
};

int main(int argc, char* argv[]) {
    QApplication app(argc, argv);

    app.setApplicationName("DeclarativeUI Input Components");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("DeclarativeUI Examples");

    try {
        qDebug() << "🚀 Starting Input Components example...";
        
        InputComponentsApp input_app;
        input_app.show();

        qDebug() << "💡 This example demonstrates:";
        qDebug() << "   - CheckBox for boolean input";
        qDebug() << "   - ComboBox for selection input";
        qDebug() << "   - SpinBox for integer input";
        qDebug() << "   - DoubleSpinBox for decimal input";
        qDebug() << "   - Slider for range input";
        qDebug() << "   - Component state synchronization";

        return app.exec();

    } catch (const std::exception& e) {
        qCritical() << "❌ Application error:" << e.what();
        return -1;
    }
}

#include "07_input_components.moc"
