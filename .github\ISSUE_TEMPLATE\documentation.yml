name: 📚 Documentation Issue
description: Report issues with documentation or request documentation improvements
title: "[Docs]: "
labels: ["documentation", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for helping improve our documentation! Please provide details about the documentation issue or improvement.

  - type: dropdown
    id: type
    attributes:
      label: Documentation Issue Type
      description: What type of documentation issue is this?
      options:
        - Missing documentation
        - Incorrect/outdated information
        - Unclear explanation
        - Broken links
        - Formatting issues
        - Code examples not working
        - API reference incomplete
        - Tutorial improvement
        - Translation issue
        - Other
    validations:
      required: true

  - type: dropdown
    id: section
    attributes:
      label: Documentation Section
      description: Which section of the documentation is affected?
      options:
        - README.md
        - API Reference
        - User Guide
        - Developer Guide
        - Getting Started
        - Examples
        - Build Instructions
        - Contributing Guide
        - Architecture Documentation
        - Command System Guide
        - Integration Guide
        - Troubleshooting
        - Other
    validations:
      required: true

  - type: input
    id: location
    attributes:
      label: Specific Location
      description: Please provide the specific file path or URL where the issue is located
      placeholder: e.g., docs/api/components.md, examples/basic/README.md, line 42
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Issue Description
      description: Describe the documentation issue in detail
      placeholder: |
        - What is wrong or missing?
        - What did you expect to find?
        - How does this affect users?
    validations:
      required: true

  - type: textarea
    id: current-content
    attributes:
      label: Current Content (if applicable)
      description: Quote the current documentation content that needs to be fixed
      render: markdown
      placeholder: |
        ```
        Current documentation content that is problematic...
        ```

  - type: textarea
    id: suggested-improvement
    attributes:
      label: Suggested Improvement
      description: How should the documentation be improved?
      render: markdown
      placeholder: |
        Suggested new content or improvements:
        
        ```
        Improved documentation content...
        ```

  - type: textarea
    id: code-example
    attributes:
      label: Code Example (if applicable)
      description: If this relates to code examples, provide working examples or corrections
      render: cpp
      placeholder: |
        // Working code example
        #include "DeclarativeUI.hpp"
        
        int main() {
            // Example code
            return 0;
        }

  - type: dropdown
    id: audience
    attributes:
      label: Target Audience
      description: Who is the primary audience for this documentation?
      options:
        - New users/beginners
        - Experienced Qt developers
        - Framework contributors
        - API users
        - All users
        - Specific use case (explain in additional context)
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this documentation fix?
      options:
        - Low - Minor improvement
        - Medium - Affects user experience
        - High - Blocks user understanding
        - Critical - Prevents framework usage
    validations:
      required: true

  - type: textarea
    id: user-impact
    attributes:
      label: User Impact
      description: How does this documentation issue affect users?
      placeholder: |
        - Who is affected by this issue?
        - What problems does it cause?
        - How does fixing this help users?

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any additional information that would help improve the documentation
      placeholder: |
        - Related issues or discussions
        - Screenshots (if applicable)
        - Links to external resources
        - User feedback or questions

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided specific location information
          required: true
        - label: I have clearly described the issue and suggested improvements
          required: true
        - label: I am willing to help improve the documentation
          required: false
        - label: I can provide additional examples or clarifications if needed
          required: false
