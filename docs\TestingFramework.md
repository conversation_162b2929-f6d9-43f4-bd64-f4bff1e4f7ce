# DeclarativeUI Testing Framework

## Overview

The DeclarativeUI Testing Framework provides comprehensive testing utilities for Qt6-based applications, including UI automation, visual regression testing, component validation, performance benchmarking, and accessibility testing.

## Features

### 🤖 UI Automation
- **Mouse and Keyboard Simulation**: Simulate user interactions with widgets
- **Widget Finding**: Locate widgets by various criteria (name, text, type, position)
- **Event Processing**: Handle Qt events and wait for UI updates
- **Interaction Recording**: Record and replay user interactions

### 📸 Visual Regression Testing
- **Screenshot Capture**: Capture widget screenshots with pixel-perfect accuracy
- **Baseline Management**: Store and manage visual baselines for comparison
- **Image Comparison**: Advanced image comparison with tolerance settings
- **Difference Highlighting**: Visual diff reports showing changes

### 🧩 Component Testing
- **Component Validation**: Validate widget properties, hierarchy, and behavior
- **Mock Objects**: Comprehensive mock widgets and signal emitters
- **Signal/Slot Testing**: Test Qt signal/slot connections and emissions
- **Property Testing**: Validate widget properties using Qt's meta-object system

### ⚡ Performance Benchmarking
- **Timing Measurements**: Measure function and widget performance
- **Memory Profiling**: Track memory usage and detect leaks
- **CPU Monitoring**: Monitor CPU usage during test execution
- **Statistical Analysis**: Calculate performance statistics and trends

### ♿ Accessibility Testing
- **WCAG Compliance**: Check compliance with Web Content Accessibility Guidelines
- **Color Contrast**: Validate color contrast ratios
- **Keyboard Navigation**: Test keyboard accessibility
- **Screen Reader Support**: Validate screen reader compatibility

### 📊 Advanced Reporting
- **Multiple Formats**: HTML, JUnit XML, JSON, Markdown, CSV reports
- **Interactive Reports**: Rich HTML reports with filtering and search
- **Performance Dashboards**: Visual performance trend analysis
- **Test Coverage**: Track test coverage across components

## Quick Start

### Basic Test Case

```cpp
#include "Testing/TestingFramework.hpp"

class MyWidgetTest : public DeclarativeUI::Testing::TestCase {
    Q_OBJECT

public:
    MyWidgetTest() : TestCase("MyWidget Basic Test") {}

    void setUp() override {
        widget_ = std::make_unique<MyWidget>();
        widget_->show();
    }

    void tearDown() override {
        widget_.reset();
    }

    void run() override {
        // Test widget creation
        assertTrue(widget_ != nullptr, "Widget should be created");
        assertWidgetVisible(widget_.get(), "Widget should be visible");
        
        // Test widget properties
        assertEqual(widget_->windowTitle(), "My Widget", "Title should match");
        
        // Test user interaction
        UIAutomation automation;
        automation.clickWidget(widget_.get());
        
        // Wait for signal emission
        assertSignalEmitted(widget_.get(), SIGNAL(clicked()), 1000);
    }

private:
    std::unique_ptr<MyWidget> widget_;
};
```

### Test Runner Usage

```cpp
#include "Testing/TestRunner.hpp"

int main() {
    DeclarativeUI::Testing::TestRunner runner;
    
    // Configure test execution
    runner.setParallelExecution(true);
    runner.setMaxConcurrentTests(4);
    runner.setTimeout(30);
    
    // Add tests
    runner.addTest(std::make_unique<MyWidgetTest>());
    runner.addTest(std::make_unique<MyPerformanceTest>());
    
    // Run tests
    runner.runAllTests();
    
    // Generate reports
    runner.generateReport("test_report.html", "html");
    runner.generateJUnitXML("test_results.xml");
    
    // Print summary
    runner.printSummary();
    
    return runner.getFailedCount() == 0 ? 0 : 1;
}
```

### Visual Testing

```cpp
class VisualRegressionTest : public DeclarativeUI::Testing::TestCase {
public:
    void run() override {
        VisualTesting visual_testing;
        
        // Capture baseline (first run)
        visual_testing.captureBaseline(widget_.get(), "my_widget_baseline");
        
        // Compare with baseline (subsequent runs)
        auto result = visual_testing.compareWithBaseline(widget_.get(), "my_widget_baseline");
        
        assertTrue(result.matches, "Visual appearance should match baseline");
        
        if (!result.matches) {
            // Save difference image for analysis
            visual_testing.saveDifferenceImage(result, "visual_diff.png");
        }
    }
};
```

### Performance Testing

```cpp
class PerformanceTest : public DeclarativeUI::Testing::TestCase {
public:
    void run() override {
        PerformanceBenchmark benchmark;
        
        // Measure function performance
        auto timing_result = benchmark.measureFunction([this]() {
            widget_->performExpensiveOperation();
        }, 100); // 100 iterations
        
        // Assert performance requirements
        assertTrue(timing_result.average_ms < 50.0, "Operation should complete in <50ms");
        
        // Measure memory usage
        auto memory_before = benchmark.getCurrentMemoryUsage();
        widget_->loadLargeDataset();
        auto memory_after = benchmark.getCurrentMemoryUsage();
        
        qint64 memory_increase = memory_after - memory_before;
        assertTrue(memory_increase < 100 * 1024 * 1024, "Memory increase should be <100MB");
    }
};
```

## Architecture

### Core Components

1. **TestCase**: Base class for all test cases with assertion methods
2. **TestRunner**: Orchestrates test execution with parallel processing
3. **UIAutomation**: Simulates user interactions and finds widgets
4. **VisualTesting**: Handles screenshot capture and comparison
5. **ComponentTester**: Validates component behavior and properties
6. **PerformanceBenchmark**: Measures performance and resource usage
7. **TestReporter**: Generates reports in multiple formats

### Integration Points

- **Qt Test Framework**: Leverages QTest for low-level testing utilities
- **Qt Meta-Object System**: Uses QMetaObject for runtime property inspection
- **Qt Concurrent**: Enables parallel test execution
- **Platform APIs**: Integrates with OS-specific monitoring APIs

## Configuration

### Test Suite Configuration

```cpp
TestSuiteConfig config;
config.name = "My Application Tests";
config.description = "Comprehensive test suite";
config.output_directory = "/path/to/test/results";
config.timeout_seconds = 60;
config.enable_parallel_testing = true;
config.max_concurrent_tests = 4;
config.generate_html_report = true;
config.generate_junit_xml = true;
config.enable_visual_testing = true;
config.visual_tolerance = 0.95; // 95% similarity required

runner.setConfiguration(config);
```

### CMake Integration

```cmake
# Enable testing framework
option(BUILD_TESTING_FRAMEWORK "Build comprehensive testing framework" ON)

if(BUILD_TESTING_FRAMEWORK)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_TESTING_FRAMEWORK_ENABLED)
    
    target_sources(DeclarativeUI PRIVATE
        src/Testing/TestingFramework.hpp
        src/Testing/TestingFramework.cpp
        src/Testing/ComponentTester.cpp
        src/Testing/PerformanceBenchmark.cpp
        src/Testing/TestRunner.hpp
        src/Testing/TestRunner.cpp
    )
    
    find_package(Qt6 REQUIRED COMPONENTS Test Concurrent)
    target_link_libraries(DeclarativeUI PUBLIC Qt6::Test Qt6::Concurrent)
endif()
```

## Best Practices

### Test Organization
- Group related tests into test suites
- Use descriptive test names and descriptions
- Implement proper setUp() and tearDown() methods
- Keep tests independent and isolated

### Performance Testing
- Run performance tests multiple times for statistical accuracy
- Establish baseline measurements for comparison
- Test under different load conditions
- Monitor both CPU and memory usage

### Visual Testing
- Capture baselines on a consistent environment
- Use appropriate tolerance levels for image comparison
- Test across different screen resolutions and DPI settings
- Version control baseline images

### Accessibility Testing
- Test with keyboard-only navigation
- Validate color contrast ratios
- Check screen reader compatibility
- Test with different accessibility tools

## Example Application

The framework includes a comprehensive example application demonstrating all features:

```bash
# Build with testing framework
cmake -DBUILD_TESTING_FRAMEWORK=ON ..
make TestingFrameworkExample

# Run the example
./examples/TestingFrameworkExample
```

The example showcases:
- Live UI automation demonstrations
- Visual regression testing workflows
- Component testing with mock objects
- Performance benchmarking tools
- Accessibility validation utilities
- Interactive test reporting

## API Reference

### TestCase Methods
- `assertTrue(condition, message)`: Assert boolean condition
- `assertEqual(actual, expected, message)`: Assert equality
- `assertWidgetVisible(widget, message)`: Assert widget visibility
- `assertSignalEmitted(object, signal, timeout)`: Assert signal emission

### UIAutomation Methods
- `clickWidget(widget, button)`: Simulate mouse click
- `typeText(widget, text)`: Simulate keyboard input
- `findWidget(parent, criteria)`: Find widget by criteria
- `waitForWidget(parent, criteria, timeout)`: Wait for widget to appear

### VisualTesting Methods
- `captureBaseline(widget, name)`: Capture visual baseline
- `compareWithBaseline(widget, name)`: Compare with baseline
- `compareImages(expected, actual, tolerance)`: Compare two images

### PerformanceBenchmark Methods
- `measureFunction(func, iterations)`: Measure function performance
- `measureWidget(widget, operation)`: Measure widget operation
- `getCurrentMemoryUsage()`: Get current memory usage
- `startCPUMonitoring()`: Start CPU usage monitoring

## Contributing

The testing framework is designed to be extensible. To add new testing capabilities:

1. Extend the appropriate base class (TestCase, etc.)
2. Implement required virtual methods
3. Add CMake configuration for new components
4. Update documentation and examples
5. Add unit tests for new functionality

## License

This testing framework is part of the DeclarativeUI project and follows the same licensing terms.
