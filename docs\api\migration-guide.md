# Migration Guide

This guide helps you migrate between different versions of the DeclarativeUI Framework and provides guidance for migrating from other UI frameworks.

## Table of Contents

- [Version Migration](#version-migration)
- [Migrating from Qt Widgets](#migrating-from-qt-widgets)
- [Migrating from QML](#migrating-from-qml)
- [Migrating from Other C++ UI Frameworks](#migrating-from-other-c-ui-frameworks)
- [Breaking Changes](#breaking-changes)
- [Migration Tools](#migration-tools)

## Version Migration

### From 0.x to 1.0

#### API Changes

**Component Creation**

```cpp
// ❌ Old (0.x)
auto button = new Components::Button();
button->setText("Click me");
button->setEnabled(true);

// ✅ New (1.0)
auto button = Components::Button()
    .text("Click me")
    .enabled(true);
```

**State Management**

```cpp
// ❌ Old (0.x)
StateManager manager;
manager.set("user.name", "<PERSON>");

// ✅ New (1.0)
auto& manager = StateManager::instance();
manager.setState("user.name", "<PERSON>");
```

**Event Handling**

```cpp
// ❌ Old (0.x)
button->onClicked([](){ /* handler */ });

// ✅ New (1.0)
button.onClick([](){ /* handler */ });
```

#### Namespace Changes

```cpp
// ❌ Old (0.x)
using namespace DeclarativeUI;
auto button = Button();

// ✅ New (1.0)
using namespace DeclarativeUI::Components;
auto button = Button();
// or
auto button = DeclarativeUI::Components::Button();
```

## Migrating from Qt Widgets

### Basic Widget Conversion

**Simple Widget Creation**

```cpp
// ❌ Qt Widgets
auto* button = new QPushButton("Click me");
button->setEnabled(true);
button->setToolTip("Click this button");
layout->addWidget(button);

// ✅ DeclarativeUI
auto button = Components::Button()
    .text("Click me")
    .enabled(true)
    .tooltip("Click this button");
```

**Layout Management**

```cpp
// ❌ Qt Widgets
auto* layout = new QVBoxLayout();
layout->addWidget(new QLabel("Name:"));
layout->addWidget(new QLineEdit());
layout->addWidget(new QPushButton("Submit"));
widget->setLayout(layout);

// ✅ DeclarativeUI
auto form = Components::Container()
    .layout("VBox")
    .children({
        Components::Label().text("Name:"),
        Components::LineEdit().placeholder("Enter name"),
        Components::Button().text("Submit")
    });
```

**Signal-Slot Connections**

```cpp
// ❌ Qt Widgets
connect(button, &QPushButton::clicked, this, &MyClass::onButtonClicked);

// ✅ DeclarativeUI
auto button = Components::Button()
    .text("Click me")
    .onClick([this](){ onButtonClicked(); });
```

### Advanced Widget Features

**Custom Widgets**

```cpp
// ❌ Qt Widgets
class CustomWidget : public QWidget {
    Q_OBJECT
public:
    CustomWidget(QWidget* parent = nullptr) : QWidget(parent) {
        setupUI();
    }
private:
    void setupUI() {
        auto* layout = new QVBoxLayout(this);
        // Setup layout...
    }
};

// ✅ DeclarativeUI
class CustomComponent : public Core::UIElement {
    Q_OBJECT
public:
    CustomComponent(QObject* parent = nullptr) : UIElement(parent) {}
    
    void initialize() override {
        auto container = Components::Container()
            .layout("VBox")
            .children({
                // Component children...
            });
        setWidget(container.getWidget());
    }
};
```

### State Management Migration

```cpp
// ❌ Qt Widgets (manual state management)
class MyWidget : public QWidget {
private:
    QString userName_;
    bool isLoggedIn_ = false;
    
    void updateUI() {
        userLabel->setText(userName_);
        loginButton->setVisible(!isLoggedIn_);
        logoutButton->setVisible(isLoggedIn_);
    }
};

// ✅ DeclarativeUI (reactive state)
class MyComponent : public Core::UIElement {
public:
    MyComponent() {
        auto& state = StateManager::instance();
        
        auto userLabel = Components::Label()
            .bindToState("user.name", "text");
            
        auto loginButton = Components::Button()
            .text("Login")
            .bindToState("user.isLoggedIn", "visible", [](const QVariant& value) {
                return !value.toBool(); // Invert for login button
            });
    }
};
```

## Migrating from QML

### Declarative Syntax Comparison

**Component Declaration**

```qml
// ❌ QML
Rectangle {
    width: 200
    height: 100
    color: "blue"
    
    Text {
        text: "Hello World"
        anchors.centerIn: parent
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: console.log("Clicked")
    }
}
```

```cpp
// ✅ DeclarativeUI
auto container = Components::Container()
    .width(200)
    .height(100)
    .style("background-color: blue;")
    .children({
        Components::Label()
            .text("Hello World")
            .alignment(Qt::AlignCenter)
    })
    .onClick([]() { qDebug() << "Clicked"; });
```

**Property Binding**

```qml
// ❌ QML
Text {
    text: model.userName
    visible: model.isLoggedIn
}
```

```cpp
// ✅ DeclarativeUI
auto label = Components::Label()
    .bindToState("user.name", "text")
    .bindToState("user.isLoggedIn", "visible");
```

**Animations**

```qml
// ❌ QML
Rectangle {
    id: rect
    PropertyAnimation on x {
        to: 100
        duration: 1000
    }
}
```

```cpp
// ✅ DeclarativeUI
auto rect = Components::Container()
    .animate("x", 100, 1000);
```

### State Management

```qml
// ❌ QML
QtObject {
    property string userName: ""
    property bool isLoggedIn: false
}
```

```cpp
// ✅ DeclarativeUI
auto& state = StateManager::instance();
state.setState("user.name", QString(""));
state.setState("user.isLoggedIn", false);
```

## Migrating from Other C++ UI Frameworks

### From Dear ImGui

```cpp
// ❌ Dear ImGui
if (ImGui::Button("Click me")) {
    // Handle click
}
ImGui::Text("Hello, %s!", name.c_str());

// ✅ DeclarativeUI
auto button = Components::Button()
    .text("Click me")
    .onClick([]() { /* Handle click */ });
    
auto label = Components::Label()
    .text(QString("Hello, %1!").arg(name));
```

### From FLTK

```cpp
// ❌ FLTK
Fl_Window* window = new Fl_Window(300, 200, "My Window");
Fl_Button* button = new Fl_Button(10, 10, 100, 30, "Click me");
button->callback(button_callback);
window->end();
window->show();

// ✅ DeclarativeUI
auto window = Components::Container()
    .width(300)
    .height(200)
    .title("My Window")
    .children({
        Components::Button()
            .text("Click me")
            .x(10).y(10)
            .width(100).height(30)
            .onClick(button_callback)
    });
window.show();
```

### From wxWidgets

```cpp
// ❌ wxWidgets
wxFrame* frame = new wxFrame(nullptr, wxID_ANY, "Hello World");
wxPanel* panel = new wxPanel(frame);
wxButton* button = new wxButton(panel, wxID_ANY, "Click me");
wxBoxSizer* sizer = new wxBoxSizer(wxVERTICAL);
sizer->Add(button, 0, wxALL, 5);
panel->SetSizer(sizer);

// ✅ DeclarativeUI
auto frame = Components::Container()
    .title("Hello World")
    .layout("VBox")
    .children({
        Components::Button()
            .text("Click me")
            .margins(5)
    });
```

## Breaking Changes

### Version 1.0 Breaking Changes

1. **Namespace Reorganization**
   - All components moved to `DeclarativeUI::Components`
   - Core classes moved to `DeclarativeUI::Core`

2. **API Modernization**
   - Fluent interface for all components
   - Consistent naming conventions
   - Removed deprecated methods

3. **State Management**
   - Singleton StateManager pattern
   - New binding syntax
   - Reactive property system

4. **Build System**
   - CMake 3.20+ required
   - C++20 standard required
   - Qt6 required (Qt5 support dropped)

### Migration Checklist

- [ ] Update namespace usage
- [ ] Convert to fluent interface syntax
- [ ] Update state management calls
- [ ] Update build configuration
- [ ] Update Qt version to 6.2+
- [ ] Update compiler to C++20 compatible
- [ ] Run migration tools
- [ ] Update tests
- [ ] Update documentation

## Migration Tools

### Automated Migration Script

```bash
# Run the migration script
./scripts/migrate-to-v1.sh /path/to/your/project

# Manual steps after migration
1. Update CMakeLists.txt
2. Update include paths
3. Rebuild project
4. Run tests
```

### Migration Helper Functions

```cpp
// Temporary compatibility layer
namespace DeclarativeUI {
namespace Compat {

// Helper for old-style component creation
template<typename T>
std::unique_ptr<T> createComponent() {
    return std::make_unique<T>();
}

// Helper for old-style state management
class LegacyStateManager {
public:
    template<typename T>
    void set(const QString& key, const T& value) {
        StateManager::instance().setState(key, QVariant::fromValue(value));
    }
    
    template<typename T>
    T get(const QString& key) const {
        return StateManager::instance().getState<T>(key);
    }
};

} // namespace Compat
} // namespace DeclarativeUI
```

### Code Transformation Examples

**Batch Replacement Patterns**

```bash
# Replace old namespace usage
find . -name "*.cpp" -o -name "*.hpp" | xargs sed -i 's/DeclarativeUI::/DeclarativeUI::Components::/g'

# Replace old component creation
find . -name "*.cpp" -o -name "*.hpp" | xargs sed -i 's/new Components::/Components::/g'

# Replace old state manager usage
find . -name "*.cpp" -o -name "*.hpp" | xargs sed -i 's/StateManager manager/auto\& manager = StateManager::instance()/g'
```

## Best Practices for Migration

### Incremental Migration

1. **Start with New Components**: Use DeclarativeUI for new features
2. **Gradual Replacement**: Replace existing components one by one
3. **Maintain Compatibility**: Keep old and new code working together
4. **Test Thoroughly**: Ensure functionality is preserved

### Testing Strategy

```cpp
// Create compatibility tests
class MigrationTest : public QObject {
    Q_OBJECT
    
private slots:
    void testOldVsNewButton() {
        // Test that old and new button behavior is identical
        auto oldButton = createOldStyleButton();
        auto newButton = Components::Button().text("Test");
        
        QCOMPARE(oldButton->text(), newButton.text());
        QCOMPARE(oldButton->isEnabled(), newButton.isEnabled());
    }
};
```

### Documentation Updates

1. Update API documentation
2. Update examples and tutorials
3. Create migration guides for specific use cases
4. Update build instructions

## Getting Help

- **Migration Issues**: Create an issue with the "migration" label
- **API Questions**: Check the API documentation
- **Examples**: Look at the updated examples in the repository
- **Community**: Join the discussions for migration help
