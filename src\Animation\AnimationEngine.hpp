#pragma once

#include <QObject>
#include <QString>
#include <QWidget>
#include <QPropertyAnimation>
#include <QSequentialAnimationGroup>
#include <QParallelAnimationGroup>
#include <QEasingCurve>
#include <QTimer>
#include <QJsonObject>
// #include <QOpenGLWidget>  // Commented out - may not be available in this Qt installation

#include <memory>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <functional>
#include <chrono>
#include <atomic>
#include <shared_mutex>

namespace DeclarativeUI::Animation {

// **Easing function types**
enum class EasingType {
    Linear,
    QuadIn, QuadOut, QuadInOut,
    CubicIn, CubicOut, CubicInOut,
    QuartIn, QuartOut, QuartInOut,
    QuintIn, QuintOut, QuintInOut,
    SineIn, SineOut, SineInOut,
    ExpoIn, ExpoOut, ExpoInOut,
    CircIn, CircOut, CircInOut,
    BackIn, BackOut, BackInOut,
    ElasticIn, ElasticOut, ElasticInOut,
    BounceIn, BounceOut, BounceInOut,
    Custom
};

// **Animation state**
enum class AnimationState {
    Stopped,
    Running,
    Paused,
    Finished
};

// **Keyframe for timeline animations**
template<typename T>
struct Keyframe {
    double time_ratio;  // 0.0 to 1.0
    T value;
    EasingType easing = EasingType::Linear;
    
    Keyframe(double time, const T& val, EasingType ease = EasingType::Linear)
        : time_ratio(time), value(val), easing(ease) {}
};

// **Animation timeline**
template<typename T>
class AnimationTimeline {
public:
    void addKeyframe(double time_ratio, const T& value, EasingType easing = EasingType::Linear) {
        keyframes_.emplace_back(time_ratio, value, easing);
        std::sort(keyframes_.begin(), keyframes_.end(), 
                 [](const Keyframe<T>& a, const Keyframe<T>& b) {
                     return a.time_ratio < b.time_ratio;
                 });
    }

    T interpolate(double time_ratio) const {
        if (keyframes_.empty()) return T{};
        if (keyframes_.size() == 1) return keyframes_[0].value;
        
        // Clamp time ratio
        time_ratio = std::max(0.0, std::min(1.0, time_ratio));
        
        // Find surrounding keyframes
        auto it = std::upper_bound(keyframes_.begin(), keyframes_.end(), time_ratio,
                                  [](double time, const Keyframe<T>& kf) {
                                      return time < kf.time_ratio;
                                  });
        
        if (it == keyframes_.begin()) return keyframes_[0].value;
        if (it == keyframes_.end()) return keyframes_.back().value;
        
        auto next_kf = *it;
        auto prev_kf = *(--it);
        
        // Calculate local time ratio between keyframes
        double local_ratio = (time_ratio - prev_kf.time_ratio) / 
                           (next_kf.time_ratio - prev_kf.time_ratio);
        
        // Apply easing
        double eased_ratio = applyEasing(local_ratio, prev_kf.easing);
        
        // Interpolate between values
        return interpolateValues(prev_kf.value, next_kf.value, eased_ratio);
    }

    const std::vector<Keyframe<T>>& getKeyframes() const { return keyframes_; }
    void clear() { keyframes_.clear(); }
    
    // Made public to allow access from Animation class
    double applyEasing(double t, EasingType easing) const;

private:
    std::vector<Keyframe<T>> keyframes_;
    
    T interpolateValues(const T& from, const T& to, double ratio) const;
};

// **Physics-based animation properties**
struct PhysicsProperties {
    double mass = 1.0;
    double stiffness = 100.0;
    double damping = 10.0;
    double velocity = 0.0;
    double friction = 0.0;
    double gravity = 0.0;
    bool use_physics = false;
};

// **Advanced transition effects**
enum class TransitionType {
    None,
    Fade,
    Slide,
    Scale,
    Rotate,
    Flip,
    Bounce,
    Elastic,
    Morph,
    Particle,
    Ripple,
    Custom
};

// **Transition configuration**
struct TransitionConfig {
    TransitionType type = TransitionType::None;
    QString direction = "right";  // for slide: left, right, up, down
    QPoint origin = QPoint(0, 0);  // for scale/rotate
    double intensity = 1.0;
    bool use_3d = false;
    QJsonObject custom_params;
};

// **Animation properties**
struct AnimationProperties {
    int duration_ms = 1000;
    EasingType easing = EasingType::Linear;
    int delay_ms = 0;
    int repeat_count = 1;  // -1 for infinite
    bool auto_reverse = false;
    bool use_gpu_acceleration = false;
    double playback_rate = 1.0;
};

// **Advanced keyframe with multiple properties**
struct AdvancedKeyframe {
    double time_ratio;
    std::unordered_map<QString, QVariant> properties;
    EasingType easing = EasingType::Linear;
    TransitionConfig transition;

    AdvancedKeyframe(double time) : time_ratio(time) {}

    AdvancedKeyframe& property(const QString& name, const QVariant& value) {
        properties[name] = value;
        return *this;
    }

    AdvancedKeyframe& withEasing(EasingType ease) {
        easing = ease;
        return *this;
    }

    AdvancedKeyframe& withTransition(const TransitionConfig& config) {
        transition = config;
        return *this;
    }
};

// **Multi-property timeline**
class MultiPropertyTimeline {
public:
    void addKeyframe(const AdvancedKeyframe& keyframe);
    void addKeyframe(double time_ratio, const std::unordered_map<QString, QVariant>& properties,
                     EasingType easing = EasingType::Linear);

    std::unordered_map<QString, QVariant> interpolate(double time_ratio) const;
    const std::vector<AdvancedKeyframe>& getKeyframes() const { return keyframes_; }
    void clear() { keyframes_.clear(); }

    // Fluent interface for building timelines
    MultiPropertyTimeline& at(double time_ratio) {
        current_keyframe_ = AdvancedKeyframe(time_ratio);
        return *this;
    }

    MultiPropertyTimeline& set(const QString& property, const QVariant& value) {
        current_keyframe_.property(property, value);
        return *this;
    }

    MultiPropertyTimeline& easing(EasingType ease) {
        current_keyframe_.withEasing(ease);
        return *this;
    }

    MultiPropertyTimeline& transition(const TransitionConfig& config) {
        current_keyframe_.withTransition(config);
        return *this;
    }

    MultiPropertyTimeline& commit() {
        addKeyframe(current_keyframe_);
        return *this;
    }

private:
    std::vector<AdvancedKeyframe> keyframes_;
    AdvancedKeyframe current_keyframe_{0.0};

    QVariant interpolateProperty(const QString& property, double time_ratio) const;
};

// **Animation target**
struct AnimationTarget {
    QObject* object = nullptr;
    QString property_name;
    QVariant start_value;
    QVariant end_value;
    std::function<void(const QVariant&)> custom_setter;

    bool isValid() const { return object != nullptr && !property_name.isEmpty(); }
};

// **Advanced animation class**
class Animation : public QObject {
    Q_OBJECT

public:
    explicit Animation(QObject* parent = nullptr);
    ~Animation() override;

    // **Configuration**
    void setTarget(QObject* object, const QString& property_name);
    void setValues(const QVariant& start_value, const QVariant& end_value);
    void setProperties(const AnimationProperties& properties);
    void setCustomSetter(std::function<void(const QVariant&)> setter);

    // **Timeline support**
    template<typename T>
    void setTimeline(const AnimationTimeline<T>& timeline);
    void setMultiPropertyTimeline(const MultiPropertyTimeline& timeline);

    // **Physics support**
    void enablePhysics(const PhysicsProperties& physics);
    void disablePhysics();
    bool isPhysicsEnabled() const { return physics_enabled_; }

    // **Advanced features**
    void setTransition(const TransitionConfig& transition);
    void addProgressCallback(std::function<void(double, const QVariant&)> callback);
    void setValueTransformer(std::function<QVariant(const QVariant&, double)> transformer);

    // **Control**
    void start();
    void stop();
    void pause();
    void resume();
    void restart();

    // **State**
    AnimationState getState() const { return state_; }
    double getProgress() const { return progress_; }
    int getCurrentTime() const;
    int getDuration() const { return properties_.duration_ms; }

    // **Advanced features**
    void enableGPUAcceleration(bool enabled);
    void setPlaybackRate(double rate);
    void addProgressCallback(std::function<void(double)> callback);

signals:
    void started();
    void finished();
    void paused();
    void resumed();
    void progressChanged(double progress);
    void valueChanged(const QVariant& value);

private slots:
    void onTimerUpdate();

private:
    AnimationTarget target_;
    AnimationProperties properties_;
    AnimationState state_ = AnimationState::Stopped;

    std::unique_ptr<QTimer> timer_;
    std::chrono::steady_clock::time_point start_time_;
    std::chrono::steady_clock::time_point pause_time_;
    std::chrono::milliseconds elapsed_time_{0};

    double progress_ = 0.0;
    int current_iteration_ = 0;
    bool reverse_direction_ = false;

    // **Advanced features**
    bool physics_enabled_ = false;
    PhysicsProperties physics_properties_;
    TransitionConfig transition_config_;
    std::unique_ptr<MultiPropertyTimeline> multi_timeline_;

    std::vector<std::function<void(double)>> progress_callbacks_;
    std::vector<std::function<void(double, const QVariant&)>> advanced_callbacks_;
    std::function<QVariant(const QVariant&, double)> value_transformer_;

    void updateAnimation();
    QVariant interpolateValue(double progress) const;
    double applyEasing(double t) const;
    void applyValue(const QVariant& value);
    void updatePhysicsAnimation();
    void applyTransitionEffect(const QVariant& value);
};

// **Animation group for coordinating multiple animations**
class AnimationGroup : public QObject {
    Q_OBJECT

public:
    enum class GroupType {
        Sequential,  // Animations play one after another
        Parallel     // Animations play simultaneously
    };

    explicit AnimationGroup(QObject* parent = nullptr);
    explicit AnimationGroup(GroupType type, QObject* parent = nullptr);
    ~AnimationGroup() override;

    // **Animation management**
    void addAnimation(std::shared_ptr<Animation> animation);
    void removeAnimation(std::shared_ptr<Animation> animation);
    void clear();

    // **Control**
    void start();
    void stop();
    void pause();
    void resume();

    // **State**
    AnimationState getState() const { return state_; }
    double getProgress() const;
    int getDuration() const;

signals:
    void started();
    void finished();
    void paused();
    void resumed();
    void progressChanged(double progress);

private:
    GroupType type_;
    std::vector<std::shared_ptr<Animation>> animations_;
    AnimationState state_ = AnimationState::Stopped;
    int current_animation_index_ = 0;
    
    void onAnimationFinished();
    void updateGroupProgress();
};

// **Animation pool for performance optimization**
class AnimationPool {
public:
    static AnimationPool& instance();

    std::shared_ptr<Animation> acquire();
    void release(std::shared_ptr<Animation> animation);
    
    void setPoolSize(size_t size);
    size_t getAvailableCount() const;
    size_t getAllocatedCount() const;

private:
    std::vector<std::shared_ptr<Animation>> available_animations_;
    std::unordered_set<std::shared_ptr<Animation>> allocated_animations_;
    mutable std::shared_mutex mutex_;
    size_t max_pool_size_ = 100;
    
    void expandPool();
};

// **Physics-based animation**
class PhysicsAnimation : public QObject {
    Q_OBJECT

public:
    explicit PhysicsAnimation(QObject* parent = nullptr);
    ~PhysicsAnimation() override = default;

    void setTarget(QObject* object, const QString& property);
    void setTargetValue(const QVariant& target);
    void setPhysicsProperties(const PhysicsProperties& physics);

    void start();
    void stop();
    void pause();
    void resume();

    AnimationState getState() const { return state_; }
    QVariant getCurrentValue() const { return current_value_; }
    double getVelocity() const { return velocity_; }

signals:
    void started();
    void finished();
    void paused();
    void resumed();
    void valueChanged(const QVariant& value);

private slots:
    void updatePhysics();

private:
    QObject* target_object_ = nullptr;
    QString target_property_;
    QVariant start_value_;
    QVariant target_value_;
    QVariant current_value_;

    PhysicsProperties physics_;
    AnimationState state_ = AnimationState::Stopped;

    double position_ = 0.0;
    double velocity_ = 0.0;
    double acceleration_ = 0.0;

    std::unique_ptr<QTimer> physics_timer_;
    std::chrono::steady_clock::time_point last_update_;

    void calculatePhysics(double dt);
    QVariant interpolateValue(double progress) const;
    void applyValue(const QVariant& value);
};

// **Complex transition animator**
class TransitionAnimator : public QObject {
    Q_OBJECT

public:
    explicit TransitionAnimator(QObject* parent = nullptr);
    ~TransitionAnimator() override = default;

    void setWidgets(QWidget* from_widget, QWidget* to_widget);
    void setTransition(const TransitionConfig& config);
    void setDuration(int duration_ms);

    void start();
    void stop();

    AnimationState getState() const { return state_; }

signals:
    void started();
    void finished();
    void progressChanged(double progress);

private slots:
    void updateTransition();

private:
    QWidget* from_widget_ = nullptr;
    QWidget* to_widget_ = nullptr;
    TransitionConfig config_;
    int duration_ms_ = 1000;
    AnimationState state_ = AnimationState::Stopped;

    std::unique_ptr<QTimer> transition_timer_;
    std::chrono::steady_clock::time_point start_time_;
    double progress_ = 0.0;

    void setupTransition();
    void applyFadeTransition(double progress);
    void applySlideTransition(double progress);
    void applyScaleTransition(double progress);
    void applyRotateTransition(double progress);
    void applyFlipTransition(double progress);
    void applyBounceTransition(double progress);
    void applyElasticTransition(double progress);
    void applyRippleTransition(double progress);
};

// **GPU-accelerated animation renderer**
// Commented out due to missing QOpenGLWidget dependency
/*
class GPUAnimationRenderer : public QOpenGLWidget {
    Q_OBJECT

public:
    explicit GPUAnimationRenderer(QWidget* parent = nullptr);
    ~GPUAnimationRenderer() override;

    void addAnimatedWidget(QWidget* widget);
    void removeAnimatedWidget(QWidget* widget);
    
    void enableHardwareAcceleration(bool enabled);
    void setRenderQuality(int quality);  // 0-100

protected:
    void initializeGL() override;
    void paintGL() override;
    void resizeGL(int width, int height) override;

private:
    std::vector<QWidget*> animated_widgets_;
    bool hardware_acceleration_enabled_ = true;
    int render_quality_ = 80;
    
    void renderWidget(QWidget* widget);
    void applyGPUEffects(QWidget* widget);
};
*/

// **Main animation engine**
class AnimationEngine : public QObject {
    Q_OBJECT

public:
    static AnimationEngine& instance();
    explicit AnimationEngine(QObject* parent = nullptr);
    ~AnimationEngine() override;

    // **Animation creation**
    std::shared_ptr<Animation> createAnimation();
    std::shared_ptr<AnimationGroup> createAnimationGroup(AnimationGroup::GroupType type);
    
    // **Convenience methods**
    std::shared_ptr<Animation> animateProperty(QObject* object, const QString& property,
                                              const QVariant& start_value, const QVariant& end_value,
                                              int duration_ms = 1000, EasingType easing = EasingType::Linear);
    
    std::shared_ptr<Animation> fadeIn(QWidget* widget, int duration_ms = 500);
    std::shared_ptr<Animation> fadeOut(QWidget* widget, int duration_ms = 500);
    std::shared_ptr<Animation> slideIn(QWidget* widget, const QString& direction, int duration_ms = 500);
    std::shared_ptr<Animation> slideOut(QWidget* widget, const QString& direction, int duration_ms = 500);
    std::shared_ptr<Animation> scaleAnimation(QWidget* widget, double from_scale, double to_scale, int duration_ms = 500);

    // **Advanced animation creation**
    std::shared_ptr<PhysicsAnimation> createPhysicsAnimation();
    std::shared_ptr<TransitionAnimator> createTransitionAnimator();

    // **Timeline-based animations**
    std::shared_ptr<Animation> createTimelineAnimation(const MultiPropertyTimeline& timeline);

    // **Physics-based animations**
    std::shared_ptr<PhysicsAnimation> createSpringAnimation(QObject* object, const QString& property,
                                                           const QVariant& target_value,
                                                           double stiffness = 100.0, double damping = 10.0);

    // **Complex transitions**
    std::shared_ptr<TransitionAnimator> createPageTransition(QWidget* from_widget, QWidget* to_widget,
                                                            TransitionType type, int duration_ms = 1000);

    // **Particle and effect animations**
    std::shared_ptr<Animation> createRippleEffect(QWidget* widget, const QPoint& origin, int duration_ms = 800);
    std::shared_ptr<Animation> createMorphAnimation(QWidget* widget, const QRect& target_geometry, int duration_ms = 500);

    // **Sequence and parallel builders**
    class AnimationSequenceBuilder {
    public:
        AnimationSequenceBuilder& then(std::shared_ptr<Animation> animation);
        AnimationSequenceBuilder& wait(int duration_ms);
        AnimationSequenceBuilder& parallel(std::function<void(AnimationSequenceBuilder&)> parallel_builder);
        std::shared_ptr<AnimationGroup> build();
    private:
        std::vector<std::shared_ptr<Animation>> animations_;
    };

    AnimationSequenceBuilder sequence();

    // **Global control**
    void pauseAllAnimations();
    void resumeAllAnimations();
    void stopAllAnimations();
    
    // **Performance optimization**
    void enableGlobalGPUAcceleration(bool enabled);
    void setGlobalPlaybackRate(double rate);
    void enableAnimationPooling(bool enabled);
    void setMaxConcurrentAnimations(int max_count);

    // **Monitoring**
    QJsonObject getPerformanceMetrics() const;
    int getActiveAnimationCount() const;
    double getAverageFrameRate() const;

    // **Configuration**
    void setDefaultEasing(EasingType easing);
    void setDefaultDuration(int duration_ms);
    void enableVSync(bool enabled);

signals:
    void animationStarted(const QString& animation_id);
    void animationFinished(const QString& animation_id);
    void performanceAlert(const QString& metric, double value);

private slots:
    void onGlobalTimer();
    void onPerformanceCheck();

private:
    std::vector<std::shared_ptr<Animation>> active_animations_;
    std::vector<std::shared_ptr<AnimationGroup>> active_groups_;
    mutable std::shared_mutex animations_mutex_;
    
    // std::unique_ptr<GPUAnimationRenderer> gpu_renderer_;  // Commented out due to missing QOpenGLWidget
    std::unique_ptr<QTimer> global_timer_;
    std::unique_ptr<QTimer> performance_timer_;
    
    // **Configuration**
    std::atomic<bool> global_gpu_acceleration_{false};
    std::atomic<double> global_playback_rate_{1.0};
    std::atomic<bool> animation_pooling_enabled_{true};
    std::atomic<int> max_concurrent_animations_{50};
    EasingType default_easing_ = EasingType::Linear;
    int default_duration_ms_ = 1000;
    bool vsync_enabled_ = true;
    
    // **Performance tracking**
    std::atomic<size_t> total_animations_created_{0};
    std::atomic<size_t> total_animations_completed_{0};
    std::atomic<double> total_frame_time_{0.0};
    std::atomic<size_t> frame_count_{0};
    
    void registerAnimation(std::shared_ptr<Animation> animation);
    void unregisterAnimation(std::shared_ptr<Animation> animation);
    void updatePerformanceMetrics();
    void checkPerformanceAlerts();
    void optimizeAnimations();
};

}  // namespace DeclarativeUI::Animation
