# Debugging Guide

This guide provides comprehensive debugging strategies and tools for developing with the DeclarativeUI Framework.

## Table of Contents

- [Debug Build Configuration](#debug-build-configuration)
- [Debugging Tools](#debugging-tools)
- [Common Issues](#common-issues)
- [Performance Debugging](#performance-debugging)
- [Memory Debugging](#memory-debugging)
- [Qt-Specific Debugging](#qt-specific-debugging)
- [Framework-Specific Debugging](#framework-specific-debugging)

## Debug Build Configuration

### CMake Debug Build

```bash
# Configure for debug build
cmake --preset=debug -B build-debug
# or
cmake -B build-debug -DCMAKE_BUILD_TYPE=Debug -DDECLARATIVE_UI_DEBUG=ON

# Build with debug symbols
cmake --build build-debug --config Debug
```

### Debug Preprocessor Definitions

The framework provides several debug flags:

```cpp
// Automatically defined in Debug builds
#ifdef DECLARATIVE_UI_DEBUG
    qDebug() << "Debug information available";
#endif

// Enable command system debugging
#ifdef DECLARATIVE_UI_COMMAND_DEBUG
    qDebug() << "Command system debug output";
#endif
```

### Compiler Debug Flags

```cmake
# Additional debug flags for GCC/Clang
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG -Wall -Wextra")

# MSVC debug flags
set(CMAKE_CXX_FLAGS_DEBUG "/Zi /Od /DEBUG /D DEBUG")
```

## Debugging Tools

### GDB (Linux/macOS)

```bash
# Start debugging
gdb ./build-debug/examples/basic/HelloWorldExample

# Common GDB commands
(gdb) run                    # Start execution
(gdb) break main             # Set breakpoint at main
(gdb) break Button.cpp:42    # Set breakpoint at specific line
(gdb) continue               # Continue execution
(gdb) step                   # Step into function
(gdb) next                   # Step over function
(gdb) print variable         # Print variable value
(gdb) backtrace             # Show call stack
(gdb) info locals           # Show local variables
```

### LLDB (macOS)

```bash
# Start debugging
lldb ./build-debug/examples/basic/HelloWorldExample

# Common LLDB commands
(lldb) run                   # Start execution
(lldb) breakpoint set -n main # Set breakpoint
(lldb) continue              # Continue execution
(lldb) step                  # Step into
(lldb) next                  # Step over
(lldb) print variable        # Print variable
(lldb) bt                    # Show backtrace
```

### Visual Studio Debugger (Windows)

```cpp
// Use debug output in Visual Studio
#ifdef _WIN32
    OutputDebugStringA("Debug message\n");
#endif

// Set breakpoints in Visual Studio
// F9 - Toggle breakpoint
// F5 - Start debugging
// F10 - Step over
// F11 - Step into
```

### Qt Creator Debugging

Qt Creator provides excellent debugging support:

1. **Set Breakpoints**: Click in the left margin or press F9
2. **Debug Mode**: Press F5 to start debugging
3. **Variable Inspection**: Hover over variables or use Locals view
4. **Call Stack**: View in Debug mode
5. **Qt Object Inspector**: Inspect Qt objects and their properties

## Common Issues

### Component Not Displaying

```cpp
// ✅ Check if widget is properly initialized
void Button::initialize() {
    if (!getWidget()) {
        auto* button = new QPushButton();
        setWidget(button);
    }
}

// ✅ Ensure parent widget is shown
auto* parent = new QWidget();
auto button = Components::Button();
button.setParent(parent);
parent->show(); // Don't forget this!

// ✅ Check if widget is visible
if (auto* widget = getWidget()) {
    qDebug() << "Widget visible:" << widget->isVisible();
    qDebug() << "Widget size:" << widget->size();
}
```

### State Management Issues

```cpp
// ✅ Debug state changes
void StateManager::setState(const QString& key, const QVariant& value) {
#ifdef DECLARATIVE_UI_DEBUG
    qDebug() << "Setting state:" << key << "=" << value;
#endif
    
    // Check if key exists
    if (!hasState(key)) {
        qWarning() << "Creating new state key:" << key;
    }
    
    // Set value and emit signal
    states_[key] = value;
    emit stateChanged(key, value);
}
```

### Property Binding Problems

```cpp
// ✅ Debug property binding
class PropertyBinding {
public:
    void bind(const QString& property, const QString& stateKey) {
#ifdef DECLARATIVE_UI_DEBUG
        qDebug() << "Binding property" << property << "to state" << stateKey;
#endif
        
        // Verify state exists
        if (!StateManager::instance().hasState(stateKey)) {
            qWarning() << "State key does not exist:" << stateKey;
            return;
        }
        
        bindings_[property] = stateKey;
    }
};
```

## Performance Debugging

### Profiling with Built-in Tools

```cpp
// Use the framework's performance monitor
#include "Debug/PerformanceMonitor.hpp"

void expensiveOperation() {
    PerformanceMonitor::Timer timer("expensiveOperation");
    
    // Your code here
    
    // Timer automatically reports when destroyed
}

// Check performance metrics
auto metrics = PerformanceMonitor::instance().getMetrics();
qDebug() << "Average operation time:" << metrics.averageTime;
```

### Qt Performance Debugging

```cpp
// Enable Qt logging for performance
QLoggingCategory::setFilterRules("qt.qpa.events.debug=true");

// Use QElapsedTimer for timing
QElapsedTimer timer;
timer.start();
performOperation();
qDebug() << "Operation took:" << timer.elapsed() << "ms";
```

### Profiling Tools

```bash
# Valgrind (Linux)
valgrind --tool=callgrind ./your-application
kcachegrind callgrind.out.*

# Perf (Linux)
perf record ./your-application
perf report

# Instruments (macOS)
# Use Xcode Instruments for profiling
```

## Memory Debugging

### Valgrind (Linux)

```bash
# Memory leak detection
valgrind --leak-check=full --show-leak-kinds=all ./your-application

# Memory error detection
valgrind --tool=memcheck ./your-application

# Heap profiling
valgrind --tool=massif ./your-application
```

### AddressSanitizer

```cmake
# Enable AddressSanitizer in CMake
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fsanitize=address")
set(CMAKE_LINKER_FLAGS_DEBUG "${CMAKE_LINKER_FLAGS_DEBUG} -fsanitize=address")
```

### Qt Memory Debugging

```cpp
// Enable Qt object debugging
#ifdef QT_DEBUG
    QObject::dumpObjectTree(); // Dump object hierarchy
    QObject::dumpObjectInfo();  // Dump object information
#endif

// Check for memory leaks in Qt objects
class DebugWidget : public QWidget {
public:
    DebugWidget() {
        qDebug() << "Widget created:" << this;
    }
    
    ~DebugWidget() {
        qDebug() << "Widget destroyed:" << this;
    }
};
```

## Qt-Specific Debugging

### Signal-Slot Debugging

```cpp
// Enable signal-slot debugging
QLoggingCategory::setFilterRules("qt.core.qobject.connect.debug=true");

// Debug signal emissions
class Component : public QObject {
    Q_OBJECT
    
signals:
    void valueChanged(int value);
    
public slots:
    void setValue(int value) {
        if (value_ != value) {
            value_ = value;
            qDebug() << "Emitting valueChanged:" << value;
            emit valueChanged(value);
        }
    }
    
private:
    int value_ = 0;
};
```

### Widget Debugging

```cpp
// Debug widget hierarchy
void debugWidgetHierarchy(QWidget* widget, int level = 0) {
    QString indent(level * 2, ' ');
    qDebug() << indent << widget->metaObject()->className() 
             << widget->objectName() << widget->geometry();
    
    for (auto* child : widget->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly)) {
        debugWidgetHierarchy(child, level + 1);
    }
}

// Debug widget properties
void debugWidgetProperties(QWidget* widget) {
    qDebug() << "Widget:" << widget->objectName();
    qDebug() << "  Visible:" << widget->isVisible();
    qDebug() << "  Enabled:" << widget->isEnabled();
    qDebug() << "  Geometry:" << widget->geometry();
    qDebug() << "  Size hint:" << widget->sizeHint();
    qDebug() << "  Minimum size:" << widget->minimumSize();
    qDebug() << "  Maximum size:" << widget->maximumSize();
}
```

## Framework-Specific Debugging

### Component Debugging

```cpp
// Enable component debugging
class UIElement {
protected:
    void debugComponent(const QString& operation) const {
#ifdef DECLARATIVE_UI_DEBUG
        qDebug() << "Component" << metaObject()->className() 
                 << objectName() << operation;
#endif
    }
    
public:
    void initialize() override {
        debugComponent("initializing");
        // Implementation
        debugComponent("initialized");
    }
};
```

### Hot Reload Debugging

```cpp
// Debug file watching
class FileWatcher {
public:
    void addPath(const QString& path) {
#ifdef DECLARATIVE_UI_DEBUG
        qDebug() << "Watching file:" << path;
#endif
        watcher_.addPath(path);
    }
    
private slots:
    void onFileChanged(const QString& path) {
#ifdef DECLARATIVE_UI_DEBUG
        qDebug() << "File changed:" << path;
#endif
        emit fileChanged(path);
    }
};
```

### Command System Debugging

```cpp
// Debug command execution
class CommandSystem {
public:
    void executeCommand(std::unique_ptr<UICommand> command) {
#ifdef DECLARATIVE_UI_COMMAND_DEBUG
        qDebug() << "Executing command:" << command->type();
#endif
        
        try {
            command->execute();
#ifdef DECLARATIVE_UI_COMMAND_DEBUG
            qDebug() << "Command executed successfully";
#endif
        } catch (const std::exception& e) {
            qWarning() << "Command execution failed:" << e.what();
        }
    }
};
```

## Debugging Best Practices

### Logging Strategy

```cpp
// Use Qt logging categories
Q_LOGGING_CATEGORY(componentLog, "declarativeui.component")
Q_LOGGING_CATEGORY(stateLog, "declarativeui.state")
Q_LOGGING_CATEGORY(commandLog, "declarativeui.command")

// Use appropriate log levels
qCDebug(componentLog) << "Debug information";
qCInfo(componentLog) << "Informational message";
qCWarning(componentLog) << "Warning message";
qCCritical(componentLog) << "Critical error";
```

### Assertions

```cpp
// Use Q_ASSERT for debug builds
void setWidget(QWidget* widget) {
    Q_ASSERT(widget != nullptr);
    Q_ASSERT(widget_ == nullptr); // Ensure not already set
    widget_ = widget;
}

// Use Q_ASSERT_X for detailed messages
Q_ASSERT_X(index >= 0 && index < components_.size(), 
           "ComponentManager::getComponent", 
           "Index out of range");
```

### Debug Output Formatting

```cpp
// Custom debug output for your types
QDebug operator<<(QDebug debug, const Component& component) {
    QDebugStateSaver saver(debug);
    debug.nospace() << "Component(" 
                    << "type=" << component.type() 
                    << ", name=" << component.name() 
                    << ")";
    return debug;
}
```
