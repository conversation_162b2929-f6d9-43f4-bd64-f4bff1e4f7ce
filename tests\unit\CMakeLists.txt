# **Unit Tests Configuration**
# Core unit tests for individual components

# **Core Module Tests**
add_executable(CoreTest test_core.cpp)
target_link_libraries(CoreTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Advanced Core Module Tests**
add_executable(CoreAdvancedTest test_core_advanced.cpp)
target_link_libraries(CoreAdvancedTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
    Qt6::Concurrent
)

# **Component Tests**
add_executable(ComponentTest test_components.cpp)
target_link_libraries(ComponentTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Container Component Tests**
add_executable(ContainerComponentTest test_container_components.cpp)
target_link_libraries(ContainerComponentTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Binding Module Tests**
add_executable(PropertyBindingTest test_property_binding.cpp)
target_link_libraries(PropertyBindingTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(StateManagerTest test_state_manager.cpp)
target_link_libraries(StateManagerTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(PropertyBindingTemplateTest test_property_binding_template.cpp)
target_link_libraries(PropertyBindingTemplateTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **JSON Module Tests**
add_executable(JSONTest test_json.cpp)
target_link_libraries(JSONTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Hot Reload Module Tests** (temporarily disabled due to API mismatches)
# add_executable(HotReloadTest test_hot_reload.cpp)
# target_link_libraries(HotReloadTest
#     DeclarativeUI
#     Components
#     Qt6::Core
#     Qt6::Widgets
#     Qt6::Test
# )

# **Exception Handling Tests**
add_executable(ExceptionsTest test_exceptions.cpp)
target_link_libraries(ExceptionsTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Thread Safety Tests**
add_executable(ThreadSafetyTest test_thread_safety.cpp)
target_link_libraries(ThreadSafetyTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
    Qt6::Concurrent
)

# **Modern Component Tests**
add_executable(ToastTest test_toast.cpp)
target_link_libraries(ToastTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Enhanced Hot Reload Tests**
add_executable(EnhancedConfigurationSystemTest test_enhanced_configuration_system.cpp)
target_link_libraries(EnhancedConfigurationSystemTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
    Qt6::Concurrent
)

# **Enhanced Configuration System Simple Tests**
add_executable(EnhancedConfigurationSystemSimpleTest test_enhanced_configuration_system_simple.cpp)
target_link_libraries(EnhancedConfigurationSystemSimpleTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
    Qt6::Concurrent
)

add_executable(AdvancedFileFilterTest test_advanced_file_filter.cpp)
target_link_libraries(AdvancedFileFilterTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(HotReloadDashboardTest test_hotreload_dashboard.cpp)
target_link_libraries(HotReloadDashboardTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(SelectiveReloadManagerTest test_selective_reload_manager.cpp)
target_link_libraries(SelectiveReloadManagerTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
    Qt6::Concurrent
)

add_executable(EnhancedErrorRecoverySystemTest test_enhanced_error_recovery_system.cpp)
target_link_libraries(EnhancedErrorRecoverySystemTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
    Qt6::Concurrent
)

add_executable(HotReloadConfigTest test_hot_reload_config.cpp)
target_link_libraries(HotReloadConfigTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

add_executable(HotReloadConfigSimpleTest test_hot_reload_config_simple.cpp)
target_link_libraries(HotReloadConfigSimpleTest
    DeclarativeUI
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

# **Set output directory for unit tests**
set_target_properties(
    CoreTest
    CoreAdvancedTest
    ComponentTest
    ContainerComponentTest
    PropertyBindingTest
    StateManagerTest
    PropertyBindingTemplateTest
    JSONTest
    # HotReloadTest
    ExceptionsTest
    ThreadSafetyTest
    ToastTest
    EnhancedConfigurationSystemTest
    EnhancedConfigurationSystemSimpleTest
    AdvancedFileFilterTest
    HotReloadDashboardTest
    SelectiveReloadManagerTest
    EnhancedErrorRecoverySystemTest
    HotReloadConfigTest
    HotReloadConfigSimpleTest
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit
)

# **Make tests depend on resources**
add_dependencies(CoreTest CopyTestResources)
add_dependencies(CoreAdvancedTest CopyTestResources)
add_dependencies(ComponentTest CopyTestResources)
add_dependencies(ContainerComponentTest CopyTestResources)
add_dependencies(PropertyBindingTest CopyTestResources)
add_dependencies(StateManagerTest CopyTestResources)
add_dependencies(PropertyBindingTemplateTest CopyTestResources)
add_dependencies(JSONTest CopyTestResources)
# add_dependencies(HotReloadTest CopyTestResources)
add_dependencies(ExceptionsTest CopyTestResources)
add_dependencies(ThreadSafetyTest CopyTestResources)
add_dependencies(ToastTest CopyTestResources)
add_dependencies(EnhancedConfigurationSystemTest CopyTestResources)
add_dependencies(EnhancedConfigurationSystemSimpleTest CopyTestResources)
add_dependencies(AdvancedFileFilterTest CopyTestResources)
add_dependencies(HotReloadDashboardTest CopyTestResources)
add_dependencies(SelectiveReloadManagerTest CopyTestResources)
add_dependencies(EnhancedErrorRecoverySystemTest CopyTestResources)
add_dependencies(HotReloadConfigTest CopyTestResources)
add_dependencies(HotReloadConfigSimpleTest CopyTestResources)

# **Register tests with CTest**
add_test(NAME CoreTest COMMAND CoreTest)
add_test(NAME CoreAdvancedTest COMMAND CoreAdvancedTest)
add_test(NAME ComponentTest COMMAND ComponentTest)
add_test(NAME ContainerComponentTest COMMAND ContainerComponentTest)
add_test(NAME PropertyBindingTest COMMAND PropertyBindingTest)
add_test(NAME StateManagerTest COMMAND StateManagerTest)
add_test(NAME PropertyBindingTemplateTest COMMAND PropertyBindingTemplateTest)
add_test(NAME JSONTest COMMAND JSONTest)
# add_test(NAME HotReloadTest COMMAND HotReloadTest)
add_test(NAME ExceptionsTest COMMAND ExceptionsTest)
add_test(NAME ThreadSafetyTest COMMAND ThreadSafetyTest)
add_test(NAME ToastTest COMMAND ToastTest)
add_test(NAME EnhancedConfigurationSystemTest COMMAND EnhancedConfigurationSystemTest)
add_test(NAME EnhancedConfigurationSystemSimpleTest COMMAND EnhancedConfigurationSystemSimpleTest)
add_test(NAME AdvancedFileFilterTest COMMAND AdvancedFileFilterTest)
add_test(NAME HotReloadDashboardTest COMMAND HotReloadDashboardTest)
add_test(NAME SelectiveReloadManagerTest COMMAND SelectiveReloadManagerTest)
add_test(NAME EnhancedErrorRecoverySystemTest COMMAND EnhancedErrorRecoverySystemTest)
add_test(NAME HotReloadConfigTest COMMAND HotReloadConfigTest)
add_test(NAME HotReloadConfigSimpleTest COMMAND HotReloadConfigSimpleTest)
