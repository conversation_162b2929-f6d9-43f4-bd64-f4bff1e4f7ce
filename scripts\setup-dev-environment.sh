#!/bin/bash
# Setup development environment for DeclarativeUI Framework

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Setting up DeclarativeUI development environment...${NC}"

# Check if Python is available
if ! command -v python3 >/dev/null 2>&1; then
    echo -e "${RED}❌ Python 3 is not installed or not in PATH${NC}"
    echo "Please install Python 3.8+ and add it to PATH"
    exit 1
fi

# Check if pip is available
if ! command -v pip3 >/dev/null 2>&1; then
    echo -e "${RED}❌ pip3 is not available${NC}"
    echo "Please ensure pip is installed with Python"
    exit 1
fi

echo -e "${GREEN}✅ Python and pip are available${NC}"

# Install pre-commit if not already installed
echo -e "${BLUE}📦 Installing pre-commit...${NC}"
pip3 install --user pre-commit

# Make sure ~/.local/bin is in PATH
if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
    echo -e "${YELLOW}⚠️  Adding ~/.local/bin to PATH${NC}"
    export PATH="$HOME/.local/bin:$PATH"
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
fi

# Install pre-commit hooks
echo -e "${BLUE}🔧 Installing pre-commit hooks...${NC}"
pre-commit install

# Install additional development tools
echo -e "${BLUE}📦 Installing additional development tools...${NC}"
pip3 install --user black isort flake8 mypy

# Check if clang-format is available
if command -v clang-format >/dev/null 2>&1; then
    echo -e "${GREEN}✅ clang-format is available${NC}"
else
    echo -e "${YELLOW}⚠️  clang-format is not available${NC}"
    echo "Installing clang-format..."
    
    # Install clang-format based on the system
    if command -v apt-get >/dev/null 2>&1; then
        sudo apt-get update && sudo apt-get install -y clang-format
    elif command -v yum >/dev/null 2>&1; then
        sudo yum install -y clang-tools-extra
    elif command -v brew >/dev/null 2>&1; then
        brew install clang-format
    else
        echo -e "${YELLOW}Please install clang-format manually${NC}"
    fi
fi

# Check if CMake is available
if command -v cmake >/dev/null 2>&1; then
    CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
    echo -e "${GREEN}✅ CMake $CMAKE_VERSION is available${NC}"
    
    # Check if version is 3.20+
    if [[ $(echo "$CMAKE_VERSION 3.20" | tr ' ' '\n' | sort -V | head -n1) != "3.20" ]]; then
        echo -e "${YELLOW}⚠️  CMake version should be 3.20 or higher${NC}"
    fi
else
    echo -e "${RED}❌ CMake is not installed or not in PATH${NC}"
    echo "Please install CMake 3.20+ and add it to PATH"
    exit 1
fi

# Check if Qt6 is available
echo -e "${BLUE}🔍 Checking for Qt6...${NC}"
if command -v qmake6 >/dev/null 2>&1 || command -v qmake >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Qt6 tools found${NC}"
elif [ -d "/usr/lib/qt6" ] || [ -d "/opt/qt6" ] || [ -d "$HOME/Qt" ]; then
    echo -e "${GREEN}✅ Qt6 installation found${NC}"
else
    echo -e "${YELLOW}⚠️  Qt6 not found in standard locations${NC}"
    echo "Please ensure Qt6 is installed and CMAKE_PREFIX_PATH is set"
    echo "Install Qt6 development packages:"
    echo "  Ubuntu/Debian: sudo apt-get install qt6-base-dev qt6-tools-dev"
    echo "  Fedora: sudo dnf install qt6-qtbase-devel qt6-qttools-devel"
    echo "  macOS: brew install qt6"
fi

# Make scripts executable
echo -e "${BLUE}🔧 Making scripts executable...${NC}"
chmod +x scripts/*.sh scripts/*.py

# Create .secrets.baseline for detect-secrets
echo -e "${BLUE}📝 Creating secrets baseline...${NC}"
if [ ! -f ".secrets.baseline" ]; then
    echo '{}' > .secrets.baseline
fi

# Run initial pre-commit check
echo -e "${BLUE}🧪 Running initial pre-commit check...${NC}"
pre-commit run --all-files || true

echo ""
echo -e "${GREEN}🎉 Development environment setup complete!${NC}"
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "   1. Configure your IDE to use .editorconfig settings"
echo "   2. Set up Qt6 environment variables if needed:"
echo "      export CMAKE_PREFIX_PATH=/path/to/qt6"
echo "   3. Build the project:"
echo "      mkdir build && cd build"
echo "      cmake --preset=default .."
echo "      cmake --build . --config Release"
echo "   4. Run tests: ctest --output-on-failure"
echo ""
echo -e "${BLUE}🔧 Available development commands:${NC}"
echo "   - pre-commit run --all-files  # Run all checks"
echo "   - pre-commit run <hook-name>  # Run specific check"
echo "   - scripts/check-qt-includes.sh <files>  # Check Qt includes"
echo "   - scripts/validate-json-ui.py <files>   # Validate JSON UI"
echo ""
echo -e "${BLUE}📚 Documentation:${NC}"
echo "   - README.md - Project overview and quick start"
echo "   - CONTRIBUTING.md - Contributing guidelines"
echo "   - docs/ - Comprehensive documentation"
echo ""
