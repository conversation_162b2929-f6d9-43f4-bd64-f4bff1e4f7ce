#pragma once

#include "IFormatProcessor.hpp"
#include <QWidget>
#include <QApplication>
#include <QRegularExpression>
#include <QTextStream>
#include <QDir>
#include <memory>
#include <unordered_map>

namespace DeclarativeUI::HotReload::FormatSupport {

/**
 * @brief CSS processor with live injection capabilities
 */
class CSSProcessor : public IFormatProcessor {
    Q_OBJECT

public:
    explicit CSSProcessor(QObject* parent = nullptr);
    ~CSSProcessor() override = default;

    // IFormatProcessor interface
    QString getFormatName() const override { return "CSS"; }
    QStringList getSupportedExtensions() const override { return {"css"}; }
    bool canProcess(const QString& file_path) const override;
    
    ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    
    bool validateFile(const QString& file_path) const override;
    bool validateContent(const QString& content) const override;
    
    ProcessingConfig getDefaultConfig() const override;
    QStringList getRequiredDependencies() const override { return QStringList(); }
    bool isAvailable() const override { return true; }
    
    // Hot reload specific methods
    bool supportsLiveInjection() const override { return true; }
    ProcessingResult prepareLiveInjection(const QString& content, const QString& file_path) override;

    // CSS-specific methods
    void setTargetWidget(QWidget* widget);
    void setTargetApplication(QApplication* app);
    void enableAutoInjection(bool enabled);
    void setInjectionScope(const QString& scope); // "widget", "application", "global"
    
    // CSS processing options
    void enableMinification(bool enabled);
    void enableAutoprefixer(bool enabled);
    void setVariableSubstitution(const QVariantMap& variables);
    
    // Live injection management
    bool injectCSS(const QString& css_content, const QString& identifier = QString());
    bool removeInjectedCSS(const QString& identifier);
    void clearAllInjectedCSS();
    QStringList getInjectedCSSIdentifiers() const;

signals:
    void cssInjected(const QString& identifier, const QString& css_content);
    void cssRemoved(const QString& identifier);
    void injectionFailed(const QString& identifier, const QString& error);

private:
    struct InjectedCSS {
        QString identifier;
        QString original_content;
        QString processed_content;
        QDateTime injection_time;
        QString file_path;
    };
    
    // CSS processing methods
    QString minifyCSS(const QString& css_content) const;
    QString addAutoprefixes(const QString& css_content) const;
    QString substituteVariables(const QString& css_content, const QVariantMap& variables) const;
    QString processImports(const QString& css_content, const QString& base_path) const;
    
    // Validation methods
    bool validateCSSContent(const QString& css_content) const;
    QStringList findCSSErrors(const QString& css_content) const;
    
    // Live injection implementation
    bool injectCSSToWidget(QWidget* widget, const QString& css_content, const QString& identifier);
    bool injectCSSToApplication(QApplication* app, const QString& css_content, const QString& identifier);
    bool injectCSSGlobally(const QString& css_content, const QString& identifier);
    
    QString generateStyleSheetUpdate(const QString& existing_stylesheet, const QString& new_css, const QString& identifier) const;
    QString removeStyleSheetSection(const QString& stylesheet, const QString& identifier) const;
    
    // Configuration
    QWidget* target_widget_ = nullptr;
    QApplication* target_application_ = nullptr;
    bool auto_injection_enabled_ = true;
    QString injection_scope_ = "application"; // "widget", "application", "global"
    
    // Processing options
    bool minification_enabled_ = false;
    bool autoprefixer_enabled_ = false;
    QVariantMap variable_substitutions_;
    
    // Injection tracking
    std::unordered_map<QString, InjectedCSS> injected_css_;
    mutable QMutex injection_mutex_;
    
    // CSS parsing helpers
    QRegularExpression css_rule_regex_;
    QRegularExpression css_property_regex_;
    QRegularExpression css_import_regex_;
    QRegularExpression css_variable_regex_;
    
    void initializeRegexPatterns();
    QString createInjectionMarker(const QString& identifier) const;
    QPair<QString, QString> extractInjectionMarkers() const;
};

/**
 * @brief CSS injection helper for automatic cleanup
 */
class CSSInjectionGuard {
public:
    CSSInjectionGuard(CSSProcessor* processor, const QString& identifier);
    ~CSSInjectionGuard();
    
    bool inject(const QString& css_content);
    void release(); // Prevent automatic cleanup
    
private:
    CSSProcessor* processor_;
    QString identifier_;
    bool auto_cleanup_;
};

} // namespace DeclarativeUI::HotReload::FormatSupport
