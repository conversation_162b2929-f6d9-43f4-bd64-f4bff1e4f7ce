#include "TestingFramework.hpp"
#include <QElapsedTimer>
#include <QApplication>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QJsonDocument>
#include <QFile>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <algorithm>
#include <numeric>
#include <cmath>

#ifdef Q_OS_WIN
#include <windows.h>
#include <psapi.h>
#elif defined(Q_OS_LINUX)
#include <sys/resource.h>
#include <unistd.h>
#include <fstream>
#elif defined(Q_OS_MACOS)
#include <mach/mach.h>
#include <sys/resource.h>
#endif

namespace DeclarativeUI::Testing {

// **PerformanceBenchmark Implementation**

PerformanceBenchmark::PerformanceBenchmark(QObject* parent)
    : QObject(parent)
{
}

PerformanceBenchmark::TimingResult PerformanceBenchmark::measureFunction(std::function<void()> func, int iterations) {
    TimingResult result;
    result.iteration_count = iterations;
    
    std::vector<qint64> times;
    times.reserve(iterations);
    
    emit benchmarkStarted("Function Benchmark");
    
    for (int i = 0; i < iterations; ++i) {
        QElapsedTimer timer;
        timer.start();
        
        func();
        
        qint64 elapsed = timer.elapsed();
        times.push_back(elapsed);
        result.total_time_ms += elapsed;
        
        // Process events to keep UI responsive during benchmarking
        if (i % 10 == 0) {
            QApplication::processEvents();
        }
    }
    
    // Calculate statistics
    if (!times.empty()) {
        result.min_time_ms = *std::min_element(times.begin(), times.end());
        result.max_time_ms = *std::max_element(times.begin(), times.end());
        result.average_time_ms = result.total_time_ms / iterations;
        
        // Calculate median
        std::vector<qint64> sorted_times = times;
        std::sort(sorted_times.begin(), sorted_times.end());
        size_t mid = sorted_times.size() / 2;
        if (sorted_times.size() % 2 == 0) {
            result.median_time_ms = (sorted_times[mid - 1] + sorted_times[mid]) / 2;
        } else {
            result.median_time_ms = sorted_times[mid];
        }
        
        // Calculate standard deviation
        double sum_squared_diff = 0.0;
        for (qint64 time : times) {
            double diff = time - result.average_time_ms;
            sum_squared_diff += diff * diff;
        }
        result.standard_deviation = std::sqrt(sum_squared_diff / iterations);
    }
    
    // Add metadata
    result.metadata["function_type"] = "generic";
    result.metadata["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    emit benchmarkCompleted("Function Benchmark", result);
    return result;
}

PerformanceBenchmark::TimingResult PerformanceBenchmark::measureWidgetCreation(const QString& widget_type, int iterations) {
    auto creation_func = [&widget_type]() {
        std::unique_ptr<QWidget> widget;
        
        if (widget_type == "QLabel") {
            widget = std::make_unique<QLabel>("Test Label");
        } else if (widget_type == "QPushButton") {
            widget = std::make_unique<QPushButton>("Test Button");
        } else if (widget_type == "QLineEdit") {
            widget = std::make_unique<QLineEdit>();
        } else if (widget_type == "QWidget") {
            widget = std::make_unique<QWidget>();
        } else {
            widget = std::make_unique<QWidget>();  // Fallback
        }
        
        // Force widget initialization
        widget->show();
        widget->hide();
    };
    
    TimingResult result = measureFunction(creation_func, iterations);
    result.metadata["widget_type"] = widget_type;
    result.metadata["benchmark_type"] = "widget_creation";
    
    return result;
}

PerformanceBenchmark::TimingResult PerformanceBenchmark::measureLayoutUpdate(QWidget* widget, int iterations) {
    if (!widget) {
        TimingResult result;
        result.metadata["error"] = "Widget is null";
        return result;
    }
    
    auto layout_func = [widget]() {
        widget->updateGeometry();
        widget->update();
        QApplication::processEvents();
    };
    
    TimingResult result = measureFunction(layout_func, iterations);
    result.metadata["widget_type"] = widget->metaObject()->className();
    result.metadata["benchmark_type"] = "layout_update";
    result.metadata["widget_size"] = QString("%1x%2").arg(widget->width()).arg(widget->height());
    
    return result;
}

PerformanceBenchmark::TimingResult PerformanceBenchmark::measurePaintEvent(QWidget* widget, int iterations) {
    if (!widget) {
        TimingResult result;
        result.metadata["error"] = "Widget is null";
        return result;
    }
    
    auto paint_func = [widget]() {
        widget->repaint();
        QApplication::processEvents();
    };
    
    TimingResult result = measureFunction(paint_func, iterations);
    result.metadata["widget_type"] = widget->metaObject()->className();
    result.metadata["benchmark_type"] = "paint_event";
    result.metadata["widget_size"] = QString("%1x%2").arg(widget->width()).arg(widget->height());
    
    return result;
}

PerformanceBenchmark::MemoryResult PerformanceBenchmark::measureMemoryUsage(std::function<void()> func) {
    MemoryResult result;
    
    result.initial_memory_kb = getCurrentMemoryUsage();
    
    qint64 peak_memory = result.initial_memory_kb;
    
    // Monitor memory during function execution
    std::thread memory_monitor([&peak_memory, this]() {
        while (true) {
            qint64 current_memory = getCurrentMemoryUsage();
            if (current_memory > peak_memory) {
                peak_memory = current_memory;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    });
    
    // Execute function
    func();
    
    result.final_memory_kb = getCurrentMemoryUsage();
    result.peak_memory_kb = peak_memory;
    result.memory_delta_kb = result.final_memory_kb - result.initial_memory_kb;
    
    // Stop memory monitoring
    memory_monitor.detach();  // In a real implementation, you'd properly stop the thread
    
    // Estimate leaked memory (simplified)
    if (result.memory_delta_kb > 0) {
        result.leaked_memory_kb = result.memory_delta_kb;
    }
    
    result.metadata["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return result;
}

PerformanceBenchmark::MemoryResult PerformanceBenchmark::measureWidgetMemoryUsage(QWidget* widget) {
    if (!widget) {
        MemoryResult result;
        result.metadata["error"] = "Widget is null";
        return result;
    }
    
    auto widget_func = [widget]() {
        // Perform various operations on the widget
        widget->show();
        widget->resize(800, 600);
        widget->update();
        QApplication::processEvents();
        widget->hide();
    };
    
    MemoryResult result = measureMemoryUsage(widget_func);
    result.metadata["widget_type"] = widget->metaObject()->className();
    result.metadata["benchmark_type"] = "widget_memory";
    
    return result;
}

PerformanceBenchmark::CPUResult PerformanceBenchmark::measureCPUUsage(std::function<void()> func) {
    CPUResult result;
    
    double initial_cpu = getCurrentCPUUsage();
    
    QElapsedTimer timer;
    timer.start();
    
    func();
    
    qint64 total_time = timer.elapsed();
    double final_cpu = getCurrentCPUUsage();
    
    result.total_time_ms = total_time;
    result.cpu_usage_percentage = final_cpu - initial_cpu;
    
    // Platform-specific CPU time measurement would go here
    result.user_time_ms = total_time * 0.8;  // Simplified estimation
    result.system_time_ms = total_time * 0.2;
    
    result.metadata["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return result;
}

bool PerformanceBenchmark::compareWithBaseline(const QString& benchmark_name, const TimingResult& result, double tolerance) {
    auto baseline_it = baselines_.find(benchmark_name);
    if (baseline_it == baselines_.end()) {
        qWarning() << "No baseline found for benchmark:" << benchmark_name;
        return false;
    }
    
    const TimingResult& baseline = baseline_it->second;
    
    // Compare average execution time
    double performance_ratio = static_cast<double>(result.average_time_ms) / baseline.average_time_ms;
    
    // Performance is acceptable if it's within tolerance (e.g., not more than 10% slower)
    return performance_ratio <= (1.0 + tolerance);
}

void PerformanceBenchmark::saveBaseline(const QString& benchmark_name, const TimingResult& result) {
    baselines_[benchmark_name] = result;
    
    // Save to file for persistence
    QString baseline_dir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/benchmarks";
    QDir().mkpath(baseline_dir);
    
    QString baseline_file = baseline_dir + "/" + benchmark_name + "_baseline.json";
    
    QJsonObject baseline_json;
    baseline_json["benchmark_name"] = benchmark_name;
    baseline_json["total_time_ms"] = result.total_time_ms;
    baseline_json["average_time_ms"] = result.average_time_ms;
    baseline_json["min_time_ms"] = result.min_time_ms;
    baseline_json["max_time_ms"] = result.max_time_ms;
    baseline_json["median_time_ms"] = result.median_time_ms;
    baseline_json["standard_deviation"] = result.standard_deviation;
    baseline_json["iteration_count"] = result.iteration_count;
    baseline_json["metadata"] = result.metadata;
    baseline_json["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(baseline_json);
    QFile file(baseline_file);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        qDebug() << "Baseline saved:" << baseline_file;
    } else {
        qWarning() << "Failed to save baseline:" << baseline_file;
    }
}

void PerformanceBenchmark::generatePerformanceReport(const QString& output_file) {
    QJsonObject report;
    report["report_type"] = "performance_benchmark";
    report["generated_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    report["total_benchmarks"] = static_cast<int>(benchmark_results_.size());
    
    QJsonArray benchmarks_array;
    for (const auto& [name, result] : benchmark_results_) {
        QJsonObject benchmark_json;
        benchmark_json["name"] = name;
        benchmark_json["total_time_ms"] = result.total_time_ms;
        benchmark_json["average_time_ms"] = result.average_time_ms;
        benchmark_json["min_time_ms"] = result.min_time_ms;
        benchmark_json["max_time_ms"] = result.max_time_ms;
        benchmark_json["median_time_ms"] = result.median_time_ms;
        benchmark_json["standard_deviation"] = result.standard_deviation;
        benchmark_json["iteration_count"] = result.iteration_count;
        benchmark_json["metadata"] = result.metadata;
        
        // Add baseline comparison if available
        auto baseline_it = baselines_.find(name);
        if (baseline_it != baselines_.end()) {
            const TimingResult& baseline = baseline_it->second;
            double performance_ratio = static_cast<double>(result.average_time_ms) / baseline.average_time_ms;
            benchmark_json["baseline_comparison"] = performance_ratio;
            benchmark_json["performance_change_percent"] = (performance_ratio - 1.0) * 100.0;
        }
        
        benchmarks_array.append(benchmark_json);
    }
    
    report["benchmarks"] = benchmarks_array;
    
    // Calculate summary statistics
    if (!benchmark_results_.empty()) {
        std::vector<qint64> all_times;
        for (const auto& [name, result] : benchmark_results_) {
            all_times.push_back(result.average_time_ms);
        }
        
        qint64 total_time = std::accumulate(all_times.begin(), all_times.end(), 0LL);
        qint64 avg_time = total_time / all_times.size();
        qint64 min_time = *std::min_element(all_times.begin(), all_times.end());
        qint64 max_time = *std::max_element(all_times.begin(), all_times.end());
        
        QJsonObject summary;
        summary["total_average_time_ms"] = total_time;
        summary["overall_average_time_ms"] = avg_time;
        summary["fastest_benchmark_time_ms"] = min_time;
        summary["slowest_benchmark_time_ms"] = max_time;
        
        report["summary"] = summary;
    }
    
    // Write report to file
    QJsonDocument doc(report);
    QFile file(output_file);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        qDebug() << "Performance report generated:" << output_file;
    } else {
        qWarning() << "Failed to generate performance report:" << output_file;
    }
}

qint64 PerformanceBenchmark::getCurrentMemoryUsage() {
    qint64 memory_kb = 0;
    
#ifdef Q_OS_WIN
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        memory_kb = pmc.WorkingSetSize / 1024;
    }
#elif defined(Q_OS_LINUX)
    std::ifstream status_file("/proc/self/status");
    std::string line;
    while (std::getline(status_file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            std::istringstream iss(line);
            std::string label, value, unit;
            iss >> label >> value >> unit;
            memory_kb = std::stoll(value);
            break;
        }
    }
#elif defined(Q_OS_MACOS)
    struct mach_task_basic_info info;
    mach_msg_type_number_t info_count = MACH_TASK_BASIC_INFO_COUNT;
    if (task_info(mach_task_self(), MACH_TASK_BASIC_INFO, (task_info_t)&info, &info_count) == KERN_SUCCESS) {
        memory_kb = info.resident_size / 1024;
    }
#endif
    
    return memory_kb;
}

double PerformanceBenchmark::getCurrentCPUUsage() {
    // This is a simplified implementation
    // In practice, you'd need platform-specific code to get accurate CPU usage
    
#ifdef Q_OS_WIN
    // Windows implementation would use GetProcessTimes()
    return 0.0;
#elif defined(Q_OS_LINUX) || defined(Q_OS_MACOS)
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        // Convert to percentage (simplified)
        return (usage.ru_utime.tv_sec + usage.ru_stime.tv_sec) * 100.0 / 60.0;  // Very rough estimate
    }
#endif
    
    return 0.0;
}

} // namespace DeclarativeUI::Testing
