#include "Toast.hpp"
#include <QApplication>
#include <QScreen>
#include <QDebug>
#include <QStyle>

namespace DeclarativeUI::Components {

// Static member initialization
QList<Toast*> Toast::active_toasts_;

Toast::Toast(QObject* parent)
    : UIElement(parent)
    , toast_widget_(nullptr)
    , icon_label_(nullptr)
    , message_label_(nullptr)
    , close_button_(nullptr)
    , action_button_(nullptr)
    , main_layout_(nullptr)
    , fade_in_animation_(nullptr)
    , fade_out_animation_(nullptr)
    , slide_animation_(nullptr)
    , opacity_effect_(nullptr)
    , auto_hide_timer_(nullptr)
    , toast_type_(Type::Info)
    , duration_ms_(3000)
    , position_(Position::TopRight)
    , closable_(true)
    , persistent_(false)
{
    setupUI();
    setupAnimations();
    connectSignals();
}

void Toast::initialize() {
    // Toast is initialized in constructor, no additional setup needed
    // This method satisfies the UIElement interface requirement
}

Toast& Toast::message(const QString& text) {
    message_text_ = text;
    if (message_label_) {
        message_label_->setText(text);
    }
    return *this;
}

Toast& Toast::type(Type toast_type) {
    toast_type_ = toast_type;
    applyTypeStyle();
    return *this;
}

Toast& Toast::duration(int milliseconds) {
    duration_ms_ = milliseconds;
    return *this;
}

Toast& Toast::position(Position pos) {
    position_ = pos;
    return *this;
}

Toast& Toast::closable(bool can_close) {
    closable_ = can_close;
    if (close_button_) {
        close_button_->setVisible(can_close);
    }
    return *this;
}

Toast& Toast::persistent(bool is_persistent) {
    persistent_ = is_persistent;
    return *this;
}

Toast& Toast::icon(const QIcon& toast_icon) {
    toast_icon_ = toast_icon;
    if (icon_label_) {
        icon_label_->setPixmap(toast_icon.pixmap(16, 16));
    }
    return *this;
}

Toast& Toast::action(const QString& text, std::function<void()> callback) {
    action_text_ = text;
    action_callback_ = callback;
    
    if (action_button_) {
        action_button_->setText(text);
        action_button_->setVisible(!text.isEmpty());
    }
    return *this;
}

Toast& Toast::onClosed(std::function<void()> callback) {
    closed_callback_ = callback;
    return *this;
}

// Static convenience methods
Toast* Toast::info(const QString& message, int duration) {
    auto* toast = new Toast();
    return &toast->message(message).type(Type::Info).duration(duration);
}

Toast* Toast::success(const QString& message, int duration) {
    auto* toast = new Toast();
    return &toast->message(message).type(Type::Success).duration(duration);
}

Toast* Toast::warning(const QString& message, int duration) {
    auto* toast = new Toast();
    return &toast->message(message).type(Type::Warning).duration(duration);
}

Toast* Toast::error(const QString& message, int duration) {
    auto* toast = new Toast();
    return &toast->message(message).type(Type::Error).duration(duration);
}

void Toast::show() {
    if (!toast_widget_) return;

    // Set initial position
    calculatePosition();
    
    // Add to active toasts list
    addToActiveList(this);
    
    // Show the widget
    toast_widget_->show();
    toast_widget_->raise();
    
    // Start fade-in animation
    if (fade_in_animation_) {
        fade_in_animation_->start();
    }
    
    // Start auto-hide timer if not persistent
    if (!persistent_ && auto_hide_timer_) {
        auto_hide_timer_->start(duration_ms_);
    }
    
    qDebug() << "🍞 Toast shown:" << message_text_;
}

void Toast::hide() {
    if (!toast_widget_) return;
    
    // Start fade-out animation
    if (fade_out_animation_) {
        fade_out_animation_->start();
    } else {
        close();
    }
}

void Toast::close() {
    if (toast_widget_) {
        toast_widget_->hide();
    }
    
    // Remove from active toasts
    removeFromActiveList(this);
    
    // Emit closed signal
    if (closed_callback_) {
        closed_callback_();
    }
    emit closed();
    
    // Schedule for deletion
    deleteLater();
    
    qDebug() << "🍞 Toast closed:" << message_text_;
}

// Getters
QString Toast::getMessage() const { return message_text_; }
Toast::Type Toast::getType() const { return toast_type_; }
int Toast::getDuration() const { return duration_ms_; }
Toast::Position Toast::getPosition() const { return position_; }
bool Toast::isClosable() const { return closable_; }
bool Toast::isPersistent() const { return persistent_; }

// Private slots
void Toast::onAutoHideTimer() {
    hide();
}

void Toast::onCloseButtonClicked() {
    hide();
}

void Toast::onActionButtonClicked() {
    if (action_callback_) {
        action_callback_();
    }
    emit actionClicked();
    hide();
}

void Toast::setupUI() {
    // Create main widget
    toast_widget_ = new QWidget();
    toast_widget_->setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    toast_widget_->setAttribute(Qt::WA_TranslucentBackground);
    toast_widget_->setFixedHeight(60);
    toast_widget_->setMinimumWidth(300);
    toast_widget_->setMaximumWidth(500);
    
    // Create layout
    main_layout_ = new QHBoxLayout(toast_widget_);
    main_layout_->setContentsMargins(12, 8, 12, 8);
    main_layout_->setSpacing(8);
    
    // Create icon label
    icon_label_ = new QLabel();
    icon_label_->setFixedSize(16, 16);
    icon_label_->setScaledContents(true);
    main_layout_->addWidget(icon_label_);
    
    // Create message label
    message_label_ = new QLabel();
    message_label_->setWordWrap(true);
    message_label_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    main_layout_->addWidget(message_label_);
    
    // Create action button (initially hidden)
    action_button_ = new QPushButton();
    action_button_->setVisible(false);
    action_button_->setFlat(true);
    main_layout_->addWidget(action_button_);
    
    // Create close button
    close_button_ = new QPushButton("×");
    close_button_->setFixedSize(20, 20);
    close_button_->setFlat(true);
    close_button_->setStyleSheet("QPushButton { border: none; font-weight: bold; }");
    main_layout_->addWidget(close_button_);
    
    // Set up opacity effect
    opacity_effect_ = new QGraphicsOpacityEffect();
    toast_widget_->setGraphicsEffect(opacity_effect_);
    
    // Apply initial styling
    applyTypeStyle();
    
    // Set widget reference for UIElement
    setWidget(toast_widget_);
}

void Toast::setupAnimations() {
    // Fade-in animation
    fade_in_animation_ = new QPropertyAnimation(opacity_effect_, "opacity", this);
    fade_in_animation_->setDuration(300);
    fade_in_animation_->setStartValue(0.0);
    fade_in_animation_->setEndValue(1.0);
    
    // Fade-out animation
    fade_out_animation_ = new QPropertyAnimation(opacity_effect_, "opacity", this);
    fade_out_animation_->setDuration(300);
    fade_out_animation_->setStartValue(1.0);
    fade_out_animation_->setEndValue(0.0);
    
    // Connect fade-out finished to close
    connect(fade_out_animation_, &QPropertyAnimation::finished, this, &Toast::close);
    
    // Auto-hide timer
    auto_hide_timer_ = new QTimer(this);
    auto_hide_timer_->setSingleShot(true);
}

void Toast::connectSignals() {
    if (close_button_) {
        connect(close_button_, &QPushButton::clicked, this, &Toast::onCloseButtonClicked);
    }
    
    if (action_button_) {
        connect(action_button_, &QPushButton::clicked, this, &Toast::onActionButtonClicked);
    }
    
    if (auto_hide_timer_) {
        connect(auto_hide_timer_, &QTimer::timeout, this, &Toast::onAutoHideTimer);
    }
}

void Toast::applyTypeStyle() {
    QString base_style = R"(
        QWidget {
            background-color: %1;
            border: 1px solid %2;
            border-radius: 8px;
            color: %3;
        }
        QPushButton {
            background-color: transparent;
            border: 1px solid %3;
            border-radius: 4px;
            padding: 4px 8px;
            color: %3;
        }
        QPushButton:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
    )";

    QString bg_color, border_color, text_color;

    switch (toast_type_) {
        case Type::Info:
            bg_color = "#2196F3";
            border_color = "#1976D2";
            text_color = "white";
            break;
        case Type::Success:
            bg_color = "#4CAF50";
            border_color = "#388E3C";
            text_color = "white";
            break;
        case Type::Warning:
            bg_color = "#FF9800";
            border_color = "#F57C00";
            text_color = "white";
            break;
        case Type::Error:
            bg_color = "#F44336";
            border_color = "#D32F2F";
            text_color = "white";
            break;
    }

    if (toast_widget_) {
        toast_widget_->setStyleSheet(base_style.arg(bg_color, border_color, text_color));
    }

    // Set default icon if none provided
    if (toast_icon_.isNull() && icon_label_) {
        icon_label_->setPixmap(getDefaultIcon().pixmap(16, 16));
    }
}

QIcon Toast::getDefaultIcon() const {
    // Return appropriate icon based on type
    // In a real implementation, you would load actual icons
    switch (toast_type_) {
        case Type::Info:
            return QApplication::style()->standardIcon(QStyle::SP_MessageBoxInformation);
        case Type::Success:
            return QApplication::style()->standardIcon(QStyle::SP_DialogApplyButton);
        case Type::Warning:
            return QApplication::style()->standardIcon(QStyle::SP_MessageBoxWarning);
        case Type::Error:
            return QApplication::style()->standardIcon(QStyle::SP_MessageBoxCritical);
    }
    return QIcon();
}

void Toast::calculatePosition() {
    if (!toast_widget_) return;

    QPoint pos = getPositionCoordinates();
    toast_widget_->move(pos);
}

QPoint Toast::getPositionCoordinates() const {
    QScreen* screen = QApplication::primaryScreen();
    QRect screen_geometry = screen->availableGeometry();

    int x, y;
    int margin = 20;
    int toast_height = 60;
    int toast_width = toast_widget_ ? toast_widget_->width() : 300;

    // Calculate base position
    switch (position_) {
        case Position::TopLeft:
            x = screen_geometry.left() + margin;
            y = screen_geometry.top() + margin;
            break;
        case Position::TopCenter:
            x = screen_geometry.center().x() - toast_width / 2;
            y = screen_geometry.top() + margin;
            break;
        case Position::TopRight:
            x = screen_geometry.right() - toast_width - margin;
            y = screen_geometry.top() + margin;
            break;
        case Position::BottomLeft:
            x = screen_geometry.left() + margin;
            y = screen_geometry.bottom() - toast_height - margin;
            break;
        case Position::BottomCenter:
            x = screen_geometry.center().x() - toast_width / 2;
            y = screen_geometry.bottom() - toast_height - margin;
            break;
        case Position::BottomRight:
            x = screen_geometry.right() - toast_width - margin;
            y = screen_geometry.bottom() - toast_height - margin;
            break;
        case Position::Center:
            x = screen_geometry.center().x() - toast_width / 2;
            y = screen_geometry.center().y() - toast_height / 2;
            break;
    }

    // Adjust for other active toasts
    int offset = 0;
    for (Toast* toast : active_toasts_) {
        if (toast != this && toast->getPosition() == position_) {
            offset += toast_height + 10; // 10px spacing between toasts
        }
    }

    // Apply offset based on position
    if (position_ == Position::TopLeft || position_ == Position::TopCenter || position_ == Position::TopRight) {
        y += offset;
    } else if (position_ == Position::BottomLeft || position_ == Position::BottomCenter || position_ == Position::BottomRight) {
        y -= offset;
    }

    return QPoint(x, y);
}

// Static methods for toast management
void Toast::addToActiveList(Toast* toast) {
    if (!active_toasts_.contains(toast)) {
        active_toasts_.append(toast);
        repositionActiveToasts();
    }
}

void Toast::removeFromActiveList(Toast* toast) {
    active_toasts_.removeAll(toast);
    repositionActiveToasts();
}

void Toast::repositionActiveToasts() {
    for (Toast* toast : active_toasts_) {
        if (toast && toast->toast_widget_) {
            toast->calculatePosition();
        }
    }
}

} // namespace DeclarativeUI::Components
