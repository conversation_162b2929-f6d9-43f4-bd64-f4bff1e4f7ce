#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QLabel>
#include <QProgressBar>
#include <QPushButton>
#include <QTableWidget>
#include <QTextEdit>
#include <QTimer>
#include <QSplitter>
#include <QGroupBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QComboBox>
#include <QListWidget>
#include <QScrollArea>
#include <QFrame>
#include <QSlider>
#include <QDateTimeEdit>
#include <QJsonObject>
#include <QJsonArray>
#include <QElapsedTimer>
#include <QFileSystemWatcher>

#include <memory>
#include <vector>
#include <deque>
#include <atomic>
#include <mutex>

#include "HotReloadManager.hpp"
#include "PerformanceMonitor.hpp"
#include "FileWatcher.hpp"
#include "HotReloadConfig.hpp"
#include "AdvancedFileFilter.hpp"

namespace DeclarativeUI::HotReload {

/**
 * @brief Real-time metrics data structure for dashboard display
 */
struct DashboardMetrics {
    // **File watching metrics**
    int watched_files_count = 0;
    int watched_directories_count = 0;
    int total_file_changes = 0;
    int successful_reloads = 0;
    int failed_reloads = 0;
    double success_rate = 0.0;
    
    // **Performance metrics**
    qint64 average_reload_time_ms = 0;
    qint64 last_reload_time_ms = 0;
    qint64 fastest_reload_ms = 0;
    qint64 slowest_reload_ms = 0;
    
    // **System metrics**
    double cpu_usage_percent = 0.0;
    qint64 memory_usage_mb = 0;
    qint64 peak_memory_mb = 0;
    
    // **Filter metrics**
    qint64 files_processed = 0;
    qint64 files_included = 0;
    qint64 files_excluded = 0;
    double filter_efficiency = 0.0;
    
    // **Error metrics**
    int error_count = 0;
    int warning_count = 0;
    QString last_error;
    QDateTime last_error_time;
    
    // **Session metrics**
    QDateTime session_start_time;
    qint64 session_duration_ms = 0;
    int total_operations = 0;
};

/**
 * @brief Reload history entry for tracking past operations
 */
struct ReloadHistoryEntry {
    QString file_path;
    QDateTime timestamp;
    qint64 duration_ms;
    bool success;
    QString error_message;
    qint64 file_size_bytes;
    QString operation_type; // "reload", "add", "remove", "modify"
};

/**
 * @brief File watch status information
 */
struct FileWatchStatus {
    QString file_path;
    QDateTime last_modified;
    qint64 file_size;
    bool is_watched;
    bool is_included_by_filter;
    int change_count;
    QDateTime last_change_time;
    QString status; // "active", "paused", "error", "excluded"
};

/**
 * @brief Comprehensive Hot-Reload Dashboard Widget
 * 
 * Provides real-time monitoring and control of the hot-reload system with:
 * - Live performance metrics and charts
 * - File watch status monitoring
 * - Reload history tracking
 * - Interactive configuration controls
 * - Error reporting and diagnostics
 * - Performance analytics and optimization suggestions
 */
class HotReloadDashboard : public QWidget {
    Q_OBJECT

public:
    explicit HotReloadDashboard(QWidget* parent = nullptr);
    ~HotReloadDashboard() override;

    // **Integration with hot-reload system**
    void setHotReloadManager(HotReloadManager* manager);
    void setPerformanceMonitor(PerformanceMonitor* monitor);
    void setFileWatcher(FileWatcher* watcher);
    void setConfiguration(HotReloadConfig* config);
    void setAdvancedFilter(AdvancedFileFilter* filter);

    // **Dashboard control**
    void startMonitoring();
    void stopMonitoring();
    void pauseMonitoring();
    void resetMetrics();
    void refreshDisplay();

    // **Configuration**
    void setUpdateInterval(int milliseconds);
    void setMaxHistorySize(int max_entries);
    void enableRealTimeUpdates(bool enabled);
    void setPerformanceThresholds(double cpu_threshold, qint64 memory_threshold_mb, qint64 reload_time_threshold_ms);

    // **Data export and reporting**
    void exportMetricsToFile(const QString& file_path);
    void exportHistoryToFile(const QString& file_path);
    QJsonObject generateReport() const;
    QString generateTextReport() const;

    // **Visual customization**
    void setTheme(const QString& theme_name);
    void setChartColors(const QStringList& colors);
    void enableAnimations(bool enabled);

signals:
    void monitoringStarted();
    void monitoringStopped();
    void monitoringPaused();
    void metricsUpdated(const DashboardMetrics& metrics);
    void thresholdExceeded(const QString& metric, double value, double threshold);
    void configurationChanged();
    void exportCompleted(const QString& file_path);

public slots:
    void updateMetrics();
    void onReloadStarted(const QString& file_path);
    void onReloadCompleted(const QString& file_path, qint64 duration_ms);
    void onReloadFailed(const QString& file_path, const QString& error);
    void onFileChanged(const QString& file_path);
    void onPerformanceWarning(const QString& message);
    void onConfigurationUpdated();

private slots:
    void onUpdateTimer();
    void onStartButtonClicked();
    void onStopButtonClicked();
    void onPauseButtonClicked();
    void onResetButtonClicked();
    void onExportButtonClicked();
    void onConfigureButtonClicked();
    void onRefreshButtonClicked();
    void onThresholdChanged();
    void onFilterConfigChanged();
    void onHistoryItemSelected();
    void onWatchedFileSelected();

private:
    // **Core components**
    HotReloadManager* hot_reload_manager_ = nullptr;
    PerformanceMonitor* performance_monitor_ = nullptr;
    FileWatcher* file_watcher_ = nullptr;
    HotReloadConfig* config_ = nullptr;
    AdvancedFileFilter* advanced_filter_ = nullptr;

    // **UI setup methods**
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupOverviewTab();
    void setupPerformanceTab();
    void setupFileWatchTab();
    void setupHistoryTab();
    void setupConfigurationTab();
    void setupDiagnosticsTab();

    // **Chart and visualization setup**
    void setupPerformanceCharts();
    void setupRealtimeMetrics();
    void setupFileStatusDisplay();
    void setupHistoryTable();
    void setupErrorConsole();

    // **Data collection and processing**
    void collectMetrics();
    void updatePerformanceCharts();
    void updateFileWatchStatus();
    void updateReloadHistory();
    void updateErrorLog();
    void processPerformanceData();

    // **Utility methods**
    QString formatDuration(qint64 milliseconds) const;
    QString formatFileSize(qint64 bytes) const;
    QString formatPercentage(double value) const;
    QColor getStatusColor(const QString& status) const;
    QColor getThresholdColor(double value, double threshold) const;
    void applyTheme(const QString& theme_name);
    void updateStatusIndicators();
    void checkThresholds();

    // **Main UI components**
    QTabWidget* main_tabs_;
    QWidget* overview_tab_;
    QWidget* performance_tab_;
    QWidget* file_watch_tab_;
    QWidget* history_tab_;
    QWidget* configuration_tab_;
    QWidget* diagnostics_tab_;

    // **Control panel**
    QWidget* control_panel_;
    QPushButton* start_button_;
    QPushButton* stop_button_;
    QPushButton* pause_button_;
    QPushButton* reset_button_;
    QPushButton* export_button_;
    QPushButton* configure_button_;
    QPushButton* refresh_button_;

    // **Status indicators**
    QLabel* monitoring_status_label_;
    QLabel* session_time_label_;
    QLabel* total_operations_label_;
    QProgressBar* cpu_usage_bar_;
    QProgressBar* memory_usage_bar_;
    QLabel* success_rate_label_;

    // **Overview metrics display**
    QLabel* watched_files_label_;
    QLabel* successful_reloads_label_;
    QLabel* failed_reloads_label_;
    QLabel* average_reload_time_label_;
    QLabel* last_reload_time_label_;
    QProgressBar* performance_score_bar_;

    // **Performance monitoring**
    QWidget* performance_chart_widget_;
    QTableWidget* performance_metrics_table_;
    QTextEdit* performance_log_;

    // **File watch monitoring**
    QTableWidget* watched_files_table_;
    QListWidget* recent_changes_list_;
    QTextEdit* file_filter_status_;

    // **History tracking**
    QTableWidget* reload_history_table_;
    QTextEdit* history_details_;

    // **Configuration controls**
    QSpinBox* update_interval_spin_;
    QSpinBox* max_history_spin_;
    QCheckBox* real_time_updates_checkbox_;
    QSlider* cpu_threshold_slider_;
    QSlider* memory_threshold_slider_;
    QSlider* reload_time_threshold_slider_;
    QComboBox* theme_combo_;

    // **Diagnostics and errors**
    QTextEdit* error_console_;
    QTableWidget* diagnostics_table_;
    QLabel* system_status_label_;

    // **Data storage**
    DashboardMetrics current_metrics_;
    std::deque<ReloadHistoryEntry> reload_history_;
    std::vector<FileWatchStatus> file_watch_status_;
    std::deque<QString> error_log_;
    std::deque<DashboardMetrics> metrics_history_;

    // **Configuration and state**
    std::unique_ptr<QTimer> update_timer_;
    QElapsedTimer session_timer_;
    bool monitoring_enabled_ = false;
    bool real_time_updates_enabled_ = true;
    int update_interval_ms_ = 1000;
    int max_history_size_ = 1000;

    // **Performance thresholds**
    double cpu_threshold_ = 80.0;
    qint64 memory_threshold_mb_ = 512;
    qint64 reload_time_threshold_ms_ = 1000;

    // **Visual configuration**
    QString current_theme_ = "default";
    QStringList chart_colors_;
    bool animations_enabled_ = true;

    // **Thread safety**
    mutable std::mutex metrics_mutex_;
    mutable std::mutex history_mutex_;
    std::atomic<bool> data_collection_active_{false};
};

} // namespace DeclarativeUI::HotReload
