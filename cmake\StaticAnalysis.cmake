# Static Analysis Configuration for DeclarativeUI Framework

# =============================================================================
# Clang-Tidy Configuration
# =============================================================================

option(ENABLE_CLANG_TIDY "Enable clang-tidy static analysis" OFF)

if(ENABLE_CLANG_TIDY)
    find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
    
    if(CLANG_TIDY_EXE)
        message(STATUS "clang-tidy found: ${CLANG_TIDY_EXE}")
        
        # Set clang-tidy command
        set(CLANG_TIDY_COMMAND 
            "${CLANG_TIDY_EXE}"
            "--config-file=${CMAKE_SOURCE_DIR}/.clang-tidy"
            "--header-filter=${CMAKE_SOURCE_DIR}/src/.*\\.(h|hpp)$"
        )
        
        # Apply to all targets
        set(CMAKE_CXX_CLANG_TIDY ${CLANG_TIDY_COMMAND})
        
        # Create custom target for running clang-tidy
        add_custom_target(clang-tidy
            COMMAND ${CLANG_TIDY_EXE}
                --config-file=${CMAKE_SOURCE_DIR}/.clang-tidy
                --header-filter=${CMAKE_SOURCE_DIR}/src/.*\\.(h|hpp)$
                --format-style=file
                ${CMAKE_SOURCE_DIR}/src/*.cpp
                ${CMAKE_SOURCE_DIR}/src/*/*.cpp
                ${CMAKE_SOURCE_DIR}/src/*/*/*.cpp
                --
                -I${CMAKE_SOURCE_DIR}/src
                -I${Qt6_INCLUDE_DIRS}
                -std=c++20
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            COMMENT "Running clang-tidy static analysis"
        )
        
    else()
        message(WARNING "clang-tidy not found. Static analysis disabled.")
    endif()
endif()

# =============================================================================
# Cppcheck Configuration
# =============================================================================

option(ENABLE_CPPCHECK "Enable cppcheck static analysis" OFF)

if(ENABLE_CPPCHECK)
    find_program(CPPCHECK_EXE NAMES "cppcheck")
    
    if(CPPCHECK_EXE)
        message(STATUS "cppcheck found: ${CPPCHECK_EXE}")
        
        # Set cppcheck command
        set(CPPCHECK_COMMAND
            "${CPPCHECK_EXE}"
            "--enable=all"
            "--std=c++20"
            "--language=c++"
            "--platform=native"
            "--suppress=missingIncludeSystem"
            "--suppress=unmatchedSuppression"
            "--suppress=unusedFunction"
            "--inline-suppr"
            "--quiet"
            "--error-exitcode=1"
        )
        
        # Apply to all targets
        set(CMAKE_CXX_CPPCHECK ${CPPCHECK_COMMAND})
        
        # Create custom target for running cppcheck
        add_custom_target(cppcheck
            COMMAND ${CPPCHECK_EXE}
                --enable=all
                --std=c++20
                --language=c++
                --platform=native
                --suppress=missingIncludeSystem
                --suppress=unmatchedSuppression
                --suppress=unusedFunction
                --inline-suppr
                --quiet
                --error-exitcode=1
                --xml
                --xml-version=2
                --output-file=${CMAKE_BINARY_DIR}/cppcheck-report.xml
                -I ${CMAKE_SOURCE_DIR}/src
                -I ${Qt6_INCLUDE_DIRS}
                ${CMAKE_SOURCE_DIR}/src/
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            COMMENT "Running cppcheck static analysis"
        )
        
    else()
        message(WARNING "cppcheck not found. Static analysis disabled.")
    endif()
endif()

# =============================================================================
# Include What You Use (IWYU) Configuration
# =============================================================================

option(ENABLE_IWYU "Enable include-what-you-use analysis" OFF)

if(ENABLE_IWYU)
    find_program(IWYU_EXE NAMES "include-what-you-use" "iwyu")
    
    if(IWYU_EXE)
        message(STATUS "include-what-you-use found: ${IWYU_EXE}")
        
        # Set IWYU command
        set(IWYU_COMMAND
            "${IWYU_EXE}"
            "-Xiwyu" "--mapping_file=${CMAKE_SOURCE_DIR}/cmake/iwyu.imp"
            "-Xiwyu" "--max_line_length=120"
            "-Xiwyu" "--no_comments"
        )
        
        # Apply to all targets
        set(CMAKE_CXX_INCLUDE_WHAT_YOU_USE ${IWYU_COMMAND})
        
        # Create custom target for running IWYU
        add_custom_target(iwyu
            COMMAND ${CMAKE_COMMAND}
                --build ${CMAKE_BINARY_DIR}
                --target all
                2>&1 | tee ${CMAKE_BINARY_DIR}/iwyu-report.txt
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            COMMENT "Running include-what-you-use analysis"
        )
        
    else()
        message(WARNING "include-what-you-use not found. Analysis disabled.")
    endif()
endif()

# =============================================================================
# PVS-Studio Configuration
# =============================================================================

option(ENABLE_PVS_STUDIO "Enable PVS-Studio static analysis" OFF)

if(ENABLE_PVS_STUDIO)
    find_program(PVS_STUDIO_EXE NAMES "pvs-studio-analyzer")
    
    if(PVS_STUDIO_EXE)
        message(STATUS "PVS-Studio found: ${PVS_STUDIO_EXE}")
        
        # Create custom target for running PVS-Studio
        add_custom_target(pvs-studio
            COMMAND ${PVS_STUDIO_EXE}
                analyze
                --output-file ${CMAKE_BINARY_DIR}/pvs-studio-report.log
                --source-tree-root ${CMAKE_SOURCE_DIR}
                --exclude-path ${CMAKE_SOURCE_DIR}/build
                --exclude-path ${CMAKE_SOURCE_DIR}/.cache
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            COMMENT "Running PVS-Studio static analysis"
        )
        
    else()
        message(WARNING "PVS-Studio not found. Analysis disabled.")
    endif()
endif()

# =============================================================================
# Sanitizers Configuration
# =============================================================================

option(ENABLE_SANITIZERS "Enable runtime sanitizers" OFF)

if(ENABLE_SANITIZERS)
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        # Address Sanitizer
        option(ENABLE_ASAN "Enable AddressSanitizer" ON)
        if(ENABLE_ASAN)
            add_compile_options(-fsanitize=address -fno-omit-frame-pointer)
            add_link_options(-fsanitize=address)
            message(STATUS "AddressSanitizer enabled")
        endif()
        
        # Undefined Behavior Sanitizer
        option(ENABLE_UBSAN "Enable UndefinedBehaviorSanitizer" ON)
        if(ENABLE_UBSAN)
            add_compile_options(-fsanitize=undefined)
            add_link_options(-fsanitize=undefined)
            message(STATUS "UndefinedBehaviorSanitizer enabled")
        endif()
        
        # Thread Sanitizer (mutually exclusive with AddressSanitizer)
        option(ENABLE_TSAN "Enable ThreadSanitizer" OFF)
        if(ENABLE_TSAN AND NOT ENABLE_ASAN)
            add_compile_options(-fsanitize=thread)
            add_link_options(-fsanitize=thread)
            message(STATUS "ThreadSanitizer enabled")
        elseif(ENABLE_TSAN AND ENABLE_ASAN)
            message(WARNING "ThreadSanitizer and AddressSanitizer are mutually exclusive. Disabling ThreadSanitizer.")
        endif()
        
        # Memory Sanitizer (Clang only)
        option(ENABLE_MSAN "Enable MemorySanitizer" OFF)
        if(ENABLE_MSAN AND CMAKE_CXX_COMPILER_ID MATCHES "Clang")
            add_compile_options(-fsanitize=memory -fno-omit-frame-pointer)
            add_link_options(-fsanitize=memory)
            message(STATUS "MemorySanitizer enabled")
        endif()
        
    else()
        message(WARNING "Sanitizers are only supported with GCC or Clang")
    endif()
endif()

# =============================================================================
# Code Coverage Configuration
# =============================================================================

option(ENABLE_COVERAGE "Enable code coverage analysis" OFF)

if(ENABLE_COVERAGE)
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        add_compile_options(--coverage -O0 -g)
        add_link_options(--coverage)
        
        # Find gcov or llvm-cov
        if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
            find_program(GCOV_EXE NAMES "gcov")
            if(GCOV_EXE)
                message(STATUS "Code coverage enabled with gcov")
            endif()
        elseif(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
            find_program(LLVM_COV_EXE NAMES "llvm-cov")
            if(LLVM_COV_EXE)
                message(STATUS "Code coverage enabled with llvm-cov")
            endif()
        endif()
        
        # Create coverage target
        add_custom_target(coverage
            COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target all
            COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
            COMMAND lcov --capture --directory ${CMAKE_BINARY_DIR} --output-file coverage.info
            COMMAND lcov --remove coverage.info '/usr/*' '*/Qt/*' '*/build/*' --output-file coverage.info
            COMMAND genhtml coverage.info --output-directory ${CMAKE_BINARY_DIR}/coverage-html
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            COMMENT "Generating code coverage report"
        )
        
    else()
        message(WARNING "Code coverage is only supported with GCC or Clang")
    endif()
endif()

# =============================================================================
# Static Analysis Summary Target
# =============================================================================

add_custom_target(static-analysis
    COMMENT "Running all static analysis tools"
)

if(TARGET clang-tidy)
    add_dependencies(static-analysis clang-tidy)
endif()

if(TARGET cppcheck)
    add_dependencies(static-analysis cppcheck)
endif()

if(TARGET iwyu)
    add_dependencies(static-analysis iwyu)
endif()

if(TARGET pvs-studio)
    add_dependencies(static-analysis pvs-studio)
endif()

# =============================================================================
# Helper Functions
# =============================================================================

# Function to apply static analysis to a target
function(apply_static_analysis target_name)
    if(ENABLE_CLANG_TIDY AND CLANG_TIDY_EXE)
        set_target_properties(${target_name} PROPERTIES
            CXX_CLANG_TIDY "${CLANG_TIDY_COMMAND}"
        )
    endif()
    
    if(ENABLE_CPPCHECK AND CPPCHECK_EXE)
        set_target_properties(${target_name} PROPERTIES
            CXX_CPPCHECK "${CPPCHECK_COMMAND}"
        )
    endif()
    
    if(ENABLE_IWYU AND IWYU_EXE)
        set_target_properties(${target_name} PROPERTIES
            CXX_INCLUDE_WHAT_YOU_USE "${IWYU_COMMAND}"
        )
    endif()
endfunction()

# Function to disable static analysis for a target (e.g., third-party code)
function(disable_static_analysis target_name)
    set_target_properties(${target_name} PROPERTIES
        CXX_CLANG_TIDY ""
        CXX_CPPCHECK ""
        CXX_INCLUDE_WHAT_YOU_USE ""
    )
endfunction()
