#pragma once

#include "IFormatProcessor.hpp"
#include <QQmlEngine>
#include <QQmlComponent>
#include <QQuickItem>
#include <QQuickView>
#include <QUrl>
#include <QFileInfo>
#include <QDir>
#include <QRegularExpression>
#include <memory>

namespace DeclarativeUI::HotReload::FormatSupport {

/**
 * @brief QML file processor for hot reload support
 * 
 * Handles QML file processing with features:
 * - QML syntax validation
 * - Component dependency tracking
 * - Live QML injection
 * - Import resolution
 * - Error reporting with line numbers
 */
class QMLProcessor : public IFormatProcessor {
    Q_OBJECT

public:
    explicit QMLProcessor(QObject* parent = nullptr);
    ~QMLProcessor() override = default;

    // Core interface implementation
    QString getFormatName() const override { return "QML"; }
    QStringList getSupportedExtensions() const override { return {"qml", "js"}; }
    bool canProcess(const QString& file_path) const override;

    // Processing methods
    ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;
    ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) override;

    // Validation methods
    bool validateFile(const QString& file_path) const override;
    bool validateContent(const QString& content) const override;

    // Configuration and capabilities
    ProcessingConfig getDefaultConfig() const override;
    QStringList getRequiredDependencies() const override { return {"Qt6::Qml", "Qt6::Quick"}; }
    bool isAvailable() const override;

    // Hot reload specific methods
    bool supportsLiveInjection() const override { return true; }
    ProcessingResult prepareLiveInjection(const QString& content, const QString& file_path) override;

    // QML-specific methods
    QStringList extractImports(const QString& content) const;
    QStringList extractDependencies(const QString& file_path) const;
    QString resolveImportPath(const QString& import_statement, const QString& base_path) const;
    bool hasQMLSyntaxErrors(const QString& content, QString& error_message) const;

signals:
    void qmlComponentReady(const QString& file_path, QQmlComponent* component);
    void qmlImportsChanged(const QString& file_path, const QStringList& imports);
    void qmlDependencyDetected(const QString& file_path, const QString& dependency);

private:
    std::unique_ptr<QQmlEngine> qml_engine_;
    QStringList import_paths_;
    QRegularExpression import_regex_;
    QRegularExpression component_regex_;

    // Helper methods
    void setupQMLEngine();
    void addImportPath(const QString& path);
    QQmlComponent* createComponent(const QString& content, const QString& file_path);
    QString extractComponentName(const QString& file_path) const;
    QJsonObject createQMLMetadata(const QString& file_path, const QString& content) const;
    bool isValidQMLIdentifier(const QString& identifier) const;
    QString formatQMLError(const QQmlError& error) const;
    QStringList parseImportStatement(const QString& import_line) const;
};

} // namespace DeclarativeUI::HotReload::FormatSupport
