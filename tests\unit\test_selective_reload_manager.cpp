#include <QtTest/QtTest>
#include <QApplication>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QJsonObject>
#include <QJsonDocument>
#include <QTemporaryFile>
#include <QSignalSpy>

#include "../../src/HotReload/SelectiveReloadManager.hpp"
#include "../../src/HotReload/HotReloadManager.hpp"
#include "../../src/HotReload/AdvancedFileFilter.hpp"
#include "../../src/JSON/JSONUILoader.hpp"

using namespace DeclarativeUI::HotReload;

class TestSelectiveReloadManager : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Configuration tests
    void testConfiguration();
    void testSetExternalDependencies();

    // Component management tests
    void testComponentRegistration();
    void testComponentUnregistration();
    void testComponentConfigUpdate();
    void testGetComponentInfo();

    // Stylesheet management tests
    void testStylesheetRegistration();
    void testStylesheetUpdate();
    void testStylesheetUnregistration();

    // Resource management tests
    void testResourceRegistration();
    void testResourceUpdate();
    void testResourceUnregistration();

    // Selective reload tests
    void testComponentReload();
    void testComponentPropertyReload();
    void testStylesheetReload();
    void testResourceReload();
    void testReloadComponentsByType();
    void testReloadComponentsBySelector();

    // Batch operations tests
    void testReloadTargets();
    void testReloadDependencies();
    void testReloadAffectedComponents();

    // Dependency management tests
    void testDependencyManagement();
    void testDependencyCascading();

    // Rollback tests
    void testRollbackCreation();
    void testRollbackExecution();
    void testRollbackClear();

    // Monitoring tests
    void testMonitoringControl();
    void testAutoDetection();
    void testFileChangeDetection();

    // Performance and statistics tests
    void testPerformanceMetrics();
    void testReloadStatistics();
    void testStatisticsReset();

    // Error handling tests
    void testErrorHandling();
    void testErrorRecovery();
    void testInvalidOperations();

    // Integration tests
    void testIntegrationWithHotReloadManager();
    void testIntegrationWithUILoader();
    void testComplexReloadScenario();

private:
    QApplication* app = nullptr;
    std::unique_ptr<SelectiveReloadManager> manager;
    std::unique_ptr<HotReloadManager> hot_reload_manager;
    std::unique_ptr<AdvancedFileFilter> advanced_filter;
    std::unique_ptr<JSON::JSONUILoader> ui_loader;
    
    QWidget* test_widget = nullptr;
    QLabel* test_label = nullptr;
    QPushButton* test_button = nullptr;
    
    QString test_component_id = "test_component";
    QString test_stylesheet_id = "test_stylesheet";
    QString test_resource_id = "test_resource";
    
    QJsonObject createTestComponentConfig();
    QTemporaryFile* createTestStylesheetFile();
    QTemporaryFile* createTestResourceFile();
    void setupTestWidgets();
    void cleanupTestWidgets();
};

void TestSelectiveReloadManager::initTestCase() {
    // QApplication is required for widget operations
    if (!QApplication::instance()) {
        int argc = 0;
        char* argv[] = {nullptr};
        app = new QApplication(argc, argv);
    }
}

void TestSelectiveReloadManager::cleanupTestCase() {
    if (app) {
        delete app;
        app = nullptr;
    }
}

void TestSelectiveReloadManager::init() {
    manager = std::make_unique<SelectiveReloadManager>();
    hot_reload_manager = std::make_unique<HotReloadManager>();
    advanced_filter = std::make_unique<AdvancedFileFilter>();
    ui_loader = std::make_unique<JSON::JSONUILoader>();
    
    setupTestWidgets();
}

void TestSelectiveReloadManager::cleanup() {
    cleanupTestWidgets();
    
    manager.reset();
    hot_reload_manager.reset();
    advanced_filter.reset();
    ui_loader.reset();
}

void TestSelectiveReloadManager::setupTestWidgets() {
    test_widget = new QWidget();
    test_widget->setObjectName("test_widget");
    test_widget->resize(400, 300);
    
    auto* layout = new QVBoxLayout(test_widget);
    
    test_label = new QLabel("Test Label", test_widget);
    test_label->setObjectName("test_label");
    layout->addWidget(test_label);
    
    test_button = new QPushButton("Test Button", test_widget);
    test_button->setObjectName("test_button");
    layout->addWidget(test_button);
    
    test_widget->setLayout(layout);
}

void TestSelectiveReloadManager::cleanupTestWidgets() {
    if (test_widget) {
        delete test_widget;
        test_widget = nullptr;
        test_label = nullptr;
        test_button = nullptr;
    }
}

QJsonObject TestSelectiveReloadManager::createTestComponentConfig() {
    QJsonObject config;
    config["type"] = "QLabel";
    config["id"] = test_component_id;
    
    QJsonObject properties;
    properties["text"] = "Test Component";
    properties["wordWrap"] = true;
    properties["alignment"] = 4; // Qt::AlignCenter
    config["properties"] = properties;
    
    QJsonObject events;
    events["clicked"] = "testHandler";
    config["events"] = events;
    
    return config;
}

QTemporaryFile* TestSelectiveReloadManager::createTestStylesheetFile() {
    auto* file = new QTemporaryFile();
    file->setFileTemplate("test_stylesheet_XXXXXX.css");
    
    if (file->open()) {
        QTextStream stream(file);
        stream << "QLabel { color: blue; font-size: 14px; }\n";
        stream << "QPushButton { background-color: lightgray; border: 1px solid gray; }\n";
        file->close();
    }
    
    return file;
}

QTemporaryFile* TestSelectiveReloadManager::createTestResourceFile() {
    auto* file = new QTemporaryFile();
    file->setFileTemplate("test_resource_XXXXXX.png");
    
    if (file->open()) {
        // Create a simple 1x1 pixel image
        QByteArray imageData = QByteArray::fromHex("89504E470D0A1A0A0000000D49484452000000010000000108060000001F15C4890000000A49444154789C6300010000050001");
        file->write(imageData);
        file->close();
    }
    
    return file;
}

void TestSelectiveReloadManager::testConfiguration() {
    // Test default configuration
    auto default_config = manager->getConfiguration();
    QVERIFY(default_config.enabled);
    QVERIFY(default_config.auto_detect_changes);
    QVERIFY(default_config.cascade_dependencies);
    
    // Test configuration update
    SelectiveReloadConfig new_config;
    new_config.enabled = false;
    new_config.auto_detect_changes = false;
    new_config.reload_debounce_ms = 200;
    new_config.max_concurrent_reloads = 10;
    
    QSignalSpy config_spy(manager.get(), &SelectiveReloadManager::configurationChanged);
    
    manager->setConfiguration(new_config);
    
    QCOMPARE(config_spy.count(), 1);
    
    auto updated_config = manager->getConfiguration();
    QVERIFY(!updated_config.enabled);
    QVERIFY(!updated_config.auto_detect_changes);
    QCOMPARE(updated_config.reload_debounce_ms, 200);
    QCOMPARE(updated_config.max_concurrent_reloads, 10);
}

void TestSelectiveReloadManager::testSetExternalDependencies() {
    // Test setting HotReloadManager
    manager->setHotReloadManager(hot_reload_manager.get());
    
    // Test setting AdvancedFileFilter
    manager->setAdvancedFilter(advanced_filter.get());
    
    // Test setting UILoader
    manager->setUILoader(ui_loader.get());
    
    // No direct way to verify these are set, but they should not crash
    QVERIFY(true);
}

void TestSelectiveReloadManager::testComponentRegistration() {
    QVERIFY(test_label != nullptr);
    
    auto config = createTestComponentConfig();
    
    // Register component
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");
    
    // Verify registration
    auto registered_components = manager->getRegisteredComponents();
    QVERIFY(registered_components.contains(test_component_id));
    
    // Verify component info
    auto info = manager->getComponentInfo(test_component_id);
    QCOMPARE(info.component_id, test_component_id);
    QCOMPARE(info.component_type, QString("QLabel"));
    QCOMPARE(info.widget_instance, test_label);
    QCOMPARE(info.source_file, QString("test_file.json"));
    QCOMPARE(info.original_config, config);
    QCOMPARE(info.current_config, config);
    QVERIFY(info.bound_properties.contains("text"));
    QVERIFY(info.bound_properties.contains("wordWrap"));
    QVERIFY(info.bound_properties.contains("alignment"));
    QVERIFY(info.event_handlers.contains("clicked"));
}

void TestSelectiveReloadManager::testComponentUnregistration() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");
    
    // Verify registration
    QVERIFY(manager->getRegisteredComponents().contains(test_component_id));
    
    // Unregister component
    manager->unregisterComponent(test_component_id);
    
    // Verify unregistration
    QVERIFY(!manager->getRegisteredComponents().contains(test_component_id));
    
    // Verify component info is empty
    auto info = manager->getComponentInfo(test_component_id);
    QVERIFY(info.component_id.isEmpty());
}

void TestSelectiveReloadManager::testComponentConfigUpdate() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");
    
    // Update configuration
    QJsonObject new_config = config;
    QJsonObject new_properties = new_config["properties"].toObject();
    new_properties["text"] = "Updated Text";
    new_config["properties"] = new_properties;
    
    manager->updateComponentConfig(test_component_id, new_config);
    
    // Verify update
    auto info = manager->getComponentInfo(test_component_id);
    QCOMPARE(info.current_config, new_config);
    QVERIFY(info.current_config != info.original_config);
}

void TestSelectiveReloadManager::testGetComponentInfo() {
    // Test with non-existent component
    auto empty_info = manager->getComponentInfo("non_existent");
    QVERIFY(empty_info.component_id.isEmpty());
    
    // Test with registered component
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");
    
    auto info = manager->getComponentInfo(test_component_id);
    QVERIFY(!info.component_id.isEmpty());
    QCOMPARE(info.component_id, test_component_id);
    QVERIFY(info.widget_instance != nullptr);
}

void TestSelectiveReloadManager::testStylesheetRegistration() {
    auto* stylesheet_file = createTestStylesheetFile();
    QString file_path = stylesheet_file->fileName();
    
    // Register stylesheet
    manager->registerStylesheet(test_stylesheet_id, file_path, "application");
    
    // Verify registration
    auto registered_stylesheets = manager->getRegisteredStylesheets();
    QVERIFY(registered_stylesheets.contains(test_stylesheet_id));
    
    // Verify stylesheet info
    auto info = manager->getStylesheetInfo(test_stylesheet_id);
    QCOMPARE(info.stylesheet_id, test_stylesheet_id);
    QCOMPARE(info.file_path, file_path);
    QCOMPARE(info.injection_scope, QString("application"));
    QVERIFY(!info.css_content.isEmpty());
    
    delete stylesheet_file;
}

void TestSelectiveReloadManager::testStylesheetUpdate() {
    auto* stylesheet_file = createTestStylesheetFile();
    QString file_path = stylesheet_file->fileName();
    
    manager->registerStylesheet(test_stylesheet_id, file_path, "application");
    
    // Update stylesheet content
    QString new_css = "QLabel { color: red; font-weight: bold; }";
    manager->updateStylesheet(test_stylesheet_id, new_css);
    
    // Verify update
    auto info = manager->getStylesheetInfo(test_stylesheet_id);
    QCOMPARE(info.css_content, new_css);
    
    delete stylesheet_file;
}

void TestSelectiveReloadManager::testStylesheetUnregistration() {
    auto* stylesheet_file = createTestStylesheetFile();
    QString file_path = stylesheet_file->fileName();
    
    manager->registerStylesheet(test_stylesheet_id, file_path, "application");
    
    // Verify registration
    QVERIFY(manager->getRegisteredStylesheets().contains(test_stylesheet_id));
    
    // Unregister stylesheet
    manager->unregisterStylesheet(test_stylesheet_id);
    
    // Verify unregistration
    QVERIFY(!manager->getRegisteredStylesheets().contains(test_stylesheet_id));
    
    delete stylesheet_file;
}

void TestSelectiveReloadManager::testResourceRegistration() {
    auto* resource_file = createTestResourceFile();
    QString file_path = resource_file->fileName();

    // Register resource
    manager->registerResource(test_resource_id, file_path, "image");

    // Verify registration
    auto registered_resources = manager->getRegisteredResources();
    QVERIFY(registered_resources.contains(test_resource_id));

    // Verify resource info
    auto info = manager->getResourceInfo(test_resource_id);
    QCOMPARE(info.resource_id, test_resource_id);
    QCOMPARE(info.file_path, file_path);
    QCOMPARE(info.resource_type, QString("image"));
    QVERIFY(info.file_size > 0);
    QVERIFY(!info.checksum.isEmpty());

    delete resource_file;
}

void TestSelectiveReloadManager::testResourceUpdate() {
    auto* resource_file = createTestResourceFile();
    QString file_path = resource_file->fileName();

    manager->registerResource(test_resource_id, file_path, "image");

    auto original_info = manager->getResourceInfo(test_resource_id);

    // Simulate file modification by updating the resource
    manager->updateResource(test_resource_id);

    auto updated_info = manager->getResourceInfo(test_resource_id);
    QCOMPARE(updated_info.resource_id, original_info.resource_id);
    QCOMPARE(updated_info.file_path, original_info.file_path);

    delete resource_file;
}

void TestSelectiveReloadManager::testResourceUnregistration() {
    auto* resource_file = createTestResourceFile();
    QString file_path = resource_file->fileName();

    manager->registerResource(test_resource_id, file_path, "image");

    // Verify registration
    QVERIFY(manager->getRegisteredResources().contains(test_resource_id));

    // Unregister resource
    manager->unregisterResource(test_resource_id);

    // Verify unregistration
    QVERIFY(!manager->getRegisteredResources().contains(test_resource_id));

    delete resource_file;
}

void TestSelectiveReloadManager::testComponentReload() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    QSignalSpy reload_started_spy(manager.get(), &SelectiveReloadManager::reloadStarted);
    QSignalSpy reload_completed_spy(manager.get(), &SelectiveReloadManager::reloadCompleted);
    QSignalSpy component_reloaded_spy(manager.get(), &SelectiveReloadManager::componentReloaded);

    // Perform component reload
    auto result = manager->reloadComponent(test_component_id);

    // Verify result
    QVERIFY(result.success);
    QCOMPARE(result.target_id, test_component_id);
    QCOMPARE(result.type, SelectiveReloadType::Component);
    QVERIFY(result.duration_ms >= 0);

    // Verify signals
    QCOMPARE(reload_started_spy.count(), 1);
    QCOMPARE(reload_completed_spy.count(), 1);
    QCOMPARE(component_reloaded_spy.count(), 1);

    // Verify component info was updated
    auto info = manager->getComponentInfo(test_component_id);
    QCOMPARE(info.reload_count, 1);
}

void TestSelectiveReloadManager::testComponentPropertyReload() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    QString original_text = test_label->text();
    QString new_text = "Updated Property Text";

    // Perform property reload
    auto result = manager->reloadComponentProperty(test_component_id, "text", new_text);

    // Verify result
    QVERIFY(result.success);
    QCOMPARE(result.target_id, test_component_id);
    QCOMPARE(result.type, SelectiveReloadType::Property);

    // Verify property was updated
    QCOMPARE(test_label->text(), new_text);
    QVERIFY(test_label->text() != original_text);

    // Verify configuration was updated
    auto info = manager->getComponentInfo(test_component_id);
    QJsonObject properties = info.current_config["properties"].toObject();
    QCOMPARE(properties["text"].toString(), new_text);
}

void TestSelectiveReloadManager::testStylesheetReload() {
    auto* stylesheet_file = createTestStylesheetFile();
    QString file_path = stylesheet_file->fileName();

    manager->registerStylesheet(test_stylesheet_id, file_path, "application");

    QSignalSpy stylesheet_reloaded_spy(manager.get(), &SelectiveReloadManager::stylesheetReloaded);

    // Perform stylesheet reload
    auto result = manager->reloadStylesheet(test_stylesheet_id);

    // Verify result
    QVERIFY(result.success);
    QCOMPARE(result.target_id, test_stylesheet_id);
    QCOMPARE(result.type, SelectiveReloadType::Stylesheet);

    // Verify signals
    QCOMPARE(stylesheet_reloaded_spy.count(), 1);

    delete stylesheet_file;
}

void TestSelectiveReloadManager::testResourceReload() {
    auto* resource_file = createTestResourceFile();
    QString file_path = resource_file->fileName();

    manager->registerResource(test_resource_id, file_path, "image");

    QSignalSpy resource_reloaded_spy(manager.get(), &SelectiveReloadManager::resourceReloaded);

    // Perform resource reload
    auto result = manager->reloadResource(test_resource_id);

    // Verify result
    QVERIFY(result.success);
    QCOMPARE(result.target_id, test_resource_id);
    QCOMPARE(result.type, SelectiveReloadType::Resource);

    // Verify signals
    QCOMPARE(resource_reloaded_spy.count(), 1);

    delete resource_file;
}

void TestSelectiveReloadManager::testReloadComponentsByType() {
    // Register multiple components of the same type
    auto config1 = createTestComponentConfig();
    config1["id"] = "component1";
    manager->registerComponent("component1", test_label, config1, "test_file.json");

    auto config2 = createTestComponentConfig();
    config2["id"] = "component2";
    manager->registerComponent("component2", test_button, config2, "test_file.json");

    // Reload components by type
    auto result = manager->reloadComponentsByType("QLabel");

    // Verify result
    QVERIFY(result.success);
    QCOMPARE(result.affected_components.size(), 1);
    QVERIFY(result.affected_components.contains("component1"));
    QCOMPARE(result.metadata["success_count"].toInt(), 1);
    QCOMPARE(result.metadata["total_count"].toInt(), 1);
}

void TestSelectiveReloadManager::testReloadComponentsBySelector() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Test ID selector
    auto result = manager->reloadComponentsBySelector("#" + test_component_id);

    // Verify result
    QVERIFY(result.success);
    QCOMPARE(result.affected_components.size(), 1);
    QVERIFY(result.affected_components.contains(test_component_id));

    // Test type selector
    auto result2 = manager->reloadComponentsBySelector("QLabel");
    QVERIFY(result2.success);
    QCOMPARE(result2.affected_components.size(), 1);
}

void TestSelectiveReloadManager::testReloadTargets() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Create reload targets
    QList<SelectiveReloadTarget> targets;

    SelectiveReloadTarget target1;
    target1.target_id = test_component_id;
    target1.type = SelectiveReloadType::Component;
    target1.priority = 100;
    targets.append(target1);

    SelectiveReloadTarget target2;
    target2.target_id = test_component_id;
    target2.type = SelectiveReloadType::Property;
    target2.priority = 50;
    target2.metadata["property_name"] = "text";
    target2.metadata["property_value"] = "Batch Updated Text";
    targets.append(target2);

    // Perform batch reload
    auto results = manager->reloadTargets(targets);

    // Verify results
    QCOMPARE(results.size(), 2);
    QVERIFY(results[0].success); // Component reload (higher priority, executed first)
    QVERIFY(results[1].success); // Property reload

    // Verify property was updated
    QCOMPARE(test_label->text(), QString("Batch Updated Text"));
}

void TestSelectiveReloadManager::testReloadDependencies() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    QString dependent_id = "dependent_component";
    auto dependent_config = createTestComponentConfig();
    dependent_config["id"] = dependent_id;
    manager->registerComponent(dependent_id, test_button, dependent_config, "test_file.json");

    // Add dependency
    manager->addDependency(test_component_id, dependent_id);

    // Reload dependencies
    auto results = manager->reloadDependencies(test_component_id);

    // Verify results
    QCOMPARE(results.size(), 1);
    QVERIFY(results[0].success);
    QCOMPARE(results[0].target_id, dependent_id);
}

void TestSelectiveReloadManager::testReloadAffectedComponents() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Reload affected components for the source file
    auto results = manager->reloadAffectedComponents("test_file.json");

    // Verify results
    QCOMPARE(results.size(), 1);
    QVERIFY(results[0].success);
    QCOMPARE(results[0].target_id, test_component_id);
}

void TestSelectiveReloadManager::testDependencyManagement() {
    QString source_id = "source_component";
    QString dependent_id = "dependent_component";

    // Add dependency
    manager->addDependency(source_id, dependent_id);

    // Verify dependency was added
    auto dependencies = manager->getDependencies(source_id);
    QVERIFY(dependencies.contains(dependent_id));

    // Verify reverse lookup
    auto dependents = manager->getDependents(dependent_id);
    QVERIFY(dependents.contains(source_id));

    // Remove dependency
    manager->removeDependency(source_id, dependent_id);

    // Verify dependency was removed
    auto updated_dependencies = manager->getDependencies(source_id);
    QVERIFY(!updated_dependencies.contains(dependent_id));
}

void TestSelectiveReloadManager::testDependencyCascading() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    QString dependent_id = "dependent_component";
    auto dependent_config = createTestComponentConfig();
    dependent_config["id"] = dependent_id;
    manager->registerComponent(dependent_id, test_button, dependent_config, "test_file.json");

    // Add dependency
    manager->addDependency(test_component_id, dependent_id);

    QSignalSpy dependency_spy(manager.get(), &SelectiveReloadManager::dependencyReloadTriggered);

    // Enable cascading
    auto config_with_cascade = manager->getConfiguration();
    config_with_cascade.cascade_dependencies = true;
    manager->setConfiguration(config_with_cascade);

    // Reload source component (should cascade to dependent)
    auto result = manager->reloadComponent(test_component_id);

    // Verify cascading occurred
    QVERIFY(result.success);
    QCOMPARE(dependency_spy.count(), 1);
}

void TestSelectiveReloadManager::testRollbackCreation() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Enable rollback points
    auto rollback_config = manager->getConfiguration();
    rollback_config.create_rollback_points = true;
    manager->setConfiguration(rollback_config);

    // Create rollback point
    manager->createRollbackPoint(test_component_id);

    // Modify component
    manager->reloadComponentProperty(test_component_id, "text", "Modified Text");

    // Verify modification
    QCOMPARE(test_label->text(), QString("Modified Text"));
}

void TestSelectiveReloadManager::testRollbackExecution() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Enable rollback points
    auto rollback_config = manager->getConfiguration();
    rollback_config.create_rollback_points = true;
    manager->setConfiguration(rollback_config);

    QString original_text = test_label->text();

    // Create rollback point
    manager->createRollbackPoint(test_component_id);

    // Modify component
    manager->reloadComponentProperty(test_component_id, "text", "Modified Text");
    QCOMPARE(test_label->text(), QString("Modified Text"));

    QSignalSpy rollback_spy(manager.get(), &SelectiveReloadManager::rollbackPerformed);

    // Perform rollback
    bool rollback_success = manager->rollbackTarget(test_component_id);

    // Verify rollback
    QVERIFY(rollback_success);
    QCOMPARE(rollback_spy.count(), 1);
    // Note: The actual text restoration depends on the implementation details
}

void TestSelectiveReloadManager::testRollbackClear() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Enable rollback points
    auto rollback_config = manager->getConfiguration();
    rollback_config.create_rollback_points = true;
    manager->setConfiguration(rollback_config);

    // Create rollback point
    manager->createRollbackPoint(test_component_id);

    // Clear rollback points
    manager->clearRollbackPoints();

    // Verify rollback is no longer available
    bool rollback_success = manager->rollbackTarget(test_component_id);
    QVERIFY(!rollback_success);
}

void TestSelectiveReloadManager::testMonitoringControl() {
    // Test initial state
    QVERIFY(!manager->getPerformanceMetrics()["monitoring_enabled"].toBool());

    QSignalSpy monitoring_spy(manager.get(), &SelectiveReloadManager::monitoringStateChanged);

    // Start monitoring
    manager->startMonitoring();
    QVERIFY(manager->getPerformanceMetrics()["monitoring_enabled"].toBool());
    QCOMPARE(monitoring_spy.count(), 1);

    // Stop monitoring
    manager->stopMonitoring();
    QVERIFY(!manager->getPerformanceMetrics()["monitoring_enabled"].toBool());
    QCOMPARE(monitoring_spy.count(), 2);

    // Test pause monitoring
    manager->startMonitoring();
    manager->pauseMonitoring();
    // Pause doesn't change the monitoring state, just stops timers
}

void TestSelectiveReloadManager::testAutoDetection() {
    // Test enabling/disabling auto-detection
    manager->enableAutoDetection(true);
    QVERIFY(manager->getPerformanceMetrics()["auto_detection_enabled"].toBool());

    manager->enableAutoDetection(false);
    QVERIFY(!manager->getPerformanceMetrics()["auto_detection_enabled"].toBool());
}

void TestSelectiveReloadManager::testFileChangeDetection() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Enable auto-detection and monitoring
    manager->enableAutoDetection(true);
    manager->startMonitoring();

    // Simulate file change
    manager->onFileChanged("test_file.json");

    // The method should not crash and should handle the file change
    QVERIFY(true);
}

void TestSelectiveReloadManager::testPerformanceMetrics() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Perform some operations to generate metrics
    manager->reloadComponent(test_component_id);

    auto metrics = manager->getPerformanceMetrics();

    // Verify metrics structure
    QVERIFY(metrics.contains("session_uptime_ms"));
    QVERIFY(metrics.contains("monitoring_enabled"));
    QVERIFY(metrics.contains("auto_detection_enabled"));
    QVERIFY(metrics.contains("reload_counts"));
    QVERIFY(metrics.contains("tracked_components_count"));
    QVERIFY(metrics.contains("tracked_stylesheets_count"));
    QVERIFY(metrics.contains("tracked_resources_count"));

    // Verify component count
    QCOMPARE(metrics["tracked_components_count"].toInt(), 1);
}

void TestSelectiveReloadManager::testReloadStatistics() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Perform some reloads
    manager->reloadComponent(test_component_id);
    manager->reloadComponent(test_component_id);

    auto stats = manager->getReloadStatistics();

    // Verify statistics structure
    QVERIFY(stats.contains("total_reloads"));
    QVERIFY(stats.contains("session_duration_ms"));
    QVERIFY(stats.contains("average_reloads_per_minute"));
    QVERIFY(stats.contains("active_components"));

    // Verify reload count
    QCOMPARE(stats["total_reloads"].toInt(), 2);
}

void TestSelectiveReloadManager::testStatisticsReset() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Perform some operations
    manager->reloadComponent(test_component_id);

    auto stats_before = manager->getReloadStatistics();
    QVERIFY(stats_before["total_reloads"].toInt() > 0);

    // Reset statistics
    manager->resetStatistics();

    auto stats_after = manager->getReloadStatistics();
    QCOMPARE(stats_after["total_reloads"].toInt(), 0);

    // Verify component reload count was reset
    auto info = manager->getComponentInfo(test_component_id);
    QCOMPARE(info.reload_count, 0);
}

void TestSelectiveReloadManager::testErrorHandling() {
    // Test reload of non-existent component
    auto result = manager->reloadComponent("non_existent_component");
    QVERIFY(!result.success);
    QVERIFY(!result.error_message.isEmpty());

    // Test property reload of non-existent component
    auto prop_result = manager->reloadComponentProperty("non_existent_component", "text", "value");
    QVERIFY(!prop_result.success);

    // Test stylesheet reload of non-existent stylesheet
    auto style_result = manager->reloadStylesheet("non_existent_stylesheet");
    QVERIFY(!style_result.success);

    // Test resource reload of non-existent resource
    auto resource_result = manager->reloadResource("non_existent_resource");
    QVERIFY(!resource_result.success);
}

void TestSelectiveReloadManager::testErrorRecovery() {
    auto config = createTestComponentConfig();
    manager->registerComponent(test_component_id, test_label, config, "test_file.json");

    // Enable rollback points for error recovery
    auto recovery_config = manager->getConfiguration();
    recovery_config.create_rollback_points = true;
    manager->setConfiguration(recovery_config);

    // Create rollback point
    manager->createRollbackPoint(test_component_id);

    // The error recovery mechanism is internal and hard to test directly
    // This test verifies the setup doesn't crash
    QVERIFY(true);
}

void TestSelectiveReloadManager::testInvalidOperations() {
    // Test operations with empty/invalid parameters
    auto empty_result = manager->reloadComponent("");
    QVERIFY(!empty_result.success);

    auto null_result = manager->reloadComponentProperty("", "", QVariant());
    QVERIFY(!null_result.success);

    // Test registration with invalid parameters
    manager->registerComponent("", nullptr, QJsonObject{}, "");
    // Should not crash, but component won't be registered
    QVERIFY(manager->getRegisteredComponents().isEmpty());
}

void TestSelectiveReloadManager::testIntegrationWithHotReloadManager() {
    // Set up integration
    manager->setHotReloadManager(hot_reload_manager.get());

    // The integration is mainly through signal connections
    // This test verifies the setup doesn't crash
    QVERIFY(true);
}

void TestSelectiveReloadManager::testIntegrationWithUILoader() {
    // Set up integration
    manager->setUILoader(ui_loader.get());

    // The integration is mainly through signal connections
    // This test verifies the setup doesn't crash
    QVERIFY(true);
}

void TestSelectiveReloadManager::testComplexReloadScenario() {
    // Set up a complex scenario with multiple components, stylesheets, and resources
    auto config1 = createTestComponentConfig();
    config1["id"] = "component1";
    manager->registerComponent("component1", test_label, config1, "ui_file.json");

    auto config2 = createTestComponentConfig();
    config2["id"] = "component2";
    manager->registerComponent("component2", test_button, config2, "ui_file.json");

    auto* stylesheet_file = createTestStylesheetFile();
    manager->registerStylesheet("main_style", stylesheet_file->fileName(), "application");

    auto* resource_file = createTestResourceFile();
    manager->registerResource("main_icon", resource_file->fileName(), "image");

    // Set up dependencies
    manager->addDependency("main_style", "component1");
    manager->addDependency("main_icon", "component2");

    // Enable cascading
    auto cascade_config = manager->getConfiguration();
    cascade_config.cascade_dependencies = true;
    manager->setConfiguration(cascade_config);

    // Perform complex reload operation
    auto results = manager->reloadAffectedComponents("ui_file.json");

    // Verify results
    QVERIFY(results.size() >= 2); // At least the two components

    // Clean up
    delete stylesheet_file;
    delete resource_file;
}

QTEST_MAIN(TestSelectiveReloadManager)
#include "test_selective_reload_manager.moc"
