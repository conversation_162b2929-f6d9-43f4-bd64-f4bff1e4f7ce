# Pull Request

## 📋 Description

<!-- Provide a clear and concise description of what this PR does -->

### Type of Change

<!-- Mark the relevant option with an "x" -->

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes, no api changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test improvement
- [ ] 🏗️ Build system change
- [ ] 🎨 Style/formatting change

## 🔗 Related Issues

<!-- Link to related issues using keywords like "Fixes #123", "Closes #456", "Relates to #789" -->

- Fixes #
- Closes #
- Relates to #

## 🧪 Testing

### Test Coverage

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Performance tests added/updated (if applicable)
- [ ] Manual testing completed

### Test Results

<!-- Describe the testing you've done -->

```bash
# Test commands run and their results
ctest --output-on-failure
# All tests passed: X/Y
```

### Platforms Tested

- [ ] Windows (MSVC 2019+)
- [ ] Windows (MinGW)
- [ ] Linux (GCC 10+)
- [ ] Linux (Clang 12+)
- [ ] macOS (Clang)

## 📖 Documentation

- [ ] Code is self-documenting with clear variable/function names
- [ ] Public APIs are documented with Doxygen comments
- [ ] README.md updated (if applicable)
- [ ] API documentation updated (if applicable)
- [ ] User guide updated (if applicable)
- [ ] Examples added/updated (if applicable)
- [ ] CHANGELOG.md updated

## 🔍 Code Quality

### Code Style

- [ ] Code follows the project's style guidelines
- [ ] Code has been formatted with clang-format
- [ ] No compiler warnings introduced
- [ ] Modern C++20 features used appropriately

### Performance

- [ ] No performance regressions introduced
- [ ] Memory usage is reasonable
- [ ] No memory leaks (tested with appropriate tools)
- [ ] RAII principles followed

### Error Handling

- [ ] Proper exception handling implemented
- [ ] Error conditions are handled gracefully
- [ ] Resource cleanup is guaranteed (RAII)

## 🚀 Component Impact

<!-- Mark all components affected by this change -->

- [ ] Core Framework
- [ ] Components Library
- [ ] Command System
- [ ] State Management
- [ ] Hot Reload
- [ ] JSON Support
- [ ] Build System
- [ ] Examples
- [ ] Tests
- [ ] Documentation

## 🔄 Breaking Changes

<!-- If this is a breaking change, describe the impact and migration path -->

### API Changes

<!-- List any API changes -->

### Migration Guide

<!-- Provide guidance for users to migrate their code -->

## 📸 Screenshots/Examples

<!-- If applicable, add screenshots or code examples showing the changes -->

### Before

```cpp
// Old code example
```

### After

```cpp
// New code example
```

## 🎯 Performance Impact

<!-- Describe any performance implications -->

- [ ] No performance impact
- [ ] Performance improvement (describe below)
- [ ] Potential performance impact (describe below and mitigation)

### Benchmarks

<!-- If applicable, provide benchmark results -->

## 🔧 Build System Changes

<!-- If you've made changes to the build system -->

- [ ] CMakeLists.txt updated
- [ ] Build presets updated
- [ ] Dependencies added/removed
- [ ] Build scripts updated

### New Dependencies

<!-- List any new dependencies and justify their inclusion -->

## 📝 Additional Notes

<!-- Any additional information that reviewers should know -->

### Review Focus Areas

<!-- Highlight specific areas where you'd like focused review -->

- [ ] Algorithm correctness
- [ ] API design
- [ ] Performance implications
- [ ] Error handling
- [ ] Thread safety
- [ ] Memory management

### Known Limitations

<!-- List any known limitations or future work needed -->

## ✅ Checklist

<!-- Ensure all items are completed before requesting review -->

- [ ] I have read the [Contributing Guidelines](CONTRIBUTING.md)
- [ ] My code follows the project's coding standards
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## 🤝 Reviewer Notes

<!-- Any specific guidance for reviewers -->

### Suggested Reviewers

<!-- Tag specific people if their expertise is needed -->

- @username (for component expertise)
- @username (for performance review)

### Review Priority

- [ ] Low - Non-critical improvement
- [ ] Medium - Standard feature/fix
- [ ] High - Important fix or feature
- [ ] Critical - Urgent fix required
