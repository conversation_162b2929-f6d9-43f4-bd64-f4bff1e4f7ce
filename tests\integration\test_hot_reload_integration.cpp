#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QTemporaryFile>
#include <QTextStream>
#include <QTimer>
#include <QApplication>
#include <memory>

#include "../../src/HotReload/HotReloadManager.hpp"
#include "../../src/HotReload/EnhancedConfigurationSystem.hpp"
#include "../../src/HotReload/AdvancedFileFilter.hpp"
#include "../../src/HotReload/SelectiveReloadManager.hpp"
#include "../../src/HotReload/EnhancedErrorRecoverySystem.hpp"
#include "../../src/HotReload/DiagnosticsEngine.hpp"
#include "../../src/HotReload/PerformanceMonitor.hpp"
#include "../../src/HotReload/FileWatcher.hpp"

using namespace DeclarativeUI::HotReload;

class TestHotReloadIntegration : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // End-to-end integration tests
    void testCompleteHotReloadWorkflow();
    void testConfigurationSystemIntegration();
    void testFileFilteringIntegration();
    void testSelectiveReloadIntegration();
    void testErrorRecoveryIntegration();
    void testPerformanceMonitoringIntegration();

    // Cross-component interaction tests
    void testConfigurationAndFilteringInteraction();
    void testSelectiveReloadAndErrorRecovery();
    void testDashboardAndMonitoringIntegration();
    void testFileWatcherAndReloadManager();

    // Real-world scenario tests
    void testQmlFileModification();
    void testCssStylesheetUpdate();
    void testResourceFileChange();
    void testMultipleFileChanges();
    void testErrorScenarios();

    // Performance and stress tests
    void testHighFrequencyChanges();
    void testLargeFileHandling();
    void testConcurrentOperations();

private:
    std::unique_ptr<HotReloadManager> hot_reload_manager_;
    std::unique_ptr<EnhancedConfigurationManager> config_manager_;
    std::unique_ptr<AdvancedFileFilter> file_filter_;
    std::unique_ptr<SelectiveReloadManager> selective_reload_manager_;
    std::unique_ptr<EnhancedErrorRecoverySystem> error_recovery_system_;
    std::unique_ptr<DiagnosticsEngine> diagnostics_engine_;
    std::unique_ptr<PerformanceMonitor> performance_monitor_;
    std::unique_ptr<FileWatcher> file_watcher_;
    
    QTemporaryDir temp_dir_;
    QString test_project_dir_;
    
    void setupTestProject();
    void createTestFile(const QString& relative_path, const QString& content);
    void modifyTestFile(const QString& relative_path, const QString& new_content);
    QString getTestFilePath(const QString& relative_path) const;
    void waitForFileSystemEvents(int timeout_ms = 2000);
};

void TestHotReloadIntegration::initTestCase() {
    QVERIFY(temp_dir_.isValid());
    test_project_dir_ = temp_dir_.path();
    setupTestProject();
}

void TestHotReloadIntegration::cleanupTestCase() {
    // Cleanup handled by destructors
}

void TestHotReloadIntegration::init() {
    // Create all components
    hot_reload_manager_ = std::make_unique<HotReloadManager>();
    config_manager_ = std::make_unique<EnhancedConfigurationManager>();
    file_filter_ = std::make_unique<AdvancedFileFilter>();
    selective_reload_manager_ = std::make_unique<SelectiveReloadManager>();
    error_recovery_system_ = std::make_unique<EnhancedErrorRecoverySystem>();
    diagnostics_engine_ = std::make_unique<DiagnosticsEngine>();
    performance_monitor_ = std::make_unique<PerformanceMonitor>();
    file_watcher_ = std::make_unique<FileWatcher>();
    
    // Connect components
    error_recovery_system_->setDiagnosticsEngine(diagnostics_engine_.get());
    hot_reload_manager_->setFileWatcher(file_watcher_.get());
    hot_reload_manager_->setPerformanceMonitor(performance_monitor_.get());
    hot_reload_manager_->setDiagnosticsEngine(diagnostics_engine_.get());
    
    // Configure for testing
    ConfigurationProfile test_profile("integration_test");
    test_profile.watch_directories = {test_project_dir_};
    test_profile.file_patterns = {"*.qml", "*.css", "*.js", "*.png", "*.jpg"};
    test_profile.exclude_patterns = {"*.tmp", "*.bak"};
    test_profile.watch_delay_ms = 100; // Fast response for testing
    test_profile.enable_performance_monitoring = true;
    test_profile.enable_detailed_logging = true;
    
    config_manager_->addProfile(test_profile);
    config_manager_->setActiveProfile("integration_test");
}

void TestHotReloadIntegration::cleanup() {
    // Stop all monitoring
    if (hot_reload_manager_) {
        hot_reload_manager_->stopWatching();
    }
    if (file_watcher_) {
        file_watcher_->stopWatching();
    }
    if (performance_monitor_) {
        performance_monitor_->stopMonitoring();
    }
    
    // Reset components
    hot_reload_manager_.reset();
    config_manager_.reset();
    file_filter_.reset();
    selective_reload_manager_.reset();
    error_recovery_system_.reset();
    diagnostics_engine_.reset();
    performance_monitor_.reset();
    file_watcher_.reset();
}

void TestHotReloadIntegration::setupTestProject() {
    // Create a realistic project structure
    QDir dir(test_project_dir_);
    
    // Create directories
    QVERIFY(dir.mkpath("src/components"));
    QVERIFY(dir.mkpath("src/styles"));
    QVERIFY(dir.mkpath("resources/images"));
    QVERIFY(dir.mkpath("build"));
    
    // Create test files
    createTestFile("src/main.qml", R"(
import QtQuick 2.15
import QtQuick.Controls 2.15

ApplicationWindow {
    id: window
    width: 800
    height: 600
    visible: true
    title: "Hot Reload Test App"
    
    Rectangle {
        id: mainRect
        anchors.fill: parent
        color: "#f0f0f0"
        
        Text {
            anchors.centerIn: parent
            text: "Hello, Hot Reload!"
            font.pixelSize: 24
        }
    }
}
)");
    
    createTestFile("src/components/Button.qml", R"(
import QtQuick 2.15
import QtQuick.Controls 2.15

Button {
    id: customButton
    width: 120
    height: 40
    
    background: Rectangle {
        color: customButton.pressed ? "#0066cc" : "#0080ff"
        radius: 4
    }
    
    contentItem: Text {
        text: customButton.text
        color: "white"
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
    }
}
)");
    
    createTestFile("src/styles/main.css", R"(
.button {
    background-color: #0080ff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
}

.button:hover {
    background-color: #0066cc;
}

.button:pressed {
    background-color: #004499;
}
)");
    
    createTestFile("src/config.json", R"({
    "app_name": "Hot Reload Test",
    "version": "1.0.0",
    "debug": true,
    "theme": "light"
})");
}

void TestHotReloadIntegration::createTestFile(const QString& relative_path, const QString& content) {
    QString full_path = getTestFilePath(relative_path);
    QFileInfo file_info(full_path);
    
    // Create directory if it doesn't exist
    QDir dir = file_info.dir();
    if (!dir.exists()) {
        QVERIFY(dir.mkpath("."));
    }
    
    QFile file(full_path);
    QVERIFY(file.open(QIODevice::WriteOnly | QIODevice::Text));
    
    QTextStream stream(&file);
    stream << content;
    file.close();
}

void TestHotReloadIntegration::modifyTestFile(const QString& relative_path, const QString& new_content) {
    QString full_path = getTestFilePath(relative_path);
    QFile file(full_path);
    QVERIFY(file.open(QIODevice::WriteOnly | QIODevice::Text));
    
    QTextStream stream(&file);
    stream << new_content;
    file.close();
}

QString TestHotReloadIntegration::getTestFilePath(const QString& relative_path) const {
    return QDir(test_project_dir_).filePath(relative_path);
}

void TestHotReloadIntegration::waitForFileSystemEvents(int timeout_ms) {
    QElapsedTimer timer;
    timer.start();
    
    while (timer.elapsed() < timeout_ms) {
        QApplication::processEvents();
        QThread::msleep(10);
    }
}

void TestHotReloadIntegration::testCompleteHotReloadWorkflow() {
    // Setup signal spies
    QSignalSpy file_changed_spy(file_watcher_.get(), &FileWatcher::fileChanged);
    QSignalSpy reload_started_spy(hot_reload_manager_.get(), &HotReloadManager::reloadStarted);
    QSignalSpy reload_completed_spy(hot_reload_manager_.get(), &HotReloadManager::reloadCompleted);
    
    // Start watching
    file_watcher_->addPath(getTestFilePath("src/main.qml"));
    file_watcher_->startWatching();
    hot_reload_manager_->startWatching();
    
    // Modify a file
    modifyTestFile("src/main.qml", R"(
import QtQuick 2.15
import QtQuick.Controls 2.15

ApplicationWindow {
    id: window
    width: 800
    height: 600
    visible: true
    title: "Hot Reload Test App - MODIFIED"
    
    Rectangle {
        id: mainRect
        anchors.fill: parent
        color: "#e0e0e0"  // Changed color
        
        Text {
            anchors.centerIn: parent
            text: "Hello, Hot Reload! - UPDATED"  // Changed text
            font.pixelSize: 28  // Changed size
        }
    }
}
)");
    
    // Wait for file system events
    waitForFileSystemEvents();
    
    // Verify the workflow
    QVERIFY(file_changed_spy.count() > 0);
    QVERIFY(reload_started_spy.count() > 0);
    
    // Wait a bit more for reload completion
    waitForFileSystemEvents(1000);
    
    QVERIFY(reload_completed_spy.count() > 0);
}

void TestHotReloadIntegration::testConfigurationSystemIntegration() {
    // Create a development profile
    ConfigurationProfile dev_profile("development");
    dev_profile.watch_directories = {test_project_dir_};
    dev_profile.file_patterns = {"*.qml", "*.js"};
    dev_profile.exclude_patterns = {"*.tmp"};
    dev_profile.watch_delay_ms = 50;
    dev_profile.enable_performance_monitoring = true;
    
    config_manager_->addProfile(dev_profile);
    
    // Create a production profile
    ConfigurationProfile prod_profile("production");
    prod_profile.watch_directories = {test_project_dir_};
    prod_profile.file_patterns = {"*.qml"};
    prod_profile.exclude_patterns = {"*.tmp", "*.js"};
    prod_profile.watch_delay_ms = 500;
    prod_profile.enable_performance_monitoring = false;
    
    config_manager_->addProfile(prod_profile);
    
    // Test switching profiles
    QSignalSpy profile_changed_spy(config_manager_.get(), &EnhancedConfigurationManager::activeProfileChanged);
    
    QVERIFY(config_manager_->setActiveProfile("development"));
    QCOMPARE(profile_changed_spy.count(), 1);
    
    ConfigurationProfile active_profile = config_manager_->getActiveProfile();
    QCOMPARE(active_profile.name, "development");
    QCOMPARE(active_profile.watch_delay_ms, 50);
    QVERIFY(active_profile.enable_performance_monitoring);
    
    // Switch to production profile
    QVERIFY(config_manager_->setActiveProfile("production"));
    QCOMPARE(profile_changed_spy.count(), 2);
    
    active_profile = config_manager_->getActiveProfile();
    QCOMPARE(active_profile.name, "production");
    QCOMPARE(active_profile.watch_delay_ms, 500);
    QVERIFY(!active_profile.enable_performance_monitoring);
}

void TestHotReloadIntegration::testFileFilteringIntegration() {
    // Setup file filter rules
    file_filter_->addRule(FilterRule::createGlobRule("*.qml", FilterRule::Include, 50));
    file_filter_->addRule(FilterRule::createGlobRule("*.css", FilterRule::Include, 50));
    file_filter_->addRule(FilterRule::createGlobRule("*.tmp", FilterRule::Exclude, 60));
    file_filter_->addRule(FilterRule::createDirectoryRule("build", FilterRule::Exclude, 70));
    
    // Create test files
    createTestFile("test.qml", "import QtQuick 2.15");
    createTestFile("style.css", ".button { }");
    createTestFile("temp.tmp", "temporary");
    createTestFile("build/output.txt", "build output");
    
    // Test filtering
    QVERIFY(file_filter_->shouldIncludeFile(getTestFilePath("test.qml")));
    QVERIFY(file_filter_->shouldIncludeFile(getTestFilePath("style.css")));
    QVERIFY(!file_filter_->shouldIncludeFile(getTestFilePath("temp.tmp")));
    QVERIFY(!file_filter_->shouldIncludeFile(getTestFilePath("build/output.txt")));
    
    // Test batch filtering
    QStringList all_files = {
        getTestFilePath("test.qml"),
        getTestFilePath("style.css"),
        getTestFilePath("temp.tmp"),
        getTestFilePath("build/output.txt")
    };
    
    QStringList filtered_files = file_filter_->filterFiles(all_files);
    QCOMPARE(filtered_files.size(), 2);
    QVERIFY(filtered_files.contains(getTestFilePath("test.qml")));
    QVERIFY(filtered_files.contains(getTestFilePath("style.css")));
}

void TestHotReloadIntegration::testSelectiveReloadIntegration() {
    // Setup selective reload manager
    selective_reload_manager_->setDiagnosticsEngine(diagnostics_engine_.get());

    // Setup signal spies
    QSignalSpy component_reloaded_spy(selective_reload_manager_.get(), &SelectiveReloadManager::componentReloaded);
    QSignalSpy stylesheet_reloaded_spy(selective_reload_manager_.get(), &SelectiveReloadManager::stylesheetReloaded);

    // Test component reload
    QString component_path = getTestFilePath("src/components/Button.qml");
    auto component_future = selective_reload_manager_->reloadComponent(component_path);

    // Wait for completion
    waitForFileSystemEvents(1000);

    if (component_future.isFinished()) {
        SelectiveReloadResult result = component_future.result();
        QVERIFY(result.isSuccess());
        QCOMPARE(result.getReloadType(), SelectiveReloadResult::Component);
    }

    // Test stylesheet reload
    QString stylesheet_path = getTestFilePath("src/styles/main.css");
    auto stylesheet_future = selective_reload_manager_->reloadStylesheet(stylesheet_path);

    // Wait for completion
    waitForFileSystemEvents(1000);

    if (stylesheet_future.isFinished()) {
        SelectiveReloadResult result = stylesheet_future.result();
        QVERIFY(result.isSuccess());
        QCOMPARE(result.getReloadType(), SelectiveReloadResult::Stylesheet);
    }
}

void TestHotReloadIntegration::testErrorRecoveryIntegration() {
    // Setup error recovery system
    QSignalSpy recovery_started_spy(error_recovery_system_.get(), &EnhancedErrorRecoverySystem::recoveryStarted);
    QSignalSpy recovery_completed_spy(error_recovery_system_.get(), &EnhancedErrorRecoverySystem::recoveryCompleted);

    // Create a file with syntax error
    createTestFile("error_test.qml", R"(
import QtQuick 2.15

Rectangle {
    width: 100
    height: 100
    color: "red"
    // Missing closing brace - syntax error
)");

    // Simulate error detection
    DiagnosticInfo diagnostic;
    diagnostic.id = "syntax_error_001";
    diagnostic.severity = DiagnosticInfo::Error;
    diagnostic.file_path = getTestFilePath("error_test.qml");
    diagnostic.message = "Syntax error: Missing closing brace";
    diagnostic.auto_recoverable = true;
    diagnostic.recovery_action = "restore_backup";

    // Report error to diagnostics engine
    diagnostics_engine_->reportError(diagnostic);

    // Wait for automatic recovery
    waitForFileSystemEvents(2000);

    // Verify recovery was attempted
    QVERIFY(recovery_started_spy.count() > 0);

    // Manual recovery test
    auto recovery_future = error_recovery_system_->attemptRecovery(diagnostic.id);
    waitForFileSystemEvents(1000);

    if (recovery_future.isFinished()) {
        RecoveryResult result = recovery_future.result();
        // Recovery might succeed or fail depending on available strategies
        QVERIFY(!result.operation_id.isEmpty());
    }
}

void TestHotReloadIntegration::testPerformanceMonitoringIntegration() {
    // Start performance monitoring
    performance_monitor_->startMonitoring();

    // Setup signal spies
    QSignalSpy metrics_updated_spy(performance_monitor_.get(), &PerformanceMonitor::metricsUpdated);

    // Perform some operations that should be monitored
    for (int i = 0; i < 5; ++i) {
        QString file_name = QString("perf_test_%1.qml").arg(i);
        createTestFile(file_name, QString("import QtQuick 2.15\nRectangle { width: %1; height: %1 }").arg(100 + i * 10));

        // Simulate file processing
        waitForFileSystemEvents(200);
    }

    // Wait for metrics to be collected
    waitForFileSystemEvents(1000);

    // Verify performance metrics were collected
    PerformanceMetrics metrics = performance_monitor_->getCurrentMetrics();
    QVERIFY(metrics.total_reload_time_ms >= 0);
    QVERIFY(metrics.file_processing_count >= 0);

    // Stop monitoring
    performance_monitor_->stopMonitoring();
}

void TestHotReloadIntegration::testConfigurationAndFilteringInteraction() {
    // Create a profile with specific filtering rules
    ConfigurationProfile filter_profile("filtered");
    filter_profile.watch_directories = {test_project_dir_};
    filter_profile.file_patterns = {"*.qml"};
    filter_profile.exclude_patterns = {"*_test.qml", "*.tmp"};
    filter_profile.max_file_size_mb = 1;

    config_manager_->addProfile(filter_profile);
    config_manager_->setActiveProfile("filtered");

    // Apply configuration to file filter
    ConfigurationProfile active_profile = config_manager_->getActiveProfile();

    // Clear existing rules and apply profile rules
    file_filter_->clearRules();

    // Add include rules for file patterns
    for (const QString& pattern : active_profile.file_patterns) {
        file_filter_->addRule(FilterRule::createGlobRule(pattern, FilterRule::Include, 50));
    }

    // Add exclude rules for exclude patterns
    for (const QString& pattern : active_profile.exclude_patterns) {
        file_filter_->addRule(FilterRule::createGlobRule(pattern, FilterRule::Exclude, 60));
    }

    // Add file size rule
    if (active_profile.max_file_size_mb > 0) {
        file_filter_->addRule(FilterRule::createFileSizeRule(
            active_profile.max_file_size_mb * 1024 * 1024, FilterRule::Exclude, 70));
    }

    // Test the interaction
    createTestFile("component.qml", "import QtQuick 2.15\nRectangle { }");
    createTestFile("component_test.qml", "import QtQuick 2.15\nRectangle { }");
    createTestFile("style.css", ".button { }");
    createTestFile("temp.tmp", "temporary");

    // Verify filtering based on configuration
    QVERIFY(file_filter_->shouldIncludeFile(getTestFilePath("component.qml"))); // Matches *.qml
    QVERIFY(!file_filter_->shouldIncludeFile(getTestFilePath("component_test.qml"))); // Excluded by *_test.qml
    QVERIFY(!file_filter_->shouldIncludeFile(getTestFilePath("style.css"))); // Not in file_patterns
    QVERIFY(!file_filter_->shouldIncludeFile(getTestFilePath("temp.tmp"))); // Excluded by *.tmp
}

void TestHotReloadIntegration::testSelectiveReloadAndErrorRecovery() {
    // Setup both systems
    selective_reload_manager_->setDiagnosticsEngine(diagnostics_engine_.get());
    error_recovery_system_->setDiagnosticsEngine(diagnostics_engine_.get());

    // Create a component with an error
    createTestFile("error_component.qml", R"(
import QtQuick 2.15

Rectangle {
    width: 100
    height: 100
    color: "invalid_color_name"  // This will cause an error
}
)");

    // Attempt selective reload which should fail
    QString component_path = getTestFilePath("error_component.qml");
    auto reload_future = selective_reload_manager_->reloadComponent(component_path);

    waitForFileSystemEvents(1000);

    if (reload_future.isFinished()) {
        SelectiveReloadResult reload_result = reload_future.result();

        if (!reload_result.isSuccess()) {
            // Reload failed, error recovery should be triggered
            DiagnosticInfo diagnostic;
            diagnostic.id = "component_reload_error";
            diagnostic.severity = DiagnosticInfo::Error;
            diagnostic.file_path = component_path;
            diagnostic.message = reload_result.getErrorMessage();
            diagnostic.auto_recoverable = true;

            // Report error
            diagnostics_engine_->reportError(diagnostic);

            // Attempt recovery
            auto recovery_future = error_recovery_system_->attemptRecovery(diagnostic.id);
            waitForFileSystemEvents(1000);

            if (recovery_future.isFinished()) {
                RecoveryResult recovery_result = recovery_future.result();
                // Recovery should attempt to fix the issue
                QVERIFY(!recovery_result.operation_id.isEmpty());
            }
        }
    }
}

void TestHotReloadIntegration::testDashboardAndMonitoringIntegration() {
    // This test would require the dashboard widget to be instantiated
    // For now, we'll test the data flow between monitoring and potential dashboard

    // Start monitoring
    performance_monitor_->startMonitoring();

    // Simulate dashboard requesting metrics
    PerformanceMetrics initial_metrics = performance_monitor_->getCurrentMetrics();
    QVERIFY(initial_metrics.total_reload_time_ms >= 0);

    // Perform some operations
    for (int i = 0; i < 3; ++i) {
        createTestFile(QString("dashboard_test_%1.qml").arg(i), "import QtQuick 2.15\nRectangle { }");
        waitForFileSystemEvents(100);
    }

    // Get updated metrics
    PerformanceMetrics updated_metrics = performance_monitor_->getCurrentMetrics();

    // Verify metrics were updated
    QVERIFY(updated_metrics.file_processing_count >= initial_metrics.file_processing_count);

    performance_monitor_->stopMonitoring();
}

void TestHotReloadIntegration::testFileWatcherAndReloadManager() {
    // Setup signal spies
    QSignalSpy file_changed_spy(file_watcher_.get(), &FileWatcher::fileChanged);
    QSignalSpy reload_triggered_spy(hot_reload_manager_.get(), &HotReloadManager::reloadStarted);

    // Connect file watcher to reload manager
    connect(file_watcher_.get(), &FileWatcher::fileChanged,
            hot_reload_manager_.get(), &HotReloadManager::onFileChanged);

    // Start watching
    QString watch_file = getTestFilePath("watch_test.qml");
    createTestFile("watch_test.qml", "import QtQuick 2.15\nRectangle { }");

    file_watcher_->addPath(watch_file);
    file_watcher_->startWatching();
    hot_reload_manager_->startWatching();

    // Modify the file
    modifyTestFile("watch_test.qml", "import QtQuick 2.15\nRectangle { color: \"blue\" }");

    // Wait for events
    waitForFileSystemEvents();

    // Verify the chain of events
    QVERIFY(file_changed_spy.count() > 0);

    // The reload manager should have been notified
    // Note: The actual reload might not trigger if the file doesn't meet certain criteria
    // but the signal connection should work
}

void TestHotReloadIntegration::testQmlFileModification() {
    // Create a QML component
    createTestFile("TestComponent.qml", R"(
import QtQuick 2.15

Rectangle {
    id: root
    width: 200
    height: 100
    color: "lightblue"

    Text {
        anchors.centerIn: parent
        text: "Original Text"
        font.pixelSize: 16
    }
}
)");

    // Setup monitoring
    QString component_path = getTestFilePath("TestComponent.qml");
    file_watcher_->addPath(component_path);
    file_watcher_->startWatching();

    QSignalSpy file_changed_spy(file_watcher_.get(), &FileWatcher::fileChanged);

    // Modify the component
    modifyTestFile("TestComponent.qml", R"(
import QtQuick 2.15

Rectangle {
    id: root
    width: 250
    height: 120
    color: "lightgreen"

    Text {
        anchors.centerIn: parent
        text: "Modified Text"
        font.pixelSize: 18
        color: "darkblue"
    }
}
)");

    // Wait for file system events
    waitForFileSystemEvents();

    // Verify file change was detected
    QVERIFY(file_changed_spy.count() > 0);

    // Verify the file was actually modified
    QFile file(component_path);
    QVERIFY(file.open(QIODevice::ReadOnly | QIODevice::Text));
    QString content = file.readAll();
    QVERIFY(content.contains("Modified Text"));
    QVERIFY(content.contains("lightgreen"));
    file.close();
}

void TestHotReloadIntegration::testCssStylesheetUpdate() {
    // Create a CSS stylesheet
    createTestFile("app.css", R"(
.main-button {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 14px;
}

.main-button:hover {
    background-color: #2980b9;
}
)");

    // Setup monitoring
    QString css_path = getTestFilePath("app.css");
    file_watcher_->addPath(css_path);

    QSignalSpy file_changed_spy(file_watcher_.get(), &FileWatcher::fileChanged);

    // Modify the stylesheet
    modifyTestFile("app.css", R"(
.main-button {
    background-color: #e74c3c;
    color: white;
    border: 2px solid #c0392b;
    border-radius: 6px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: bold;
}

.main-button:hover {
    background-color: #c0392b;
    transform: scale(1.05);
}

.main-button:active {
    transform: scale(0.95);
}
)");

    // Wait for file system events
    waitForFileSystemEvents();

    // Verify file change was detected
    QVERIFY(file_changed_spy.count() > 0);

    // Test selective stylesheet reload
    if (selective_reload_manager_) {
        auto reload_future = selective_reload_manager_->reloadStylesheet(css_path);
        waitForFileSystemEvents(500);

        if (reload_future.isFinished()) {
            SelectiveReloadResult result = reload_future.result();
            QCOMPARE(result.getReloadType(), SelectiveReloadResult::Stylesheet);
        }
    }
}

void TestHotReloadIntegration::testResourceFileChange() {
    // Create a mock image file (just text for testing)
    createTestFile("resources/icon.png", "PNG_MOCK_DATA");

    // Setup monitoring
    QString resource_path = getTestFilePath("resources/icon.png");
    file_watcher_->addPath(resource_path);

    QSignalSpy file_changed_spy(file_watcher_.get(), &FileWatcher::fileChanged);

    // Modify the resource file
    modifyTestFile("resources/icon.png", "PNG_MOCK_DATA_UPDATED");

    // Wait for file system events
    waitForFileSystemEvents();

    // Verify file change was detected
    QVERIFY(file_changed_spy.count() > 0);

    // Test selective resource reload
    if (selective_reload_manager_) {
        auto reload_future = selective_reload_manager_->reloadResource(resource_path);
        waitForFileSystemEvents(500);

        if (reload_future.isFinished()) {
            SelectiveReloadResult result = reload_future.result();
            QCOMPARE(result.getReloadType(), SelectiveReloadResult::Resource);
        }
    }
}

void TestHotReloadIntegration::testMultipleFileChanges() {
    // Create multiple files
    createTestFile("Component1.qml", "import QtQuick 2.15\nRectangle { color: \"red\" }");
    createTestFile("Component2.qml", "import QtQuick 2.15\nRectangle { color: \"green\" }");
    createTestFile("style1.css", ".button1 { background: blue; }");
    createTestFile("style2.css", ".button2 { background: yellow; }");

    // Setup monitoring for all files
    QStringList file_paths = {
        getTestFilePath("Component1.qml"),
        getTestFilePath("Component2.qml"),
        getTestFilePath("style1.css"),
        getTestFilePath("style2.css")
    };

    for (const QString& path : file_paths) {
        file_watcher_->addPath(path);
    }

    QSignalSpy file_changed_spy(file_watcher_.get(), &FileWatcher::fileChanged);

    // Modify all files simultaneously
    modifyTestFile("Component1.qml", "import QtQuick 2.15\nRectangle { color: \"darkred\" }");
    modifyTestFile("Component2.qml", "import QtQuick 2.15\nRectangle { color: \"darkgreen\" }");
    modifyTestFile("style1.css", ".button1 { background: darkblue; }");
    modifyTestFile("style2.css", ".button2 { background: orange; }");

    // Wait for file system events
    waitForFileSystemEvents(2000); // Longer wait for multiple files

    // Verify multiple file changes were detected
    QVERIFY(file_changed_spy.count() >= 4);

    // Test batch selective reload
    if (selective_reload_manager_) {
        QStringList qml_files = {
            getTestFilePath("Component1.qml"),
            getTestFilePath("Component2.qml")
        };

        auto batch_future = selective_reload_manager_->reloadComponents(qml_files);
        waitForFileSystemEvents(1000);

        if (batch_future.isFinished()) {
            QList<SelectiveReloadResult> results = batch_future.result();
            QCOMPARE(results.size(), 2);

            for (const auto& result : results) {
                QCOMPARE(result.getReloadType(), SelectiveReloadResult::Component);
            }
        }
    }
}

void TestHotReloadIntegration::testErrorScenarios() {
    // Test 1: Syntax error in QML file
    createTestFile("syntax_error.qml", R"(
import QtQuick 2.15

Rectangle {
    width: 100
    height: 100
    color: "red"
    // Missing closing brace
)");

    // Test 2: Invalid CSS
    createTestFile("invalid.css", R"(
.button {
    background-color: invalid-color-value;
    border-radius: not-a-number;
}
)");

    // Test 3: Non-existent import
    createTestFile("bad_import.qml", R"(
import QtQuick 2.15
import NonExistentModule 1.0

Rectangle {
    width: 100
    height: 100
}
)");

    // Setup error monitoring
    QSignalSpy error_reported_spy(diagnostics_engine_.get(), &DiagnosticsEngine::errorReported);
    QSignalSpy recovery_started_spy(error_recovery_system_.get(), &EnhancedErrorRecoverySystem::recoveryStarted);

    // Attempt to process these files
    QStringList error_files = {
        getTestFilePath("syntax_error.qml"),
        getTestFilePath("invalid.css"),
        getTestFilePath("bad_import.qml")
    };

    for (const QString& file_path : error_files) {
        // Simulate file processing that would detect errors
        if (selective_reload_manager_) {
            if (file_path.endsWith(".qml")) {
                auto reload_future = selective_reload_manager_->reloadComponent(file_path);
                waitForFileSystemEvents(500);
            } else if (file_path.endsWith(".css")) {
                auto reload_future = selective_reload_manager_->reloadStylesheet(file_path);
                waitForFileSystemEvents(500);
            }
        }
    }

    // Manually report some errors to test error recovery
    DiagnosticInfo syntax_error;
    syntax_error.id = "syntax_error_test";
    syntax_error.severity = DiagnosticInfo::Error;
    syntax_error.file_path = getTestFilePath("syntax_error.qml");
    syntax_error.message = "Syntax error: Missing closing brace";
    syntax_error.auto_recoverable = true;

    diagnostics_engine_->reportError(syntax_error);

    // Wait for error processing and potential recovery
    waitForFileSystemEvents(1000);

    // Verify error was reported
    QVERIFY(error_reported_spy.count() > 0);
}

void TestHotReloadIntegration::testHighFrequencyChanges() {
    // Create a test file
    createTestFile("high_freq_test.qml", "import QtQuick 2.15\nRectangle { }");

    QString file_path = getTestFilePath("high_freq_test.qml");
    file_watcher_->addPath(file_path);

    QSignalSpy file_changed_spy(file_watcher_.get(), &FileWatcher::fileChanged);

    // Make rapid changes
    for (int i = 0; i < 10; ++i) {
        modifyTestFile("high_freq_test.qml",
                      QString("import QtQuick 2.15\nRectangle { width: %1; height: %1 }").arg(100 + i * 10));
        QThread::msleep(50); // Very short delay between changes
    }

    // Wait for all events to be processed
    waitForFileSystemEvents(2000);

    // The file watcher should handle rapid changes gracefully
    // Due to debouncing, we might not get 10 separate events
    QVERIFY(file_changed_spy.count() > 0);
    QVERIFY(file_changed_spy.count() <= 10); // Should be debounced
}

void TestHotReloadIntegration::testLargeFileHandling() {
    // Create a large file (simulate with repeated content)
    QString large_content = "import QtQuick 2.15\n\nRectangle {\n";
    for (int i = 0; i < 1000; ++i) {
        large_content += QString("    // Comment line %1\n").arg(i);
    }
    large_content += "}\n";

    createTestFile("large_file.qml", large_content);

    // Test file filtering with size limits
    file_filter_->clearRules();
    file_filter_->addRule(FilterRule::createFileSizeRule(1024, FilterRule::Exclude, 50)); // 1KB limit

    QString large_file_path = getTestFilePath("large_file.qml");

    // Large file should be excluded
    QVERIFY(!file_filter_->shouldIncludeFile(large_file_path));

    // Test with higher size limit
    file_filter_->clearRules();
    file_filter_->addRule(FilterRule::createFileSizeRule(1024 * 1024, FilterRule::Exclude, 50)); // 1MB limit

    // Now it should be included
    QVERIFY(file_filter_->shouldIncludeFile(large_file_path));
}

void TestHotReloadIntegration::testConcurrentOperations() {
    // Create multiple files for concurrent processing
    QStringList test_files;
    for (int i = 0; i < 5; ++i) {
        QString file_name = QString("concurrent_test_%1.qml").arg(i);
        createTestFile(file_name, QString("import QtQuick 2.15\nRectangle { width: %1 }").arg(100 + i * 20));
        test_files.append(getTestFilePath(file_name));
    }

    // Test concurrent selective reloads
    if (selective_reload_manager_) {
        QList<QFuture<SelectiveReloadResult>> futures;

        // Start multiple reload operations concurrently
        for (const QString& file_path : test_files) {
            auto future = selective_reload_manager_->reloadComponent(file_path);
            futures.append(future);
        }

        // Wait for all operations to complete
        waitForFileSystemEvents(2000);

        // Check results
        int completed_count = 0;
        for (const auto& future : futures) {
            if (future.isFinished()) {
                completed_count++;
                SelectiveReloadResult result = future.result();
                QCOMPARE(result.getReloadType(), SelectiveReloadResult::Component);
            }
        }

        // At least some operations should have completed
        QVERIFY(completed_count > 0);
    }

    // Test concurrent error recovery operations
    if (error_recovery_system_) {
        QStringList diagnostic_ids;

        // Create multiple diagnostic entries
        for (int i = 0; i < 3; ++i) {
            DiagnosticInfo diagnostic;
            diagnostic.id = QString("concurrent_error_%1").arg(i);
            diagnostic.severity = DiagnosticInfo::Warning;
            diagnostic.file_path = test_files[i];
            diagnostic.message = QString("Test error %1").arg(i);
            diagnostic.auto_recoverable = true;

            diagnostics_engine_->reportError(diagnostic);
            diagnostic_ids.append(diagnostic.id);
        }

        // Attempt batch recovery
        auto batch_future = error_recovery_system_->attemptBatchRecovery(diagnostic_ids);
        waitForFileSystemEvents(1500);

        if (batch_future.isFinished()) {
            QList<RecoveryResult> results = batch_future.result();
            QCOMPARE(results.size(), 3);

            for (const auto& result : results) {
                QVERIFY(!result.operation_id.isEmpty());
            }
        }
    }
}

QTEST_MAIN(TestHotReloadIntegration)
#include "test_hot_reload_integration.moc"
