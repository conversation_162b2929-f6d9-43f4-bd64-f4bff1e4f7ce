#pragma once

#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QString>
#include <QStringList>
#include <QVariant>
#include <QSettings>
#include <QDir>
#include <QStandardPaths>
#include <memory>
#include <atomic>
#include <functional>

class QFileSystemWatcher;

namespace DeclarativeUI::HotReload {

/**
 * @brief File filtering configuration
 */
struct FileFilterConfig {
    QStringList include_patterns = {"*.json", "*.qml", "*.ui", "*.css", "*.scss"};
    QStringList exclude_patterns = {"*.tmp", "*.bak", "*~", ".git/*", "node_modules/*"};
    QStringList exclude_directories = {".git", ".svn", "node_modules", "build", "dist"};
    
    // Size limits
    qint64 max_file_size_bytes = 10 * 1024 * 1024; // 10MB
    qint64 min_file_size_bytes = 0;
    
    // Content-based filtering
    bool enable_content_filtering = false;
    QStringList content_include_patterns;
    QStringList content_exclude_patterns;
    
    // Performance settings
    bool enable_fast_filtering = true;
    bool cache_filter_results = true;
    int filter_cache_size = 1000;
    
    QJsonObject toJson() const;
    static FileFilterConfig fromJson(const QJsonObject& json);
    bool matchesFile(const QString& file_path) const;
    bool matchesContent(const QString& content) const;
};

/**
 * @brief Performance monitoring configuration
 */
struct PerformanceConfig {
    bool enable_monitoring = true;
    bool enable_real_time_analytics = true;
    bool enable_bottleneck_detection = true;
    bool enable_memory_profiling = false;
    bool enable_predictive_modeling = false;
    
    // Thresholds
    int warning_threshold_ms = 1000;
    int error_threshold_ms = 5000;
    qint64 memory_warning_threshold_mb = 100;
    qint64 memory_error_threshold_mb = 500;
    double cpu_warning_threshold_percent = 80.0;
    double cpu_error_threshold_percent = 95.0;
    
    // History and reporting
    int max_history_size = 1000;
    int report_interval_ms = 5000;
    bool auto_save_reports = false;
    QString reports_directory = "hot_reload_reports";
    
    // Optimization
    bool enable_automatic_optimizations = true;
    bool enable_adaptive_thresholds = true;
    
    QJsonObject toJson() const;
    static PerformanceConfig fromJson(const QJsonObject& json);
};

/**
 * @brief Error handling and recovery configuration
 */
struct ErrorRecoveryConfig {
    bool enable_automatic_rollback = true;
    bool enable_graceful_degradation = true;
    bool enable_user_notifications = true;
    bool enable_detailed_error_reporting = true;
    
    // Rollback settings
    int max_rollback_points = 10;
    bool auto_create_rollback_points = true;
    int rollback_timeout_ms = 5000;
    
    // Recovery strategies
    enum class RecoveryStrategy {
        None,
        Rollback,
        Retry,
        Skip,
        Prompt
    };
    RecoveryStrategy default_recovery_strategy = RecoveryStrategy::Rollback;
    
    // Notification settings
    bool show_error_dialogs = true;
    bool show_warning_dialogs = false;
    bool log_to_console = true;
    bool log_to_file = false;
    QString log_file_path = "hot_reload_errors.log";
    
    QJsonObject toJson() const;
    static ErrorRecoveryConfig fromJson(const QJsonObject& json);
};

/**
 * @brief File watching configuration
 */
struct FileWatchConfig {
    bool enable_file_watching = true;
    bool enable_directory_watching = true;
    bool recursive_directory_watching = true;
    
    // Debouncing
    int debounce_interval_ms = 100;
    int max_debounce_interval_ms = 1000;
    bool enable_adaptive_debouncing = true;
    
    // Performance
    int max_watched_files = 10000;
    int max_watched_directories = 1000;
    bool enable_batch_processing = true;
    int batch_size = 50;
    int thread_pool_size = 4;
    
    // Advanced features
    bool enable_change_frequency_tracking = true;
    bool enable_smart_filtering = true;
    bool enable_file_content_hashing = false;
    
    QJsonObject toJson() const;
    static FileWatchConfig fromJson(const QJsonObject& json);
};

/**
 * @brief Reload strategy configuration
 */
struct ReloadConfig {
    enum class Strategy {
        Immediate,
        Debounced,
        Batched,
        Smart,
        Manual
    };
    Strategy strategy = Strategy::Smart;
    
    // Timing
    int reload_delay_ms = 100;
    int batch_timeout_ms = 500;
    int max_reload_time_ms = 10000;
    
    // Concurrency
    int max_concurrent_reloads = 4;
    bool enable_parallel_processing = true;
    bool enable_incremental_reloading = true;
    
    // Caching and optimization
    bool enable_smart_caching = true;
    bool enable_preload_dependencies = false;
    qint64 cache_size_limit_mb = 50;
    
    // Memory management
    qint64 memory_limit_mb = 100;
    bool enable_memory_optimization = true;
    bool enable_garbage_collection = true;
    
    QJsonObject toJson() const;
    static ReloadConfig fromJson(const QJsonObject& json);
};

/**
 * @brief Environment-specific configuration profile
 */
struct ConfigProfile {
    QString name;
    QString description;
    bool is_active = false;
    
    FileFilterConfig file_filter;
    PerformanceConfig performance;
    ErrorRecoveryConfig error_recovery;
    FileWatchConfig file_watch;
    ReloadConfig reload;
    
    // Custom settings
    QJsonObject custom_settings;
    
    QJsonObject toJson() const;
    static ConfigProfile fromJson(const QJsonObject& json);
    void applyTo(class HotReloadConfig* config) const;
};

/**
 * @brief Main hot-reload configuration class
 */
class HotReloadConfig : public QObject {
    Q_OBJECT

public:
    explicit HotReloadConfig(QObject* parent = nullptr);
    ~HotReloadConfig() = default;

    // **Configuration management**
    bool loadFromFile(const QString& file_path);
    bool saveToFile(const QString& file_path) const;
    bool loadFromJson(const QJsonObject& json);
    QJsonObject toJson() const;
    
    // **Profile management**
    void addProfile(const ConfigProfile& profile);
    void removeProfile(const QString& profile_name);
    bool setActiveProfile(const QString& profile_name);
    QString getActiveProfileName() const;
    QStringList getProfileNames() const;
    ConfigProfile getProfile(const QString& profile_name) const;
    
    // **Default configurations**
    static ConfigProfile createDevelopmentProfile();
    static ConfigProfile createProductionProfile();
    static ConfigProfile createTestingProfile();
    static ConfigProfile createMinimalProfile();
    
    // **Configuration access**
    const FileFilterConfig& getFileFilterConfig() const { return file_filter_config_; }
    const PerformanceConfig& getPerformanceConfig() const { return performance_config_; }
    const ErrorRecoveryConfig& getErrorRecoveryConfig() const { return error_recovery_config_; }
    const FileWatchConfig& getFileWatchConfig() const { return file_watch_config_; }
    const ReloadConfig& getReloadConfig() const { return reload_config_; }
    
    // **Configuration updates**
    void setFileFilterConfig(const FileFilterConfig& config);
    void setPerformanceConfig(const PerformanceConfig& config);
    void setErrorRecoveryConfig(const ErrorRecoveryConfig& config);
    void setFileWatchConfig(const FileWatchConfig& config);
    void setReloadConfig(const ReloadConfig& config);
    
    // **Runtime configuration updates**
    void updateConfiguration(const QJsonObject& updates);
    void resetToDefaults();
    void validateConfiguration() const;
    
    // **Environment detection**
    static QString detectEnvironment();
    void loadEnvironmentSpecificConfig();
    
    // **Configuration watching**
    void enableConfigurationWatching(bool enabled);
    bool isConfigurationWatchingEnabled() const;

signals:
    void configurationChanged();
    void profileChanged(const QString& profile_name);
    void configurationError(const QString& error);

private slots:
    void onConfigurationFileChanged();

private:
    // **Core configuration**
    FileFilterConfig file_filter_config_;
    PerformanceConfig performance_config_;
    ErrorRecoveryConfig error_recovery_config_;
    FileWatchConfig file_watch_config_;
    ReloadConfig reload_config_;
    
    // **Profile management**
    QMap<QString, ConfigProfile> profiles_;
    QString active_profile_name_;
    
    // **Configuration file watching**
    std::unique_ptr<QFileSystemWatcher> config_watcher_;
    QString config_file_path_;
    std::atomic<bool> config_watching_enabled_{false};
    
    // **Validation and defaults**
    void initializeDefaults();
    void validateFileFilterConfig() const;
    void validatePerformanceConfig() const;
    void validateErrorRecoveryConfig() const;
    void validateFileWatchConfig() const;
    void validateReloadConfig() const;
    
    // **Helper methods**
    QString getDefaultConfigPath() const;
    void createDefaultProfiles();
    void mergeConfiguration(const QJsonObject& base, const QJsonObject& overlay);
};

} // namespace DeclarativeUI::HotReload
