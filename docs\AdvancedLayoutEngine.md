# Advanced Layout Engine

The DeclarativeUI Advanced Layout Engine provides sophisticated layout capabilities that go beyond traditional widget layouts. It includes CSS Grid-like layouts, Flexbox-style arrangements, responsive design features, and constraint-based positioning.

## 🏗️ Architecture Overview

The Advanced Layout Engine consists of four main layout types:

### 1. **GridLayout** - CSS Grid-like Layout System
- **Grid tracks** with flexible sizing (auto, fixed, percentage, fraction, min-content, max-content, fit-content)
- **Named grid lines and areas** for semantic layout definition
- **Grid alignment properties** (justify-content, align-content, justify-items, align-items)
- **Item positioning** with row/column spans and individual alignment

### 2. **FlexLayout** - Flexbox-style Layout System
- **Flex direction** (row, row-reverse, column, column-reverse)
- **Flex wrapping** capabilities with multi-line support
- **Flex item properties** (grow, shrink, basis) for flexible sizing
- **Justify-content and align-items** alignment with space distribution

### 3. **ResponsiveLayout** - Responsive Design System
- **Breakpoint management** with customizable breakpoints
- **Responsive property configuration** for different screen sizes
- **Automatic layout updates** based on container size changes
- **Bootstrap-like breakpoint defaults** (xs, sm, md, lg, xl)

### 4. **ConstraintLayout** - Constraint-based Positioning
- **Constraint relationships** between widgets with mathematical expressions
- **Convenience methods** for common alignments (center, align edges, etc.)
- **Priority-based constraint solving** with conflict detection
- **Dynamic constraint updates** with automatic layout recalculation

## 🚀 Quick Start

### Basic Usage

```cpp
#include "Layout/AdvancedLayoutEngine.hpp"

// Create the layout engine
auto layout_engine = std::make_unique<DeclarativeUI::Layout::AdvancedLayoutEngine>(parent);

// Create a grid layout
auto grid_layout = layout_engine->createGridLayout(container);

// Configure grid with 3 columns and 2 rows
grid_layout->templateColumns({
    {GridLayout::GridSizing::Fixed, 200.0},      // 200px
    {GridLayout::GridSizing::Fraction, 1.0},     // 1fr (flexible)
    {GridLayout::GridSizing::Fixed, 150.0}       // 150px
})
.templateRows({
    {GridLayout::GridSizing::Auto, 0.0},         // auto-size
    {GridLayout::GridSizing::Fraction, 1.0}      // 1fr (flexible)
})
.gap(16)  // 16px gap between items
.addItem(header_widget, 1, 1, 1, 3)  // span all columns
.addItem(sidebar_widget, 2, 1)       // first column, second row
.addItem(main_widget, 2, 2)          // second column, second row
.addItem(aside_widget, 2, 3);        // third column, second row

// Update the layout
grid_layout->updateLayout();
```

### Flexbox Layout

```cpp
// Create a flex layout
auto flex_layout = layout_engine->createFlexLayout(container);

// Configure flexbox properties
flex_layout->direction(FlexLayout::FlexDirection::Row)
           .wrap(FlexLayout::FlexWrap::Wrap)
           .justifyContent(FlexLayout::FlexAlignment::SpaceBetween)
           .alignItems(FlexLayout::FlexAlignment::Center)
           .gap(12)
           .addItem(item1, 1.0, 1.0, -1)    // flex: 1 1 auto
           .addItem(item2, 2.0, 1.0, -1)    // flex: 2 1 auto
           .addItem(item3, 0.0, 0.0, 200);  // flex: 0 0 200px

flex_layout->updateLayout();
```

### Responsive Layout

```cpp
// Create a responsive layout
auto responsive_layout = layout_engine->createResponsiveLayout(container);

// Configure responsive spacing
responsive_layout->responsiveSpacing({
    {"xs", 8},   // 8px spacing on extra small screens
    {"sm", 12},  // 12px spacing on small screens
    {"md", 16},  // 16px spacing on medium screens
    {"lg", 20},  // 20px spacing on large screens
    {"xl", 24}   // 24px spacing on extra large screens
});

// Set layout functions for different breakpoints
responsive_layout->setLayoutForBreakpoint("xs", [this]() {
    // Mobile: single column layout
    arrangeInSingleColumn();
});

responsive_layout->setLayoutForBreakpoint("md", [this]() {
    // Tablet: two column layout
    arrangeInTwoColumns();
});

responsive_layout->setLayoutForBreakpoint("lg", [this]() {
    // Desktop: three column layout
    arrangeInThreeColumns();
});

// Update for current container size
responsive_layout->updateForCurrentBreakpoint(container->width());
```

### Constraint Layout

```cpp
// Create a constraint layout
auto constraint_layout = layout_engine->createConstraintLayout(container);

// Add widgets and define constraints
constraint_layout->centerHorizontally(center_widget)
                 .centerVertically(center_widget)
                 .setWidth(center_widget, 200)
                 .setHeight(center_widget, 150)
                 .alignLeft(left_widget, center_widget, -20)  // 20px to the left
                 .alignTop(left_widget, center_widget)
                 .alignRight(right_widget, center_widget, 20) // 20px to the right
                 .alignTop(right_widget, center_widget);

constraint_layout->updateLayout();
```

## 🎛️ Advanced Features

### Named Grid Areas

```cpp
// Define named grid areas
grid_layout->defineArea("header", 1, 2, 1, 4)    // row 1-2, col 1-4
           .defineArea("sidebar", 2, 3, 1, 2)    // row 2-3, col 1-2
           .defineArea("main", 2, 3, 2, 3)       // row 2-3, col 2-3
           .defineArea("footer", 3, 4, 1, 4);    // row 3-4, col 1-4

// Place widgets in named areas
grid_layout->placeInArea(header_widget, "header")
           .placeInArea(sidebar_widget, "sidebar")
           .placeInArea(main_widget, "main")
           .placeInArea(footer_widget, "footer");
```

### Flex Item Properties

```cpp
// Configure individual flex item properties
flex_layout->setFlexGrow(item1, 2.0)      // grows twice as much
           .setFlexShrink(item2, 0.5)      // shrinks half as much
           .setFlexBasis(item3, 300)       // base size of 300px
           .setFlexProperties(item4, 1.0, 1.0, 200);  // flex: 1 1 200px
```

### Responsive Breakpoints

```cpp
// Custom breakpoint configuration
responsive_layout->addBreakpoint("xs", 0, 575)
                 .addBreakpoint("sm", 576, 767)
                 .addBreakpoint("md", 768, 991)
                 .addBreakpoint("lg", 992, 1199)
                 .addBreakpoint("xl", 1200, INT_MAX);

// Responsive margins and padding
responsive_layout->responsiveMargins({
    {"xs", QMargins(8, 8, 8, 8)},
    {"md", QMargins(16, 16, 16, 16)},
    {"lg", QMargins(24, 24, 24, 24)}
});
```

### Complex Constraints

```cpp
// Create complex constraint relationships
constraint_layout->addConstraint(
    widget1, ConstraintAttribute::Right,
    ConstraintType::Equal,
    widget2, ConstraintAttribute::Left,
    1.0, -10.0,  // multiplier: 1.0, constant: -10px
    ConstraintPriority::High
);

// Constraint with mathematical relationship
constraint_layout->addConstraint(
    widget1, ConstraintAttribute::Width,
    ConstraintType::Equal,
    widget2, ConstraintAttribute::Width,
    0.5, 0.0,  // width1 = 0.5 * width2
    ConstraintPriority::Medium
);
```

## 🎨 Layout Debugging

### Debug Mode

```cpp
// Enable debug mode for visual debugging
layout_engine->enableDebugMode(true);

// Debug specific layouts
LayoutUtils::debugLayout(container, "GridLayout");
LayoutUtils::highlightLayoutBounds(widget, Qt::red);
LayoutUtils::showLayoutGrid(container, 20);  // 20px grid
```

### Performance Monitoring

```cpp
// Monitor layout performance
connect(layout_engine.get(), &AdvancedLayoutEngine::layoutUpdated,
        [](QWidget* container) {
    qDebug() << "Layout updated for:" << container->objectName();
});

// Animate layout changes
LayoutUtils::animateLayoutChange(container, 300);  // 300ms animation
LayoutUtils::animateWidgetMove(widget, target_geometry, 250);
```

## 📱 Responsive Design Patterns

### Mobile-First Approach

```cpp
// Start with mobile layout
responsive_layout->setLayoutForBreakpoint("xs", [this]() {
    // Stack everything vertically
    for (int i = 0; i < widgets.size(); ++i) {
        widgets[i]->setGeometry(10, i * 90 + 10, container_width - 20, 80);
    }
});

// Enhance for larger screens
responsive_layout->setLayoutForBreakpoint("md", [this]() {
    // Two-column layout for tablets
    int col_width = (container_width - 30) / 2;
    for (int i = 0; i < widgets.size(); ++i) {
        int col = i % 2;
        int row = i / 2;
        widgets[i]->setGeometry(10 + col * (col_width + 10), row * 90 + 10, col_width, 80);
    }
});
```

## 🔧 Integration with Existing Framework

The Advanced Layout Engine integrates seamlessly with the existing DeclarativeUI framework:

```cpp
// Use with existing components
auto button = DeclarativeUI::Components::Button()
    .text("Click Me")
    .onClick([](){ qDebug() << "Button clicked!"; })
    .build();

// Add to advanced layout
grid_layout->addItem(button, 1, 1);

// Use with command system
auto layout_command = DeclarativeUI::Commands::Command()
    .name("UpdateLayout")
    .execute([grid_layout](){ grid_layout->updateLayout(); })
    .build();
```

## 📚 Example Application

A comprehensive example application demonstrating all layout features is available in `examples/AdvancedLayoutExample.cpp`. It includes:

- **Interactive controls** for real-time layout parameter adjustment
- **Live preview** of all layout types
- **Responsive design demonstration** with breakpoint visualization
- **Performance monitoring** and debugging tools
- **Configuration export/import** for layout persistence

To run the example:

```bash
mkdir build && cd build
cmake .. -DBUILD_ADVANCED_LAYOUTS=ON -DBUILD_EXAMPLES=ON
make AdvancedLayoutExample
./examples/advanced/AdvancedLayoutExample
```

## 🎯 Best Practices

1. **Choose the right layout type** for your use case:
   - **GridLayout**: Complex 2D layouts with precise positioning
   - **FlexLayout**: 1D layouts with flexible item sizing
   - **ResponsiveLayout**: Adaptive layouts for different screen sizes
   - **ConstraintLayout**: Precise relationships between elements

2. **Use named areas** in GridLayout for semantic clarity
3. **Implement responsive design** from the start
4. **Test on different screen sizes** and orientations
5. **Use debug mode** during development
6. **Animate layout changes** for smooth user experience

## 🔮 Future Enhancements

- **CSS-like syntax** for layout definitions
- **Visual layout editor** with drag-and-drop interface
- **Layout templates** for common patterns
- **Advanced constraint solver** with better conflict resolution
- **Performance optimizations** for large layouts
- **Integration with Qt Quick** layouts
