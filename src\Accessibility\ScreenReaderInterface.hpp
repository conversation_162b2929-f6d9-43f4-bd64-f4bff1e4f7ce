#pragma once

#include <QObject>
#include <QWidget>
#include <QAccessible>
#include <QAccessibleInterface>
#include <QTimer>
#include <QQueue>
#include <QJsonObject>
#include <QHash>

namespace DeclarativeUI::Accessibility {

/**
 * @brief Screen reader announcement types
 */
enum class AnnouncementType {
    Navigation,     // Focus changes, navigation
    Action,         // Button clicks, actions
    Status,         // Status updates, notifications
    Error,          // Error messages
    Warning,        // Warning messages
    Success,        // Success messages
    Live,           // Live region updates
    System          // System announcements
};

/**
 * @brief Screen reader announcement priority
 */
enum class AnnouncementPriority {
    Low = 0,        // Background information
    Normal = 1,     // Standard announcements
    High = 2,       // Important information
    Critical = 3    // Urgent announcements that interrupt
};

/**
 * @brief Screen reader announcement
 */
struct Announcement {
    QString text;
    AnnouncementType type = AnnouncementType::Status;
    AnnouncementPriority priority = AnnouncementPriority::Normal;
    QWidget* source = nullptr;
    qint64 timestamp = 0;
    bool interrupt = false;
    QString language;
    
    bool operator<(const Announcement& other) const {
        return priority < other.priority;
    }
};

/**
 * @brief Screen reader settings
 */
struct ScreenReaderSettings {
    bool enabled = false;
    bool speechEnabled = true;
    bool brailleEnabled = false;
    double speechRate = 1.0;
    double speechVolume = 0.8;
    QString voice;
    QString language = "en-US";
    bool announceKeystrokes = false;
    bool announceCharacters = false;
    bool announceWords = true;
    bool announcePunctuation = false;
    bool verboseMode = false;
    int announcementDelay = 100;  // ms
    int maxAnnouncementLength = 500;  // characters
    
    QJsonObject toJson() const;
    static ScreenReaderSettings fromJson(const QJsonObject& json);
};

/**
 * @brief Enhanced accessible interface for screen readers
 */
class ScreenReaderAccessibleInterface : public QAccessibleInterface, public QAccessibleActionInterface {
public:
    explicit ScreenReaderAccessibleInterface(QWidget* widget);
    
    // QAccessibleInterface implementation
    bool isValid() const override;
    QObject* object() const override;
    QWindow* window() const override;
    QVector<QPair<QAccessibleInterface*, QAccessible::Relation>> relations(QAccessible::Relation match = QAccessible::AllRelations) const override;
    QAccessibleInterface* focusChild() const override;
    
    QRect rect() const override;
    QAccessibleInterface* parent() const override;
    QAccessibleInterface* child(int index) const override;
    int childCount() const override;
    int indexOfChild(const QAccessibleInterface* child) const override;
    
    QString text(QAccessible::Text t) const override;
    void setText(QAccessible::Text t, const QString& text) override;
    QAccessible::Role role() const override;
    QAccessible::State state() const override;
    
    QColor foregroundColor() const override;
    QColor backgroundColor() const override;
    
    QStringList actionNames() const override;
    void doAction(const QString& actionName) override;
    QStringList keyBindingsForAction(const QString& actionName) const override;

    // QAccessibleInterface pure virtual methods
    QAccessibleInterface* childAt(int x, int y) const override;
    
    // Enhanced screen reader features
    QString getDetailedDescription() const;
    QString getContextualInformation() const;
    QStringList getAvailableActions() const;
    QString getNavigationHints() const;
    
private:
    QWidget* widget_;
    mutable QHash<QAccessible::Text, QString> cached_text_;
};

/**
 * @brief Comprehensive screen reader interface
 */
class ScreenReaderInterface : public QObject {
    Q_OBJECT
    
public:
    explicit ScreenReaderInterface(QObject* parent = nullptr);
    ~ScreenReaderInterface() override;
    
    // Settings management
    void setSettings(const ScreenReaderSettings& settings);
    ScreenReaderSettings getSettings() const { return settings_; }
    
    void setEnabled(bool enabled);
    bool isEnabled() const { return settings_.enabled; }
    
    // Speech synthesis
    void setSpeechEnabled(bool enabled);
    bool isSpeechEnabled() const { return settings_.speechEnabled; }
    
    void setSpeechRate(double rate);
    double getSpeechRate() const { return settings_.speechRate; }
    
    void setSpeechVolume(double volume);
    double getSpeechVolume() const { return settings_.speechVolume; }
    
    void setVoice(const QString& voice);
    QString getVoice() const { return settings_.voice; }
    
    QStringList getAvailableVoices() const;
    
    // Announcements
    void announce(const QString& text, 
                 AnnouncementType type = AnnouncementType::Status,
                 AnnouncementPriority priority = AnnouncementPriority::Normal,
                 QWidget* source = nullptr);
    
    void announceWidget(QWidget* widget, bool detailed = false);
    void announceNavigation(QWidget* from, QWidget* to);
    void announceAction(const QString& action, QWidget* widget = nullptr);
    void announceStatus(const QString& status, AnnouncementPriority priority = AnnouncementPriority::Normal);
    void announceError(const QString& error);
    void announceWarning(const QString& warning);
    void announceSuccess(const QString& success);
    
    // Live regions
    void registerLiveRegion(QWidget* widget, const QString& politeness = "polite");
    void unregisterLiveRegion(QWidget* widget);
    void updateLiveRegion(QWidget* widget, const QString& text);
    
    // Widget enhancement
    void enhanceWidget(QWidget* widget);
    void setWidgetDescription(QWidget* widget, const QString& description);
    void setWidgetRole(QWidget* widget, const QString& role);
    void setWidgetState(QWidget* widget, const QString& state, bool enabled = true);
    void setWidgetValue(QWidget* widget, const QString& value);
    
    // Navigation assistance
    void announceNavigationOptions(QWidget* widget);
    void announceShortcuts(QWidget* widget);
    void announceContextualHelp(QWidget* widget);
    
    // Reading modes
    void startContinuousReading(QWidget* container);
    void stopContinuousReading();
    bool isContinuousReading() const { return continuous_reading_; }
    
    void readCurrentLine();
    void readCurrentWord();
    void readCurrentCharacter();
    void readAll(QWidget* container);
    
    // Braille support (placeholder for future implementation)
    void setBrailleEnabled(bool enabled);
    bool isBrailleEnabled() const { return settings_.brailleEnabled; }
    
    // Screen reader detection
    static bool isScreenReaderActive();
    static QString getActiveScreenReader();
    
    // Accessibility tree navigation
    QAccessibleInterface* getCurrentAccessibleInterface() const;
    QAccessibleInterface* getAccessibleInterface(QWidget* widget) const;
    QString getAccessiblePath(QWidget* widget) const;
    
    // Validation and testing
    QStringList validateAccessibility(QWidget* widget) const;
    void generateAccessibilityReport(QWidget* root, const QString& filename) const;
    
signals:
    void announcementQueued(const Announcement& announcement);
    void announcementStarted(const QString& text);
    void announcementFinished(const QString& text);
    void speechStateChanged(int state);
    void settingsChanged(const ScreenReaderSettings& settings);
    void liveRegionUpdated(QWidget* widget, const QString& text);
    
private slots:
    void processAnnouncementQueue();
    void onSpeechStateChanged(int state);
    void onWidgetDestroyed(QObject* obj);
    void onApplicationFocusChanged(QWidget* old, QWidget* now);
    
private:
    // Core functionality
    void initializeSpeechEngine();
    void setupAccessibilityHooks();
    void installAccessibleFactory();
    
    // Announcement processing
    void queueAnnouncement(const Announcement& announcement);
    void speakText(const QString& text);
    void stopSpeaking();
    bool shouldInterrupt(const Announcement& announcement) const;
    QString preprocessText(const QString& text) const;
    QString formatAnnouncementText(const Announcement& announcement) const;
    
    // Widget analysis
    QString analyzeWidget(QWidget* widget, bool detailed = false) const;
    QString getWidgetRole(QWidget* widget) const;
    QString getWidgetState(QWidget* widget) const;
    QString getWidgetValue(QWidget* widget) const;
    QString getWidgetDescription(QWidget* widget) const;
    QString getWidgetContext(QWidget* widget) const;
    
    // Navigation helpers
    QString getNavigationInstructions(QWidget* widget) const;
    QStringList getAvailableActions(QWidget* widget) const;
    QString getShortcutInformation(QWidget* widget) const;
    
    // Continuous reading
    void setupContinuousReading(QWidget* container);
    void readNextElement();
    QList<QWidget*> getReadableElements(QWidget* container) const;
    
    // Text processing
    QString cleanupText(const QString& text) const;
    QString expandAbbreviations(const QString& text) const;
    QString addPronunciationHints(const QString& text) const;
    
private:
    ScreenReaderSettings settings_;
    
    // Speech synthesis (platform-specific implementation)
    void* speech_engine_ = nullptr;  // Platform-specific speech engine
    bool speech_available_ = false;
    
    // Announcement queue
    QQueue<Announcement> announcement_queue_;
    QTimer* announcement_timer_;
    bool currently_speaking_ = false;
    Announcement current_announcement_;
    
    // Live regions
    QHash<QWidget*, QString> live_regions_;  // widget -> politeness
    
    // Widget tracking
    QHash<QWidget*, QString> widget_descriptions_;
    QHash<QWidget*, QString> widget_roles_;
    QHash<QWidget*, QHash<QString, bool>> widget_states_;
    QHash<QWidget*, QString> widget_values_;
    
    // Continuous reading
    bool continuous_reading_ = false;
    QWidget* reading_container_ = nullptr;
    QList<QWidget*> reading_elements_;
    int current_reading_index_ = 0;
    
    // Accessibility interfaces
    QHash<QWidget*, QAccessibleInterface*> accessible_interfaces_;
    
    // State tracking
    QWidget* last_announced_widget_ = nullptr;
    QString last_announcement_text_;
    qint64 last_announcement_time_ = 0;
};

} // namespace DeclarativeUI::Accessibility
